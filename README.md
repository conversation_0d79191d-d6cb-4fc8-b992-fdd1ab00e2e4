# Steam Aggregation Backend

一个基于 FastAPI 的内容聚合后端服务，支持视频、文章、评论等多种内容类型的管理和分发。

## 项目概述

本项目是一个企业级内容聚合平台，提供完整的内容管理、用户管理、权限控制、监控告警和数据分析功能。项目采用 Spec-Driven Development 方法论，确保高质量的代码交付和可维护性。

### 核心特性

- 📚 **内容管理**: 支持视频、文章、评论等多种内容类型的完整生命周期管理
- 👥 **用户管理**: 完整的用户体系，包括注册、登录、权限管理、设备管理
- 🔐 **权限控制**: 基于 RBAC 的细粒度权限控制系统
- 📊 **数据分析**: 用户行为分析、内容效果分析、系统性能监控
- 🚨 **监控告警**: 实时系统监控、智能告警、多渠道通知
- ⚙️ **配置管理**: 动态配置中心、功能开关、配置变更审计
- 🔔 **通知系统**: 支持多种通知渠道，包括站内信、邮件、钉钉等

## 技术栈

### 后端技术
- **框架**: FastAPI (异步 Web 框架)
- **数据库**: PostgreSQL (主数据库) + TimescaleDB (时序数据)
- **缓存**: Redis (缓存 + 消息队列)
- **ORM**: SQLAlchemy 2.0
- **任务队列**: Celery
- **认证**: JWT + OAuth2
- **文档**: OpenAPI 3.0

### 开发工具
- **包管理**: uv (Python 包管理器)
- **代码检查**: Ruff
- **测试**: pytest + pytest-asyncio
- **数据库迁移**: Alembic
- **API 文档**: Swagger UI + ReDoc
- **规范开发**: Spec Kit

### 部署运维
- **容器化**: Docker + Docker Compose
- **进程管理**: systemd
- **日志**: Loguru
- **监控**: 自研监控系统 + Prometheus
- **告警**: 自研告警系统

## 快速开始

### 环境要求

- Python 3.12+
- PostgreSQL 14+
- Redis 6.0+
- Node.js 18+ (前端开发)
- FFmpeg 4.2.7+（视频转码为 HLS/m3u8 的最低版本要求）

### 安装步骤

1. **克隆项目**
   ```bash
   git clone https://github.com/your-org/steam-aggregation-backend.git
   cd steam-aggregation-backend
   ```

2. **安装依赖**
   ```bash
   # 安装 uv
   curl -LsSf https://astral.sh/uv/install.sh | sh

   # 创建虚拟环境
   uv venv

   # 激活虚拟环境
   source .venv/bin/activate

   # 安装项目依赖
   uv pip install -r pyproject.toml
   ```

3. **配置环境变量**
   ```bash
   # 复制环境变量模板
   cp .env.example .env

   # 编辑环境变量
   vim .env
   ```

4. **初始化数据库**
   ```bash
   # 创建数据库
   createdb steam_aggregation_db

   # 运行数据库迁移
   alembic upgrade head

   # 初始化基础数据
   python app/db/init_db.py
   python app/db/init_permissions.py
   ```

5. **启动服务**
   ```bash
   # 启动 Redis
   redis-server

   # 启动 Celery Worker
   celery -A app.tasks worker --loglevel=info

   # 启动 Celery Beat
   celery -A app.tasks beat --loglevel=info

   # 启动 FastAPI 应用
   uvicorn app.main:app --reload
   ```

6. **验证安装**
   ```bash
   # 访问健康检查
   curl http://localhost:8000/health

   # 访问 API 文档
   open http://localhost:8000/docs
   ```

## 项目结构

```
steam-aggregation-backend/
├── app/                          # 应用主目录
│   ├── api/                      # API 路由
│   │   ├── endpoints/            # API 端点实现
│   │   ├── deps.py               # 依赖注入
│   │   └── middleware.py         # 中间件
│   ├── core/                     # 核心功能
│   │   ├── security.py           # 安全相关
│   │   └── config.py             # 配置管理
│   ├── crud/                     # 数据库操作
│   ├── models/                   # 数据模型
│   ├── schemas/                  # Pydantic 模型
│   ├── services/                 # 业务逻辑
│   ├── tasks/                    # Celery 任务
│   └── main.py                   # 应用入口
├── tests/                        # 测试文件
├── alembic/                      # 数据库迁移
├── scripts/                      # 脚本文件
├── docs/                         # 文档
├── .specify/                     # Spec Kit 配置
│   ├── memory/                   # 项目记忆
│   ├── specs/                    # 功能规格
│   └── templates/                # 模板文件
├── docker-compose.yml            # Docker 编排
├── pyproject.toml               # 项目配置
└── README.md                    # 项目说明
```

## 开发指南

### 代码规范

项目遵循以下编码规范：

- **缩进**: 4 个空格
- **行宽**: 100 字符
- **引号**: 优先使用双引号
- **命名**: 
  - 模块/函数/变量: snake_case
  - 类: PascalCase
  - 常量: UPPER_SNAKE_CASE
- **导入顺序**: 标准库 → 第三方 → 应用模块

### 测试规范

- **测试文件命名**: `test_*.py`
- **测试函数命名**: `test_*`
- **测试覆盖率**: ≥ 85%
- **测试类型**: 单元测试、集成测试、端到端测试

### 提交流程

1. **分支策略**: Git Flow
2. **提交信息规范**: `type(scope): summary`
   - `type`: feat, fix, refactor, docs, chore, test
   - `scope`: 影响的模块或功能
   - `summary**: 简洁的变更描述

3. **PR 要求**:
   - 详细的变更说明
   - 相关的 issue 链接
   - 测试覆盖率报告
   - 代码审查通过

### Spec Kit 工作流

本项目采用 Spec-Driven Development 方法论：

1. **`/speckit.constitution`**: 定义项目原则和约束
2. **`/speckit.specify`**: 编写功能规格说明
3. **`/speckit.plan`**: 制定技术实现方案
4. **`/speckit.tasks`**: 拆分可执行任务
5. **`/speckit.implement`**: 执行开发实现

详细使用说明请参考 [AGENTS.md](AGENTS.md)。

## 当前功能开发

### 管理系统增强阶段一

当前正在进行管理系统增强阶段一的开发，重点构建配置与监控基线能力：

#### 已完成
- ✅ API 契约定义
- ✅ 数据模型设计
- ✅ 技术调研评估
- ✅ 快速入门指南

#### 进行中
- 🔄 数据库迁移
- 🔄 模型实现
- 🔄 API 开发
- 🔄 权限集成

#### 计划中
- ⏳ 测试用例编写
- ⏳ 性能优化
- ⏳ 文档完善
- ⏳ 部署准备

详细开发计划请参考 [管理系统功能增强计划](docs/admin_system_enhancement_plan.md)。

## API 文档

启动服务后，可以通过以下地址访问 API 文档：

- **Swagger UI**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc

### 主要 API 模块

#### 认证授权
- `POST /api/auth/login` - 用户登录
- `POST /api/auth/refresh` - 刷新令牌
- `POST /api/auth/logout` - 用户登出

#### 内容管理
- `GET /api/videos` - 获取视频列表
- `POST /api/videos` - 创建视频
- `GET /api/articles` - 获取文章列表
- `POST /api/articles` - 创建文章

#### 用户管理
- `GET /api/users` - 获取用户列表
- `GET /api/users/me` - 获取当前用户信息
- `PUT /api/users/me` - 更新用户信息

#### 管理系统 (新功能)
- `GET /api/admin/config` - 获取系统配置
- `PUT /api/admin/config` - 更新系统配置
- `GET /api/admin/monitoring/dashboard` - 获取监控面板
- `GET /api/admin/analytics/dashboard` - 获取数据分析面板

## 部署指南

### Docker 部署

```bash
# 构建镜像
docker-compose build

# 启动服务
docker-compose up -d

# 查看日志
docker-compose logs -f
```

### 生产环境部署

1. **环境准备**
   ```bash
   # 安装依赖
   sudo apt-get update
   sudo apt-get install -y python3-pip postgresql redis-server nginx
   
   # 配置数据库
   sudo -u postgres createdb steam_aggregation_db
   sudo -u postgres createuser steam_aggregation_user
   ```

2. **应用部署**
   ```bash
   # 克隆代码
   git clone https://github.com/your-org/steam-aggregation-backend.git
   cd steam-aggregation-backend
   
   # 安装依赖
   uv pip install -r pyproject.toml
   
   # 配置环境变量
   cp .env.example .env
   vim .env
   ```

3. **服务配置**
   ```bash
   # 配置 systemd 服务
   sudo cp scripts/steam-aggregation.service /etc/systemd/system/
   sudo systemctl enable steam-aggregation
   sudo systemctl start steam-aggregation
   
   # 配置 nginx
   sudo cp scripts/nginx.conf /etc/nginx/sites-available/steam-aggregation
   sudo ln -s /etc/nginx/sites-available/steam-aggregation /etc/nginx/sites-enabled/
   sudo nginx -t
   sudo systemctl reload nginx
   ```

## 监控与运维

### 健康检查

- **应用健康**: `GET /health`
- **数据库健康**: `GET /health/database`
- **Redis 健康**: `GET /health/redis`
- **Celery 健康**: `GET /health/celery`

### 日志管理

- **应用日志**: `/var/log/steam-aggregation/app.log`
- **错误日志**: `/var/log/steam-aggregation/error.log`
- **访问日志**: `/var/log/nginx/access.log`

### 监控指标

- **系统指标**: CPU、内存、磁盘、网络
- **应用指标**: 请求量、响应时间、错误率
- **业务指标**: 用户活跃度、内容访问量

### 告警配置

告警规则通过管理界面配置，支持：

- 阈值告警
- 趋势告警
- 异常检测
- 多渠道通知（钉钉、邮件、短信）

## 贡献指南

### 开发环境设置

1. **Fork 项目**
2. **创建功能分支**
   ```bash
   git checkout -b feature/your-feature-name
   ```
3. **开发并测试**
4. **提交变更**
   ```bash
   git commit -m "feat(auth): add OAuth2 support"
   ```
5. **推送分支**
   ```bash
   git push origin feature/your-feature-name
   ```
6. **创建 Pull Request**

### 代码审查

所有代码变更都需要经过代码审查，审查要点：

- 代码质量和规范性
- 测试覆盖率
- 性能影响
- 安全性考虑
- 文档完整性

## 常见问题

### Q: 如何重置数据库？
```bash
# 删除数据库
dropdb steam_aggregation_db

# 重新创建
createdb steam_aggregation_db

# 重新运行迁移
alembic upgrade head

# 重新初始化数据
python app/db/init_db.py
```

### Q: 如何处理 Celery 任务失败？
```bash
# 查看失败任务
celery -A app.tasks inspect failed

# 重试失败任务
celery -A app.tasks inspect retry

# 清理失败任务
celery -A app.tasks purge
```

### Q: 如何优化数据库性能？
```bash
# 分析慢查询
psql $DATABASE_URL -c "SELECT query, mean_time, calls FROM pg_stat_statements ORDER BY mean_time DESC LIMIT 10;"

# 重建索引
psql $DATABASE_URL -c "REINDEX DATABASE steam_aggregation_db;"

# 更新统计信息
psql $DATABASE_URL -c "ANALYZE;"
```

### Q: 如何调试通知 WebSocket 鉴权？
```bash
# 方式 1：临时启用调试日志（写入 /tmp/ws_auth_debug.log）
export WS_AUTH_DEBUG_ENABLED=true
uv run main.py

# 方式 2：使用调试脚本
uv run python scripts/debug_ws_auth.py --token <JWT> --enable-debug-log
```
- 日志写入 `/tmp/ws_auth_debug.log`，敏感信息默认脱敏。
- 调试结束后请取消环境变量或停止服务，避免生产环境长期输出调试日志。

## 许可证

本项目采用 MIT 许可证，详情请参阅 [LICENSE](LICENSE) 文件。

## 联系方式

- **项目主页**: https://github.com/your-org/steam-aggregation-backend
- **问题反馈**: https://github.com/your-org/steam-aggregation-backend/issues
- **文档**: https://github.com/your-org/steam-aggregation-backend/wiki
- **邮件**: <EMAIL>

## 致谢

感谢所有为这个项目做出贡献的开发者和社区成员。

---

**最后更新**: 2025-10-11
**版本**: 1.0.0