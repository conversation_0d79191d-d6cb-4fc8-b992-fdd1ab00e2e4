#!/usr/bin/env python3
"""
修复权限数据脚本

该脚本用于清理数据库中的错误权限记录，特别是使用了不正确资源类型的权限。
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from sqlalchemy import select, update
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker

# 直接使用数据库URL，避免复杂的配置导入
DATABASE_URL = "postgresql+asyncpg://steam_user:steam_password@localhost:5432/steam_aggregation"

from app.core.permission_system import ResourceType
from app.models.user import Permission as PermissionModel


async def analyze_permission_data():
    """分析权限数据，找出问题"""
    engine = create_async_engine(DATABASE_URL)
    async_session = sessionmaker(engine, class_=AsyncSession, expire_on_commit=False)

    async with async_session() as session:
        print("🔍 分析数据库中的权限数据...")

        # 获取所有权限记录
        result = await session.execute(select(PermissionModel))
        permissions = result.scalars().all()

        print(f"📊 总权限记录数: {len(permissions)}")

        # 按资源类型分组
        resource_types = {}
        invalid_permissions = []

        for perm in permissions:
            resource = perm.resource or "unknown"
            if resource not in resource_types:
                resource_types[resource] = []
            resource_types[resource].append(perm)

            # 检查是否是有效的资源类型
            try:
                ResourceType(resource)
            except ValueError:
                invalid_permissions.append(perm)

        print("\n📋 按资源类型分组:")
        for resource, perms in sorted(resource_types.items()):
            print(f"  {resource}: {len(perms)} 个权限")

        if invalid_permissions:
            print(f"\n❌ 发现 {len(invalid_permissions)} 个无效权限:")
            for perm in invalid_permissions:
                print(f"  ID: {perm.id}, Code: {perm.code}, Resource: {perm.resource}")
        else:
            print("\n✅ 所有权限的资源类型都是有效的")

        return invalid_permissions


async def fix_content_permissions():
    """修复使用 'content' 资源类型的权限"""
    engine = create_async_engine(DATABASE_URL)
    async_session = sessionmaker(engine, class_=AsyncSession, expire_on_commit=False)

    async with async_session() as session:
        print("🔧 修复 'content' 资源类型的权限...")

        # 查找所有使用 'content' 资源类型的权限
        result = await session.execute(
            select(PermissionModel).where(PermissionModel.resource == "content")
        )
        content_permissions = result.scalars().all()

        if not content_permissions:
            print("✅ 没有发现使用 'content' 资源类型的权限")
            return

        print(f"📋 发现 {len(content_permissions)} 个使用 'content' 资源类型的权限:")

        for perm in content_permissions:
            print(f"  ID: {perm.id}, Code: {perm.code}, Name: {perm.name}")

            # 根据权限代码判断应该使用什么资源类型
            if "article" in perm.code.lower() or "content" in perm.code.lower():
                new_resource = "article"
                new_code = perm.code.replace("content:", "article:")
            elif "video" in perm.code.lower():
                new_resource = "video"
                new_code = perm.code.replace("content:", "video:")
            else:
                # 默认使用 article
                new_resource = "article"
                new_code = perm.code.replace("content:", "article:")

            print(f"    -> 修改为: Resource: {new_resource}, Code: {new_code}")

            # 更新权限记录
            await session.execute(
                update(PermissionModel)
                .where(PermissionModel.id == perm.id)
                .values(resource=new_resource, code=new_code)
            )

        await session.commit()
        print(f"✅ 成功修复 {len(content_permissions)} 个权限记录")


async def remove_content_resource_type():
    """从ResourceType中移除CONTENT类型"""
    print("🗑️  准备从ResourceType中移除CONTENT类型...")

    # 这需要手动修改代码文件
    permission_system_file = project_root / "app" / "core" / "permission_system.py"

    with open(permission_system_file, encoding="utf-8") as f:
        content = f.read()

    # 移除CONTENT行
    lines = content.split("\n")
    new_lines = []
    for line in lines:
        if 'CONTENT = "content"' in line and "# 临时添加" in line:
            print("  移除临时添加的CONTENT资源类型")
            continue
        new_lines.append(line)

    new_content = "\n".join(new_lines)

    with open(permission_system_file, "w", encoding="utf-8") as f:
        f.write(new_content)

    print("✅ 已从ResourceType中移除CONTENT类型")


async def main():
    """主函数"""
    print("🚀 开始修复权限数据...")

    try:
        # 1. 分析当前权限数据
        invalid_permissions = await analyze_permission_data()

        if not invalid_permissions:
            print("\n✅ 权限数据没有问题，无需修复")
            return

        # 2. 修复content权限
        await fix_content_permissions()

        # 3. 再次分析确认修复结果
        print("\n🔍 验证修复结果...")
        invalid_permissions_after = await analyze_permission_data()

        if not invalid_permissions_after:
            print("\n✅ 权限数据修复成功！")

            # 4. 移除临时的CONTENT资源类型
            await remove_content_resource_type()

            print("\n🎉 权限系统修复完成！")
            print("💡 建议重启应用程序以确保更改生效")
        else:
            print(f"\n❌ 仍有 {len(invalid_permissions_after)} 个无效权限需要手动处理")

    except Exception as e:
        print(f"❌ 修复过程中出现错误: {e}")
        import traceback

        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
