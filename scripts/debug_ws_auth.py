"""手动测试 WebSocket 鉴权依赖的脚本。

用法示例：

    uv run python scripts/debug_ws_auth.py --token <JWT> --enable-debug-log

- 默认打印简要调试信息；如需写入 `/tmp/ws_auth_debug.log`，可在命令中追加
  `--enable-debug-log` 或预先导出环境变量 `WS_AUTH_DEBUG_ENABLED=true`。
- 脚本会创建一个简易的 WebSocket mock，并调用
  `app.api.deps.get_current_user_websocket`，打印鉴权结果与关键阶段。
"""

import argparse
import asyncio
from dataclasses import dataclass
from typing import Any

from app.api import deps
from app.config import get_settings
from app.db.session import SessionLocal
from app.services.token_service import TokenService


def _mask_token(token: str | None) -> str:
    if not token:
        return "<empty>"
    token = token.strip()
    if len(token) <= 10:
        return token
    return f"{token[:4]}...{token[-4:]}"


@dataclass
class MockWebSocket:
    """满足鉴权依赖所需接口的 WebSocket mock。"""

    token: str | None

    def __post_init__(self) -> None:
        # 依赖中会读取以下属性
        self.headers: dict[str, Any] = {}
        self.cookies: dict[str, Any] = {}
        self.client: tuple[str, int] | None = ("127.0.0.1", 0)
        self.url: str = "/api/v1/notifications/ws"

    async def close(self, code: int, reason: str) -> None:
        print(f"MockWebSocket.close called code={code} reason={reason}")


async def run(token: str) -> None:
    print(f"[debug] start websocket auth check masked_token={_mask_token(token)}")

    async with SessionLocal() as db:
        token_service = TokenService(auth_config=get_settings())
        ws = MockWebSocket(token)

        try:
            user = await deps.get_current_user_websocket(
                websocket=ws,
                token=token,
                db=db,
                token_service=token_service,
            )
        except Exception as exc:  # noqa: BLE001 - 调试阶段需要完整异常
            print(f"[debug] authentication failed: {exc!r}")
        else:
            print(
                "[debug] authentication success user_id={} username={}".format(
                    getattr(user, "id", "<unknown>"),
                    getattr(user, "username", "<unknown>"),
                )
            )


def main() -> None:
    parser = argparse.ArgumentParser(description="调试 WebSocket 鉴权依赖")
    parser.add_argument("--token", required=True, help="需要验证的 JWT")
    parser.add_argument(
        "--enable-debug-log",
        action="store_true",
        help="同时开启 WS_AUTH_DEBUG_ENABLED 输出 `/tmp/ws_auth_debug.log`",
    )
    args = parser.parse_args()

    if args.enable_debug_log:
        deps.settings.WS_AUTH_DEBUG_ENABLED = True
        print("[debug] WS_AUTH_DEBUG_ENABLED set to True for this run")

    asyncio.run(run(args.token))


if __name__ == "__main__":
    main()
