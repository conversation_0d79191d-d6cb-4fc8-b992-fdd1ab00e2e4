# 视频模块 API 文档

> 基础路径统一为 `/api/v1/videos`，除特别说明外均返回 JSON。需要鉴权的接口请求头携带 Token：`Authorization: Bearer <token>`。

## 公共约定

### 鉴权与权限
- **登录用户**：`deps.get_current_user_optional` 允许匿名访问；`deps.get_current_active_user` 表示必须登录。
- **管理员权限**：需要具备 `VIDEO.MANAGE.ALL`（`Permission(ResourceType.VIDEO, Action.MANAGE, Scope.ALL)`）。

### 游标分页规范
大部分列表接口返回 `CursorPaginationResponse`：

```json
{
  "items": [...],
  "has_next": true,
  "has_previous": false,
  "next_cursor": "opaque-string-or-id",
  "previous_cursor": null,
  "total_count": 120
}
```

常见查询参数：
- `cursor`：上一页返回的 `next_cursor`；首次请求可省略。
- `size`：每页数量，1~100，默认 20。
- `order_by`：排序字段，默认 `id`。
- `order_direction`：`asc` 或 `desc`，默认 `desc`。

### 核心数据结构
`VideoOut`（Pydantic）主要字段：

| 字段 | 类型 | 说明 |
| --- | --- | --- |
| `id` | int | 视频 ID |
| `title` | str | 视频标题 |
| `description` | str\|null | 视频简介 |
| `url` | str | 视频地址（已自动拼接 CDN 前缀） |
| `cover_url` | str\|null | 封面图（同样拼接 CDN） |
| `duration` | int\|null | 视频时长（秒） |
| `is_published` | bool | 是否已发布 |
| `is_approved` | bool | 是否通过审核 |
| `category` | CategoryBase\|null | 分类信息 |
| `tags` | TagBase[]\|null | 标签 |
| `author` | UserAggregated\|null | 作者信息（含统计与关注状态） |
| `stats` | ContentStats\|null | 点赞/收藏/访问等统计，含当前用户个性化标记 |
| `folder` | VideoFolderOut\|null | 所属文件夹 |
| `meta` | ContentMeta\|null | SEO 元信息（content_id、slug、keywords 等） |
| `extra` | dict | 扩展字段 |

---

## 推荐与发现

### GET `/recommendations`
- **说明**：获取当前登录用户的视频推荐。
- **鉴权**：登录。
- **查询参数**：游标分页通用参数。
- **响应**：`CursorPaginationResponse[VideoOut]`。无推荐时返回空数组、`has_next=false`。

### GET `/{video_id}/similar`
- **说明**：获取与指定视频相似的内容。
- **鉴权**：公开。
- **路径参数**：`video_id`（int）。
- **响应**：`CursorPaginationResponse[VideoOut]`，内部保持推荐顺序，`has_next=false`。

### GET `/hot`
- **说明**：热门视频列表。
- **鉴权**：公开。
- **查询参数**：游标分页通用参数，内部通过 offset 查询热门数据。
- **响应**：`CursorPaginationResponse[VideoOut]`。

### GET `/category/{category_id}`
- **说明**：获取指定分类下的视频，`category_id=0` 表示全部公开视频。
- **鉴权**：公开。
- **路径参数**：`category_id`（int）。
- **查询参数**：游标分页通用参数。
- **响应**：`CursorPaginationResponse[VideoOut]`；分类不存在返回 404。

---

## 文件夹与用户内容

### GET `/users/{user_id}/folders`
- **说明**：获取用户的视频文件夹列表，可附加统计与子节点信息。
- **路径参数**：`user_id`（int）。
- **查询参数**：
  - `cursor`, `size`, `order_by`, `order_direction`
  - `include_video_count`（bool，默认 true）
  - `include_children`（bool，默认 false）
  - `include_recent_videos`（bool，默认 false）：附带最多 3 个最新视频概要。
  - `include_default_folder`（bool，默认 true）
  - `include_total`（bool，默认 false）：计算总数，性能开销较大。
- **鉴权**：
  - 用户本人：可访问全部文件夹。
  - 其他用户/匿名：仅可访问公开文件夹；具备 FOLDER.ALL 权限可突破限制。
- **响应**：`CursorPaginationResponse[VideoFolder]`，若包含最近视频则在 `VideoFolder` 附加 `recent_videos`。

### GET `/folders/{folder_id}/videos`
- **说明**：获取指定文件夹内的视频列表（重构版）。
- **路径参数**：`folder_id`（int）。
- **查询参数**：`cursor`（默认 0）、`size`（默认 20，1~100）、`status`（可选，`draft|pending|rejected|approved`）。
- **鉴权**：
  - 文件夹所有者或管理员可访问全部状态。
  - 其他用户仅可访问公开文件夹，且结果限制为已发布且已审核的视频。
- **响应**：`CursorPaginationResponse[VideoOut]`。

---

## 管理端接口

### GET `/admin`
- **说明**：管理员视频列表，支持多条件过滤。
- **鉴权**：登录 + `VIDEO.MANAGE.ALL`。
- **查询参数**：
  - 游标分页通用参数
  - `status`: `all|draft|published_approved|published_pending|published_rejected`
  - `author_id`, `category_id`, `keyword`
  - `start_date`, `end_date`（`YYYY-MM-DD`）
- **响应**：`CursorPaginationResponse[VideoOut]`；日期格式或状态非法返回 400。

### PUT `/admin/{video_id}/status`
- **说明**：单条更新视频状态，可附带原因并写入审计。
- **路径参数**：`video_id`（int）。
- **查询参数**：`status`（必填）、`reason`（选填）。
- **鉴权**：登录 + `VIDEO.MANAGE.ALL`。
- **响应**：`VideoOut`（更新后）。

### POST `/admin/batch-status`
- **说明**：批量更新视频状态，写入批量审计记录。
- **鉴权**：登录 + `VIDEO.MANAGE.ALL`。
- **请求体**：

```json
{
  "video_ids": [1, 2, 3],
  "status": "published_pending",
  "reason": "批量审核待处理"
}
```

- **响应**：`{"message": "成功更新 3 个视频状态", "updated_count": 3}`。

### DELETE `/admin/{video_id}`
- **说明**：管理员删除视频（软删），带确认流程。
- **路径参数**：`video_id`（int）。
- **请求体**：
  - 第一步 `{ "confirm": false }`（默认）：返回视频详情、影响范围、下一步提示。
  - 第二步 `{ "confirm": true }`：执行删除并更新统计。
- **鉴权**：登录 + `VIDEO.MANAGE.ALL`。
- **响应**：
  - 确认前：返回确认信息。
  - 删除后：`{"message": "视频删除成功", "video_id": 1, "deleted_by": 42}`。

---

## 通用视频操作

### GET `/{video_id}`
- **说明**：获取单个视频的完整聚合信息。
- **鉴权**：公开访问已发布视频；未审核或无权限会返回 404。
- **路径参数**：`video_id`（int）。
- **响应**：`VideoOut`。

### PUT `/{video_id}`
- **说明**：更新视频，底层使用 `VideoFolderService.update_video`。
- **鉴权**：登录用户，服务内部校验是否作者或具备管理权限。
- **路径参数**：`video_id`（int）。
- **请求体**（`VideoUpdate` 示例）：

```json
{
  "title": "新标题",
  "description": "简介",
  "url": "https://cdn.xxx/video.mp4",
  "cover_url": "https://cdn.xxx/cover.jpg",
  "duration": 120,
  "is_published": true,
  "tags": ["教程", "速览"],
  "category_id": 3,
  "folder_id": 12
}
```

- **响应**：`VideoOut`。

### DELETE `/{video_id}`
- **说明**：作者或管理员删除视频（软删），调用 `VideoFolderService.delete_video`。
- **鉴权**：登录，后端校验所有权或权限。
- **路径参数**：`video_id`（int）。
- **响应**：`204 No Content`。

---

## 用户播放进度

### POST `/{video_id}/progress`
- **说明**：上报用户播放进度，支持断点续播。
- **路径参数**：`video_id`（int）。
- **查询参数**：`progress_seconds`（必填 int）、`total_duration`（必填 int）。
- **鉴权**：登录。
- **响应**：

```json
{
  "message": "播放进度已更新",
  "progress_seconds": 85,
  "completion_rate": 0.85,
  "is_completed": false
}
```

### GET `/{video_id}/progress`
- **说明**：获取用户最近一次播放进度。
- **路径参数**：`video_id`（int）。
- **鉴权**：登录。
- **响应**：

```json
{
  "progress_seconds": 85,
  "completion_rate": 0.85,
  "is_completed": false,
  "last_watched": "2025-10-29T08:12:33.123456"
}
```

- 无记录时返回进度 0、完成率 0。

---

## 后续建议
- 若前端需要接口示例或 Swagger 摘要，可基于本文档补充 mock 示例。
- 推荐将此文档纳入 Spec Kit 或团队知识库，便于后续版本更新时同步维护。
