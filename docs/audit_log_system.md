# 审计日志系统设计文档

## 概述

审计日志系统是一个完整的管理员操作记录和追踪系统，用于记录所有管理员的操作行为，提供完整的审计追踪能力。

## 系统架构

### 1. 数据模型

#### AuditLog 表结构
```sql
CREATE TABLE audit_logs (
    id SERIAL PRIMARY KEY,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    user_id INTEGER NOT NULL,
    user_name VARCHAR(100) NOT NULL,
    user_email VARCHAR(255),
    ip_address VARCHAR(45),
    user_agent TEXT,
    request_id VARCHAR(100),
    session_id VARCHAR(100),
    action VARCHAR(50) NOT NULL,
    resource_type VARCHAR(50) NOT NULL,
    resource_id INTEGER,
    resource_name VARCHAR(255),
    description TEXT,
    reason TEXT,
    old_values JSON,
    new_values JSON,
    success BOOLEAN DEFAULT TRUE,
    error_message TEXT,
    endpoint VARCHAR(255),
    method VARCHAR(10),
    duration_ms INTEGER
);
```

#### 索引设计
- 主键索引：`id`
- 时间索引：`timestamp`
- 用户索引：`user_id`
- 操作类型索引：`action`
- 资源类型索引：`resource_type`
- 复合索引：`(user_id, timestamp)`, `(resource_type, resource_id, timestamp)`

### 2. 核心组件

#### AuditLogService
负责审计日志的记录和查询：
- `log_action()`: 记录操作日志
- `get_logs()`: 查询日志列表
- `get_log_count()`: 获取日志总数

#### 审计装饰器
提供便捷的日志记录方式：
- `@audit_log`: 通用审计装饰器
- `@audit_create`: 创建操作审计
- `@audit_update`: 更新操作审计
- `@audit_delete`: 删除操作审计

## 使用方法

### 1. 手动记录日志

```python
from app.services.audit_log_service import AuditLogService
from app.models.audit_log import AuditAction, AuditResourceType

# 记录操作日志
await AuditLogService.log_action(
    db=db,
    user=current_user,
    action=AuditAction.UPDATE,
    resource_type=AuditResourceType.ARTICLE,
    resource_id=article_id,
    resource_name=article.title,
    description="更新文章状态",
    reason="内容违规",
    old_values={"status": "published"},
    new_values={"status": "blocked"},
    request=request
)
```

### 2. 使用装饰器

```python
from app.core.audit_decorator import audit_update
from app.models.audit_log import AuditResourceType

@audit_update(
    resource_type=AuditResourceType.ARTICLE,
    description="更新文章状态",
    get_resource_id=lambda result, *args, **kwargs: kwargs.get('article_id'),
    get_resource_name=lambda result, *args, **kwargs: result.title if result else None
)
async def update_article_status(
    db: AsyncSession,
    current_user: User,
    article_id: int,
    status: str
):
    # 业务逻辑
    pass
```

### 3. API 接口使用

#### 查询审计日志
```http
GET /api/audit/?user_id=123&action=UPDATE&start_date=2024-01-01&limit=50
```

#### 获取统计信息
```http
GET /api/audit/stats/overview?days=30
```

#### 用户活动摘要
```http
GET /api/audit/users/activity?days=7&limit=20
```

#### 资源活动摘要
```http
GET /api/audit/resources/activity?resource_type=ARTICLE&days=30
```

## 权限控制

### 权限级别
- `AUDIT_READ_ALL`: 查看所有审计日志
- `AUDIT_READ_OWN`: 只能查看自己的操作日志
- `AUDIT_EXPORT_ALL`: 导出审计日志

### 数据隔离
- 超级管理员：可以查看所有日志
- 部门管理员：只能查看本部门相关的日志
- 普通管理员：只能查看自己的操作日志

## 性能优化

### 1. 数据库优化
- 按时间分表（月表或季度表）
- 创建合适的索引
- 考虑使用时序数据库

### 2. 查询优化
- 分页查询避免大量数据加载
- 使用缓存减少重复查询
- 异步处理导出等耗时操作

### 3. 存储优化
- 热数据存储在主数据库
- 冷数据归档到对象存储
- 定期清理过期数据

## 安全考虑

### 1. 敏感信息保护
- 密码等敏感字段不记录或脱敏
- 个人隐私信息需要特殊权限才能查看

### 2. 日志完整性
- 审计日志本身不允许修改或删除
- 只有系统级别的清理任务可以删除过期日志

### 3. 访问控制
- 严格的权限控制
- 操作日志的查看也会被记录

## 监控和告警

### 1. 异常监控
- 大量失败操作告警
- 异常IP地址告警
- 敏感操作告警

### 2. 性能监控
- 日志写入性能监控
- 查询响应时间监控
- 存储空间监控

## 扩展功能

### 1. 报表功能
- 用户活跃度报表
- 操作频次统计
- 安全风险分析

### 2. 导出功能
- CSV格式导出
- Excel格式导出
- PDF报告生成

### 3. 实时通知
- 重要操作实时通知
- 异常行为告警
- 定期审计报告

## 部署和维护

### 1. 数据库迁移
```bash
# 运行迁移
alembic upgrade head
```

### 2. 定期维护
- 定期清理过期日志
- 数据归档
- 索引优化

### 3. 备份策略
- 定期备份审计日志
- 异地备份
- 恢复测试
