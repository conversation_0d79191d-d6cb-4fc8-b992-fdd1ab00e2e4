# Spec Kit 规范化开发指南

本指南帮助团队成员在现有 Steam Aggregation Backend 项目中，高效、规范地使用 Spec Kit 与 Slash 命令完成“需求→计划→任务→实现”的闭环流程。阅读完本文件后，你应能清楚何时运行各个命令、应该准备哪些输入、以及如何与仓库内的文档与分支策略协同。

## 1. 前置环境与常用命令

| 组件 / 配置 | 说明 |
| --- | --- |
| Python | 使用项目自带的 `.venv`（Python 3.12.10），满足 Spec Kit 对 3.11+ 的要求 |
| uv | ≥ 0.8.10，负责依赖安装与 `specify-cli` 管理 |
| Specify CLI | 已预装 `specify-cli 0.0.19`，可运行 Slash 命令 |
| AI Agent | Codex CLI（推荐）或其他支持 Slash 命令的代理 |
| 环境变量 | 进入项目根目录后执行 `export CODEX_HOME=/usr/local/data/steam_Aggregation_backend/.codex`，确保代理能读取缓存 |

常见自检命令：

```bash
specify check              # 校验 Git、AI Agent、Python、uv 等依赖
uv pip install -r pyproject.toml  # 通过 uv 安装/更新依赖
pytest tests/...                   # 针对当前阶段运行测试套件（config/monitoring/analytics 等）
ruff check app tests               # 静态检查确保代码风格与质量
alembic upgrade --sql head         # 生成最新迁移 SQL，供审计/DBA 复核
```

## 2. Spec Kit 产物结构

Spec Kit 生成的所有资料位于 `.specify/` 目录下：

- `memory/constitution.md`：项目宪章，约束通用开发准则。
- `specs/<feature-id>/spec.md`：功能规格（What/Why）。
- `specs/<feature-id>/plan.md`：技术方案（How）。
- `specs/<feature-id>/tasks.md`：实施任务拆分（含顺序与依赖）。
- `specs/<feature-id>/contracts/`：接口/数据契约文档。
- `specs/<feature-id>/quickstart.md`、`research.md` 等：补充引导与调研成果。

> **提示**：`<feature-id>` 对应分支或特性编号，例如 `002-admin-enhancement-phase1`。请在提交前确保 `.specify/` 与 `docs/` 中相关文档同步更新。

## 3. 标准工作流总览

| 阶段 | 对应命令 | 目标 | 输出 | 质量检查 |
| --- | --- | --- | --- | --- |
| 原则对齐 | `/speckit.constitution` | 建立/更新团队开发宪章、质量守则 | `memory/constitution.md` | 是否覆盖代码质量、测试、性能、体验等维度 |
| 需求规格 | `/speckit.specify` | 将需求转化为结构化功能规格 | `spec.md` | 用户故事完整性、验收标准可验证 |
| 技术方案 | `/speckit.plan` | 结合项目栈生成实现路径 | `plan.md`、`data-model.md`、`contracts/` | 是否引用既有服务与约束，避免过度设计 |
| 任务拆分 | `/speckit.tasks` | 形成可执行任务列表与依赖 | `tasks.md` | 任务是否对应代码目录，TDD 顺序明确 |
| 计划执行 | `/speckit.implement` | 自动化执行任务、推进开发 | 更新代码、测试、文档 | 任务状态正确，单元测试与静态检查通过 |

## 4. 各阶段操作细则

### 4.1 `/speckit.constitution`

- **适用时机**：项目初始化、重大流程变更或跨团队协作前。
- **输入要点**：明确代码质量标准、测试覆盖率、性能与安全要求、PR 审核准则。
- **输出检视**：确认文件中包含最新的发布流程、代码风格、依赖管理策略；必要时纳入 `docs/` 相关文档。
- **维护建议**：每轮大版本迭代或引入重要工具时复核一次，避免宪章与实际流程脱节。

### 4.2 `/speckit.specify`

- **目标**：聚焦 “做什么 / 为什么”，不讨论技术细节。
- **执行步骤**：
  1. 准备需求背景、目标用户、核心流程、验收标准。
  2. 在代理终端运行命令并粘贴需求描述。
  3. 使用 `/speckit.clarify`（可选）补充缺失信息，确认 Review Checklist。
- **交付物**：`spec.md`，内含用户故事、验收标准、未决问题。
- **质量检查**：确保所有功能都有可验证的验收标准；对不确定项在 Clarifications 中列出。

### 4.3 `/speckit.plan`

- **目标**：产出技术实现方案，与现有 FastAPI/SQLAlchemy/Celery 栈协同。
- **输入要点**：技术栈约束、数据库/缓存约束、部署要求、性能指标。
- **输出检视**：
  - `plan.md`：列出服务组件、模块划分、依赖关系。
  - `data-model.md`：数据库/模型变更。
  - `contracts/`：API 契约、事件/消息格式。
- **最佳实践**：评估方案是否复用现有模块（例如 `app/services/`），避免重复造轮子；若涉及权限/配置，引用 `docs/permissions_guide.md` 与 `.specify/.../quickstart.md`。

### 4.4 `/speckit.tasks`

- **目标**：将方案拆成可执行任务，指导 `/speckit.implement` 自动化推进。
- **任务编排建议**：
  - 以目录为单位（`api/`、`services/`、`schemas/`、`tests/`）组织任务。
  - 优先考虑 TDD：先编写/补充测试，再实现功能。
  - 使用任务依赖标记确保数据库迁移、权限初始化等前后顺序正确。
- **审核要点**：确认任务覆盖所有关键模块；输出中应包含测试、文档更新、审计日志集成等环节。

### 4.5 `/speckit.implement`

- **目标**：根据 `tasks.md` 顺序执行开发与验证。
- **准备工作**：确保宪章、规格、计划、任务均为最新；本地依赖和服务（PostgreSQL、Redis、Celery）已就绪。
- **执行提示**：
  - 命令会触发多条子任务（创建文件、运行测试等），请关注终端输出。
  - 若步骤失败，可修复问题后重新运行，它会从未完成的任务继续。
  - 对生成的代码进行人工复核，尤其是安全、权限、性能相关逻辑。
- **完成标准**：所有任务标记为 Done，`pytest`、`ruff check`、`alembic upgrade --sql` 等质量门禁通过。

## 5. 协作与版本控制策略

- **分支命名**：遵循 `type(scope): summary` 的提交信息与 `feat/<scope>-<desc>` 的分支约定；Spec Kit 生成的 `<feature-id>` 可直接用于分支名称。
- **文档同步**：当 Spec Kit 文档更新时，请同步维护 `docs/` 下的指南或 API 契约，保证团队成员能快速查阅。
- **权限与配置**：新增管理后台功能需遵循 `.specify/specs/002-admin-enhancement-phase1/quickstart.md` 中的 RBAC 要求，并审计操作日志。
- **测试策略**：保持 ≥85% 覆盖率；涉及异步任务或外部服务时，利用 `tests/mocks/` 中的桩对象。
- **代码评审**：PR 描述应包含问题背景、解决方案、验证方式、相关工单；引用 Spec Kit 产物作为设计依据。

## 6. 常见问题排查

| 场景 | 排查办法 |
| --- | --- |
| Slash 命令不可用 | 确认已导出 `CODEX_HOME`，并在项目根目录运行代理；执行 `specify check` 检查环境 |
| `specify check` 失败 | 按提示补齐缺失工具（Git、uv、Python、AI Agent）；若是权限问题，可重装或升级 `specify-cli` |
| `/speckit.implement` 中途失败 | 根据终端日志定位失败的任务，手动修复后重新运行命令，流程会从未完成任务继续 |
| 文档与实现不一致 | 先更新 `.specify/specs/<feature-id>/` 下的对应文件，再同步 `docs/` 中的公共文档 |
| Slash 命令生成多余内容 | 审核 Spec/Plan 后，可手动编辑产物并记录调整原因，避免后续任务误用 |

## 7. 参考资料

- `AGENTS.md`：环境状态、命令说明、进行中的功能列表与注意事项。
- `docs/project_usage_guide.md`：项目整体使用指南及本地运行说明。
- `.specify/specs/002-admin-enhancement-phase1/`：当前管理后台增强项目的详尽规格与计划。
- Spec Kit 官方文档：https://github.com/github/spec-kit

如需扩展 Spec Kit 工作流（例如增加 `/speckit.analyze`、`/speckit.checklist`），可先在宪章中补充治理准则，再与团队约定使用场景。祝开发顺利！
