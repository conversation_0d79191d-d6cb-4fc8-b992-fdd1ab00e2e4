# 日志配置说明

## 概述
项目使用 loguru 作为日志库，支持在生产模式下按日期自动分割日志文件。

## 日志模式

### 开发模式 (MODE=dev)
- 日志仅输出到 stderr
- 日志级别：DEBUG
- 输出格式：可读文本

### 生产模式 (MODE=production 或 MODE=prod)
- 日志输出到 stderr 和文件
- 文件路径：`logs/app_YYYY-MM-DD.log`
- 日志级别：INFO
- 输出格式：JSON（便于日志分析工具处理）

## 日志文件管理

### 自动分割
- **分割方式**：每天午夜（00:00）自动创建新的日志文件
- **文件命名**：`app_YYYY-MM-DD.log`（例如：`app_2025-10-23.log`）
- **编码**：UTF-8

### 日志保留
- **保留期限**：30天
- **自动清理**：超过30天的日志文件会被自动删除
- **压缩**：旧日志文件会自动压缩为 .zip 格式以节省存储空间

## 配置环境变量

在 `.env` 文件中设置运行模式：

```bash
# 开发模式（默认）
MODE=dev

# 生产模式
MODE=production
# 或
MODE=prod
```

## 日志使用示例

```python
from app.core.logging import logger

# 基础日志
logger.info("用户登录成功")
logger.warning("缓存未命中")
logger.error("数据库连接失败")

# 带上下文的日志
logger.bind(user_id=123, action="login").info("用户操作")

# 异常日志
try:
    # some code
    pass
except Exception as e:
    logger.exception("处理请求时发生异常")
```

## 日志格式

生产模式下的 JSON 日志格式示例：

```json
{
  "text": "用户登录成功\n",
  "record": {
    "elapsed": {"repr": "0:00:00.037101", "seconds": 0.037101},
    "exception": null,
    "extra": {"user_id": 123},
    "file": {"name": "auth.py", "path": "/app/api/auth.py"},
    "function": "login",
    "level": {"icon": "ℹ️", "name": "INFO", "no": 20},
    "line": 45,
    "message": "用户登录成功",
    "time": {"repr": "2025-10-23 05:52:54.838746+00:00", "timestamp": 1761198774.838746}
  }
}
```

## 注意事项

1. **生产环境部署**：确保 `MODE` 环境变量设置为 `production` 或 `prod`
2. **日志目录权限**：确保应用有权限在 `logs/` 目录创建和写入文件
3. **磁盘空间**：定期监控日志目录的磁盘使用情况
4. **日志分析**：可使用 ELK、Grafana Loki 等工具分析 JSON 格式的日志
5. **敏感信息**：避免在日志中记录密码、token 等敏感信息

## 故障排查

### 日志文件未生成
1. 检查 `MODE` 环境变量是否设置为生产模式
2. 检查 `logs/` 目录是否存在且有写入权限
3. 查看 stderr 输出是否有错误信息

### 磁盘空间不足
1. 调整 `retention` 参数减少日志保留天数
2. 确认旧日志文件正确压缩和清理
3. 考虑使用外部日志收集系统

## 配置文件位置
`app/core/logging.py`
