# Steam Aggregation Backend 使用指南

本指南帮助首次接触本项目的成员快速完成本地环境搭建、运行、测试与常见运维操作。若你希望了解系统架构和增强路线，请参阅 `docs/architecture_overview.md` 与 `docs/admin_system_enhancement_plan.md`。

## 1. 项目概览

- **框架栈**：Python 3.12（`.venv` 虚拟环境）、FastAPI、SQLAlchemy、Redis、Celery、PostgreSQL。
- **核心目录**：
  - `app/`：业务代码，包含 `api/`（FastAPI 路由）、`services/`、`crud/`、`schemas/`、`tasks/`、`models/`。
  - `tests/`：pytest 用例，结构与 `app/` 镜像对应。
  - `alembic/`：数据库迁移脚本。
  - `docs/`：功能规格、架构与运维文档。
  - `.specify/`：Spec Kit 产物（spec、plan、tasks 等）。

## 2. 前置要求

| 组件 | 版本 / 说明 |
| --- | --- |
| Python | 3.12.x（项目自带 `.venv`，建议直接使用） |
| [uv](https://docs.astral.sh/uv/) | ≥ 0.8.10，用于依赖安装和运行 Specify CLI |
| PostgreSQL & Redis | 本地服务或通过 `docker-compose` 启动 |
| Specify CLI | 已安装 `specify-cli 0.0.19`，结合 `.specify` 工作流 |
| AI Agent | Codex CLI（推荐）或其他支持 Slash 命令的代理 |

> **Tip**：进入终端后导出 `CODEX_HOME=/usr/local/data/steam_Aggregation_backend/.codex`，确保 Codex CLI 能读取缓存与 Slash 配置。

## 3. 本地环境搭建

1. **克隆仓库并进入项目根目录**  
   ```bash
   git clone <repo-url>
   cd steam_Aggregation_backend
   ```

2. **安装依赖（使用 uv）**  
   ```bash
   uv pip install -r pyproject.toml
   ```
   首次安装后确保 `.venv/` 下的 Python 版本为 3.12.x。

3. **配置环境变量**  
   - 复制模板：`cp .env.example .env`，根据本地环境修改数据库、Redis、第三方服务配置。  
   - 如需单独的开发配置，可参考 `.env.dev`。

4. **启动基础服务（可选）**  
   - 如果本地未运行 PostgreSQL/Redis，可执行：
     ```bash
     docker-compose up -d
     ```
   - 如需主从或更多组件，参考 `docker-compose.master-slave.yml`。

5. **初始化数据库**  
   ```bash
   alembic upgrade head
   ```
   或使用封装脚本 `python migrate_database.py`（根据需要选择）。

## 4. 启动应用与后台任务

| 功能 | 命令 | 说明 |
| --- | --- | --- |
| FastAPI 服务 | `uvicorn app.main:app --reload` | 默认监听 `http://127.0.0.1:8000`，Swagger UI 在 `/docs`，ReDoc 在 `/redoc` |
| Celery Worker | `./run_worker.sh` 或 `celery -A app.core.celery_app worker -l info` | 执行异步任务（默认使用 `prefork` 池，可通过环境变量 `CELERY_POOL` 覆盖） |
| Celery Beat | `./run_beat.sh` 或 `celery -A app.core.celery_app beat -l info` | 定时任务调度 |
| 状态检查 | `GET /api/status/health`（示例） | 具体端点以 `docs/*` API 文档为准 |

启动时请确保 `.env` 中的消息队列、数据库连接均可用。日志输出默认使用 Loguru，可在 `logs/` 查看历史。

## 5. 测试与质量保障

| 项目 | 命令 |
| --- | --- |
| 单元 / 集成测试 | `pytest` |
| 指定模块测试 | `pytest tests/api/test_tags.py` 等 |
| 静态检查 | `ruff check app tests` |
| 格式化（如有需要） | `ruff format app tests` |
| 数据库迁移 SQL 预览 | `alembic upgrade head --sql` |

新增功能需保持 ≥85% 测试覆盖率，并在对应模块下补充用例（`tests/api/`、`tests/services/`、`tests/tasks/` 等）。

## 6. Spec Kit 使用速览

项目已集成 Spec Kit，用于规范需求→计划→任务→实现的流程。常用命令：

| 命令 | 作用 |
| --- | --- |
| `/speckit.constitution` | 生成/更新项目宪章，约束质量与流程 |
| `/speckit.specify` | 编写功能规格（聚焦 What/Why） |
| `/speckit.plan` | 结合既有栈生成技术方案 |
| `/speckit.tasks` | 拆分可执行任务，与 `app/`、`tests/` 结构呼应 |
| `/speckit.implement` | 按任务顺序执行开发（需其他文档就绪） |

所有产物存放在 `.specify/specs/<feature-id>/` 下，包含 `spec.md`、`plan.md`、`tasks.md`、`contracts/` 等文件。

## 7. 常见开发流程

1. **拉取最新主干**：`git pull origin main`  
2. **创建功能分支**：`git checkout -b feat/<scope>-short-desc`  
3. **执行 Spec Kit 流程**（如为新功能）：依次运行 `/speckit.specify` → `/speckit.plan` → `/speckit.tasks`。  
4. **TDD 开发**：先写/补测试，再实现代码。保持结构化日志与 RBAC 守则。  
5. **本地验证**：运行 `pytest`、`ruff check`、`alembic upgrade --sql` 等。  
6. **准备提交**：遵循 `type(scope): summary` 格式（如 `feat(config): add dynamic admin config apis`），描述背景、方案、验证方式。  
7. **运行文档更新**：若涉及新 API 或流程，更新 `docs/` 中对应文档。

## 8. 运维与调试提示

- `.env` 管理本地环境变量，避免将敏感信息提交仓库（`.gitignore` 已忽略）。  
- 生产环境中使用 `docker-compose.yml` 启动前，请确认 Redis、PostgreSQL、Celery Worker 均已就绪。  
- 监控与审计相关脚本位于 `scripts/`，例如数据库巡检、缓存清理等。  
- 日志系统使用 Loguru，可通过设置环境变量（例如 `LOG_LEVEL`) 控制输出。  
- 如需排查权限问题，参考 `docs/permissions_guide.md` 与 `app/db/init_permissions.py`。

## 9. 进一步阅读

- `docs/admin_system_enhancement_plan.md`：管理后台增强路线图。  
- `docs/` 目录下的 API 文档（如 `backpack_api.md`、`comments_api.md`、`notifications_api.md`）。  
- `.specify/specs/002-admin-enhancement-phase1/`：当前进行中的管理后台增强项目资料。  
- `AGENTS.md`：Spec Kit 环境说明、命令清单和注意事项。

如在使用过程中遇到问题，可先查阅 `docs/` 与 `.specify/` 对应文档，或在团队内同步最新的宪章与计划。祝开发顺利！
