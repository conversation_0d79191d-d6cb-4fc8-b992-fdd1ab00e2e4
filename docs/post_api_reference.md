# 沸点（Post）API 参考

本文件汇总 `app/api/endpoints/posts.py` 中实现的所有沸点（Post）相关接口，方便前端在管理系统增强阶段接入。所有路径默认带有全局前缀 `/api/v1`，并通过 FastAPI `tags=["posts"]` 暴露。

## 基础信息
- **基准路径**：`/api/v1`
- **资源前缀**：`/posts`
- **认证方式**：需要时使用 `Authorization: Bearer <token>`。`current_user` 依赖为可选时表示匿名访问可用。
- **响应统一包装**：由中间件 `ResponseFormatterMiddleware` 处理，默认返回结构内会包含 `data` 字段。

## 端点总览
| 方法 | 完整路径 | 是否需登录 | 说明 |
| - | - | - | - |
| POST | `/api/v1/posts/` | 是 | 创建沸点（支持文本、媒体、投票、转发） |
| GET | `/api/v1/posts/` | 否 | 列表查询（分页、排序、筛选） |
| GET | `/api/v1/posts/hot` | 否 | 热门沸点列表（按热度分） |
| GET | `/api/v1/posts/following` | 是 | 关注用户沸点流 |
| GET | `/api/v1/posts/topics/{topic}` | 否 | 指定话题下的沸点 |
| GET | `/api/v1/posts/{post_id}` | 否 | 单条详情 |
| PUT | `/api/v1/posts/{post_id}` | 是（作者） | 更新沸点基础信息 |
| DELETE | `/api/v1/posts/{post_id}` | 是（作者） | 删除沸点 |
| POST | `/api/v1/posts/drafts` | 是 | 保存或更新草稿 |
| GET | `/api/v1/posts/drafts` | 是 | 获取当前用户草稿列表 |
| DELETE | `/api/v1/posts/drafts/{draft_id}` | 是 | 删除草稿 |
| POST | `/api/v1/posts/{post_id}/comments` | 是 | 在沸点下发评论/回复 |
| GET | `/api/v1/posts/{post_id}/comments` | 否 | 获取沸点评论（游标分页、可扁平化） |
| POST | `/api/v1/posts/{post_id}/like` | 是 | 点赞沸点 |
| DELETE | `/api/v1/posts/{post_id}/like` | 是 | 取消点赞 |
| POST | `/api/v1/posts/{post_id}/poll/vote` | 是 | 为投票型沸点投票 |
| POST | `/api/v1/posts/upload/media` | 是 | 上传沸点配套媒体（图片、视频） |
| POST | `/api/v1/upload/file` | 是 | 通用图片上传（返回 FileHash 信息） |
| **GET** | **`/api/v1/posts/topics/hot`** | **否** | **热门话题列表（支持分页、过滤）** |
| **GET** | **`/api/v1/posts/topics/trending`** | **否** | **趋势话题列表（增长最快的话题）** |
| **GET** | **`/api/v1/posts/topics/{topic}/detail`** | **否** | **话题详细统计信息** |
| **GET** | **`/api/v1/posts/tags/hot`** | **否** | **热门标签列表** |

## 1. 核心 CRUD

### 1.1 创建沸点
- **方法/路径**：`POST /api/v1/posts/`
- **认证**：必需
- **请求体**（`PostCreate`）
  - `content`(str, ≤500)：文本内容，可为空（例如纯图片）
  - `post_type`(`text|image|video|link|poll|repost`)：沸点类型，默认 `text`
  - `visibility`(`public|followers|private`)：可见性，默认 `public`
  - `topic`(str, ≤100)：话题标签
  - `location`(str, ≤200)：位置信息
  - `original_post_id`(int?)：转发目标ID；会校验原帖存在
  - `repost_comment`(str, ≤200)：转发附言
  - `link_url/title/description/image`：链接分享相关字段
  - `poll_options`(list[str])：投票选项，必须为 2-10 个
  - `poll_expires_hours`(int, 1-168)：投票有效期
  - `poll_multiple_choice`(bool)：是否多选
  - `media_files`(list[str])：已上传媒体 URL 列表
  - `mentions`(list[str])：@用户名称集合
- **响应**：`200` 包裹 `PostOut`，包含作者信息、统计、媒体、投票信息等。
- **错误提示**：404 原始沸点不存在；400 投票参数不足/超限。

> **草稿发布**：请求体新增字段 `draft_id`（int?），当传入时会将对应草稿转为已发布帖子；服务端会校验草稿归属及状态。

### 1.2 列表查询
- **方法/路径**：`GET /api/v1/posts/`
- **认证**：可选（匿名也可访问）
- **查询参数**（`PostListQuery`）
  - `author_id`(int?)：按作者筛选
  - `post_type`：同上类型枚举
  - `topic`(str?)：按话题筛选
  - `is_hot`(bool?)：过滤热门
  - `page`(int, ≥1, 默认 1)
  - `size`(int, 1-100, 默认 20)
  - `sort_by`：`created_at|hot_score|like_count|comment_count`
  - `sort_order`：`asc|desc`，默认 `desc`
- **响应**：`PostOut[]`。
- **备注**：若携带登录态，会在 `stats.is_liked_by_user` 等字段中反映当前用户状态。

### 1.3 获取详情
- **方法/路径**：`GET /api/v1/posts/{post_id}`
- **认证**：可选
- **响应**：`PostOut`；不存在返回 404。

### 1.4 更新沸点
- **方法/路径**：`PUT /api/v1/posts/{post_id}`
- **认证**：必需，且仅作者可改
- **请求体**（`PostUpdate`）
  - `content`(str?)、`visibility`、`topic`、`location`
- **响应**：最新 `PostOut`。
- **错误提示**：404 未找到，403 非作者。

### 1.5 删除沸点
- **方法/路径**：`DELETE /api/v1/posts/{post_id}`
- **认证**：必需，且仅作者可删
- **响应**：204 无内容；404/403 同上。

## 2. 列表派生接口

### 2.1 热门沸点
- `GET /api/v1/posts/hot`
- **认证**：可选
- **查询参数**：`page`、`size` 同上
- **行为**：内部强制 `is_hot=True`、`sort_by=hot_score desc`。

### 2.2 关注流
- `GET /api/v1/posts/following`
- **认证**：必需
- **查询参数**：`page`、`size`
- **响应**：当前用户关注的作者沸点列表。

### 2.3 话题列表
- `GET /api/v1/posts/topics/{topic}`
- **认证**：可选
- **查询参数**：`page`(≥1，默认 1)、`size`(1-100，默认 20)
- **响应**：`PostsPageResponse`
  ```json
  {
    "posts": [
      {
        "id": 101,
        "topic": "Python",
        "content": "Flask vs FastAPI?",
        "post_type": "text",
        "author": {
          "id": 9,
          "username": "dev999"
        },
        "stats": {
          "like_count": 42,
          "comment_count": 8,
          "is_liked_by_user": false
        },
        "created_at": "2024-01-15T10:30:00+00:00"
      }
    ],
    "total_count": 265,
    "has_next": true,
    "has_previous": false,
    "page": 1,
    "size": 20
  }
  ```
- **实现说明**：
  - 列表由 `PostAggregationService.get_posts` 聚合，底层查询 `crud.post.get_posts_with_filters` 并只返回 `visibility=public` 且 `status=published` 的帖子。
  - 为提升性能，会命中 `PostCacheService.get_posts_by_topic` 的 Redis 缓存（键形如 `posts:topic:{topic}:limit:{limit}:offset:{offset}`）。创建、删除、点赞等会触发缓存失效与话题统计刷新。
  - 排序固定为按 `created_at` 倒序；需要其他排序逻辑时可追加分页参数在服务层扩展。

### 2.4 草稿管理
- **保存/更新草稿**：`POST /api/v1/posts/drafts`
  - **认证**：必需
  - **请求体**（`PostDraftSave`）
    - `id`(int?)：存在时表示更新；为空表示新建
    - `content`、`post_type`、`visibility`、`topic`、`location`、`media_files`：同 `PostCreate`，均可选
    - 不传 `post_type/visibility` 时，后端默认 `text`/`private`
    - `media_files` 缺省表示草稿不关联媒体
  - **响应**：`PostDraftOut`（包含 `status=draft`、`created_at`、`updated_at`、`media`）

- **草稿列表**：`GET /api/v1/posts/drafts`
  - **认证**：必需
  - **查询参数**：`page`(默认1)、`size`(默认20)
  - **响应**：`PostDraftListResponse`，仅返回当前用户草稿，不含已发布/删除帖子
  - **实现备注**：查询草稿时需要预加载 `Post.media` 关系（例如使用 `selectinload`），以避免 Pydantic 在序列化 `PostDraftOut` 时触发异步懒加载并导致 `MissingGreenlet` 异常。

- **删除草稿**：`DELETE /api/v1/posts/drafts/{draft_id}`
  - **认证**：必需
  - **行为**：彻底删除草稿记录；草稿不存在或不归属当前用户时返回 404

> 草稿与正式帖子列表完全隔离，公共列表接口仅返回 `status=published` 的记录。

## 3. 热门话题和标签接口

### 3.1 热门话题列表
- **方法/路径**：`GET /api/v1/posts/topics/hot`
- **认证**：可选
- **查询参数**：
  - `sort_by`(str, 默认 `hot_score`)：排序字段，可选 `hot_score|post_count|trend_score`
  - `min_post_count`(int?)：最小帖子数量过滤，≥1
  - `days`(int, 默认 30)：统计天数范围，1-365
  - `page`(int, ≥1, 默认 1)：页码
  - `size`(int, 1-100, 默认 20)：每页数量
- **响应**：
  ```json
  {
    "topics": [
      {
        "topic": "Python",
        "post_count": 150,
        "hot_score": 89.5,
        "trend_score": 12.3,
        "rank": 1,
        "rank_change": 0
      }
    ],
    "total": 100,
    "page": 1,
    "size": 20,
    "has_next": true
  }
  ```

### 3.2 趋势话题列表
- **方法/路径**：`GET /api/v1/posts/topics/trending`
- **认证**：可选
- **查询参数**：
  - `min_post_count`(int?)：最小帖子数量过滤，≥1
  - `page`(int, ≥1, 默认 1)：页码
  - `size`(int, 1-100, 默认 20)：每页数量
- **响应**：
  ```json
  {
    "topics": [
      {
        "topic": "ChatGPT",
        "post_count": 45,
        "trend_score": 25.8,
        "growth_rate": 2.5,
        "rank": 1
      }
    ],
    "total": 50,
    "page": 1,
    "size": 20,
    "has_next": false
  }
  ```

### 3.3 话题详情
- **方法/路径**：`GET /api/v1/posts/topics/{topic}/detail`
- **认证**：可选
- **响应**：
  ```json
  {
    "topic": "Python",
    "post_count": 150,
    "total_likes": 1250,
    "total_comments": 890,
    "total_reposts": 234,
    "total_views": 5670,
    "hot_score": 89.5,
    "trend_score": 12.3,
    "last_post_at": "2024-01-15T10:30:00Z",
    "peak_time": "2024-01-14T15:20:00Z",
    "created_at": "2023-12-15T08:00:00Z"
  }
  ```
- **错误提示**：404 话题不存在。

### 3.4 话题数据维护与缓存
- **数据来源**：所有话题类接口依赖 `topic_stats` / `topic_trends` 表。`TopicStats` 持久化话题的累计帖子数、互动量、热度分数与峰值时间。
- **统计更新**：
  - 发帖、点赞、取消点赞、删除等行为均通过 `PostAggregationService` 调用 `TopicStatsService.increment_topic_usage` / `decrement_topic_usage`。
  - 点赞等互动会传入 `likes_delta` 等增量字段，用于实时更新 `total_likes`、`trend_score`。
- **缓存策略**：
  - 热门/趋势列表使用 Redis Key `topics:hot:*`、`topics:trending:*` 缓存分页数据，默认过期时间来自 `PostConfig.POST_TRENDING_TOPICS_CACHE_SECONDS`。
  - 话题详情缓存键为 `topic:detail:{topic}`，在统计更新后统一清理。
  - 定时任务可调用 `TopicStatsService.update_hot_rankings` 批量刷新热度并同步排行榜。
- **失效时机**：创建 / 删除帖子、互动数据更新、缓存失效任务与后台修正都会清理相关键，确保热门与趋势数据不失真。

### 3.5 热门标签列表
- **方法/路径**：`GET /api/v1/posts/tags/hot`
- **认证**：可选
- **查询参数**：
  - `content_type`(str, 默认 `post`)：内容类型过滤
  - `limit`(int, 1-100, 默认 20)：返回数量
- **响应**：
  ```json
  [
    {
      "id": 1,
      "name": "Python",
      "content_type": "post",
      "usage_count": 150,
      "is_trending": true
    }
  ]
  ```

## 4. 评论接口

### 4.1 发表/回复评论
- `POST /api/v1/posts/{post_id}/comments`
- **认证**：必需
- **请求体**（`PostCommentCreatePayload`）
  - `content`(str, 1-500)：评论正文
  - `parent_id`(int?)：回复目标 ID
- **逻辑**：
  - 校验目标沸点存在且 `status=PUBLISHED`
  - 若是回复，校验父评论属于同一沸点
  - 创建后调用 `ContentStatsService.update_comment_count` 增量统计
- **响应**：`Comment`（含 `like_count`、`is_liked` 等字段）。

### 4.2 获取评论列表
- `GET /api/v1/posts/{post_id}/comments`
- **认证**：可选
- **查询参数**：
  - `cursor`(str?)：游标
  - `size`(int, 默认 20，最大 100)
  - `sort_by`：`like_count|created_at`，默认 `like_count`
  - `flat`(bool, 默认 `true`)：是否返回扁平结构
  - `max_level`(int, 默认 10)：扁平模式下的最大层级
- **响应类型**：
  - `flat=true` → `FlatCommentList`（带 `level`、`path`、`total_reply_count`）
  - `flat=false` → `CommentCursorList`（嵌套 `CommentWithReplies`）
- **补充**：接口会批量查询评论的点赞统计并回填 `like_count`、`is_liked`。

## 5. 互动行为

### 5.1 点赞 / 取消点赞
- `POST /api/v1/posts/{post_id}/like` → 204
- `DELETE /api/v1/posts/{post_id}/like` → 204
- **认证**：必需
- **说明**：上下游依赖 `PostAggregationService.like_post/unlike_post`，内部会更新统计并触发通知。

### 5.2 投票
- `POST /api/v1/posts/{post_id}/poll/vote`
- **认证**：必需
- **请求体**（`PostPollVoteRequest`）：`option_indexes`(list[int])，支持多选
- **响应**：`200 OK`，返回实时投票摘要 `PostPollSummary`
  ```json
  {
    "post_id": 123,
    "total_votes": 420,
    "option_stats": [
      {"index": 0, "text": "选项A", "vote_count": 260, "vote_ratio": 0.619},
      {"index": 1, "text": "选项B", "vote_count": 160, "vote_ratio": 0.381}
    ],
    "user_choices": [0],
    "multiple_choice": false,
    "expires_at": "2025-01-01T00:00:00Z"
  }
  ```
- **错误场景**：选项不存在、投票已结束、单选场景传入多个选项等会返回 400/404/409 等业务错误。
- **实时性**：同一接口返回值即可用于前端直观刷新选项票数与占比，无需额外轮询详情接口。

## 6. 媒体上传

### 6.1 沸点媒体上传
- **方法/路径**：`POST /api/v1/posts/upload/media`
- **认证**：必需
- **请求格式**：`multipart/form-data`，字段名必须为 `file`
- **文件限制**：
  - 图片：`image/jpeg|png|gif|webp`，≤10 MB
  - 视频：`video/mp4|webm|ogg`，≤500 MB
- **响应示例**：
  ```json
  {
    "file_url": "https://cdn.example.com/steam/images/xxx.webp",
    "file_hash": "<文件哈希>",
    "media_type": "image",
    "width": 800,
    "height": 600
  }
  ```
- **视频上传响应**：
  ```json
  {
    "file_url": "https://cdn.example.com/steam/post-videos/<hash>.mp4",
    "file_hash": "<文件哈希>",
    "media_type": "video",
    "cover_url": "https://cdn.example.com/steam/images/<hash>_cover.webp",
    "duration": 120,
    "width": 1920,
    "height": 1080,
    "size": 123456789
  }
  ```
- **实现要点**：
  - 图片沿用 `handle_single_image_upload` 的处理与秒传逻辑。
  - 视频复用单文件直传服务：同步生成封面、写入 `file_hash` 元数据并直接上传原文件。
  - 若视频哈希命中已有记录，会直接返回历史 URL 与元数据。

### 6.2 通用图片上传
- **方法/路径**：`POST /api/v1/upload/file`
- **认证**：必需
- **请求格式**：`multipart/form-data`，字段名 `file`
- **功能说明**：将单张图片流式写入临时目录，计算 SHA-256 哈希后触发 `process_image_task`。若 `file_hash` 已存在则直接返回历史 URL，实现秒传。
- **大小限制**：受请求头 `Content-Length` 控制，单文件不超过 5 GB；内部仅支持图片类型。
- **响应模型**：`UploadResponse`
  ```json
  {
    "file_hash": "<sha256>",
    "file_url": "https://oss.example.com/path/to/image.jpg",
    "duration": null,
    "width": null,
    "height": null
  }
  ```
- **补充说明**：
  - `file_url` 为空时表示仍在异步处理，可使用 `file_hash` 轮询缓存。
  - `duration/width/height` 会在后续流程写入元数据后透传。

## 7. 响应模型要点

### 7.1 `PostOut`
- 基础字段：`id`、`author_id`、`content`、`post_type`、`status`、`visibility`、`topic`、`location`、`created_at`、`updated_at`
- 嵌套对象：
  - `author`：`UserAggregated`
  - `stats`：`like_count`、`comment_count`、`repost_count`、`view_count`、以及当前用户态（例如 `is_liked_by_user`）
  - `media`：`PostMediaOut[]`（包含 CDN 处理逻辑）
  - `mentions`：`PostMentionOut[]`
  - `review`：审核信息
  - `original_post`：如果是转发，携带完整被转发沸点
  - 投票相关字段：
    - `poll_options`：包含 `text`、`vote_count`、`vote_ratio`（实时占比，范围 0-1）
    - `poll_expires_at`、`poll_multiple_choice`
    - `user_poll_votes`：当前用户选择的选项索引列表（未投票时为空）
    - `poll_summary`：同 `PostPollSummary` 结构，可复用为前端渲染源

### 7.2 评论返回结构
- `Comment`：包含 `id/comment_id`、`author`、`like_count`、`is_liked`、`created_at`
- `FlatComment`：额外提供 `level`、`path`、`reply_count`、`total_reply_count`
- 分页容器统一带 `has_next`、`has_previous`、`next_cursor`、`previous_cursor`、`total_count`

### 7.3 热门话题和标签响应结构
- **热门话题列表**：`HotTopicsResponse`，包含 `topics`、`total`、`page`、`size`、`has_next`
- **趋势话题列表**：`TrendingTopicsResponse`，包含 `topics`、`total`、`page`、`size`、`has_next`
- **话题详情**：`TopicDetail`，包含完整的话题统计信息
- **热门标签**：标签对象数组，包含 `id`、`name`、`content_type`、`usage_count`、`is_trending`

## 8. 前端对接建议
- **鉴权**：所有需要 `current_user` 的接口均依赖后端登录态；在未登录状态下调用会返回 401。
- **分页**：列表接口采用传统分页参数，评论接口使用游标；前端需保留 `next_cursor` 做增量加载。
- **多态内容**：根据 `post_type` 动态渲染：
  - `image` / `video`：依赖 `media` 列表
  - `poll`：展示 `poll_options`，使用 `vote_ratio`、`poll_summary` 中的 `total_votes`/`option_stats` 实时刷新视图，并根据 `user_poll_votes` 标记所选项
  - `repost`：渲染 `original_post`
- **统计更新**：点赞/评论后前端可直接增量更新 `stats`，也可重新请求详情。
- **媒体上传**：图片上传后立即拿到可用 URL；视频需走现有上传服务或等待后续实现。
- **热门话题**：
  - 使用 `/topics/hot` 获取热门话题列表，支持按热度、帖子数量等排序
  - 使用 `/topics/trending` 获取增长最快的话题，适合发现新兴话题
  - 使用 `/topics/{topic}/detail` 获取特定话题的详细统计信息
  - 支持分页加载，建议实现无限滚动或分页导航
- **热门标签**：
  - 使用 `/tags/hot` 获取热门标签，可用于话题推荐或标签云展示
  - 根据 `content_type` 过滤不同类型的标签
  - `is_trending` 字段可用于高亮显示趋势标签
- **缓存策略**：热门话题和标签数据更新频率较低，前端可适当缓存以提升用户体验

> 若需要更多背景信息，可参考 `docs/post_feature_design.md` 与 `docs/comments_api.md`。

## 9. 推荐用户接口补充

### 9.1 获取推荐用户列表
- **方法/路径**：`GET /api/v1/recommendations/users`
- **认证**：必需（依赖 `get_current_user`）
- **查询参数**：
  - `algorithm_type`(str?)：指定推荐算法，支持 `collaborative|content_based|social_network|popular|hybrid`，默认混合算法
  - `page`(int, ≥1, 默认 1)：页码
  - `page_size`(int, 1-50, 默认 20)：分页大小
- **行为摘要**：
  - 通过 `UserRecommendationService` 综合多种算法生成推荐用户
  - 批量聚合用户档案与粉丝/关注统计
  - 计算当前用户与推荐用户的共同关注数 `mutual_following_count`
- **响应**：`UserRecommendationResponse`
  ```json
  {
    "items": [
      {
        "user_id": 123,
        "username": "user123",
        "nickname": "昵称",
        "avatar": "https://...",
        "description": "个性签名",
        "follower_count": 340,
        "following_count": 58,
        "score": 0.82,
        "reason": "与你行为相似的用户",
        "algorithm_type": "collaborative",
        "mutual_following_count": 3
      }
    ],
    "total_count": 120,
    "page": 1,
    "page_size": 20,
    "has_next": true,
    "algorithm_type": "hybrid",
    "generated_at": "2025-01-15T10:00:00Z"
  }
  ```
- **错误提示**：内部异常时返回 500，错误信息包含原因 `detail`。

### 9.2 推荐用户反馈
- **方法/路径**：`POST /api/v1/recommendations/users/feedback`
- **认证**：必需（依赖 `get_current_user`）
- **请求体**（`UserRecommendationFeedback`）：
  - `user_id`(int)：被推荐的用户 ID
  - `feedback_type`(`click|follow|ignore|block`)：反馈类型
  - `algorithm_type`(str)：触发推荐的算法类型
  - `position`(int?)：推荐位次，可用于埋点
  - `extra_data`(dict?)：可选附加信息
- **行为摘要**：
  - 所有反馈都会写入 `RecommendationLog`
  - 当 `feedback_type=follow` 时：
    - 校验禁止自我关注、目标用户存在、避免重复关注
    - 调用 `crud.user.follow` 完成关注关系写入
    - 触发 `UserRecommendationTrackingService.track_follow_conversion` 记录推荐转化
- **响应**：成功返回 `{ "message": "反馈提交成功" }`
- **错误提示**：
  - 400：尝试关注自己或重复关注
  - 404：目标用户不存在
  - 401：当前登录态失效
  - 500：其他异常，`detail` 包含具体原因

