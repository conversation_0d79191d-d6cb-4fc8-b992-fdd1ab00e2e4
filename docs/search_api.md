# 搜索 API 文档

## 概述

搜索 API 提供统一的内容搜索功能，支持文章、视频、Scratch 项目的全文搜索、过滤、排序等功能。

**基础路径**: `/api/search`

**认证**: 大部分接口支持匿名访问，部分功能需要登录

**速率限制**: 
- 匿名用户: 20次/分钟
- 登录用户: 60次/分钟

---

## 目录

1. [统一搜索](#1-统一搜索)
2. [分类搜索](#2-分类搜索)
3. [搜索建议](#3-搜索建议)
4. [热门搜索](#4-热门搜索)
5. [搜索历史](#5-搜索历史)
6. [数据模型](#数据模型)
7. [错误码](#错误码)

---

## 1. 统一搜索

### 1.1 搜索所有内容

搜索文章、视频、Scratch 项目。

**接口**: `GET /api/search`

**请求参数**:

| 参数 | 类型 | 必需 | 说明 | 示例 |
|------|------|------|------|------|
| q | string | 是 | 搜索关键词（2-100字符） | `Python教程` |
| type | string | 否 | 内容类型：`all`/`article`/`video`/`scratch` | `article` |
| category_id | integer | 否 | 类别ID | `5` |
| tag_ids | string | 否 | 标签ID，逗号分隔 | `10,11,12` |
| author_id | integer | 否 | 作者ID | `123` |
| sort_by | string | 否 | 排序方式：`relevance`/`hot`/`recent` | `hot` |
| difficulty | string | 否 | 难度（仅Scratch）：`easy`/`medium`/`hard` | `medium` |
| cursor | string | 否 | 游标位置 | - |
| size | integer | 否 | 每页大小（1-50，默认20） | `20` |
| highlight | boolean | 否 | 是否返回高亮（默认true） | `true` |

**请求示例**:

```bash
# 基本搜索
GET /api/search?q=Python教程

# 带过滤条件
GET /api/search?q=机器学习&type=article&category_id=5&sort_by=hot&size=10

# 搜索带标签
GET /api/search?q=数据分析&tag_ids=10,11&sort_by=recent
```

**响应示例**:

```json
{
  "items": [
    {
      "id": 123,
      "type": "article",
      "title": "Python 入门教程",
      "description": "适合初学者的 Python 编程教程...",
      "cover_url": "https://cdn.example.com/images/123.jpg",
      "author": {
        "id": 456,
        "username": "tech_teacher",
        "avatar_url": "https://cdn.example.com/avatars/456.jpg"
      },
      "category": {
        "id": 5,
        "name": "编程教程"
      },
      "tags": [
        {"id": 10, "name": "Python"},
        {"id": 11, "name": "教程"}
      ],
      "stats": {
        "visit_count": 1520,
        "like_count": 85,
        "comment_count": 23
      },
      "highlight": {
        "title": "<em>Python</em> 入门<em>教程</em>",
        "content": "...这是一个适合初学者的 <em>Python</em> 编程<em>教程</em>..."
      },
      "relevance_score": 0.95,
      "created_at": "2025-10-15T10:30:00Z",
      "updated_at": "2025-10-20T15:45:00Z"
    }
  ],
  "has_next": true,
  "has_previous": false,
  "next_cursor": "eyJpZCI6MTIzfQ==",
  "previous_cursor": null,
  "total_count": 156,
  "search_time_ms": 45,
  "suggestions": ["Python 教程", "Python 入门"]
}
```

**响应字段说明**:

| 字段 | 类型 | 说明 |
|------|------|------|
| items | array | 搜索结果列表 |
| has_next | boolean | 是否有下一页 |
| has_previous | boolean | 是否有上一页 |
| next_cursor | string | 下一页游标 |
| previous_cursor | string | 上一页游标 |
| total_count | integer | 结果总数（可能为null） |
| search_time_ms | integer | 搜索耗时（毫秒） |
| suggestions | array | 相关搜索建议 |

---

## 2. 分类搜索

### 2.1 搜索文章

**接口**: `GET /api/search/articles`

**参数**: 同统一搜索，但不需要 `type` 参数

**请求示例**:

```bash
GET /api/search/articles?q=JavaScript&category_id=5&sort_by=recent
```

**响应**: 同统一搜索，但 `items` 中只包含文章

### 2.2 搜索视频

**接口**: `GET /api/search/videos`

**参数**: 同统一搜索，但不需要 `type` 参数

**请求示例**:

```bash
GET /api/search/videos?q=数据分析&sort_by=hot
```

**响应**: 同统一搜索，但 `items` 中只包含视频

**视频特有字段**:

```json
{
  "id": 789,
  "type": "video",
  "duration": 1850,
  "width": 1920,
  "height": 1080,
  // ... 其他字段
}
```

### 2.3 搜索 Scratch 项目

**接口**: `GET /api/search/scratch`

**参数**: 同统一搜索，额外支持 `difficulty` 参数

**请求示例**:

```bash
GET /api/search/scratch?q=游戏&difficulty=easy&sort_by=hot
```

**Scratch 特有字段**:

```json
{
  "id": 1001,
  "type": "scratch",
  "difficulty": "easy",
  "can_adapt": true,
  "adapt_count": 25,
  "original_project_id": null,
  // ... 其他字段
}
```

---

## 3. 搜索建议

### 3.1 获取搜索建议（自动补全）

**接口**: `GET /api/search/suggestions`

**请求参数**:

| 参数 | 类型 | 必需 | 说明 |
|------|------|------|------|
| q | string | 是 | 搜索前缀（至少1个字符） |
| type | string | 否 | 内容类型（默认all） |
| limit | integer | 否 | 返回数量（1-20，默认10） |

**请求示例**:

```bash
GET /api/search/suggestions?q=Py&limit=5
```

**响应示例**:

```json
{
  "suggestions": [
    {
      "keyword": "Python 教程",
      "type": "popular",
      "count": 1250
    },
    {
      "keyword": "Python 入门",
      "type": "popular",
      "count": 980
    },
    {
      "keyword": "Python 数据分析",
      "type": "trending",
      "count": 756
    }
  ]
}
```

**建议类型**:
- `popular`: 热门搜索词
- `trending`: 趋势搜索词
- `related`: 相关搜索词

---

## 4. 热门搜索

### 4.1 获取热门搜索词

**接口**: `GET /api/search/trending`

**请求参数**:

| 参数 | 类型 | 必需 | 说明 |
|------|------|------|------|
| type | string | 否 | 内容类型（默认all） |
| period | string | 否 | 时间范围：`hour`/`day`/`week`（默认day） |
| limit | integer | 否 | 返回数量（1-50，默认10） |

**请求示例**:

```bash
GET /api/search/trending?period=day&limit=10
```

**响应示例**:

```json
{
  "trending_keywords": [
    {
      "keyword": "机器学习",
      "search_count": 1250,
      "trend": "up",
      "change_rate": 0.35
    },
    {
      "keyword": "React 教程",
      "search_count": 980,
      "trend": "up",
      "change_rate": 0.28
    },
    {
      "keyword": "Python",
      "search_count": 856,
      "trend": "stable",
      "change_rate": 0.05
    }
  ],
  "period": "day",
  "updated_at": "2025-10-27T12:00:00Z"
}
```

**趋势类型**:
- `up`: 上升趋势
- `down`: 下降趋势
- `stable`: 稳定

---

## 5. 搜索历史

### 5.1 获取搜索历史

**接口**: `GET /api/search/history`

**认证**: 必需

**请求参数**:

| 参数 | 类型 | 必需 | 说明 |
|------|------|------|------|
| type | string | 否 | 内容类型（默认all） |
| limit | integer | 否 | 返回数量（1-100，默认20） |

**请求示例**:

```bash
GET /api/search/history?limit=10
Authorization: Bearer <token>
```

**响应示例**:

```json
{
  "history": [
    {
      "id": 12345,
      "query": "Python 教程",
      "content_type": "article",
      "result_count": 156,
      "searched_at": "2025-10-27T10:30:00Z"
    },
    {
      "id": 12344,
      "query": "JavaScript 框架",
      "content_type": "all",
      "result_count": 89,
      "searched_at": "2025-10-26T15:20:00Z"
    }
  ],
  "total": 2
}
```

### 5.2 删除搜索历史

**接口**: `DELETE /api/search/history/{history_id}`

**认证**: 必需

**请求示例**:

```bash
DELETE /api/search/history/12345
Authorization: Bearer <token>
```

**响应示例**:

```json
{
  "message": "搜索历史已删除"
}
```

### 5.3 清空搜索历史

**接口**: `DELETE /api/search/history`

**认证**: 必需

**请求示例**:

```bash
DELETE /api/search/history
Authorization: Bearer <token>
```

**响应示例**:

```json
{
  "message": "所有搜索历史已清空",
  "deleted_count": 25
}
```

---

## 数据模型

### SearchResult（搜索结果）

```typescript
interface SearchResult {
  id: number;                    // 内容ID
  type: 'article' | 'video' | 'scratch';  // 内容类型
  title: string;                 // 标题
  description?: string;          // 描述
  cover_url?: string;            // 封面图URL
  author: AuthorInfo;            // 作者信息
  category?: CategoryInfo;       // 类别信息
  tags: TagInfo[];               // 标签列表
  stats: ContentStats;           // 统计信息
  highlight?: SearchHighlight;   // 高亮信息
  relevance_score: number;       // 相关度分数（0-1）
  created_at: string;            // 创建时间（ISO 8601）
  updated_at?: string;           // 更新时间（ISO 8601）
}
```

### AuthorInfo（作者信息）

```typescript
interface AuthorInfo {
  id: number;                    // 作者ID
  username: string;              // 用户名
  avatar_url?: string;           // 头像URL
}
```

### CategoryInfo（类别信息）

```typescript
interface CategoryInfo {
  id: number;                    // 类别ID
  name: string;                  // 类别名称
}
```

### TagInfo（标签信息）

```typescript
interface TagInfo {
  id: number;                    // 标签ID
  name: string;                  // 标签名称
}
```

### ContentStats（内容统计）

```typescript
interface ContentStats {
  visit_count: number;           // 访问次数
  like_count: number;            // 点赞数
  comment_count: number;         // 评论数
}
```

### SearchHighlight（搜索高亮）

```typescript
interface SearchHighlight {
  title?: string;                // 高亮的标题（含<em>标签）
  content?: string;              // 高亮的内容片段
  description?: string;          // 高亮的描述
}
```

### VideoSearchResult（视频搜索结果）

```typescript
interface VideoSearchResult extends SearchResult {
  type: 'video';
  duration?: number;             // 视频时长（秒）
  width?: number;                // 视频宽度
  height?: number;               // 视频高度
}
```

### ScratchSearchResult（Scratch搜索结果）

```typescript
interface ScratchSearchResult extends SearchResult {
  type: 'scratch';
  difficulty?: 'easy' | 'medium' | 'hard';  // 难度级别
  can_adapt: boolean;            // 是否允许改编
  adapt_count: number;           // 改编次数
  original_project_id?: number;  // 原始项目ID
}
```

---

## 错误码

| 错误码 | HTTP状态码 | 说明 | 解决方法 |
|--------|-----------|------|---------|
| INVALID_QUERY | 400 | 搜索关键词无效 | 检查关键词格式 |
| QUERY_TOO_SHORT | 400 | 搜索关键词太短（< 2字符） | 至少输入2个字符 |
| QUERY_TOO_LONG | 400 | 搜索关键词太长（> 100字符） | 缩短搜索关键词 |
| INVALID_SORT | 400 | 排序参数无效 | 使用 relevance/hot/recent |
| INVALID_TYPE | 400 | 内容类型参数无效 | 使用 all/article/video/scratch |
| RATE_LIMIT_EXCEEDED | 429 | 超出请求频率限制 | 稍后重试 |
| UNAUTHORIZED | 401 | 未登录 | 需要登录才能访问 |
| NOT_FOUND | 404 | 资源不存在 | 检查资源ID |
| INTERNAL_ERROR | 500 | 服务器内部错误 | 联系技术支持 |

---

## 前端集成示例

### React + Axios 示例

```typescript
import axios from 'axios';

const API_BASE_URL = 'http://localhost:8000/api';

// 搜索接口
export const searchAPI = {
  // 统一搜索
  search: async (params: {
    q: string;
    type?: 'all' | 'article' | 'video' | 'scratch';
    category_id?: number;
    tag_ids?: string;
    sort_by?: 'relevance' | 'hot' | 'recent';
    cursor?: string;
    size?: number;
  }) => {
    const response = await axios.get(`${API_BASE_URL}/search`, { params });
    return response.data;
  },

  // 搜索文章
  searchArticles: async (params: {
    q: string;
    category_id?: number;
    sort_by?: string;
  }) => {
    const response = await axios.get(`${API_BASE_URL}/search/articles`, { params });
    return response.data;
  },

  // 搜索建议
  getSuggestions: async (prefix: string) => {
    const response = await axios.get(`${API_BASE_URL}/search/suggestions`, {
      params: { q: prefix }
    });
    return response.data.suggestions;
  },

  // 热门搜索
  getTrending: async (period: 'hour' | 'day' | 'week' = 'day') => {
    const response = await axios.get(`${API_BASE_URL}/search/trending`, {
      params: { period }
    });
    return response.data.trending_keywords;
  },

  // 搜索历史
  getHistory: async (token: string) => {
    const response = await axios.get(`${API_BASE_URL}/search/history`, {
      headers: { Authorization: `Bearer ${token}` }
    });
    return response.data.history;
  },

  // 删除历史
  deleteHistory: async (historyId: number, token: string) => {
    await axios.delete(`${API_BASE_URL}/search/history/${historyId}`, {
      headers: { Authorization: `Bearer ${token}` }
    });
  },

  // 清空历史
  clearHistory: async (token: string) => {
    await axios.delete(`${API_BASE_URL}/search/history`, {
      headers: { Authorization: `Bearer ${token}` }
    });
  },
};

// 使用示例
async function handleSearch(keyword: string) {
  try {
    const results = await searchAPI.search({
      q: keyword,
      type: 'all',
      sort_by: 'relevance',
      size: 20
    });
    
    console.log('搜索结果:', results.items);
    console.log('搜索耗时:', results.search_time_ms, 'ms');
  } catch (error) {
    console.error('搜索失败:', error);
  }
}
```

### Vue 3 示例

```vue
<template>
  <div class="search-page">
    <!-- 搜索框 -->
    <div class="search-box">
      <input
        v-model="keyword"
        @input="handleInput"
        @keyup.enter="handleSearch"
        placeholder="搜索文章、视频、Scratch项目..."
      />
      <button @click="handleSearch">搜索</button>
    </div>

    <!-- 搜索建议 -->
    <div v-if="suggestions.length" class="suggestions">
      <div
        v-for="item in suggestions"
        :key="item.keyword"
        @click="selectSuggestion(item.keyword)"
      >
        {{ item.keyword }}
      </div>
    </div>

    <!-- 搜索结果 -->
    <div class="results">
      <div v-for="item in results" :key="item.id" class="result-item">
        <h3 v-html="item.highlight?.title || item.title"></h3>
        <p v-html="item.highlight?.description || item.description"></p>
        <div class="meta">
          <span>{{ item.author.username }}</span>
          <span>{{ item.stats.visit_count }} 次浏览</span>
        </div>
      </div>
    </div>

    <!-- 加载更多 -->
    <button v-if="hasNext" @click="loadMore">加载更多</button>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import { searchAPI } from './api';
import { debounce } from 'lodash-es';

const keyword = ref('');
const suggestions = ref([]);
const results = ref([]);
const hasNext = ref(false);
const nextCursor = ref(null);

// 输入时获取建议（防抖）
const handleInput = debounce(async () => {
  if (keyword.value.length >= 1) {
    suggestions.value = await searchAPI.getSuggestions(keyword.value);
  } else {
    suggestions.value = [];
  }
}, 300);

// 执行搜索
const handleSearch = async () => {
  if (!keyword.value) return;
  
  const data = await searchAPI.search({
    q: keyword.value,
    size: 20
  });
  
  results.value = data.items;
  hasNext.value = data.has_next;
  nextCursor.value = data.next_cursor;
  suggestions.value = [];
};

// 选择建议
const selectSuggestion = (kw: string) => {
  keyword.value = kw;
  handleSearch();
};

// 加载更多
const loadMore = async () => {
  const data = await searchAPI.search({
    q: keyword.value,
    cursor: nextCursor.value,
    size: 20
  });
  
  results.value.push(...data.items);
  hasNext.value = data.has_next;
  nextCursor.value = data.next_cursor;
};
</script>
```

---

## 性能优化建议

### 1. 搜索建议防抖

使用防抖（debounce）避免频繁请求：

```typescript
import { debounce } from 'lodash-es';

const getSuggestions = debounce(async (keyword: string) => {
  const results = await searchAPI.getSuggestions(keyword);
  // 更新UI
}, 300); // 300ms 延迟
```

### 2. 结果缓存

缓存搜索结果避免重复请求：

```typescript
const cache = new Map();

async function search(keyword: string) {
  const cacheKey = `search:${keyword}`;
  
  if (cache.has(cacheKey)) {
    return cache.get(cacheKey);
  }
  
  const results = await searchAPI.search({ q: keyword });
  cache.set(cacheKey, results);
  
  return results;
}
```

### 3. 高亮渲染安全

使用 `v-html` 或 `dangerouslySetInnerHTML` 时注意 XSS 防护：

```typescript
// 后端已经做了安全处理，只包含 <em> 标签
// 但前端也应该验证
function sanitizeHighlight(html: string): string {
  // 只允许 <em> 标签
  return html.replace(/<(?!em>|\/em>)[^>]*>/g, '');
}
```

### 4. 无限滚动

实现无限滚动加载更多结果：

```typescript
import { useInfiniteScroll } from '@vueuse/core';

const el = ref<HTMLElement | null>(null);

useInfiniteScroll(
  el,
  () => {
    if (hasNext.value) {
      loadMore();
    }
  },
  { distance: 10 }
);
```

---

## 常见问题

### Q1: 如何高亮搜索关键词？

A: 后端返回的 `highlight` 字段已经包含了 `<em>` 标签，直接使用即可：

```html
<div v-html="item.highlight?.title || item.title"></div>
```

CSS 样式：

```css
em {
  font-style: normal;
  font-weight: bold;
  color: #ff6b6b;
  background-color: #fff3cd;
}
```

### Q2: 如何实现搜索历史？

A: 登录用户的搜索历史会自动记录，调用历史接口即可获取：

```typescript
const history = await searchAPI.getHistory(userToken);
```

### Q3: 如何处理搜索为空的情况？

A: 检查 `items` 数组长度：

```typescript
if (results.items.length === 0) {
  // 显示"未找到结果"提示
  // 可以显示热门搜索或搜索建议
}
```

### Q4: 游标分页如何使用？

A: 使用 `next_cursor` 加载下一页：

```typescript
// 第一页
const page1 = await searchAPI.search({ q: 'Python', size: 20 });

// 第二页
const page2 = await searchAPI.search({ 
  q: 'Python', 
  size: 20, 
  cursor: page1.next_cursor 
});
```

---

## 更新日志

| 版本 | 日期 | 变更说明 |
|------|------|---------|
| 1.0.0 | 2025-10-27 | 初始版本，搜索 API 上线 |

---

## 技术支持

如有疑问，请联系：
- **API 文档**: http://localhost:8000/docs
- **技术支持**: <EMAIL>
- **问题反馈**: GitHub Issues
