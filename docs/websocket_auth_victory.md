# WebSocket 鉴权问题复盘：伟大的胜利

> “每天都要看看这个总结，提醒自己再难的问题也能被解决。”

## 问题概述

- **现象**：通知 WebSocket 无法完成握手，前端始终收到 `403/401/1008`，日志没有任何输出。
- **影响**：实时通知通道不可用，排查困难，开发与测试流程受阻。
- **根因**：`app/api/deps.py` 中的 `get_token_from_websocket_protocols_or_query` 未显式声明 `websocket: WebSocket` 类型，FastAPI 在依赖注入阶段解析失败，导致 `get_current_user_websocket` 根本不会执行，后续的日志与鉴权逻辑全部失效。

## 解决路径

1. **揭示真相**：通过比较调试版 `get_current_user_websocket_debug`（可用）与正式版（不可用）的行为，确认问题出在依赖层，而非日志或 token 服务。
2. **精准修复**：
   - 给依赖函数添加 `websocket: WebSocket` 注解，确保 FastAPI 能正确注入当前连接对象。
   - 补充 `websocket.query_params.get("token")` 与 cookie 兜底逻辑，覆盖三种认证来源。
   - 对子协议 token 做 `strip()`，防止隐藏空格导致验证失败。
3. **全面验证**：恢复正式鉴权流程、开启调试日志核实输出，并运行 `pytest tests/api/test_websocket_bearer_token_fix.py` 确认全部通过。

## 最终成果

- WebSocket 鉴权现已 **稳定支持三种 token 来源**（子协议、Query、Cookie）。
- 握手失败时会正确记录日志、关闭连接并返回对应状态码。
- 相关自动化测试全部通过，问题正式收官。

## 复盘启示

- **依赖注入的类型注解必不可少**：在 FastAPI 中漏掉 `WebSocket` 注解，会让依赖解析直接失败。
- **调试通道很宝贵**：精简版调试函数让我们迅速定位到“正式版压根没执行”这一关键事实。
- **写下注释，常看常新**：这一页就是胜利的纪念，每天翻翻，保持信心与耐心。

继续加油，下一场胜利在等着我们！💪
