# 后台管理 API 接入指南

本文档梳理项目中全部后台管理相关接口，覆盖账号与权限、内容运营（文章/视频/分类/标签/评论）、系统运营（通知、推荐、审计）、配置与观测、设备安全管理、内容创作平台（Scratch/背包）、文件资源管理、以及用户行为分析等模块，便于前端管理端页面接入。

## 1. 基础信息
- **Base URL**：`http://localhost:8000/api/v1`（按环境调整）
- **认证方式**：Bearer Token，`Authorization: Bearer <access_token>`
- **统一响应格式**：成功响应将被中间件包装为
  ```json
  {
    "success": true,
    "code": "OK",
    "message": "操作成功",
    "data": { /* 各接口具体数据 */ },
    "timestamp": "2025-10-11T08:30:00.123456"
  }
  ```
  业务端只需根据 `success` / `code` 处理即可；特定模块可能返回自定义错误结构（例如配置中心的 `{ "status": "error", "error": {...} }`），建议统一异常兜底。
- **分页模式**：多数列表接口使用 `CursorPaginationResponse`，包含 `items`、`total_count`、`has_next`、`next_cursor` 等字段，前端请求时通过 `cursor`（初次可为空）与 `size` 控制翻页。
- **常用权限点**（字符串格式遵循 `resource:action:scope`）：

  | 功能模块 | 后端常量 | 权限字符串示例 |
  | --- | --- | --- |
  | 用户管理 | `Permissions.USER_MANAGE_ALL` | `user:manage:all` |
  | 角色管理 | `Permissions.ROLE_MANAGE_ALL` | `role:manage:all` |
  | 文章管理 | `Permissions.ARTICLE_MANAGE_ALL` | `article:manage:all` |
  | 视频管理 | `Permission(ResourceType.VIDEO, Action.MANAGE, Scope.ALL)` | `video:manage:all` |
  | 标签管理 | `Permissions.TAG_MANAGE_ALL` | `tag:manage:all` |
  | 分类管理 | `Permissions.CATEGORY_MANAGE_ALL` | `category:manage:all` |
  | 评论管理 | `Permission(ResourceType.COMMENT, Action.MANAGE, Scope.ALL)` | `comment:manage:all` |
  | 轮播图 | `Permission(ResourceType.BANNER, Action.MANAGE, Scope.ALL)` | `banner:manage:all` |
  | 推荐/系统运营 | `Permission(ResourceType.SYSTEM, Action.READ, Scope.ALL)` | `system:read:all` |
  | 通知运营 | `Permission(ResourceType.NOTIFICATION, Action.MANAGE, Scope.ALL)` | `notification:manage:all` |
  | 配置中心 | `Permissions.ADMIN_CONFIG_*` | `admin_config:read/write` |
  | 监控告警 | `Permissions.ADMIN_MONITORING_*` | `admin_monitoring:*` |
  | 数据分析 | `Permissions.ADMIN_ANALYTICS_*` | `admin_analytics:*` |
  | 设备管理 | `Permission(ResourceType.DEVICE, Action.MANAGE, Scope.ALL)` | `device:manage:all` |
  | Scratch项目 | `Permissions.SCRATCH_PROJECT_*` | `scratch_project:create/read/update/delete:own` |
  | 背包管理 | `Permissions.USER_READ/UPDATE_OWN` | `user:read/update:own` |
  | 文件上传 | 无特殊权限要求 | 登录用户即可 |
  | 用户行为 | 无特殊权限要求 | 仅可访问自己的数据 |

  **提示**：大部分接口通过依赖 `require_permission` 或 `PermissionChecker` 自动校验权限，前端只需在登录后保存的 JWT 中携带对应权限。

---

## 2. 身份与权限管理

### 2.1 用户管理（`/api/v1/users`）
**基础操作**
- `GET /users/` — 游标分页获取用户列表。Query：`cursor`、`size`、`order_by`、`order_direction`。权限：`user:read:all`。响应：`CursorPaginationResponse[UserAggregated]`（含基础信息与 `stats` 聚合数据）。
- `GET /users/{user_id}` — 获取用户详情（无需额外权限，管理员可用于查看目标用户）。
- `PUT /users/{user_id}` — 更新用户资料。主体：`UserUpdate`（可选字段 `email`、`nickname`、`avatar`、`is_active`、`role_id` 等）。需本人或 `user:update:all`。
- `DELETE /users/{user_id}` — 软删除用户并触发缓存清理，要求 `user:delete:all`，禁止删除自己及非超管删除超管。

**用户封禁管理（管理员专用）**
- `POST /users/admin/ban` — 封禁用户（管理员）。权限：`user:update:all`。
  - **功能**：
    1. 设置用户 `is_active = False`（禁止登录）
    2. 撤销用户所有 Token（强制下线）
    3. 清除用户缓存
    4. 记录审计日志
  - **Body**：`UserBanRequest`
    ```jsonc
    {
      "user_id": 3,
      "reason": "违反社区规则"  // 必填，1-500字符
    }
    ```
  - **响应**：
    ```jsonc
    {
      "message": "用户封禁成功",
      "user_id": 3,
      "username": "user_a",
      "revoked_tokens": 2  // 撤销的Token数量
    }
    ```
  - **安全限制**：
    - ❌ 不能封禁自己
    - ❌ 普通管理员不能封禁超级管理员
    - ❌ 不能重复封禁已被封禁的用户

- `POST /users/admin/unban` — 解除用户封禁（管理员）。权限：`user:update:all`。
  - **功能**：
    1. 设置用户 `is_active = True`（恢复登录）
    2. 清除用户缓存
    3. 记录审计日志
  - **Body**：`UserUnbanRequest`
    ```jsonc
    {
      "user_id": 3,
      "reason": "申诉成功，解除封禁"  // 可选
    }
    ```
  - **响应**：
    ```jsonc
    {
      "message": "用户解除封禁成功",
      "user_id": 3,
      "username": "user_a"
    }
    ```

- `POST /users/admin/batch-ban` — 批量封禁用户（管理员）。权限：`user:update:all`。
  - **Body**：
    ```jsonc
    {
      "user_ids": [3, 4, 5],
      "reason": "批量处理违规账号"  // 必填
    }
    ```
  - **响应**：
    ```jsonc
    {
      "message": "成功封禁 3 个用户",
      "banned_count": 3,
      "total_revoked_tokens": 5  // 总撤销Token数
    }
    ```
  - **容错设计**：自动跳过不存在、已封禁、超级管理员（非超管操作时）、自己的用户，不影响其他用户的封禁操作。

- `POST /users/admin/batch-unban` — 批量解除用户封禁（管理员）。权限：`user:update:all`。
  - **Body**：
    ```jsonc
    {
      "user_ids": [3, 4, 5],
      "reason": "批量解封"  // 可选
    }
    ```
  - **响应**：
    ```jsonc
    {
      "message": "成功解除 3 个用户封禁",
      "unbanned_count": 3
    }
    ```

**工作原理**：
- **Token失效方案**：封禁时撤销用户所有Token（从Redis删除），被封禁用户下次请求时Token验证失败（401），前端自动跳转登录页。用户尝试重新登录时会被 `is_active` 检查拦截，提示"用户未激活"。
- **无需WebSocket推送**：采用Token失效方案，无需前端任何改动，用户自然下线，体验流畅。
- **审计追踪**：所有封禁/解封操作都会记录到审计日志，包含操作人、操作原因、撤销Token数量等详细信息。

### 2.2 角色与权限（`/api/v1/roles`）
- `GET /roles/` — 获取全部角色。
- `POST /roles/` — 创建角色。Body：`RoleCreate`（`name`、`description`、`is_default`、`is_active`、`permission_ids`）。
- `GET /roles/{role_id}`、`PUT /roles/{role_id}`、`DELETE /roles/{role_id}` — 角色详情、更新、删除。删除时若角色仍关联用户将返回 400。
- 权限字典：
  - `GET /roles/permissions/` — 查看全量权限。
  - `POST /roles/permissions/`、`PUT /roles/permissions/{id}`、`DELETE /roles/permissions/{id}` — 仅超级管理员可创建/修改/删除权限。

---

## 3. 内容管理与审核

### 3.1 文章管理（`/api/v1/articles`）
**基础操作**
- `POST /articles/create` — 创建空白草稿，返回 `{ "article_id": number }`。需 `article:create:own`。
- `PUT /articles/{article_id}` — 更新正文、封面、标签、分类等。Body：`ArticleUpdate`（`title`、`content`、`description`、`cover_url`、`tags`、`category_id`、`is_published`）。服务层会根据 `is_published` 与内容变更自动生成或刷新审核记录。
- `DELETE /articles/{article_id}` — 删除文章，同时清理审核记录与缓存。需 `article:delete:own` 或管理员。
- `GET /articles/{article_id}` — 获取文章聚合详情（含作者、分类、统计数据、正文）。
- `GET /articles/{user_id}/articles` — 按作者分页列出文章，`status` 参数可过滤 `ArticleStatus`：`draft`、`published_pending`、`published_approved`、`published_rejected`。

**管理员专用**
- `GET /articles/admin` — 后台文章列表。Query：`status`、`author_id`、`category_id`、`keyword`、`start_date`、`end_date`。权限：`article:manage:all`。响应：`CursorPaginationResponse[ArticleOut]`。
- `PUT /articles/admin/{article_id}/status` — 更新文章状态与管理备注。Query：`status`（与前端约定值，例如 `published`、`draft`、`rejected`）、`reason`。返回更新后的 `ArticleOut`。
- `POST /articles/admin/batch-status` — 批量审核/归档文章。Body 示例：
  ```jsonc
  {
    "article_ids": [1, 2, 3],
    "status": "published",
    "reason": "专题上线"
  }
  ```
  返回 `{ "message": "...", "updated_count": 3 }`。
- `DELETE /articles/admin/{article_id}` — 删除文章（管理员）- 带确认机制。权限：`article:manage:all`。
  - **两步删除流程**：
    1. 首次调用（`confirm=false`）：返回文章详情和影响范围，供前端展示确认对话框
    2. 二次调用（`confirm=true`）：执行实际删除操作
  - **Body**：`{ "confirm": boolean }`
  - **第一步响应示例（confirm=false）**：
    ```jsonc
    {
      "message": "请确认删除操作",
      "article": {
        "id": 123,
        "title": "示例文章标题",
        "author_id": 456,
        "created_at": "2025-01-01T12:00:00",
        "visit_count": 1500
      },
      "impact": {
        "comment_count": 25,
        "like_count": 180,
        "favorite_count": 45
      },
      "warning": "删除后数据无法恢复，相关的评论、点赞、收藏等数据也将被删除",
      "next_step": "请在请求体中设置 confirm=true 来确认删除"
    }
    ```
  - **第二步响应示例（confirm=true）**：
    ```jsonc
    {
      "message": "文章删除成功",
      "article_id": 123,
      "deleted_by": 1
    }
    ```
  - **删除操作包含**：
    - 删除文章本体
    - 删除关联的审核记录
    - 清理所有相关缓存（文章缓存、推荐缓存）
    - 更新用户文章数统计
    - 记录审计日志

### 3.2 文章审核流程（`/api/v1/reviews`）
- 审核状态枚举：`pending`、`approved`、`rejected`；内容类型：`article`、`video`。
- `POST /reviews/` — 创建审核记录（首次发布触发）。Body：`ReviewCreate`。
- `GET /reviews/` — 游标分页查看审核任务。Query：`content_type`、`status`、`cursor` 等。权限：`system:manage:all`。
- `GET /reviews/{review_id}` — 审核详情。
- `PUT /reviews/{review_id}` — 更新审核结果。Body：`ReviewUpdate`（`status`、`comment`、`reviewer_id`）。批准/拒绝后会同步更新对应文章/视频的发布状态，并写入审计日志。

### 3.3 视频管理（`/api/v1/videos`）
**核心端点**
- `GET /videos/admin` — 后台视频列表，支持 `status`、`author_id`、`category_id`、`keyword`、`start_date`、`end_date` 过滤。权限：`video:manage:all`。
- `PUT /videos/admin/{video_id}/status` — 更新视频状态及备注。Query：`status`、`reason`。
- `POST /videos/admin/batch-status` — 批量调整视频状态。Body 与文章批量更新类似。
- `DELETE /videos/admin/{video_id}` — 删除视频（管理员）- 带确认机制。权限：`video:manage:all`。
  - **两步删除流程**：
    1. 首次调用（`confirm=false`）：返回视频详情和影响范围，供前端展示确认对话框
    2. 二次调用（`confirm=true`）：执行实际删除操作
  - **Body**：`{ "confirm": boolean }`
  - **第一步响应示例（confirm=false）**：
    ```jsonc
    {
      "message": "请确认删除操作",
      "video": {
        "id": 456,
        "title": "示例视频标题",
        "author_id": 789,
        "created_at": "2025-01-01T15:30:00",
        "visit_count": 3200,
        "duration": 300
      },
      "impact": {
        "comment_count": 42,
        "like_count": 320,
        "favorite_count": 88
      },
      "warning": "删除后数据无法恢复，相关的评论、点赞、收藏等数据也将被删除",
      "next_step": "请在请求体中设置 confirm=true 来确认删除"
    }
    ```
  - **第二步响应示例（confirm=true）**：
    ```jsonc
    {
      "message": "视频删除成功",
      "video_id": 456,
      "deleted_by": 1
    }
    ```
  - **删除操作包含**：
    - 软删除视频（标记 `is_deleted=true`）
    - 删除关联的审核记录
    - 更新用户视频数统计
    - 记录审计日志

**作者侧常用（管理后台可复用）**
- `PUT /videos/{video_id}` — 更新视频信息（`VideoUpdate`）。
- `DELETE /videos/{video_id}` — 删除视频，遵循权限检查。
- `GET /videos/{video_id}`、`GET /videos/hot` 等用于前台展示，可视需求复用。

### 3.4 分类管理（`/api/v1/categories`）
- `GET /categories/tree` — 全量分类树，`category_type` 支持 `article` / `video` / `scratch`。
- 管理端：
  - `POST /categories/admin` — 创建分类。Body：`CategoryCreate`（`name`、`description`、`parent_id`、`category_type` 等）。
  - `PUT /categories/{category_id}/admin` — 更新分类。Body：`CategoryUpdate`。
  - `DELETE /categories/{category_id}/admin` — 软删除分类。
  - `PUT /categories/{category_id}/sort` — 更新排序权重（Query：`sort_order`）。
  权限均为 `category:manage:all`。

### 3.5 标签管理（`/api/v1/tags`）
**基础查询**
- `GET /tags/default` — 查询默认标签，可按 `content_type` 过滤（`article`/`video`/`scratch`/`post`/`global`）。

**管理员操作**
- `GET /tags/admin` — 标签列表（管理员）。Query：`content_type`、`skip`、`limit`。权限：`tag:manage:all`。
  - **响应字段**：
    ```jsonc
    {
      "id": 1,
      "name": "Python",
      "is_default": false,
      "content_type": "article",
      "created_at": "2025-10-24T08:30:00.123456",  // 新增：标签创建时间
      "updated_at": "2025-10-24T09:15:30.789012"   // 新增：标签最后更新时间
    }
    ```
  - **字段说明**：
    - `id`：标签唯一ID
    - `name`：标签名称（最长50字符，唯一）
    - `is_default`：是否为默认标签
    - `content_type`：标签所属内容类型
    - `created_at`：标签创建时间（ISO 8601格式）
    - `updated_at`：标签最后更新时间（ISO 8601格式）

- `POST /tags/admin` — 创建标签（管理员）。Body：
  ```jsonc
  {
    "name": "Python",                    // 必填：标签名称
    "content_type": "article",           // 可选：内容类型，默认"global"
    "is_default": false                  // 可选：是否默认标签，默认false
  }
  ```
  权限：`tag:manage:all`。响应格式同标签列表。

- `PUT /tags/admin/{tag_id}` — 更新标签（管理员）。Body（所有字段均为可选）：
  ```jsonc
  {
    "name": "Python进阶",                // 可选：新标签名称
    "content_type": "video",             // 可选：新内容类型
    "is_default": true                   // 可选：新默认状态
  }
  ```
  权限：`tag:manage:all`。更新成功后 `updated_at` 字段自动更新。响应格式同标签列表。

- `DELETE /tags/admin/{tag_id}` — 删除标签（管理员）。权限：`tag:manage:all`。
  - **响应**：
    ```jsonc
    {
      "message": "标签删除成功"
    }
    ```

**时间字段使用说明**
- `created_at` 和 `updated_at` 在所有标签接口的响应中均包含
- 时间格式为 ISO 8601 标准（示例：`2025-10-24T08:30:00.123456`）
- 可用于前端展示标签创建/更新时间，便于管理员追踪标签变更历史
- 配合审计日志使用，可实现完整的标签管理追溯

### 3.6 评论与互动治理（`/api/v1/comments`）
- `GET /comments/admin` — 后台评论列表。Query：`content_type`（`article`/`video`/`scratch`）、`content_id`、`status`、`reported`、`skip`、`limit`。权限：`comment:manage:all`。
- `PUT /comments/admin/{comment_id}/status` — 审核单条评论。Query：`status`（如 `approved`、`rejected`）、`reason`。
- `POST /comments/admin/batch-review` — 批量操作。Body：
  ```jsonc
  {
    "comment_ids": [10, 11],
    "action": "approve", // approve / reject / delete
    "reason": "人工巡检"
  }
  ```
- `GET /comments/admin/reports` — 查询被举报评论，支持分页。

相关操作会记录审计日志，前端可根据返回的 `message` 与 `processed_count` 做提示。

### 3.7 轮播图管理（`/api/v1/banners`）
- `POST /banners/` — 创建轮播图。Body：`BannerCreate`（`title`、`image_url`、`banner_type`、`video_id` 等），会校验视频存在。
- `GET /banners/` — 条件检索，Query：`banner_type`、`is_active`、`skip`、`limit`。
- `PUT /banners/{banner_id}` — 更新。Body：`BannerUpdate`。
- `DELETE /banners/{banner_id}` — 软删除。
- `GET /banners/home` — 获取首页轮播图含视频摘要，可用于前台预览。
权限依据操作分别为 `banner:create/update/delete:all`。

### 3.8 推荐评估与运营（`/api/v1/recommendations`）
- `GET /recommendations/stats` — 推荐统计概览。Query：`days`（默认 7）。权限：`system:read:all`。
- `GET /recommendations/evaluation/report` — 综合评估报告。
- `GET /recommendations/evaluation/algorithm/{algorithm_type}` — 单算法效果指标（`algorithm_type` 支持 `collaborative`、`content_based`、`hot`、`hybrid`）。
- `GET /recommendations/evaluation/user/{user_id}` — 指定用户推荐质量评估，管理员或用户本人可访问。

### 3.9 用户推荐追踪（`/api/v1/recommendations/users`）
**核心列表**
- `GET /recommendations/users` — 获取推荐用户列表。Query：`algorithm_type`（可选，`collaborative`/`content_based`/`social_network`/`popular`/`hybrid`）、`page`、`page_size`。响应：`UserRecommendationResponse`，含 `items`（`user_id`、`username`、`nickname`、`mutual_following_count`、`score`、`reason`、`algorithm_type` 等）、`has_next`、`generated_at`。
- `GET /recommendations/users/similarity/{target_user_id}` — 计算与目标用户的相似度。Query：`similarity_types`（列表，默认 `overall`，可选 `behavior`、`interest`、`content`、`social`）。响应返回 `current_user_id`、`target_user_id`、`similarities` 字典及计算时间。

**反馈与行为上报**
- `POST /recommendations/users/feedback` — 提交用户推荐反馈。Body：`UserRecommendationFeedback`（`user_id`、`feedback_type`=`click`/`follow`/`ignore`/`block`、`algorithm_type`、可选 `position`、`extra_data`）。若反馈为 `follow` 会自动触发关注及转化追踪。
- `POST /recommendations/users/track/display` — 记录推荐展示。Body：`recommended_user_ids`（被推荐用户ID列表）、`algorithm_type`、可选 `position`、`extra_data`。成功返回 `{ "message": "展示事件追踪成功" }`。
- `POST /recommendations/users/track/click` — 记录推荐点击。Body：`clicked_user_id`、`algorithm_type`、可选 `position`、`extra_data`。成功返回 `{ "message": "点击事件追踪成功" }`。

**效果指标（管理员）**
- `GET /recommendations/users/metrics` — 推荐效果指标面板。Query：`algorithm_type`（可选）、`days`（1-30）。权限：`system:read:all`。响应包含 `period`、`algorithm_type`、`total_displays`、`total_clicks`、`total_follows`、`click_through_rate`、`follow_conversion_rate`、`overall_conversion_rate`、`algorithm_distribution`（各算法曝光数）、`user_engagement`（`active_users`、`average_recommendations_per_user`）、`generated_at`。
- `GET /recommendations/users/metrics/comparison` — 算法对比分析。Query：`days`（默认 7）。权限：`system:read:all`。响应提供 `algorithms` 字典（按算法返回 `displays`、`clicks`、`follows`、`ctr`、`conversion_rate`、`overall_conversion`）以及 `best_ctr_algorithm`、`best_conversion_algorithm`。

### 3.10 沸点管理（`/api/v1/posts`）
- `GET /posts/` — 沸点列表查询。支持 `author_id`、`post_type`、`topic`、`is_hot`、`sort_by`、`sort_order` 等过滤排序参数，响应 `PostsPageResponse`（`posts`、`total_count`、`has_next`、`has_previous`、分页信息）。目前未提供后台专属筛选，运营可结合 `topic` 与热门排序查看重点内容。访问需登录，未额外定义 RBAC 常量。
- `GET /posts/{post_id}` — 沸点详情。返回 `PostOut`，包含作者、统计、媒体、投票信息等。若帖子不存在返回 404。
- `POST /posts/` — 创建沸点。Body：`PostCreate`（文本、媒体、投票、转发等字段）。仅支持作者自身创建；后台若需代发需以目标作者身份调用。
- `PUT /posts/{post_id}` — 更新沸点。Body：`PostUpdate`（`content`、`visibility`、`topic`、`location` 等可选字段）。仅作者本人可更新。
- `DELETE /posts/{post_id}` — 删除沸点。作者本人可删除并同步更新统计，非作者请求会返回 403。
- `POST /posts/drafts`、`GET /posts/drafts`、`DELETE /posts/drafts/{draft_id}` — 草稿保存、草稿列表、草稿删除接口，均要求作者身份。用于后台内容团队先保存草稿再发布。
- `POST /posts/upload/media` — 沸点媒体上传。支持图片/视频，返回 CDN URL 与元信息，可与 `PostCreate.media_files` 配合使用。

> **当前限制**：沸点模块暂未实现带有 `/admin/posts` 路径或 RBAC 常量的专用后台接口，内容运营依赖作者身份操作。若后续需要管理员直接审核/屏蔽沸点，可在本章节扩展子节，或新增 `PostsAdminRouter` 以覆盖审核、屏蔽、批量处理等功能。

---

## 4. 运营工具

### 4.1 系统通知（`/api/v1/notifications`）
- `GET /notifications/` — 分页获取通知（支持 `status`、`channel`、`target_type` 等过滤）。
- `POST /notifications/` — 创建通知。Body：`NotificationCreate`（标题、正文、目标用户/群体等），权限：`notification:manage:all`。
- `PATCH /notifications/{id}` — 更新通知状态或内容。
- `PATCH /notifications/read-all` — 标记所有通知为已读（当前登录用户）。
- `DELETE /notifications/{id}` — 删除通知。
- `POST /notifications/broadcast` — 向所有用户广播。Body：`NotificationBroadcastRequest`（支持定时发送、TTL、渠道配置）。
- `POST /notifications/broadcast/online-only` — 仅面向在线用户广播。

### 4.2 审计日志（`/api/v1/audit`）
- `GET /audit/` — 日志列表。筛选条件：`user_id`、`action`、`resource_type`、`resource_id`、`start_date`、`end_date`、`success`、`keyword`、分页参数。若无 `system:read:all` 权限仅返回当前用户日志。
- `GET /audit/{log_id}` — 日志详情。
- `GET /audit/stats/overview` — KPI 统计（总量、成功率、Top 用户/操作/资源）。
- `GET /audit/stats/resource-activity`、`GET /audit/stats/user-activity`（参见 `app/schemas/audit_log.py`）可按需扩展。

---

## 5. 系统配置与观测

### 5.1 动态配置中心（`/api/v1/admin/config`）
- `GET /admin/config/` — 配置与功能开关列表，支持 `category` 过滤。敏感值返回 `"***"`。
- `PUT /admin/config/` — 批量更新配置。Body：`ConfigUpdateRequest`（需携带 `version` 乐观锁，`reason` 用于审计）。冲突时返回 409。
- `GET /admin/config/history` — 配置变更历史，分页支持过滤 `config_key` 与时间范围。
- `POST /admin/config/rollback/{change_id}` — 回滚，Body：`ConfigRollbackRequest`（`confirm` 必须为 `true`）。
- `GET /admin/config/features`、`PUT /admin/config/features/{name}` — 功能开关列表与状态切换，响应内含 `broadcast_status` 便于展示配置广播进度。

### 5.2 监控与告警（`/api/v1/admin/monitoring`）
- `GET /dashboard` — 系统/应用指标摘要，`time_range` 支持 `5m`、`15m`、`30m`、`1h`、`6h`、`24h`。
- `GET /metrics` — 指标时序数据，若未传 `start`/`end` 默认使用最近 30 分钟。
- `GET /alerts` — 告警事件列表，支持状态、级别、分页过滤。
- `POST /alerts`、`PUT /alerts/{rule_id}`、`DELETE /alerts/{rule_id}` — 告警规则 CRUD。Body：`AlertRuleCreateRequest` / `AlertRuleUpdateRequest`，其中 `operator` 枚举：`greater_than`、`greater_or_equal`、`less_than`、`less_or_equal`、`equal`、`not_equal`；`severity`：`info`、`warning`、`error`、`critical`。
- `GET /channels`、`POST /test-alert` — 通知渠道列表与联调。

### 5.3 数据分析（`/api/v1/admin/analytics`）
- `GET /dashboard`、`GET /users`、`GET /content`、`GET /system` — 根据 `date_range`（`today` / `yesterday` / `last_7_days` / `last_30_days` / `last_90_days` / `custom`）或 (`from_date`,`to_date`) 返回分析快照。响应包含 `summary` 与 `rows`。
- `POST /export` — 导出 CSV。Body：`AnalyticsExportRequest`，字段：`dataset`（`dashboard`/`users`/`content`/`system`）、`time_range`、`filters`、`async_mode`（当前实现为同步导出，直接返回 `path` 和 `rows`）。

---

## 6. 联调提示
- 所有成功响应统一走包装结构，异常请统一落到全局错误提示。
- 管理端常见枚举：
  - `ArticleStatus`: `draft` / `published_pending` / `published_approved` / `published_rejected`.
  - `ReviewStatus`: `pending` / `approved` / `rejected`.
  - `AlertOperator`: `greater_than` / `greater_or_equal` / `less_than` / `less_or_equal` / `equal` / `not_equal`.
  - `AlertSeverity`: `info` / `warning` / `error` / `critical`.
  - `TimeRange`（分析模块）: `today` / `yesterday` / `last_7_days` / `last_30_days` / `last_90_days` / `custom`.
- 批量操作（文章/视频/评论）统一要求传 ID 列表与目标状态/动作，返回处理条数，可据此提示用户。
- 审计日志记录了文章/视频/评论状态调整与配置变更，可与运营面板联动展示。
---

## 7. 设备安全管理

### 7.1 设备管理（`/api/v1/device`）
**设备查询**
- `GET /device/admin/devices` — 获取所有设备列表（管理员）。Query：`user_id`、`is_blocked`、`risk_level`（high/medium/low）、`skip`、`limit`。权限：`device:manage:all`。响应：`list[DeviceInfo]`（含设备信息、信任分数、封禁状态等）。
  - **风险等级过滤**：
    - `high`：trust_score ≤ 30（高风险）
    - `medium`：30 < trust_score ≤ 60（中风险）
    - `low`：trust_score > 60（低风险）
- `GET /device/admin/devices/risk` — 获取风险设备列表（管理员）。Query：`max_trust_score`（默认30）、`skip`、`limit`。权限：`device:manage:all`。返回信任分数低于阈值的设备，按信任分数升序排列。

**设备封禁管理**
- `POST /device/admin/devices/block` — 封禁单个设备。Body：`DeviceActionRequest`（`device_id`、`reason`）。权限：`device:manage:all`。同时记录审计日志。
  - **响应**：
    ```jsonc
    {
      "message": "设备封禁成功",
      "device_id": 8
    }
    ```
- `POST /device/admin/devices/batch-block` — 批量封禁设备。Body：`device_ids: list[int]`、`reason: str`。返回处理统计信息。
  - **响应**：
    ```jsonc
    {
      "message": "成功封禁 3 个设备",
      "blocked_count": 3
    }
    ```

**设备解封管理**
- `POST /device/admin/devices/unblock` — 解除单个设备封禁。Body：`DeviceActionRequest`（`device_id`、`reason`）。权限：`device:manage:all`。同时记录审计日志。
  - **响应**：
    ```jsonc
    {
      "message": "设备解除封禁成功",
      "device_id": 8
    }
    ```
- `POST /device/admin/devices/batch-unblock` — 批量解除设备封禁。Body：`device_ids: list[int]`、`reason: str`（可选）。返回处理统计信息。
  - **响应**：
    ```jsonc
    {
      "message": "成功解除 3 个设备封禁",
      "unblocked_count": 3
    }
    ```

---

## 8. 内容创作平台管理

### 8.1 Scratch项目管理（`/api/v1/scratch`）
**核心管理功能**
- `GET /scratch/` — 获取Scratch项目列表，支持分页和多条件筛选（`difficulty_level`、`category`、`author_id`、`adaptation_type`）。自动权限过滤，仅显示公开项目或用户有权访问的项目。
- `GET /scratch/{project_id}` — 获取项目详情，支持权限控制（公开项目游客可访问，私有项目需相应权限）。
- `GET /scratch/user/{user_id}/projects` — 获取指定用户的项目列表，支持权限控制（作者本人可见全部，其他用户仅可见公开项目）。
- `GET /scratch/search` — 项目搜索功能，支持关键词、难度等级、分类等多维度搜索。

**项目生命周期管理**
- `POST /scratch/create` — 创建项目（支持空白项目、复制、重混）。权限：`scratch_project:create:own`。
- `PUT /scratch/{project_id}` — 更新项目基本信息。权限：`scratch_project:update:own`（作者本人或管理员）。
- `PUT /scratch/{project_id}/detail` — 更新项目详细信息。
- `POST /scratch/{project_id}/share` — 分享项目（设为公开）。
- `POST /scratch/{project_id}/unshare` — 取消分享（设为私有）。
- `DELETE /scratch/{project_id}` — 删除项目。权限：`scratch_project:delete:own`。

### 8.2 背包系统管理（`/api/v1/backpack`）
- `GET /backpack/{user_id}` — 获取用户背包项目列表。Query：支持类型筛选（`costume`/`sound`/`sprite`/`script`）、分页（`limit`/`offset`）。权限：`user:read:own`（用户本人或管理员）。
- `POST /backpack/{user_id}` — 创建背包项目。Body：`BackpackItemCreate`（`type`、`mime`、`name`、`body`、`thumbnail`）。支持Base64数据处理和文件去重。权限：`user:update:own`。
- `DELETE /backpack/{user_id}/{item_id}` — 删除背包项目。同时清理文件存储和数据库记录。权限：`user:update:own`。
- `GET /backpack/{user_id}/stats` — 获取背包统计信息。返回按类型分组的数量和大小统计。

---

## 9. 文件与资源管理

### 9.1 文件上传管理（`/api/v1/upload`）
- `POST /upload/image` — 单图片上传。支持文件哈希去重、自动压缩处理。返回：`UploadResponse`（`file_hash`、`file_url`、元数据）。
- `POST /upload/file` — 通用文件上传。支持多种文件类型，自动哈希校验和去重。
- `POST /upload/batch` — 批量文件上传。Body：`files: list[UploadFile]`、`folder_id: int`、`video_quality: VideoQuality`。支持流式处理和任务状态跟踪。

### 9.2 视频文件夹管理（`/api/v1/video-folders`）
**文件夹结构管理**
- 支持层级文件夹结构（父子关系）
- 权限控制：私有（0）、公开（1）、链接可见（2）、指定用户可见（3）
- 自动创建默认文件夹
- 文件夹视频数量统计

**访问权限控制**
- 文件夹所有者：可访问所有状态的视频
- 其他用户：仅可访问公开文件夹中已发布且已审核的视频
- 管理员：可访问所有文件夹和视频

---

## 10. 用户行为分析与历史

### 10.1 用户行为追踪（`/api/v1/user/behavior`）
- `POST /user/behavior/track` — 记录用户行为数据。Body：行为类型、内容ID、时长等。用于推荐算法和用户画像分析。
- `GET /user/behavior/interest-analysis` — 获取用户兴趣分析。Query：`days`（分析天数，7-365）。返回：`UserInterestProfile`（兴趣标签、偏好分类、活跃度分数）。
- `GET /user/behavior/popular-content` — 获取热门内容统计。Query：`content_type`、`limit`、`days`。基于浏览历史统计热门内容。

### 10.2 用户历史记录（`/api/v1/user/history`）
- `GET /user/history/` — 获取用户浏览历史。Query：`content_type`、`skip`、`limit`、`days`。支持按内容类型过滤。
- `POST /user/history/` — 添加历史记录。Body：`content_type`、`content_id`。同时更新缓存和数据库。
- `DELETE /user/history/` — 清空历史记录。Query：`content_type`（可选，指定类型清空）。同时清理缓存和数据库数据。

---

## 11. 删除功能前端集成指南

### 11.1 基本流程

删除功能采用两步确认机制，确保管理员在删除前充分了解影响范围：

```javascript
// 第一步：获取删除确认信息
async function getDeleteConfirmation(contentType, contentId) {
  const endpoint = contentType === 'article' 
    ? `/api/v1/articles/admin/${contentId}`
    : `/api/v1/videos/admin/${contentId}`;
  
  const response = await fetch(endpoint, {
    method: 'DELETE',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    },
    body: JSON.stringify({ confirm: false })
  });
  
  return await response.json();
}

// 第二步：执行删除
async function confirmDelete(contentType, contentId) {
  const endpoint = contentType === 'article'
    ? `/api/v1/articles/admin/${contentId}`
    : `/api/v1/videos/admin/${contentId}`;
  
  const response = await fetch(endpoint, {
    method: 'DELETE',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    },
    body: JSON.stringify({ confirm: true })
  });
  
  return await response.json();
}
```

### 11.2 UI 展示建议

删除确认对话框应包含以下信息：

1. **内容信息**：标题、作者、创建时间、访问量
2. **影响范围**：评论数、点赞数、收藏数
3. **警告信息**：突出显示"删除后数据无法恢复"
4. **操作按钮**：取消按钮（默认）、确认删除按钮（红色）

### 11.3 React 组件示例

```jsx
import { useState } from 'react';
import { Modal, Button, Alert, Descriptions, Statistic, Row, Col } from 'antd';
import { ExclamationCircleOutlined } from '@ant-design/icons';

function DeleteConfirmModal({ visible, onCancel, confirmData, onConfirm }) {
  const [loading, setLoading] = useState(false);
  
  const handleConfirm = async () => {
    setLoading(true);
    try {
      await onConfirm();
    } finally {
      setLoading(false);
    }
  };
  
  if (!confirmData) return null;
  
  const { article, video, impact, warning } = confirmData;
  const content = article || video;
  const contentType = article ? '文章' : '视频';
  
  return (
    <Modal
      title={
        <span>
          <ExclamationCircleOutlined style={{ color: '#ff4d4f', marginRight: 8 }} />
          确认删除{contentType}
        </span>
      }
      open={visible}
      onCancel={onCancel}
      footer={[
        <Button key="cancel" onClick={onCancel}>
          取消
        </Button>,
        <Button 
          key="delete" 
          type="primary" 
          danger 
          loading={loading}
          onClick={handleConfirm}
        >
          确认删除
        </Button>
      ]}
      width={600}
    >
      <Alert
        message="警告"
        description={warning}
        type="error"
        showIcon
        style={{ marginBottom: 16 }}
      />
      
      <Descriptions title={`${contentType}信息`} bordered column={1}>
        <Descriptions.Item label="标题">{content.title}</Descriptions.Item>
        <Descriptions.Item label="ID">{content.id}</Descriptions.Item>
        <Descriptions.Item label="作者ID">{content.author_id}</Descriptions.Item>
        <Descriptions.Item label="创建时间">{content.created_at}</Descriptions.Item>
        <Descriptions.Item label="访问量">{content.visit_count}</Descriptions.Item>
        {video && (
          <Descriptions.Item label="时长">
            {Math.floor(content.duration / 60)}分{content.duration % 60}秒
          </Descriptions.Item>
        )}
      </Descriptions>
      
      <div style={{ marginTop: 16 }}>
        <h4>影响范围</h4>
        <Row gutter={16}>
          <Col span={8}>
            <Statistic title="评论数" value={impact.comment_count} />
          </Col>
          <Col span={8}>
            <Statistic title="点赞数" value={impact.like_count} />
          </Col>
          <Col span={8}>
            <Statistic title="收藏数" value={impact.favorite_count} />
          </Col>
        </Row>
      </div>
    </Modal>
  );
}

export default DeleteConfirmModal;
```

### 11.4 使用示例

```jsx
function ArticleManagement() {
  const [deleteModalVisible, setDeleteModalVisible] = useState(false);
  const [confirmData, setConfirmData] = useState(null);
  const [selectedId, setSelectedId] = useState(null);
  
  const handleDeleteClick = async (articleId) => {
    setSelectedId(articleId);
    const data = await getDeleteConfirmation('article', articleId);
    setConfirmData(data);
    setDeleteModalVisible(true);
  };
  
  const handleConfirmDelete = async () => {
    await confirmDelete('article', selectedId);
    message.success('删除成功');
    setDeleteModalVisible(false);
    refreshList();
  };
  
  return (
    <>
      <Button danger onClick={() => handleDeleteClick(article.id)}>
        删除
      </Button>
      
      <DeleteConfirmModal
        visible={deleteModalVisible}
        onCancel={() => setDeleteModalVisible(false)}
        confirmData={confirmData}
        onConfirm={handleConfirmDelete}
      />
    </>
  );
}
```

### 11.5 错误处理

| 状态码 | 说明 | 处理建议 |
|--------|------|----------|
| 403 | 权限不足 | 提示用户无权限执行此操作 |
| 404 | 内容不存在 | 提示内容已被删除或不存在 |
| 400 | 请求参数错误 | 检查请求参数格式 |

### 11.6 安全注意事项

1. **权限控制**：确保只有具有相应权限的管理员才能访问删除接口
2. **审计日志**：所有删除操作都会记录在系统日志中，包括操作者ID和删除的内容ID
3. **不可逆性**：删除操作是永久性的，无法恢复，请务必谨慎操作
4. **级联删除**：删除内容时会同时删除相关的评论、点赞、收藏等数据
5. **缓存清理**：删除操作会自动清理相关缓存

---

## 12. 联调提示与补充说明

### 11.1 新增模块的通用特性
- **权限控制**：所有管理接口都集成了完整的RBAC权限系统
- **审计日志**：关键操作（设备封禁、项目删除等）自动记录审计日志
- **错误处理**：统一的异常处理和错误响应格式
- **分页支持**：大部分列表接口支持游标分页或传统分页

### 11.2 文件存储特性
- **去重机制**：基于SHA256哈希值自动去重，避免重复存储
- **缓存优化**：Redis缓存文件哈希映射，提升性能
- **CDN支持**：集成阿里云OSS，支持CDN加速访问
- **引用计数**：智能删除，文件不被使用时才真正删除

### 11.3 行为分析特性
- **实时追踪**：用户行为实时记录和分析
- **双重存储**：缓存+数据库，保证性能和数据持久性
- **隐私保护**：用户只能访问自己的行为数据
- **推荐支持**：为推荐算法提供数据基础

### 11.4 权限矩阵补充

| 功能模块 | 权限常量 | 权限字符串 |
| --- | --- | --- |
| 设备管理 | `Permission(ResourceType.DEVICE, Action.MANAGE, Scope.ALL)` | `device:manage:all` |
| Scratch项目 | `Permissions.SCRATCH_PROJECT_*` | `scratch_project:create/read/update/delete:own` |
| 背包管理 | `Permissions.USER_READ/UPDATE_OWN` | `user:read/update:own` |
| 文件上传 | 无特殊权限要求 | 登录用户即可 |
| 行为分析 | 无特殊权限要求 | 仅可访问自己的数据 |

- 若需扩展其他模块，可参考 `app/api/endpoints` 中相应文件，接口风格与本文一致。
