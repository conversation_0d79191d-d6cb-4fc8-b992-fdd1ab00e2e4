from datetime import datetime, timezone
from unittest.mock import AsyncMock

import pytest
from sqlalchemy.ext.asyncio import AsyncSession

from app import schemas
from app.services.user_aggregation_service import UserAggregationService


@pytest.mark.asyncio
async def test_get_users_by_ids_returns_aggregated_map():
    now = datetime.now(timezone.utc)

    user_base = schemas.UserBase(
        id=1,
        username="tester",
        role_id=1,
        nickname="测试用户",
        description="desc",
        email="<EMAIL>",
        avatar=None,
        cover=None,
        is_active=True,
        is_superuser=False,
        last_login=None,
        created_at=now,
        updated_at=now,
        likes_privacy_settings=3,
        favorites_privacy_settings=3,
        wechat_openid=None,
        wechat_unionid=None,
        login_type="password",
    )

    cache_service = AsyncMock()
    cache_service.get_entities_batch.return_value = {1: user_base}

    stats_service = AsyncMock()
    stats_service.get_user_stats.return_value = schemas.UserStats(user_id=1, follower_count=5)

    service = UserAggregationService(
        user_cache_service=cache_service,
        user_stats_service=stats_service,
    )

    db_session = AsyncMock(spec=AsyncSession)
    result = await service.get_users_by_ids(db_session, [1])

    assert 1 in result
    aggregated = result[1]
    assert aggregated.username == "tester"
    assert aggregated.stats is not None
    assert aggregated.stats.follower_count == 5
