from __future__ import annotations

from datetime import datetime, timezone
from typing import Async<PERSON>enerator
from unittest.mock import Async<PERSON>ock

import pytest
import pytest_asyncio
from sqlalchemy import <PERSON><PERSON>nteger
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.ext.compiler import compiles
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker

from app.db.session import Base
from app.models import AdminAnalyticsSnapshot, AdminAnalyticsSnapshotData, load_all_models
from app.services.analytics.pipeline import AnalyticsPipeline
from app.services.storage.export_store import ExportStorage


@compiles(JSONB, "sqlite")
def compile_jsonb(element, compiler, **kw):  # type: ignore[override]
    return "JSON"


@compiles(BigInteger, "sqlite")
def compile_bigint(element, compiler, **kw):  # type: ignore[override]
    return "INTEGER"


class InMemoryStorage(ExportStorage):
    def __init__(self) -> None:
        super().__init__(base_dir="tmp/test_exports")


@pytest_asyncio.fixture
async def async_session() -> AsyncGenerator[AsyncSession, None]:
    load_all_models()
    engine = create_async_engine("sqlite+aiosqlite:///:memory:", future=True)
    async with engine.begin() as conn:
        await conn.run_sync(
            Base.metadata.create_all,
            tables=[
                AdminAnalyticsSnapshot.__table__,
                AdminAnalyticsSnapshotData.__table__,
            ],
        )

    async_session_factory = sessionmaker(engine, class_=AsyncSession, expire_on_commit=False)
    async with async_session_factory() as session:  # type: ignore[call-arg]
        yield session
    await engine.dispose()


@pytest.mark.asyncio
async def test_refresh_snapshot_with_custom_provider(async_session: AsyncSession):
    async def provider(db, snapshot_type, time_range, filters):
        return {
            "summary": {"total_users": 10},
            "rows": [{"metric": "total_users", "value": 10}],
        }

    pipeline = AnalyticsPipeline(storage=InMemoryStorage(), data_provider=provider)
    snapshot = await pipeline.refresh_snapshot(
        async_session,
        snapshot_type="dashboard",
        time_range="last_7_days",
    )

    assert snapshot.status == "completed"
    data = await pipeline.get_snapshot_data(
        async_session, snapshot_type="dashboard", time_range="last_7_days", ensure_fresh=False
    )
    assert data["summary"]["total_users"] == 10


@pytest.mark.asyncio
async def test_export_snapshot_returns_path(async_session: AsyncSession):
    async def provider(db, snapshot_type, time_range, filters):
        return {
            "summary": {},
            "rows": [
                {"metric": "total_users", "value": 15},
                {"metric": "articles", "value": 5},
            ],
        }

    pipeline = AnalyticsPipeline(storage=InMemoryStorage(), data_provider=provider)
    result = await pipeline.export_snapshot(
        async_session,
        snapshot_type="dashboard",
        time_range="last_7_days",
    )
    assert result.rows == 2
    assert result.path.endswith(".csv")
