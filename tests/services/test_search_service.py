"""
搜索服务测试

测试核心搜索功能，包括：
- 分词功能
- 查询哈希生成
- 文章搜索
- 结果高亮
"""

import pytest
from app.services.search_service import SearchService


class TestSearchService:
    """搜索服务测试类"""

    def test_tokenize_query_with_jieba(self):
        """测试使用 jieba 分词"""
        query = "Python机器学习教程"
        result = SearchService.tokenize_query(query, use_jieba=True)
        
        # 验证返回的是空格分隔的字符串
        assert isinstance(result, str)
        assert len(result) > 0
        # jieba 应该将其分为多个词
        words = result.split()
        assert len(words) >= 3
        print(f"分词结果: {words}")

    def test_tokenize_query_without_jieba(self):
        """测试不使用 jieba 分词"""
        query = "Python tutorial"
        result = SearchService.tokenize_query(query, use_jieba=False)
        
        # 不使用 jieba 时应该返回原字符串
        assert result == query

    def test_generate_query_hash(self):
        """测试查询哈希生成"""
        query = "Python"
        filters = {"category_id": 5, "sort_by": "hot"}
        
        hash1 = SearchService.generate_query_hash(query, filters)
        hash2 = SearchService.generate_query_hash(query, filters)
        
        # 相同输入应该生成相同哈希
        assert hash1 == hash2
        assert len(hash1) == 32  # MD5 哈希长度
        
        # 不同输入应该生成不同哈希
        hash3 = SearchService.generate_query_hash("Java", filters)
        assert hash1 != hash3

    def test_highlight_text(self):
        """测试文本高亮"""
        text = "这是一篇关于Python编程的教程，讲解了Python的基础知识。"
        query = "Python"
        
        result = SearchService.highlight_text(text, query, max_length=200)
        
        # 验证包含高亮标签
        assert "<em>" in result
        assert "</em>" in result
        assert "Python" in result or "python" in result

    def test_highlight_text_not_found(self):
        """测试关键词未找到的情况"""
        text = "这是一篇关于Java编程的教程"
        query = "Python"
        
        result = SearchService.highlight_text(text, query, max_length=200)
        
        # 关键词未找到，应该返回开头片段
        assert result == text[:200]
        assert "<em>" not in result

    def test_highlight_text_empty_input(self):
        """测试空输入"""
        assert SearchService.highlight_text("", "test") == ""
        assert SearchService.highlight_text("some text", "") == "some text"
        assert SearchService.highlight_text(None, "test") == ""


# 集成测试示例（需要数据库）
@pytest.mark.asyncio
@pytest.mark.skip(reason="需要数据库连接，手动运行")
async def test_search_articles_integration(db_session):
    """测试文章搜索集成功能"""
    from app.core.pagination import CursorPaginationParams
    
    # 准备测试数据
    pagination = CursorPaginationParams(size=10, cursor=None)
    
    # 执行搜索
    results = await SearchService.search_articles(
        db=db_session,
        query="Python教程",
        category_id=None,
        sort_by="relevance",
        pagination=pagination
    )
    
    # 验证结果
    assert isinstance(results, list)
    # 如果有数据，验证返回的是 Article 对象
    if results:
        from app.models.article import Article
        assert isinstance(results[0], Article)


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v", "-s"])
