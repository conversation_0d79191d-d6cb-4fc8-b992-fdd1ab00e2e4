import pytest
import pytest_asyncio
from fastapi import <PERSON><PERSON><PERSON><PERSON>xception
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker

from app.db.session import Base
from app.models.article import Article
from app.models.category import Category
from app.models.review import ContentType, Review, ReviewStatus
from app.models.user import User
from app.models.video import Video
from app.models.video_folder import VideoFolder
from app.services.content_service import ContentService


@pytest_asyncio.fixture
async def async_session() -> AsyncSession:
    engine = create_async_engine("sqlite+aiosqlite:///:memory:")
    async with engine.begin() as conn:
        await conn.run_sync(
            Base.metadata.create_all,
            tables=[
                User.__table__,
                Category.__table__,
                VideoFolder.__table__,
                Article.__table__,
                Video.__table__,
                Review.__table__,
            ],
        )
    async_session_factory = sessionmaker(engine, class_=AsyncSession, expire_on_commit=False)
    async with async_session_factory() as session:  # type: ignore[arg-type]
        yield session
    await engine.dispose()


@pytest.mark.asyncio
async def test_article_publish_creates_review(async_session: AsyncSession):
    author = User(username="author-article")
    async_session.add(author)
    await async_session.commit()
    await async_session.refresh(author)

    article = Article(
        title="Publishable Article",
        content="Full content ready for review.",
        author_id=author.id,
        is_published=False,
        is_approved=False,
    )
    async_session.add(article)
    await async_session.commit()
    await async_session.refresh(article)

    service = ContentService("article")
    await service.handle_publish_status(async_session, article, is_published=True)

    await async_session.refresh(article)
    review = (
        await async_session.execute(
            select(Review).where(
                Review.content_type == ContentType.ARTICLE, Review.content_id == article.id
            )
        )
    ).scalar_one()

    assert article.is_published is True
    assert article.is_approved is False
    assert review.status == ReviewStatus.PENDING


@pytest.mark.asyncio
async def test_video_publish_creates_review(async_session: AsyncSession):
    author = User(username="author-video")
    async_session.add(author)
    await async_session.commit()
    await async_session.refresh(author)

    folder = VideoFolder(name="默认文件夹", user_id=author.id, path="/user/default", is_default=True)
    async_session.add(folder)
    await async_session.commit()
    await async_session.refresh(folder)

    video = Video(
        title="Preview Clip",
        description="Test video content",
        url="https://cdn.example.com/video.mp4",
        author_id=author.id,
        folder_id=folder.id,
        is_published=False,
        is_approved=False,
    )
    async_session.add(video)
    await async_session.commit()
    await async_session.refresh(video)

    service = ContentService("video")
    await service.handle_publish_status(async_session, video, is_published=True)

    await async_session.refresh(video)
    review = (
        await async_session.execute(
            select(Review).where(
                Review.content_type == ContentType.VIDEO, Review.content_id == video.id
            )
        )
    ).scalar_one()

    assert video.is_published is True
    assert video.is_approved is False
    assert review.status == ReviewStatus.PENDING


@pytest.mark.asyncio
async def test_video_publish_without_url_fails(async_session: AsyncSession):
    author = User(username="author-video-missing-url")
    async_session.add(author)
    await async_session.commit()
    await async_session.refresh(author)

    folder = VideoFolder(name="默认文件夹", user_id=author.id, path="/user/default", is_default=True)
    async_session.add(folder)
    await async_session.commit()
    await async_session.refresh(folder)

    video = Video(
        title="Draft Clip",
        author_id=author.id,
        folder_id=folder.id,
        is_published=False,
        is_approved=False,
    )
    async_session.add(video)
    await async_session.commit()
    await async_session.refresh(video)

    service = ContentService("video")
    with pytest.raises(HTTPException) as exc:
        await service.handle_publish_status(async_session, video, is_published=True)

    assert "播放地址" in exc.value.detail
    reviews = (
        await async_session.execute(
            select(Review).where(
                Review.content_type == ContentType.VIDEO, Review.content_id == video.id
            )
        )
    ).scalars().all()
    assert reviews == []
    await async_session.refresh(video)
    assert video.is_published is False
