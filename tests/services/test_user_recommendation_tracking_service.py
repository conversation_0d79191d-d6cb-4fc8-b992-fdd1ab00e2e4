"""
用户推荐追踪服务测试

测试UserRecommendationTrackingService的事件追踪和指标计算功能
"""

import json
from datetime import datetime, timedelta, timezone
from typing import AsyncGenerator
from unittest.mock import patch

import pytest
import pytest_asyncio
from sqlalchemy import BigInteger
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.ext.compiler import compiles
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker

from app import models
from app.db.session import Base
from app.models import load_all_models
from app.services.user_recommendation_tracking_service import UserRecommendationTrackingService
from tests.utils.test_helpers import MockRedis


@compiles(JSONB, "sqlite")
def compile_jsonb(element, compiler, **kw):  # type: ignore[override]
    return "JSON"


@compiles(BigInteger, "sqlite")
def compile_bigint(element, compiler, **kw):  # type: ignore[override]
    return "INTEGER"


@pytest_asyncio.fixture
async def async_session() -> AsyncGenerator[AsyncSession, None]:
    """提供带有加载模型的异步内存SQLite会话"""
    load_all_models()
    engine = create_async_engine("sqlite+aiosqlite:///:memory:", future=True)
    
    async with engine.begin() as conn:
        await conn.run_sync(
            Base.metadata.create_all,
            tables=[
                models.User.__table__,
                models.RecommendationLog.__table__,
            ],
        )

    async_session_factory = sessionmaker(
        engine,
        class_=AsyncSession,
        expire_on_commit=False,
    )

    async with async_session_factory() as session:  # type: ignore[call-arg]
        yield session
    await engine.dispose()


@pytest.fixture
def mock_redis() -> MockRedis:
    """Mock Redis客户端"""
    return MockRedis()


@pytest.fixture
def tracking_service(mock_redis: MockRedis) -> UserRecommendationTrackingService:
    """推荐追踪服务实例"""
    with patch('app.services.user_recommendation_tracking_service.redis_master_client', mock_redis):
        return UserRecommendationTrackingService()


@pytest_asyncio.fixture
async def sample_users(async_session: AsyncSession) -> list[models.User]:
    """创建示例用户数据"""
    users = []
    for i in range(1, 6):
        user = models.User(
            id=i,
            username=f"user{i}",
            email=f"user{i}@test.com",
            nickname=f"用户{i}",
            is_active=True,
            created_at=datetime.now(timezone.utc),
            updated_at=datetime.now(timezone.utc),
        )
        async_session.add(user)
        users.append(user)
    
    await async_session.commit()
    return users


@pytest_asyncio.fixture
async def sample_recommendation_logs(
    async_session: AsyncSession, sample_users: list[models.User]
) -> None:
    """创建示例推荐日志数据"""
    logs = [
        # 展示事件
        models.RecommendationLog(
            user_id=1,
            algorithm_type="collaborative",
            recommended_items=json.dumps({"user_ids": [2, 3, 4]}),
            recommendation_reason="user_recommendation_display",
            position="homepage",
            created_at=datetime.now(timezone.utc),
        ),
        models.RecommendationLog(
            user_id=1,
            algorithm_type="content_based",
            recommended_items=json.dumps({"user_ids": [3, 4, 5]}),
            recommendation_reason="user_recommendation_display",
            position="sidebar",
            created_at=datetime.now(timezone.utc),
        ),
        # 点击事件
        models.RecommendationLog(
            user_id=1,
            algorithm_type="collaborative",
            recommended_items=json.dumps({"clicked_user_id": 2}),
            recommendation_reason="user_recommendation_click",
            position="1",
            created_at=datetime.now(timezone.utc),
        ),
        models.RecommendationLog(
            user_id=1,
            algorithm_type="content_based",
            recommended_items=json.dumps({"clicked_user_id": 3}),
            recommendation_reason="user_recommendation_click",
            position="2",
            created_at=datetime.now(timezone.utc),
        ),
        # 关注事件
        models.RecommendationLog(
            user_id=1,
            algorithm_type="collaborative",
            recommended_items=json.dumps({"followed_user_id": 2}),
            recommendation_reason="user_recommendation_follow",
            position="recommendation",
            created_at=datetime.now(timezone.utc),
        ),
        # 其他用户的事件
        models.RecommendationLog(
            user_id=2,
            algorithm_type="social_network",
            recommended_items=json.dumps({"user_ids": [3, 4]}),
            recommendation_reason="user_recommendation_display",
            position="homepage",
            created_at=datetime.now(timezone.utc),
        ),
        models.RecommendationLog(
            user_id=2,
            algorithm_type="social_network",
            recommended_items=json.dumps({"clicked_user_id": 3}),
            recommendation_reason="user_recommendation_click",
            position="1",
            created_at=datetime.now(timezone.utc),
        ),
    ]
    
    for log in logs:
        async_session.add(log)
    
    await async_session.commit()


class TestUserRecommendationTrackingService:
    """用户推荐追踪服务测试类"""

    @pytest.mark.asyncio
    async def test_track_recommendation_display(
        self,
        async_session: AsyncSession,
        tracking_service: UserRecommendationTrackingService,
        sample_users: list[models.User],
    ):
        """测试推荐展示事件追踪"""
        success = await tracking_service.track_recommendation_display(
            db=async_session,
            user_id=1,
            recommended_user_ids=[2, 3, 4],
            algorithm_type="collaborative",
            position="homepage",
            extra_data={"test": "data"},
        )
        
        assert success is True
        
        # 验证数据库中记录了事件
        from sqlalchemy import select
        stmt = select(models.RecommendationLog).where(
            models.RecommendationLog.user_id == 1,
            models.RecommendationLog.recommendation_reason == "user_recommendation_display",
        )
        result = await async_session.execute(stmt)
        log = result.scalar_one_or_none()
        
        assert log is not None
        assert log.algorithm_type == "collaborative"
        assert log.position == "homepage"
        
        # 验证推荐项数据
        recommended_items = json.loads(log.recommended_items)
        assert recommended_items["user_ids"] == [2, 3, 4]

    @pytest.mark.asyncio
    async def test_track_recommendation_click(
        self,
        async_session: AsyncSession,
        tracking_service: UserRecommendationTrackingService,
        sample_users: list[models.User],
    ):
        """测试推荐点击事件追踪"""
        success = await tracking_service.track_recommendation_click(
            db=async_session,
            user_id=1,
            clicked_user_id=2,
            algorithm_type="content_based",
            position=1,
            extra_data={"source": "homepage"},
        )
        
        assert success is True
        
        # 验证数据库中记录了事件
        from sqlalchemy import select
        stmt = select(models.RecommendationLog).where(
            models.RecommendationLog.user_id == 1,
            models.RecommendationLog.recommendation_reason == "user_recommendation_click",
        )
        result = await async_session.execute(stmt)
        log = result.scalar_one_or_none()
        
        assert log is not None
        assert log.algorithm_type == "content_based"
        assert log.position == "1"
        
        # 验证点击数据
        recommended_items = json.loads(log.recommended_items)
        assert recommended_items["clicked_user_id"] == 2

    @pytest.mark.asyncio
    async def test_track_follow_conversion(
        self,
        async_session: AsyncSession,
        tracking_service: UserRecommendationTrackingService,
        sample_users: list[models.User],
    ):
        """测试关注转化事件追踪"""
        success = await tracking_service.track_follow_conversion(
            db=async_session,
            user_id=1,
            followed_user_id=3,
            algorithm_type="social_network",
            source="recommendation",
            extra_data={"conversion_time": "immediate"},
        )
        
        assert success is True
        
        # 验证数据库中记录了事件
        from sqlalchemy import select
        stmt = select(models.RecommendationLog).where(
            models.RecommendationLog.user_id == 1,
            models.RecommendationLog.recommendation_reason == "user_recommendation_follow",
        )
        result = await async_session.execute(stmt)
        log = result.scalar_one_or_none()
        
        assert log is not None
        assert log.algorithm_type == "social_network"
        assert log.position == "recommendation"
        
        # 验证关注数据
        recommended_items = json.loads(log.recommended_items)
        assert recommended_items["followed_user_id"] == 3

    @pytest.mark.asyncio
    async def test_get_recommendation_metrics(
        self,
        async_session: AsyncSession,
        tracking_service: UserRecommendationTrackingService,
        sample_users: list[models.User],
        sample_recommendation_logs: None,
    ):
        """测试获取推荐效果指标"""
        metrics = await tracking_service.get_recommendation_metrics(
            db=async_session,
            algorithm_type=None,  # 所有算法
            days=7,
        )
        
        # 验证指标结构
        assert "period" in metrics
        assert "algorithm_type" in metrics
        assert "total_displays" in metrics
        assert "total_clicks" in metrics
        assert "total_follows" in metrics
        assert "click_through_rate" in metrics
        assert "follow_conversion_rate" in metrics
        assert "overall_conversion_rate" in metrics
        assert "algorithm_distribution" in metrics
        assert "user_engagement" in metrics
        assert "generated_at" in metrics
        
        # 验证指标值
        assert metrics["total_displays"] >= 0
        assert metrics["total_clicks"] >= 0
        assert metrics["total_follows"] >= 0
        assert 0 <= metrics["click_through_rate"] <= 100
        assert 0 <= metrics["follow_conversion_rate"] <= 100
        assert 0 <= metrics["overall_conversion_rate"] <= 100
        
        # 验证算法分布
        assert isinstance(metrics["algorithm_distribution"], dict)
        
        # 验证用户参与度
        assert "active_users" in metrics["user_engagement"]
        assert "average_recommendations_per_user" in metrics["user_engagement"]

    @pytest.mark.asyncio
    async def test_get_recommendation_metrics_specific_algorithm(
        self,
        async_session: AsyncSession,
        tracking_service: UserRecommendationTrackingService,
        sample_users: list[models.User],
        sample_recommendation_logs: None,
    ):
        """测试获取特定算法的推荐指标"""
        metrics = await tracking_service.get_recommendation_metrics(
            db=async_session,
            algorithm_type="collaborative",
            days=7,
        )
        
        assert metrics["algorithm_type"] == "collaborative"
        
        # 验证只统计了collaborative算法的数据
        # 根据示例数据，collaborative算法有1次展示、1次点击、1次关注
        assert metrics["total_displays"] >= 1
        assert metrics["total_clicks"] >= 1
        assert metrics["total_follows"] >= 1

    @pytest.mark.asyncio
    async def test_get_algorithm_comparison(
        self,
        async_session: AsyncSession,
        tracking_service: UserRecommendationTrackingService,
        sample_users: list[models.User],
        sample_recommendation_logs: None,
    ):
        """测试算法效果对比"""
        comparison = await tracking_service.get_algorithm_comparison(
            db=async_session,
            days=7,
        )
        
        # 验证对比结构
        assert "period" in comparison
        assert "algorithms" in comparison
        assert "best_ctr_algorithm" in comparison
        assert "best_conversion_algorithm" in comparison
        assert "generated_at" in comparison
        
        # 验证算法数据
        algorithms = comparison["algorithms"]
        expected_algorithms = ["collaborative", "content_based", "social_network", "popular", "hybrid"]
        
        for algorithm in expected_algorithms:
            assert algorithm in algorithms
            algo_data = algorithms[algorithm]
            assert "displays" in algo_data
            assert "clicks" in algo_data
            assert "follows" in algo_data
            assert "ctr" in algo_data
            assert "conversion_rate" in algo_data
            assert "overall_conversion" in algo_data
        
        # 验证最佳算法
        assert "name" in comparison["best_ctr_algorithm"]
        assert "ctr" in comparison["best_ctr_algorithm"]
        assert "name" in comparison["best_conversion_algorithm"]
        assert "conversion_rate" in comparison["best_conversion_algorithm"]

    @pytest.mark.asyncio
    async def test_metrics_caching(
        self,
        async_session: AsyncSession,
        tracking_service: UserRecommendationTrackingService,
        sample_users: list[models.User],
        sample_recommendation_logs: None,
        mock_redis: MockRedis,
    ):
        """测试指标缓存"""
        # 第一次调用，应该计算并缓存结果
        metrics1 = await tracking_service.get_recommendation_metrics(
            db=async_session,
            algorithm_type="collaborative",
            days=7,
        )
        
        # 验证缓存中有数据
        cache_keys = [key for key in mock_redis.data.keys() if "rec_metrics" in key]
        assert len(cache_keys) > 0
        
        # 第二次调用，应该从缓存获取
        metrics2 = await tracking_service.get_recommendation_metrics(
            db=async_session,
            algorithm_type="collaborative",
            days=7,
        )
        
        # 验证两次结果一致（从缓存获取）
        assert metrics1["total_displays"] == metrics2["total_displays"]
        assert metrics1["total_clicks"] == metrics2["total_clicks"]
        assert metrics1["total_follows"] == metrics2["total_follows"]

    @pytest.mark.asyncio
    async def test_metrics_time_range(
        self,
        async_session: AsyncSession,
        tracking_service: UserRecommendationTrackingService,
        sample_users: list[models.User],
    ):
        """测试不同时间范围的指标"""
        # 创建不同时间的日志
        old_log = models.RecommendationLog(
            user_id=1,
            algorithm_type="test",
            recommended_items=json.dumps({"user_ids": [2]}),
            recommendation_reason="user_recommendation_display",
            created_at=datetime.now(timezone.utc) - timedelta(days=10),  # 10天前
        )
        
        recent_log = models.RecommendationLog(
            user_id=1,
            algorithm_type="test",
            recommended_items=json.dumps({"user_ids": [3]}),
            recommendation_reason="user_recommendation_display",
            created_at=datetime.now(timezone.utc) - timedelta(hours=1),  # 1小时前
        )
        
        async_session.add(old_log)
        async_session.add(recent_log)
        await async_session.commit()
        
        # 测试7天范围（应该只包含recent_log）
        metrics_7days = await tracking_service.get_recommendation_metrics(
            db=async_session,
            algorithm_type="test",
            days=7,
        )
        
        # 测试30天范围（应该包含两个日志）
        metrics_30days = await tracking_service.get_recommendation_metrics(
            db=async_session,
            algorithm_type="test",
            days=30,
        )
        
        # 30天的数据应该比7天的多
        assert metrics_30days["total_displays"] >= metrics_7days["total_displays"]

    @pytest.mark.asyncio
    async def test_empty_metrics(
        self,
        async_session: AsyncSession,
        tracking_service: UserRecommendationTrackingService,
    ):
        """测试空数据的指标计算"""
        metrics = await tracking_service.get_recommendation_metrics(
            db=async_session,
            algorithm_type="nonexistent",
            days=7,
        )
        
        # 应该返回零值而不是报错
        assert metrics["total_displays"] == 0
        assert metrics["total_clicks"] == 0
        assert metrics["total_follows"] == 0
        assert metrics["click_through_rate"] == 0.0
        assert metrics["follow_conversion_rate"] == 0.0
        assert metrics["overall_conversion_rate"] == 0.0

    @pytest.mark.asyncio
    async def test_redis_counter_updates(
        self,
        async_session: AsyncSession,
        tracking_service: UserRecommendationTrackingService,
        sample_users: list[models.User],
        mock_redis: MockRedis,
    ):
        """测试Redis计数器更新"""
        # 追踪展示事件
        await tracking_service.track_recommendation_display(
            db=async_session,
            user_id=1,
            recommended_user_ids=[2, 3],
            algorithm_type="test_algo",
        )
        
        # 验证展示计数器
        display_key = "rec_display_count:test_algo"
        display_count = await mock_redis.get(display_key)
        assert display_count == "2"  # 2个推荐用户
        
        # 追踪点击事件
        await tracking_service.track_recommendation_click(
            db=async_session,
            user_id=1,
            clicked_user_id=2,
            algorithm_type="test_algo",
        )
        
        # 验证点击计数器
        click_key = "rec_click_count:test_algo"
        click_count = await mock_redis.get(click_key)
        assert click_count == "1"
        
        # 追踪关注事件
        await tracking_service.track_follow_conversion(
            db=async_session,
            user_id=1,
            followed_user_id=2,
            algorithm_type="test_algo",
        )
        
        # 验证关注计数器
        conversion_key = "rec_conversion_count:test_algo"
        conversion_count = await mock_redis.get(conversion_key)
        assert conversion_count == "1"

    @pytest.mark.asyncio
    async def test_tracking_disabled(
        self,
        async_session: AsyncSession,
        tracking_service: UserRecommendationTrackingService,
        sample_users: list[models.User],
    ):
        """测试禁用追踪功能"""
        # 禁用追踪
        tracking_service.tracking_enabled = False
        
        # 尝试追踪事件
        success = await tracking_service.track_recommendation_display(
            db=async_session,
            user_id=1,
            recommended_user_ids=[2, 3],
            algorithm_type="test",
        )
        
        # 应该返回True但不记录数据
        assert success is True
        
        # 验证数据库中没有记录
        from sqlalchemy import select
        stmt = select(models.RecommendationLog).where(
            models.RecommendationLog.user_id == 1,
            models.RecommendationLog.algorithm_type == "test",
        )
        result = await async_session.execute(stmt)
        log = result.scalar_one_or_none()
        
        assert log is None
        
        # 恢复追踪功能
        tracking_service.tracking_enabled = True
