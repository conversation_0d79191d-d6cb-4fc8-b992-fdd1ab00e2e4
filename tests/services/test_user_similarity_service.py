"""
用户相似度服务测试

测试UserSimilarityService的相似度计算算法
"""

import math
from datetime import datetime, timedelta, timezone
from typing import AsyncGenerator
from unittest.mock import patch

import pytest
import pytest_asyncio
from sqlalchemy import BigInteger
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.ext.compiler import compiles
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker

from app import models
from app.db.session import Base
from app.models import load_all_models
from app.services.user_similarity_service import UserSimilarityService
from app.services.recommendation_cache_service import RecommendationCacheService
from tests.utils.test_helpers import Mock<PERSON>ed<PERSON>


@compiles(JSONB, "sqlite")
def compile_jsonb(element, compiler, **kw):  # type: ignore[override]
    return "JSON"


@compiles(BigInteger, "sqlite")
def compile_bigint(element, compiler, **kw):  # type: ignore[override]
    return "INTEGER"


@pytest_asyncio.fixture
async def async_session() -> AsyncGenerator[AsyncSession, None]:
    """提供带有加载模型的异步内存SQLite会话"""
    load_all_models()
    engine = create_async_engine("sqlite+aiosqlite:///:memory:", future=True)
    
    async with engine.begin() as conn:
        await conn.run_sync(
            Base.metadata.create_all,
            tables=[
                models.User.__table__,
                models.UserProfile.__table__,
                models.UserInteraction.__table__,
                models.UserFollow.__table__,
                models.UserInterest.__table__,
                models.Article.__table__,
                models.Video.__table__,
            ],
        )

    async_session_factory = sessionmaker(
        engine,
        class_=AsyncSession,
        expire_on_commit=False,
    )

    async with async_session_factory() as session:  # type: ignore[call-arg]
        yield session
    await engine.dispose()


@pytest.fixture
def mock_redis() -> MockRedis:
    """Mock Redis客户端"""
    return MockRedis()


@pytest.fixture
def mock_cache_service(mock_redis: MockRedis) -> RecommendationCacheService:
    """Mock缓存服务"""
    with patch('app.services.recommendation_cache_service.redis_master_client', mock_redis):
        return RecommendationCacheService()


@pytest.fixture
def similarity_service(mock_cache_service: RecommendationCacheService) -> UserSimilarityService:
    """用户相似度服务实例"""
    service = UserSimilarityService()
    service.cache_service = mock_cache_service
    return service


@pytest_asyncio.fixture
async def sample_users(async_session: AsyncSession) -> list[models.User]:
    """创建示例用户数据"""
    users = []
    for i in range(1, 6):
        user = models.User(
            id=i,
            username=f"user{i}",
            email=f"user{i}@test.com",
            nickname=f"用户{i}",
            is_active=True,
            created_at=datetime.now(timezone.utc),
            updated_at=datetime.now(timezone.utc),
        )
        async_session.add(user)
        users.append(user)
    
    await async_session.commit()
    return users


@pytest_asyncio.fixture
async def sample_user_interactions(
    async_session: AsyncSession, sample_users: list[models.User]
) -> None:
    """创建示例用户交互数据"""
    # 创建一些文章和视频
    articles = []
    for i in range(1, 4):
        article = models.Article(
            id=i,
            title=f"文章{i}",
            content=f"这是文章{i}的内容",
            author_id=1,
            created_at=datetime.now(timezone.utc),
            updated_at=datetime.now(timezone.utc),
        )
        async_session.add(article)
        articles.append(article)
    
    videos = []
    for i in range(1, 3):
        video = models.Video(
            id=i,
            title=f"视频{i}",
            description=f"这是视频{i}的描述",
            author_id=1,
            created_at=datetime.now(timezone.utc),
            updated_at=datetime.now(timezone.utc),
        )
        async_session.add(video)
        videos.append(video)
    
    # 创建用户交互数据
    interactions = [
        # 用户1和用户2有相似的交互行为
        models.UserInteraction(
            user_id=1,
            content_type="article",
            content_id=1,
            interaction_type="view",
            interaction_weight=1.0,
            created_at=datetime.now(timezone.utc),
        ),
        models.UserInteraction(
            user_id=1,
            content_type="article",
            content_id=1,
            interaction_type="like",
            interaction_weight=2.0,
            created_at=datetime.now(timezone.utc),
        ),
        models.UserInteraction(
            user_id=1,
            content_type="video",
            content_id=1,
            interaction_type="view",
            interaction_weight=1.0,
            created_at=datetime.now(timezone.utc),
        ),
        # 用户2的相似交互
        models.UserInteraction(
            user_id=2,
            content_type="article",
            content_id=1,
            interaction_type="view",
            interaction_weight=1.0,
            created_at=datetime.now(timezone.utc),
        ),
        models.UserInteraction(
            user_id=2,
            content_type="article",
            content_id=1,
            interaction_type="like",
            interaction_weight=2.0,
            created_at=datetime.now(timezone.utc),
        ),
        models.UserInteraction(
            user_id=2,
            content_type="video",
            content_id=1,
            interaction_type="view",
            interaction_weight=1.0,
            created_at=datetime.now(timezone.utc),
        ),
        # 用户3的不同交互
        models.UserInteraction(
            user_id=3,
            content_type="article",
            content_id=2,
            interaction_type="view",
            interaction_weight=1.0,
            created_at=datetime.now(timezone.utc),
        ),
        models.UserInteraction(
            user_id=3,
            content_type="video",
            content_id=2,
            interaction_type="like",
            interaction_weight=2.0,
            created_at=datetime.now(timezone.utc),
        ),
    ]
    
    for interaction in interactions:
        async_session.add(interaction)
    
    await async_session.commit()


@pytest_asyncio.fixture
async def sample_user_interests(
    async_session: AsyncSession, sample_users: list[models.User]
) -> None:
    """创建示例用户兴趣标签"""
    interests = [
        # 用户1和用户2有相似兴趣
        models.UserInterest(user_id=1, interest_tag="游戏", interest_weight=0.8),
        models.UserInterest(user_id=1, interest_tag="科技", interest_weight=0.6),
        models.UserInterest(user_id=1, interest_tag="编程", interest_weight=0.7),
        
        models.UserInterest(user_id=2, interest_tag="游戏", interest_weight=0.9),
        models.UserInterest(user_id=2, interest_tag="科技", interest_weight=0.5),
        models.UserInterest(user_id=2, interest_tag="动漫", interest_weight=0.7),
        
        # 用户3有不同兴趣
        models.UserInterest(user_id=3, interest_tag="音乐", interest_weight=0.8),
        models.UserInterest(user_id=3, interest_tag="艺术", interest_weight=0.6),
        models.UserInterest(user_id=3, interest_tag="摄影", interest_weight=0.7),
        
        # 用户4和用户5
        models.UserInterest(user_id=4, interest_tag="体育", interest_weight=0.8),
        models.UserInterest(user_id=4, interest_tag="健身", interest_weight=0.6),
        
        models.UserInterest(user_id=5, interest_tag="编程", interest_weight=0.9),
        models.UserInterest(user_id=5, interest_tag="科技", interest_weight=0.8),
    ]
    
    for interest in interests:
        async_session.add(interest)
    
    await async_session.commit()


@pytest_asyncio.fixture
async def sample_user_follows(
    async_session: AsyncSession, sample_users: list[models.User]
) -> None:
    """创建示例用户关注关系"""
    follows = [
        # 用户1和用户2有共同关注
        models.UserFollow(follower_id=1, following_id=3, created_at=datetime.now(timezone.utc)),
        models.UserFollow(follower_id=1, following_id=4, created_at=datetime.now(timezone.utc)),
        
        models.UserFollow(follower_id=2, following_id=3, created_at=datetime.now(timezone.utc)),
        models.UserFollow(follower_id=2, following_id=5, created_at=datetime.now(timezone.utc)),
        
        # 其他关注关系
        models.UserFollow(follower_id=3, following_id=4, created_at=datetime.now(timezone.utc)),
        models.UserFollow(follower_id=4, following_id=5, created_at=datetime.now(timezone.utc)),
    ]
    
    for follow in follows:
        async_session.add(follow)
    
    await async_session.commit()


class TestUserSimilarityService:
    """用户相似度服务测试类"""

    @pytest.mark.asyncio
    async def test_calculate_user_similarity_basic(
        self,
        async_session: AsyncSession,
        similarity_service: UserSimilarityService,
        sample_users: list[models.User],
        sample_user_interactions: None,
        sample_user_interests: None,
        sample_user_follows: None,
    ):
        """测试基本用户相似度计算"""
        similarities = await similarity_service.calculate_user_similarity(
            db=async_session,
            user_id1=1,
            user_id2=2,
            similarity_types=["behavior", "interest", "social", "overall"],
        )
        
        # 验证返回的相似度类型
        assert "behavior" in similarities
        assert "interest" in similarities
        assert "social" in similarities
        assert "overall" in similarities
        
        # 验证相似度值范围
        for sim_type, score in similarities.items():
            assert 0 <= score <= 1, f"{sim_type}相似度应该在0-1之间，实际为: {score}"

    @pytest.mark.asyncio
    async def test_behavior_similarity(
        self,
        async_session: AsyncSession,
        similarity_service: UserSimilarityService,
        sample_users: list[models.User],
        sample_user_interactions: None,
    ):
        """测试行为相似度计算"""
        similarities = await similarity_service.calculate_user_similarity(
            db=async_session,
            user_id1=1,
            user_id2=2,
            similarity_types=["behavior"],
        )
        
        behavior_sim = similarities["behavior"]
        
        # 用户1和用户2有相似的交互行为，相似度应该较高
        assert behavior_sim > 0.5, f"用户1和用户2的行为相似度应该较高，实际为: {behavior_sim}"
        
        # 测试与没有交互的用户的相似度
        similarities_no_interaction = await similarity_service.calculate_user_similarity(
            db=async_session,
            user_id1=1,
            user_id2=4,  # 用户4没有交互数据
            similarity_types=["behavior"],
        )
        
        # 与没有交互的用户相似度应该为0
        assert similarities_no_interaction["behavior"] == 0.0

    @pytest.mark.asyncio
    async def test_interest_similarity(
        self,
        async_session: AsyncSession,
        similarity_service: UserSimilarityService,
        sample_users: list[models.User],
        sample_user_interests: None,
    ):
        """测试兴趣相似度计算"""
        similarities = await similarity_service.calculate_user_similarity(
            db=async_session,
            user_id1=1,
            user_id2=2,
            similarity_types=["interest"],
        )
        
        interest_sim = similarities["interest"]
        
        # 用户1和用户2有共同兴趣（游戏、科技），相似度应该较高
        assert interest_sim > 0.3, f"用户1和用户2的兴趣相似度应该较高，实际为: {interest_sim}"
        
        # 测试与兴趣完全不同的用户
        similarities_different = await similarity_service.calculate_user_similarity(
            db=async_session,
            user_id1=1,
            user_id2=3,  # 用户3兴趣完全不同
            similarity_types=["interest"],
        )
        
        # 兴趣不同的用户相似度应该较低
        assert similarities_different["interest"] < 0.3

    @pytest.mark.asyncio
    async def test_social_similarity(
        self,
        async_session: AsyncSession,
        similarity_service: UserSimilarityService,
        sample_users: list[models.User],
        sample_user_follows: None,
    ):
        """测试社交相似度计算"""
        similarities = await similarity_service.calculate_user_similarity(
            db=async_session,
            user_id1=1,
            user_id2=2,
            similarity_types=["social"],
        )
        
        social_sim = similarities["social"]
        
        # 用户1和用户2有共同关注（用户3），相似度应该大于0
        assert social_sim > 0, f"用户1和用户2的社交相似度应该大于0，实际为: {social_sim}"
        
        # 测试没有共同关注的用户
        similarities_no_common = await similarity_service.calculate_user_similarity(
            db=async_session,
            user_id1=1,
            user_id2=5,  # 用户1和用户5没有共同关注
            similarity_types=["social"],
        )
        
        # 没有共同关注的用户社交相似度应该为0
        assert similarities_no_common["social"] == 0.0

    @pytest.mark.asyncio
    async def test_overall_similarity_calculation(
        self,
        async_session: AsyncSession,
        similarity_service: UserSimilarityService,
        sample_users: list[models.User],
        sample_user_interactions: None,
        sample_user_interests: None,
        sample_user_follows: None,
    ):
        """测试综合相似度计算"""
        similarities = await similarity_service.calculate_user_similarity(
            db=async_session,
            user_id1=1,
            user_id2=2,
            similarity_types=["behavior", "interest", "social", "overall"],
        )
        
        # 验证综合相似度是各项相似度的加权平均
        expected_overall = (
            similarities["behavior"] * similarity_service.similarity_weights["behavior"] +
            similarities["interest"] * similarity_service.similarity_weights["interest"] +
            similarities["social"] * similarity_service.similarity_weights["social"]
        )
        
        # 允许小的浮点误差
        assert abs(similarities["overall"] - expected_overall) < 0.001

    @pytest.mark.asyncio
    async def test_similarity_caching(
        self,
        async_session: AsyncSession,
        similarity_service: UserSimilarityService,
        sample_users: list[models.User],
        sample_user_interests: None,
        mock_redis: MockRedis,
    ):
        """测试相似度缓存"""
        # 第一次计算，应该缓存结果
        similarities1 = await similarity_service.calculate_user_similarity(
            db=async_session,
            user_id1=1,
            user_id2=2,
            similarity_types=["interest"],
        )
        
        # 验证缓存中有数据
        cache_keys = [key for key in mock_redis.data.keys() if "user_similarity" in key]
        assert len(cache_keys) > 0
        
        # 第二次计算，应该从缓存获取
        similarities2 = await similarity_service.calculate_user_similarity(
            db=async_session,
            user_id1=1,
            user_id2=2,
            similarity_types=["interest"],
        )
        
        # 验证两次结果一致
        assert similarities1["interest"] == similarities2["interest"]

    @pytest.mark.asyncio
    async def test_get_similar_users(
        self,
        async_session: AsyncSession,
        similarity_service: UserSimilarityService,
        sample_users: list[models.User],
        sample_user_interests: None,
    ):
        """测试获取相似用户列表"""
        similar_users = await similarity_service.get_similar_users(
            db=async_session,
            user_id=1,
            similarity_type="interest",
            limit=3,
            min_similarity=0.1,
        )
        
        # 验证返回格式
        assert isinstance(similar_users, list)
        
        # 验证相似度排序
        if len(similar_users) > 1:
            similarities = [user["similarity"] for user in similar_users]
            assert similarities == sorted(similarities, reverse=True)
        
        # 验证最小相似度阈值
        for user in similar_users:
            assert user["similarity"] >= 0.1

    @pytest.mark.asyncio
    async def test_cosine_similarity_calculation(
        self,
        similarity_service: UserSimilarityService,
    ):
        """测试余弦相似度计算"""
        # 测试相同向量
        vector1 = {"a": 1.0, "b": 2.0, "c": 3.0}
        vector2 = {"a": 1.0, "b": 2.0, "c": 3.0}
        similarity = similarity_service._calculate_cosine_similarity(vector1, vector2)
        assert abs(similarity - 1.0) < 0.001
        
        # 测试正交向量
        vector3 = {"a": 1.0, "b": 0.0}
        vector4 = {"a": 0.0, "b": 1.0}
        similarity = similarity_service._calculate_cosine_similarity(vector3, vector4)
        assert abs(similarity - 0.0) < 0.001
        
        # 测试空向量
        vector5 = {}
        vector6 = {"a": 1.0}
        similarity = similarity_service._calculate_cosine_similarity(vector5, vector6)
        assert similarity == 0.0

    @pytest.mark.asyncio
    async def test_jaccard_similarity_calculation(
        self,
        similarity_service: UserSimilarityService,
    ):
        """测试Jaccard相似度计算"""
        # 测试相同集合
        set1 = {"a", "b", "c"}
        set2 = {"a", "b", "c"}
        similarity = similarity_service._calculate_jaccard_similarity(set1, set2)
        assert abs(similarity - 1.0) < 0.001
        
        # 测试部分重叠
        set3 = {"a", "b", "c"}
        set4 = {"b", "c", "d"}
        similarity = similarity_service._calculate_jaccard_similarity(set3, set4)
        expected = 2 / 4  # 交集2个，并集4个
        assert abs(similarity - expected) < 0.001
        
        # 测试无重叠
        set5 = {"a", "b"}
        set6 = {"c", "d"}
        similarity = similarity_service._calculate_jaccard_similarity(set5, set6)
        assert similarity == 0.0
        
        # 测试空集合
        set7 = set()
        set8 = {"a"}
        similarity = similarity_service._calculate_jaccard_similarity(set7, set8)
        assert similarity == 0.0

    @pytest.mark.asyncio
    async def test_similarity_weights_configuration(
        self,
        async_session: AsyncSession,
        similarity_service: UserSimilarityService,
        sample_users: list[models.User],
        sample_user_interests: None,
    ):
        """测试相似度权重配置"""
        # 修改权重配置
        original_weights = similarity_service.similarity_weights.copy()
        similarity_service.similarity_weights = {
            "behavior": 0.1,
            "interest": 0.8,  # 提高兴趣权重
            "content": 0.05,
            "social": 0.05,
        }
        
        try:
            similarities = await similarity_service.calculate_user_similarity(
                db=async_session,
                user_id1=1,
                user_id2=2,
                similarity_types=["interest", "overall"],
            )
            
            # 验证权重影响综合相似度
            assert "interest" in similarities
            assert "overall" in similarities
            
        finally:
            # 恢复原始权重
            similarity_service.similarity_weights = original_weights

    @pytest.mark.asyncio
    async def test_invalid_user_ids(
        self,
        async_session: AsyncSession,
        similarity_service: UserSimilarityService,
    ):
        """测试无效用户ID"""
        # 测试不存在的用户
        similarities = await similarity_service.calculate_user_similarity(
            db=async_session,
            user_id1=999,  # 不存在的用户
            user_id2=1000,  # 不存在的用户
            similarity_types=["behavior", "interest"],
        )
        
        # 应该返回0相似度而不是报错
        assert similarities["behavior"] == 0.0
        assert similarities["interest"] == 0.0

    @pytest.mark.asyncio
    async def test_same_user_similarity(
        self,
        async_session: AsyncSession,
        similarity_service: UserSimilarityService,
        sample_users: list[models.User],
    ):
        """测试同一用户的相似度"""
        similarities = await similarity_service.calculate_user_similarity(
            db=async_session,
            user_id1=1,
            user_id2=1,  # 同一用户
            similarity_types=["behavior", "interest", "social"],
        )
        
        # 同一用户的相似度应该为1（除了社交相似度，因为用户不会关注自己）
        assert similarities["behavior"] == 1.0
        assert similarities["interest"] == 1.0
        # 社交相似度可能为0，因为用户通常不关注自己
