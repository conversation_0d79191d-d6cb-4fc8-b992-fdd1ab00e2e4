from __future__ import annotations

import asyncio
from datetime import datetime, timezone
from typing import Async<PERSON>enerator
from unittest.mock import AsyncMock

import pytest
import pytest_asyncio
from sqlalchemy import BigInteger
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.ext.compiler import compiles
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker

from app.core.cache import ConfigCache
from app.db.session import Base
from app.models import (
    AdminConfigChangeLog,
    AdminConfigSetting,
    AdminFeatureToggle,
    load_all_models,
)
from app.models.user import User
from app.schemas.admin_config import (
    ConfigMutation,
    ConfigUpdateRequest,
    FeatureToggleUpdateRequest,
)
from app.services.config_service import ConfigService
from tests.utils.test_helpers import MockRedis


@compiles(JSONB, "sqlite")
def compile_jsonb(element, compiler, **kw):  # type: ignore[override]
    return "JSON"


@compiles(BigInteger, "sqlite")
def compile_bigint(element, compiler, **kw):  # type: ignore[override]
    return "INTEGER"


@pytest_asyncio.fixture
async def async_session() -> AsyncGenerator[AsyncSession, None]:
    """Provide an async in-memory SQLite session with loaded models."""
    load_all_models()
    engine = create_async_engine("sqlite+aiosqlite:///:memory:", future=True)
    async with engine.begin() as conn:
        await conn.run_sync(
            Base.metadata.create_all,
            tables=[
                AdminConfigSetting.__table__,
                AdminConfigChangeLog.__table__,
                AdminFeatureToggle.__table__,
                User.__table__,
            ],
        )

    async_session_factory = sessionmaker(
        engine,
        class_=AsyncSession,
        expire_on_commit=False,
    )

    async with async_session_factory() as session:  # type: ignore[call-arg]
        yield session
    await engine.dispose()


@pytest.fixture
def mock_cache() -> MockRedis:
    return MockRedis()


@pytest.fixture
def config_service(mock_cache: MockRedis) -> ConfigService:
    broadcast_stub = AsyncMock()
    return ConfigService(
        cache=ConfigCache(master=mock_cache, reader=mock_cache),
        broadcaster=broadcast_stub,
        audit_logger=AsyncMock(),
    )


def _make_user(user_id: int = 1) -> User:
    return User(
        id=user_id,
        username=f"admin{user_id}",
        email=f"admin{user_id}@example.com",
        is_active=True,
        is_superuser=True,
        created_at=datetime.now(timezone.utc),
        updated_at=datetime.now(timezone.utc),
    )


@pytest.mark.asyncio
async def test_batch_update_configs_success(
    async_session: AsyncSession, config_service: ConfigService, mock_cache: MockRedis
):
    setting = AdminConfigSetting(
        category="redis",
        key="redis.timeout",
        value="300",
        value_type="int",
        description="Redis连接超时时间",
        version=1,
        updated_at=datetime.now(timezone.utc),
        updated_by=None,
        is_sensitive=False,
    )
    async_session.add(setting)
    await async_session.commit()

    request = ConfigUpdateRequest(
        configs=[
            ConfigMutation(
                key="redis.timeout",
                value=600,
                value_type="int",
                reason="Increase timeout for slow network",
                version=1,
            )
        ]
    )

    user = _make_user()
    response = await config_service.batch_update_configs(
        async_session, current_user=user, update_request=request
    )

    assert response.change_id.startswith("chg_")
    assert response.updated_configs[0].new_value == 600
    assert response.updated_configs[0].status == "updated"

    await async_session.refresh(setting)
    assert setting.value == "600"
    assert setting.version == 2

    log_entry = await async_session.get(AdminConfigChangeLog, 1)
    assert log_entry is not None
    assert log_entry.change_reason == "Increase timeout for slow network"

    cached_value = await mock_cache.get("admin:config:redis.timeout")
    assert cached_value is not None


@pytest.mark.asyncio
async def test_batch_update_configs_version_conflict(
    async_session: AsyncSession, config_service: ConfigService
):
    setting = AdminConfigSetting(
        category="core",
        key="core.max_connections",
        value="10",
        value_type="int",
        description="Maximum DB connections",
        version=5,
        updated_at=datetime.now(timezone.utc),
        updated_by=None,
        is_sensitive=False,
    )
    async_session.add(setting)
    await async_session.commit()

    request = ConfigUpdateRequest(
        configs=[
            ConfigMutation(
                key="core.max_connections",
                value=12,
                value_type="int",
                version=3,
            )
        ]
    )

    with pytest.raises(ConfigService.ConfigConflictError):
        await config_service.batch_update_configs(
            async_session, current_user=_make_user(), update_request=request
        )


@pytest.mark.asyncio
async def test_batch_update_configs_sensitive_mask(
    async_session: AsyncSession, config_service: ConfigService
):
    setting = AdminConfigSetting(
        category="security",
        key="security.api_key",
        value="super-secret",
        value_type="secret",
        description="API key",
        version=1,
        updated_at=datetime.now(timezone.utc),
        updated_by=None,
        is_sensitive=True,
    )
    async_session.add(setting)
    await async_session.commit()

    response = await config_service.list_configs(async_session)
    masked = next(item for item in response.configs if item.key == "security.api_key")
    assert masked.value == "***"


@pytest.mark.asyncio
async def test_rollback_change_success(
    async_session: AsyncSession, config_service: ConfigService
):
    setting = AdminConfigSetting(
        category="redis",
        key="redis.pool_size",
        value="20",
        value_type="int",
        description="Redis pool size",
        version=1,
        updated_at=datetime.now(timezone.utc),
        updated_by=None,
        is_sensitive=False,
    )
    async_session.add(setting)
    await async_session.commit()

    update_request = ConfigUpdateRequest(
        configs=[
            ConfigMutation(
                key="redis.pool_size",
                value=30,
                value_type="int",
                version=1,
            )
        ]
    )

    user = _make_user()
    update_response = await config_service.batch_update_configs(
        async_session, current_user=user, update_request=update_request
    )

    rollback_response = await config_service.rollback_change(
        async_session,
        change_id=update_response.change_id,
        current_user=user,
        reason="Rollback due to increased latency",
    )

    assert rollback_response.status == "completed"

    await async_session.refresh(setting)
    assert setting.value == "20"
    assert setting.version == 3  # update + rollback increments


@pytest.mark.asyncio
async def test_update_feature_toggle(
    async_session: AsyncSession, config_service: ConfigService, mock_cache: MockRedis
):
    toggle = AdminFeatureToggle(
        name="user_analytics",
        is_enabled=False,
        description="User analytics feature",
        scope="global",
        toggle_config={},
        updated_at=datetime.now(timezone.utc),
        updated_by=None,
    )
    async_session.add(toggle)
    await async_session.commit()

    request = FeatureToggleUpdateRequest(is_enabled=True, reason="Enable analytics")
    response = await config_service.update_feature_toggle(
        async_session,
        name="user_analytics",
        current_user=_make_user(),
        update_request=request,
    )

    assert response.is_enabled is True

    await async_session.refresh(toggle)
    assert toggle.is_enabled is True

    cached = await mock_cache.get("admin:feature:user_analytics")
    assert cached == "true"
