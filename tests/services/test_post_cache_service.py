"""PostCacheService 行为测试"""

from datetime import UTC, datetime
from unittest.mock import AsyncMock

import pytest

from app import schemas
from app.services.post_cache_service import PostCacheService


class DummyBloomFilter:
    async def exists(self, key: str) -> bool:  # pragma: no cover - 测试中不会命中
        return True

    async def exists_multi(self, keys: list[str]) -> list[bool]:  # pragma: no cover
        return [True] * len(keys)


@pytest.mark.asyncio
async def test_get_posts_by_ids_uses_batch_fetch(monkeypatch):
    """批量获取沸点时应统一走 batch 查询以避免并发数据库操作"""
    service = PostCacheService(bloom_filter=DummyBloomFilter())

    db_session = AsyncMock()
    now = datetime.now(UTC)

    post1 = schemas.PostBase(
        id=1,
        author_id=10,
        content="post-1",
        created_at=now,
        updated_at=now,
    )
    post3 = schemas.PostBase(
        id=3,
        author_id=20,
        content="post-3",
        created_at=now,
        updated_at=now,
    )

    mock_batch = AsyncMock(return_value={1: post1, 2: None, 3: post3})
    monkeypatch.setattr(service, "get_entities_batch", mock_batch)

    result = await service.get_posts_by_ids(db_session, post_ids=[1, 2, 3, 1])

    assert result == [post1, post3]
    assert mock_batch.await_args_list[0].args[0] is db_session
    assert mock_batch.await_args_list[0].args[1] == [1, 2, 3]
