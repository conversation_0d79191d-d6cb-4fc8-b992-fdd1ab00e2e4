from types import SimpleNamespace

import pytest

from app.services.oss_access_service import OSSAccessService, settings


class DummyBucket:
    def __init__(self, base: str = "https://oss-example.com"):
        self.base = base.rstrip("/")
        self.calls: list[tuple[str, str, int]] = []

    def sign_url(self, method: str, object_name: str, expires: int) -> str:
        self.calls.append((method, object_name, expires))
        return f"{self.base}/{object_name}?Expires={expires}&Signature=dummy"


def make_service() -> tuple[OSSAccessService, DummyBucket]:
    bucket = DummyBucket()
    partial_upload = SimpleNamespace(bucket=bucket)
    return OSSAccessService(partial_upload=partial_upload), bucket


@pytest.mark.asyncio
async def test_generate_signed_url_with_cdn(monkeypatch):
    service, bucket = make_service()

    monkeypatch.setattr(settings, "OSS_SIGN_URL_EXPIRE", 600)
    monkeypatch.setattr(settings, "OSS_SIGN_URL_USE_CDN", True)
    monkeypatch.setattr(settings, "OSS_CDN_DOMAIN", "https://cdn.example.com")
    monkeypatch.setattr(settings, "OSS_SIGN_URL_CACHE_ENABLED", False)

    result = await service.generate_signed_url("steam/images/logo.png")

    assert bucket.calls == [("GET", "steam/images/logo.png", 600)]
    assert result.url.startswith("https://cdn.example.com/steam/images/logo.png")
    assert "Signature=dummy" in result.url
    assert result.expires_in == 600
    assert result.use_cdn is True


@pytest.mark.asyncio
async def test_generate_signed_url_without_cdn(monkeypatch):
    service, bucket = make_service()

    monkeypatch.setattr(settings, "OSS_SIGN_URL_EXPIRE", 120)
    monkeypatch.setattr(settings, "OSS_SIGN_URL_USE_CDN", False)
    monkeypatch.setattr(settings, "OSS_CDN_DOMAIN", "")
    monkeypatch.setattr(settings, "OSS_SIGN_URL_CACHE_ENABLED", False)

    result = await service.generate_signed_url("https://oss-example.com/steam/videos/demo.mp4")

    assert bucket.calls == [("GET", "steam/videos/demo.mp4", 120)]
    assert result.url.startswith("https://oss-example.com/steam/videos/demo.mp4")
    assert result.use_cdn is False


@pytest.mark.asyncio
async def test_generate_signed_url_invalid_path(monkeypatch):
    service, _ = make_service()
    monkeypatch.setattr(settings, "OSS_SIGN_URL_CACHE_ENABLED", False)

    with pytest.raises(ValueError):
        await service.generate_signed_url("")
