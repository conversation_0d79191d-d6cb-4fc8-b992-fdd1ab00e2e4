from datetime import UTC, datetime
from fnmatch import fnmatch
from types import SimpleNamespace

import pytest
import pytest_asyncio
from sqlalchemy.exc import ProgrammingError
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker

from app.db.session import Base
from app.models.tag import Tag
from app.models.tag_stats import TagStats
from app.services.tag_hot_service import TagHotService


@pytest_asyncio.fixture
async def async_session() -> AsyncSession:
    engine = create_async_engine("sqlite+aiosqlite:///:memory:")
    async with engine.begin() as conn:
        await conn.run_sync(
            Base.metadata.create_all,
            tables=[Tag.__table__, TagStats.__table__],
        )
    async_session_factory = sessionmaker(engine, class_=AsyncSession, expire_on_commit=False)
    async with async_session_factory() as session:  # type: ignore[arg-type]
        yield session
    await engine.dispose()


@pytest.fixture(autouse=True)
def stub_redis(monkeypatch):
    store: dict[str, dict] = {
        "sorted": {},
        "hash": {},
        "hll": {},
        "string": {},
        "counter": {},
    }

    async def sorted_set_increment_by(key: str, member: str, amount: float) -> float:
        bucket = store["sorted"].setdefault(key, {})
        bucket[member] = bucket.get(member, 0.0) + amount
        return bucket[member]

    async def sorted_set_get_range(key: str, start: int, end: int, with_scores: bool, desc: bool):
        bucket = store["sorted"].get(key, {})
        items = sorted(bucket.items(), key=lambda kv: kv[1], reverse=desc)
        slice_items = items[start : end + 1 if end >= 0 else None]
        if with_scores:
            return [(member, score) for member, score in slice_items]
        return [member for member, _ in slice_items]

    async def sorted_set_get_score(key: str, member: str) -> float | None:
        bucket = store["sorted"].get(key, {})
        return bucket.get(member)

    async def hyperloglog_add(key: str, *values) -> int:
        bucket = store["hll"].setdefault(key, set())
        original = len(bucket)
        bucket.update(values)
        return int(len(bucket) > original)

    async def hyperloglog_count(key: str) -> int:
        bucket = store["hll"].get(key, set())
        return len(bucket)

    async def hash_set(key: str, field: str, value) -> int:
        bucket = store["hash"].setdefault(key, {})
        bucket[field] = value
        return 1

    async def hash_get(key: str, field: str):
        return store["hash"].get(key, {}).get(field)

    async def set_key(key: str, value, expire: int = 0) -> bool:
        store["string"][key] = value
        return True

    async def get_key(key: str):
        return store["string"].get(key)

    async def delete_key(key: str) -> None:
        store["sorted"].pop(key, None)
        store["hash"].pop(key, None)
        store["string"].pop(key, None)

    async def increment_key(key: str, expire: int = 0):  # noqa: ARG001
        bucket = store["counter"]
        bucket[key] = bucket.get(key, 0) + 1
        return bucket[key]

    async def get_redis_master():  # pragma: no cover - simple stub
        async def expire(key: str, seconds: int) -> None:  # noqa: ARG001
            return None

        async def scan_iter(match: str):
            for candidate in list(store["sorted"].keys()):
                if fnmatch(candidate, match):
                    yield candidate

        async def delete(key: str) -> None:
            await delete_key(key)

        return SimpleNamespace(expire=expire, scan_iter=scan_iter, delete=delete)

    patch_targets = {
        "sorted_set_increment_by": sorted_set_increment_by,
        "sorted_set_get_range": sorted_set_get_range,
        "sorted_set_get_score": sorted_set_get_score,
        "hyperloglog_add": hyperloglog_add,
        "hyperloglog_count": hyperloglog_count,
        "hash_set": hash_set,
        "hash_get": hash_get,
        "set_key": set_key,
        "get_key": get_key,
        "delete_key": delete_key,
        "get_redis_master": get_redis_master,
        "increment_key": increment_key,
    }

    for name, func in patch_targets.items():
        monkeypatch.setattr("app.services.tag_hot_service." + name, func)

    return store


@pytest.mark.asyncio
async def test_record_event_and_fetch(async_session: AsyncSession, stub_redis):
    tag = Tag(name="AI", is_default=False, content_type="article")
    async_session.add(tag)
    await async_session.commit()

    service = TagHotService()
    await service.record_bulk_events(tag_ids=[tag.id], content_type="article", user_id=1)

    items = await service.get_hot_tags(async_session, content_type="article", limit=5)

    assert len(items) == 1
    assert items[0].name == "AI"
    assert items[0].unique_users == 1


@pytest.mark.asyncio
async def test_fallback_to_database(async_session: AsyncSession, monkeypatch):
    tag = Tag(name="Cloud", is_default=False, content_type="video")
    async_session.add(tag)
    await async_session.flush()

    stats = TagStats(
        tag_id=tag.id,
        content_type="video",
        total_score=42.0,
        unique_users_est=5,
        window_delta=3.5,
        last_seen_at=datetime.now(UTC),
    )
    async_session.add(stats)
    await async_session.commit()

    # 让 Redis 调用返回空列表
    async def empty_range(*args, **kwargs):  # noqa: ARG001
        return []

    monkeypatch.setattr("app.services.tag_hot_service.sorted_set_get_range", empty_range)

    service = TagHotService()
    items = await service.get_hot_tags(async_session, content_type="video", limit=5)

    assert len(items) == 1
    assert items[0].score == pytest.approx(42.0)
    assert items[0].unique_users == 5


@pytest.mark.asyncio
async def test_fallback_to_snapshot_when_table_missing(async_session: AsyncSession, monkeypatch, stub_redis):
    service = TagHotService()
    snapshot_payload = [
        {
            "tag_id": 777,
            "name": "FallbackTag",
            "content_type": "article",
            "score": 12.5,
            "window_score": 1.3,
            "unique_users": 4,
            "last_event_at": datetime.now(UTC).isoformat(),
        }
    ]
    await service.cache_hot_tags_snapshot(content_type="article", payload=snapshot_payload, expire_seconds=120)

    class DummyOrig(Exception):
        def __init__(self, message: str) -> None:
            super().__init__(message)
            self.args = (message,)

    async def broken_execute(*args, **kwargs):  # noqa: ARG001
        raise ProgrammingError("stmt", {}, DummyOrig('relation "tag_stats" does not exist'))

    async def empty_range(*args, **kwargs):  # noqa: ARG001
        return []

    monkeypatch.setattr(async_session, "execute", broken_execute)
    monkeypatch.setattr("app.services.tag_hot_service.sorted_set_get_range", empty_range)

    items = await service.get_hot_tags(async_session, content_type="article", limit=5)

    assert [item.tag_id for item in items] == [777]
    assert items[0].name == "FallbackTag"
