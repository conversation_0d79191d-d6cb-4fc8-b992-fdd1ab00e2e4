from __future__ import annotations

from datetime import datetime, timedelta, timezone
from typing import Async<PERSON>enerator

import pytest
import pytest_asyncio
from sqlalchemy import BigInteger
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.ext.compiler import compiles
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker

from app.db.session import Base
from app.models import (
    AdminAlertEvent,
    AdminAlertRule,
    AdminMonitoringMetric,
    load_all_models,
)
from app.schemas.admin_monitoring import AlertSeverity
from app.services.monitoring.collector import MetricSample, MonitoringCollector
from app.services.monitoring.alert_engine import MonitoringAlertEngine
from app.services.monitoring.service import MonitoringService


@compiles(JSONB, "sqlite")
def compile_jsonb(element, compiler, **kw):  # type: ignore[override]
    return "JSON"


@compiles(BigInteger, "sqlite")
def compile_bigint(element, compiler, **kw):  # type: ignore[override]
    return "INTEGER"


@pytest_asyncio.fixture
async def async_session() -> AsyncGenerator[AsyncSession, None]:
    load_all_models()
    engine = create_async_engine("sqlite+aiosqlite:///:memory:", future=True)
    async with engine.begin() as conn:
        await conn.run_sync(
            Base.metadata.create_all,
            tables=[
                AdminMonitoringMetric.__table__,
                AdminAlertRule.__table__,
                AdminAlertEvent.__table__,
            ],
        )

    async_session_factory = sessionmaker(
        engine,
        expire_on_commit=False,
        class_=AsyncSession,
    )

    async with async_session_factory() as session:  # type: ignore[call-arg]
        yield session
    await engine.dispose()


@pytest.mark.asyncio
async def test_record_samples_and_dashboard(async_session: AsyncSession):
    collector = MonitoringCollector()
    now = datetime.now(timezone.utc)
    samples = [
        MetricSample(metric_name="cpu", value=55.2, labels={"unit": "percent"}, timestamp=now),
        MetricSample(
            metric_name="memory", value=70.1, labels={"unit": "percent"}, timestamp=now - timedelta(minutes=1)
        ),
        MetricSample(
            metric_name="requests.count",
            value=120,
            labels={"unit": "count"},
            timestamp=now - timedelta(minutes=1),
        ),
    ]

    await collector.record_samples(async_session, samples, source="test")

    service = MonitoringService(collector=collector, alert_engine=MonitoringAlertEngine())
    dashboard = await service.get_dashboard(async_session, time_range="15m")

    assert dashboard["system_metrics"]["cpu"]["current"] == 55.2
    assert dashboard["application_metrics"]["requests"]["total"] == 120


@pytest.mark.asyncio
async def test_metric_series(async_session: AsyncSession):
    collector = MonitoringCollector()
    now = datetime.now(timezone.utc)
    await collector.record_samples(
        async_session,
        [
            MetricSample(metric_name="cpu", value=41.0, timestamp=now - timedelta(minutes=5)),
            MetricSample(metric_name="cpu", value=48.0, timestamp=now),
        ],
        source="test",
    )

    service = MonitoringService(collector=collector, alert_engine=MonitoringAlertEngine())
    series = await service.get_metric_series(
        async_session, metric_name="cpu", start=now - timedelta(minutes=10), end=now
    )

    assert series.metric_name == "cpu"
    assert len(series.points) == 2
    assert series.points[0].value == 41.0


@pytest.mark.asyncio
async def test_alert_engine_triggers_event(async_session: AsyncSession):
    collector = MonitoringCollector()
    engine = MonitoringAlertEngine()
    service = MonitoringService(collector=collector, alert_engine=engine)

    now = datetime.now(timezone.utc)
    await collector.record_samples(
        async_session,
        [
            MetricSample(metric_name="cpu", value=85.0, timestamp=now - timedelta(minutes=2)),
            MetricSample(metric_name="cpu", value=90.0, timestamp=now - timedelta(minutes=1)),
        ],
        source="worker",
    )

    rule = AdminAlertRule(
        name="high_cpu",
        metric_name="cpu",
        operator="greater_than",
        threshold=80,
        duration="5m",
        severity=AlertSeverity.WARNING.value,
        channels=[],
        enabled=True,
        created_at=now,
        updated_at=now,
    )
    async_session.add(rule)
    await async_session.commit()

    await engine.evaluate_rules(async_session)

    events = (
        await async_session.execute(
            AdminAlertEvent.__table__.select().where(AdminAlertEvent.rule_id == rule.id)
        )
    ).all()
    assert len(events) == 1
    assert events[0].notification_status == "pending"
