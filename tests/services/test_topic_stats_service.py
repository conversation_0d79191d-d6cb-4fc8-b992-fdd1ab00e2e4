"""
话题统计服务测试用例

测试TopicStatsService的核心功能，包括：
- 话题使用统计
- 热度分数计算
- 热门话题排行
- 缓存功能
- 错误处理
"""

import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime, timedelta

from app.config.post_config import PostConfig
from app.services.topic_stats_service import TopicStatsService
from app.schemas.topic_stats import TopicListQuery, HotTopicsResponse, TrendingTopicsResponse
from app.models.topic_stats import TopicStats


@pytest.fixture
def config():
    """测试配置"""
    return PostConfig()


@pytest.fixture
def topic_stats_service(config):
    """话题统计服务实例"""
    return TopicStatsService(config=config)


@pytest.fixture
def mock_db():
    """Mock数据库会话"""
    return AsyncMock()


@pytest.fixture
def sample_topic_stats():
    """示例话题统计数据"""
    return [
        TopicStats(
            id=1,
            topic="Python",
            post_count=100,
            total_likes=500,
            total_comments=200,
            total_reposts=50,
            total_views=2000,
            hot_score=85.5,
            trend_score=12.3,
            last_post_at=datetime.utcnow() - timedelta(hours=1),
            created_at=datetime.utcnow() - timedelta(days=30),
            updated_at=datetime.utcnow(),
        ),
        TopicStats(
            id=2,
            topic="JavaScript",
            post_count=80,
            total_likes=400,
            total_comments=150,
            total_reposts=30,
            total_views=1500,
            hot_score=72.1,
            trend_score=8.9,
            last_post_at=datetime.utcnow() - timedelta(hours=3),
            created_at=datetime.utcnow() - timedelta(days=25),
            updated_at=datetime.utcnow(),
        ),
    ]


class TestTopicStatsService:
    """话题统计服务测试"""

    @patch('app.crud.topic_stats.increment_post_count')
    @patch('app.crud.topic_stats.update_hot_score')
    async def test_increment_topic_usage_new_post(
        self, mock_update_score, mock_increment, topic_stats_service, mock_db
    ):
        """测试增加话题使用统计 - 新帖子"""
        mock_stats = MagicMock()
        mock_increment.return_value = mock_stats
        mock_update_score.return_value = mock_stats
        
        await topic_stats_service.increment_topic_usage(mock_db, topic="Python")
        
        mock_increment.assert_called_once_with(mock_db, topic="Python", commit=False)
        mock_update_score.assert_called_once_with(mock_db, topic="Python", commit=True)

    @patch('app.crud.topic_stats.update_interaction_stats')
    @patch('app.crud.topic_stats.update_hot_score')
    async def test_increment_topic_usage_with_interactions(
        self, mock_update_score, mock_update_stats, topic_stats_service, mock_db
    ):
        """测试增加话题使用统计 - 带互动数据"""
        mock_stats = MagicMock()
        mock_update_stats.return_value = mock_stats
        mock_update_score.return_value = mock_stats
        
        await topic_stats_service.increment_topic_usage(
            mock_db,
            topic="Python",
            likes_delta=5,
            comments_delta=2,
            reposts_delta=1,
            views_delta=10,
        )
        
        mock_update_stats.assert_called_once_with(
            mock_db,
            topic="Python",
            likes_delta=5,
            comments_delta=2,
            reposts_delta=1,
            views_delta=10,
            commit=False,
        )
        mock_update_score.assert_called_once_with(mock_db, topic="Python", commit=True)

    @patch('app.crud.topic_stats.decrement_post_count')
    @patch('app.crud.topic_stats.update_hot_score')
    async def test_decrement_topic_usage(
        self, mock_update_score, mock_decrement, topic_stats_service, mock_db
    ):
        """测试减少话题使用统计"""
        mock_stats = MagicMock()
        mock_decrement.return_value = mock_stats
        mock_update_score.return_value = mock_stats
        
        await topic_stats_service.decrement_topic_usage(mock_db, topic="Python")
        
        mock_decrement.assert_called_once_with(mock_db, topic="Python", commit=False)
        mock_update_score.assert_called_once_with(mock_db, topic="Python", commit=True)

    @patch('app.crud.topic_stats.get_hot_topics')
    @patch('app.db.redis.get_key')
    @patch('app.db.redis.set_key')
    async def test_get_hot_topics_from_db(
        self, mock_set_key, mock_get_key, mock_get_hot, topic_stats_service, mock_db, sample_topic_stats
    ):
        """测试从数据库获取热门话题"""
        # Mock缓存未命中
        mock_get_key.return_value = None
        mock_get_hot.return_value = sample_topic_stats
        
        query_params = TopicListQuery(page=1, size=20)
        result = await topic_stats_service.get_hot_topics(mock_db, query_params=query_params)
        
        assert isinstance(result, HotTopicsResponse)
        assert len(result.topics) == 2
        assert result.topics[0].topic == "Python"
        assert result.topics[0].rank == 1
        assert result.total == 2
        
        # 验证缓存被设置
        mock_set_key.assert_called_once()

    @patch('app.crud.topic_stats.get_trending_topics')
    @patch('app.db.redis.get_key')
    async def test_get_trending_topics(
        self, mock_get_key, mock_get_trending, topic_stats_service, mock_db, sample_topic_stats
    ):
        """测试获取趋势话题"""
        mock_get_key.return_value = None
        mock_get_trending.return_value = sample_topic_stats
        
        query_params = TopicListQuery(page=1, size=20)
        result = await topic_stats_service.get_trending_topics(mock_db, query_params=query_params)
        
        assert isinstance(result, TrendingTopicsResponse)
        assert len(result.topics) == 2
        assert result.topics[0].topic == "Python"

    @patch('app.crud.topic_stats.get_by_topic')
    @patch('app.db.redis.get_key')
    @patch('app.db.redis.set_key')
    async def test_get_topic_detail(
        self, mock_set_key, mock_get_key, mock_get_by_topic, topic_stats_service, mock_db, sample_topic_stats
    ):
        """测试获取话题详情"""
        mock_get_key.return_value = None
        mock_get_by_topic.return_value = sample_topic_stats[0]
        
        result = await topic_stats_service.get_topic_detail(mock_db, topic="Python")
        
        assert result is not None
        assert result.topic == "Python"
        assert result.post_count == 100
        assert result.hot_score == 85.5
        
        # 验证缓存被设置
        mock_set_key.assert_called_once()

    @patch('app.crud.topic_stats.get_by_topic')
    async def test_get_topic_detail_not_found(
        self, mock_get_by_topic, topic_stats_service, mock_db
    ):
        """测试获取不存在的话题详情"""
        mock_get_by_topic.return_value = None
        
        result = await topic_stats_service.get_topic_detail(mock_db, topic="NonExistent")
        
        assert result is None

    @patch('app.crud.topic_stats.batch_update_hot_scores')
    @patch('app.crud.topic_stats.get_hot_topics')
    @patch('app.db.redis.sorted_set_add')
    async def test_update_hot_rankings(
        self, mock_sorted_set_add, mock_get_hot, mock_batch_update, topic_stats_service, mock_db, sample_topic_stats
    ):
        """测试更新热门话题排行榜"""
        mock_batch_update.return_value = 2
        mock_get_hot.return_value = sample_topic_stats
        
        result = await topic_stats_service.update_hot_rankings(mock_db)
        
        assert result == 2
        mock_batch_update.assert_called_once_with(mock_db)
        mock_get_hot.assert_called_once_with(mock_db, limit=100)
        mock_sorted_set_add.assert_called_once()

    def test_paginate_topics(self, topic_stats_service):
        """测试话题列表分页"""
        from app.schemas.topic_stats import HotTopic
        
        topics = [
            HotTopic(topic=f"Topic{i}", post_count=i*10, hot_score=i*5.0, trend_score=i*2.0, rank=i+1)
            for i in range(25)
        ]
        
        query_params = TopicListQuery(page=2, size=10)
        result = topic_stats_service._paginate_topics(topics, query_params, HotTopicsResponse)
        
        assert len(result.topics) == 10
        assert result.page == 2
        assert result.size == 10
        assert result.total == 25
        assert result.has_next is True

    @patch('app.db.redis.delete_key')
    async def test_clear_topic_caches(self, mock_delete_key, topic_stats_service):
        """测试清除话题缓存"""
        await topic_stats_service._clear_topic_caches("Python")
        
        # 验证删除了相关缓存键
        assert mock_delete_key.call_count >= 1

    async def test_increment_topic_usage_error_handling(self, topic_stats_service, mock_db):
        """测试话题统计错误处理"""
        with patch('app.crud.topic_stats.increment_post_count', side_effect=Exception("DB Error")):
            with pytest.raises(Exception):
                await topic_stats_service.increment_topic_usage(mock_db, topic="Python")


class TestTopicStatsServiceCaching:
    """话题统计服务缓存测试"""

    @patch('app.db.redis.get_key')
    async def test_get_hot_topics_from_cache(
        self, mock_get_key, topic_stats_service, mock_db
    ):
        """测试从缓存获取热门话题"""
        import json
        
        cached_data = [
            {
                "topic": "Python",
                "post_count": 100,
                "hot_score": 85.5,
                "trend_score": 12.3,
                "rank": 1,
                "rank_change": None,
            }
        ]
        mock_get_key.return_value = json.dumps(cached_data)
        
        query_params = TopicListQuery(page=1, size=20)
        result = await topic_stats_service.get_hot_topics(mock_db, query_params=query_params)
        
        assert isinstance(result, HotTopicsResponse)
        assert len(result.topics) == 1
        assert result.topics[0].topic == "Python"

    @patch('app.db.redis.get_key')
    async def test_cache_data_corruption_fallback(
        self, mock_get_key, topic_stats_service, mock_db
    ):
        """测试缓存数据损坏时的降级处理"""
        # Mock损坏的缓存数据
        mock_get_key.return_value = "invalid json"
        
        with patch('app.crud.topic_stats.get_hot_topics', return_value=[]):
            query_params = TopicListQuery(page=1, size=20)
            result = await topic_stats_service.get_hot_topics(mock_db, query_params=query_params)
            
            # 应该能正常返回结果，即使缓存数据损坏
            assert isinstance(result, HotTopicsResponse)
