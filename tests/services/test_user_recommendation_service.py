"""
用户推荐服务测试

测试UserRecommendationService的各种推荐算法和功能
"""

import json
from datetime import datetime, timedelta, timezone
from typing import AsyncGenerator
from unittest.mock import AsyncMock, MagicMock, patch

import pytest
import pytest_asyncio
from sqlalchemy import BigInteger
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.ext.compiler import compiles
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker

from app import models, schemas
from app.db.session import Base
from app.models import load_all_models
from app.services.user_recommendation_service import UserRecommendationService
from app.services.recommendation_cache_service import RecommendationCacheService
from tests.utils.test_helpers import MockRedis, TestUserFactory


@compiles(JSONB, "sqlite")
def compile_jsonb(element, compiler, **kw):  # type: ignore[override]
    return "JSON"


@compiles(BigInteger, "sqlite")
def compile_bigint(element, compiler, **kw):  # type: ignore[override]
    return "INTEGER"


@pytest_asyncio.fixture
async def async_session() -> AsyncGenerator[AsyncSession, None]:
    """提供带有加载模型的异步内存SQLite会话"""
    load_all_models()
    engine = create_async_engine("sqlite+aiosqlite:///:memory:", future=True)
    
    async with engine.begin() as conn:
        await conn.run_sync(
            Base.metadata.create_all,
            tables=[
                models.User.__table__,
                models.UserProfile.__table__,
                models.UserInteraction.__table__,
                models.UserFollow.__table__,
                models.UserInterest.__table__,
                models.Article.__table__,
                models.Video.__table__,
                models.RecommendationLog.__table__,
            ],
        )

    async_session_factory = sessionmaker(
        engine,
        class_=AsyncSession,
        expire_on_commit=False,
    )

    async with async_session_factory() as session:  # type: ignore[call-arg]
        yield session
    await engine.dispose()


@pytest.fixture
def mock_redis() -> MockRedis:
    """Mock Redis客户端"""
    return MockRedis()


@pytest.fixture
def mock_cache_service(mock_redis: MockRedis) -> RecommendationCacheService:
    """Mock缓存服务"""
    with patch('app.services.recommendation_cache_service.redis_master_client', mock_redis):
        cache_service = RecommendationCacheService()
    cache_service._redis_master_client = mock_redis
    cache_service._redis_slave_client = mock_redis
    return cache_service


@pytest.fixture
def recommendation_service(mock_cache_service: RecommendationCacheService) -> UserRecommendationService:
    """用户推荐服务实例"""
    service = UserRecommendationService()
    service.cache_service = mock_cache_service
    return service


@pytest_asyncio.fixture
async def sample_users(async_session: AsyncSession) -> list[models.User]:
    """创建示例用户数据"""
    users = []
    for i in range(1, 6):
        user = models.User(
            id=i,
            username=f"user{i}",
            email=f"user{i}@test.com",
            nickname=f"用户{i}",
            is_active=True,
            created_at=datetime.now(timezone.utc),
            updated_at=datetime.now(timezone.utc),
        )
        async_session.add(user)
        users.append(user)
    
    await async_session.commit()
    return users


@pytest_asyncio.fixture
async def sample_user_interactions(
    async_session: AsyncSession, sample_users: list[models.User]
) -> None:
    """创建示例用户交互数据"""
    interactions = [
        # 用户1的交互
        models.UserInteraction(
            user_id=1,
            content_type="article",
            content_id=1,
            interaction_type="view",
            interaction_weight=1.0,
            created_at=datetime.now(timezone.utc),
        ),
        models.UserInteraction(
            user_id=1,
            content_type="article",
            content_id=1,
            interaction_type="like",
            interaction_weight=2.0,
            created_at=datetime.now(timezone.utc),
        ),
        # 用户2的交互（与用户1相似）
        models.UserInteraction(
            user_id=2,
            content_type="article",
            content_id=1,
            interaction_type="view",
            interaction_weight=1.0,
            created_at=datetime.now(timezone.utc),
        ),
        models.UserInteraction(
            user_id=2,
            content_type="video",
            content_id=1,
            interaction_type="like",
            interaction_weight=2.0,
            created_at=datetime.now(timezone.utc),
        ),
    ]
    
    for interaction in interactions:
        async_session.add(interaction)
    
    await async_session.commit()


@pytest_asyncio.fixture
async def sample_user_follows(
    async_session: AsyncSession, sample_users: list[models.User]
) -> None:
    """创建示例用户关注关系"""
    follows = [
        models.UserFollow(follower_id=1, following_id=2, created_at=datetime.now(timezone.utc)),
        models.UserFollow(follower_id=1, following_id=3, created_at=datetime.now(timezone.utc)),
        models.UserFollow(follower_id=2, following_id=3, created_at=datetime.now(timezone.utc)),
        models.UserFollow(follower_id=2, following_id=4, created_at=datetime.now(timezone.utc)),
        models.UserFollow(follower_id=3, following_id=4, created_at=datetime.now(timezone.utc)),
        models.UserFollow(follower_id=3, following_id=5, created_at=datetime.now(timezone.utc)),
    ]
    
    for follow in follows:
        async_session.add(follow)
    
    await async_session.commit()


@pytest_asyncio.fixture
async def sample_user_interests(
    async_session: AsyncSession, sample_users: list[models.User]
) -> None:
    """创建示例用户兴趣标签"""
    interests = [
        models.UserInterest(user_id=1, interest_tag="游戏", interest_weight=0.8),
        models.UserInterest(user_id=1, interest_tag="科技", interest_weight=0.6),
        models.UserInterest(user_id=2, interest_tag="游戏", interest_weight=0.9),
        models.UserInterest(user_id=2, interest_tag="动漫", interest_weight=0.7),
        models.UserInterest(user_id=3, interest_tag="科技", interest_weight=0.8),
        models.UserInterest(user_id=3, interest_tag="编程", interest_weight=0.9),
        models.UserInterest(user_id=4, interest_tag="动漫", interest_weight=0.8),
        models.UserInterest(user_id=4, interest_tag="音乐", interest_weight=0.6),
        models.UserInterest(user_id=5, interest_tag="编程", interest_weight=0.9),
        models.UserInterest(user_id=5, interest_tag="科技", interest_weight=0.7),
    ]
    
    for interest in interests:
        async_session.add(interest)
    
    await async_session.commit()


class TestUserRecommendationService:
    """用户推荐服务测试类"""

    @pytest.mark.asyncio
    async def test_get_user_recommendations_basic(
        self,
        async_session: AsyncSession,
        recommendation_service: UserRecommendationService,
        sample_users: list[models.User],
        sample_user_interactions: None,
        sample_user_follows: None,
        sample_user_interests: None,
    ):
        """测试基本用户推荐功能"""
        # 测试混合推荐
        response = await recommendation_service.get_user_recommendations(
            db=async_session,
            user_id=1,
            algorithm_type="hybrid",
            page=1,
            page_size=10,
        )
        
        assert isinstance(response, schemas.PaginatedRecommendationResponse)
        assert response.algorithm_type == "hybrid"
        assert response.page == 1
        assert response.page_size == 10
        assert len(response.items) >= 0  # 可能没有推荐结果，但不应该报错

    @pytest.mark.asyncio
    async def test_collaborative_filtering_algorithm(
        self,
        async_session: AsyncSession,
        recommendation_service: UserRecommendationService,
        sample_users: list[models.User],
        sample_user_interactions: None,
        sample_user_follows: None,
    ):
        """测试协同过滤算法"""
        response = await recommendation_service.get_user_recommendations(
            db=async_session,
            user_id=1,
            algorithm_type="collaborative",
            page=1,
            page_size=5,
        )
        
        assert response.algorithm_type == "collaborative"
        assert isinstance(response.items, list)
        
        # 验证推荐结果格式
        for item in response.items:
            assert isinstance(item, schemas.RecommendationItem)
            assert item.content_type == "user"
            assert isinstance(item.content_id, int)
            assert isinstance(item.score, float)
            assert item.algorithm_type == "collaborative"

    @pytest.mark.asyncio
    async def test_content_based_algorithm(
        self,
        async_session: AsyncSession,
        recommendation_service: UserRecommendationService,
        sample_users: list[models.User],
        sample_user_interests: None,
    ):
        """测试基于内容的推荐算法"""
        response = await recommendation_service.get_user_recommendations(
            db=async_session,
            user_id=1,
            algorithm_type="content_based",
            page=1,
            page_size=5,
        )
        
        assert response.algorithm_type == "content_based"
        assert isinstance(response.items, list)
        
        # 验证推荐结果
        for item in response.items:
            assert item.content_type == "user"
            assert item.algorithm_type == "content_based"
            assert item.score > 0

    @pytest.mark.asyncio
    async def test_social_network_algorithm(
        self,
        async_session: AsyncSession,
        recommendation_service: UserRecommendationService,
        sample_users: list[models.User],
        sample_user_follows: None,
    ):
        """测试社交网络推荐算法"""
        response = await recommendation_service.get_user_recommendations(
            db=async_session,
            user_id=1,
            algorithm_type="social_network",
            page=1,
            page_size=5,
        )
        
        assert response.algorithm_type == "social_network"
        assert isinstance(response.items, list)
        
        # 验证推荐结果
        for item in response.items:
            assert item.content_type == "user"
            assert item.algorithm_type == "social_network"
            # 社交网络推荐不应该推荐已关注的用户
            assert item.content_id not in [2, 3]  # 用户1已关注用户2和3

    @pytest.mark.asyncio
    async def test_popular_user_algorithm(
        self,
        async_session: AsyncSession,
        recommendation_service: UserRecommendationService,
        sample_users: list[models.User],
        sample_user_interactions: None,
    ):
        """测试热门用户推荐算法"""
        response = await recommendation_service.get_user_recommendations(
            db=async_session,
            user_id=1,
            algorithm_type="popular",
            page=1,
            page_size=5,
        )
        
        assert response.algorithm_type == "popular"
        assert isinstance(response.items, list)
        
        # 验证推荐结果
        for item in response.items:
            assert item.content_type == "user"
            assert item.algorithm_type == "popular"
            assert item.score > 0

    @pytest.mark.asyncio
    async def test_recommendation_caching(
        self,
        async_session: AsyncSession,
        recommendation_service: UserRecommendationService,
        sample_users: list[models.User],
        mock_redis: MockRedis,
    ):
        """测试推荐结果缓存"""
        # 第一次调用，应该计算并缓存结果
        response1 = await recommendation_service.get_user_recommendations(
            db=async_session,
            user_id=1,
            algorithm_type="hybrid",
            page=1,
            page_size=5,
        )
        
        # 验证缓存中有数据
        cache_keys = [key for key in mock_redis.data.keys() if "user_rec" in key]
        assert len(cache_keys) > 0
        
        # 第二次调用，应该从缓存获取
        response2 = await recommendation_service.get_user_recommendations(
            db=async_session,
            user_id=1,
            algorithm_type="hybrid",
            page=1,
            page_size=5,
        )
        
        # 验证两次结果一致（从缓存获取）
        assert response1.algorithm_type == response2.algorithm_type
        assert len(response1.items) == len(response2.items)

    @pytest.mark.asyncio
    async def test_pagination(
        self,
        async_session: AsyncSession,
        recommendation_service: UserRecommendationService,
        sample_users: list[models.User],
        sample_user_interactions: None,
        sample_user_follows: None,
        sample_user_interests: None,
    ):
        """测试分页功能"""
        # 测试第一页
        page1 = await recommendation_service.get_user_recommendations(
            db=async_session,
            user_id=1,
            algorithm_type="hybrid",
            page=1,
            page_size=2,
        )
        
        assert page1.page == 1
        assert page1.page_size == 2
        
        # 测试第二页
        page2 = await recommendation_service.get_user_recommendations(
            db=async_session,
            user_id=1,
            algorithm_type="hybrid",
            page=2,
            page_size=2,
        )
        
        assert page2.page == 2
        assert page2.page_size == 2
        
        # 验证分页结果不重复（如果有足够的推荐结果）
        if len(page1.items) > 0 and len(page2.items) > 0:
            page1_ids = {item.content_id for item in page1.items}
            page2_ids = {item.content_id for item in page2.items}
            assert page1_ids.isdisjoint(page2_ids)

    @pytest.mark.asyncio
    async def test_invalid_algorithm_type(
        self,
        async_session: AsyncSession,
        recommendation_service: UserRecommendationService,
        sample_users: list[models.User],
    ):
        """测试无效算法类型"""
        with pytest.raises(ValueError, match="不支持的推荐算法类型"):
            await recommendation_service.get_user_recommendations(
                db=async_session,
                user_id=1,
                algorithm_type="invalid_algorithm",
                page=1,
                page_size=5,
            )

    @pytest.mark.asyncio
    async def test_nonexistent_user(
        self,
        async_session: AsyncSession,
        recommendation_service: UserRecommendationService,
    ):
        """测试不存在的用户"""
        response = await recommendation_service.get_user_recommendations(
            db=async_session,
            user_id=999,  # 不存在的用户ID
            algorithm_type="hybrid",
            page=1,
            page_size=5,
        )
        
        # 应该返回空结果而不是报错
        assert isinstance(response, schemas.PaginatedRecommendationResponse)
        assert len(response.items) == 0

    @pytest.mark.asyncio
    async def test_exclude_already_following(
        self,
        async_session: AsyncSession,
        recommendation_service: UserRecommendationService,
        sample_users: list[models.User],
        sample_user_follows: None,
    ):
        """测试排除已关注用户"""
        response = await recommendation_service.get_user_recommendations(
            db=async_session,
            user_id=1,
            algorithm_type="hybrid",
            page=1,
            page_size=10,
        )

        # 验证推荐结果不包含已关注的用户
        recommended_ids = {item.content_id for item in response.items}
        following_ids = {2, 3}  # 用户1关注的用户

        assert recommended_ids.isdisjoint(following_ids), "推荐结果不应包含已关注的用户"

    @pytest.mark.asyncio
    async def test_exclude_self(
        self,
        async_session: AsyncSession,
        recommendation_service: UserRecommendationService,
        sample_users: list[models.User],
    ):
        """测试排除自己"""
        response = await recommendation_service.get_user_recommendations(
            db=async_session,
            user_id=1,
            algorithm_type="hybrid",
            page=1,
            page_size=10,
        )

        # 验证推荐结果不包含自己
        recommended_ids = {item.content_id for item in response.items}
        assert 1 not in recommended_ids, "推荐结果不应包含用户自己"

    @pytest.mark.asyncio
    async def test_algorithm_weights(
        self,
        async_session: AsyncSession,
        recommendation_service: UserRecommendationService,
        sample_users: list[models.User],
        sample_user_interactions: None,
        sample_user_follows: None,
        sample_user_interests: None,
    ):
        """测试算法权重配置"""
        # 修改算法权重
        original_weights = recommendation_service.algorithm_weights.copy()
        recommendation_service.algorithm_weights = {
            "collaborative": 0.5,
            "content_based": 0.3,
            "social_network": 0.2,
            "popular": 0.0,  # 禁用热门推荐
        }

        try:
            response = await recommendation_service.get_user_recommendations(
                db=async_session,
                user_id=1,
                algorithm_type="hybrid",
                page=1,
                page_size=5,
            )

            assert response.algorithm_type == "hybrid"
            # 验证推荐结果存在
            assert isinstance(response.items, list)

        finally:
            # 恢复原始权重
            recommendation_service.algorithm_weights = original_weights

    @pytest.mark.asyncio
    async def test_recommendation_scoring(
        self,
        async_session: AsyncSession,
        recommendation_service: UserRecommendationService,
        sample_users: list[models.User],
        sample_user_interactions: None,
        sample_user_interests: None,
    ):
        """测试推荐评分机制"""
        response = await recommendation_service.get_user_recommendations(
            db=async_session,
            user_id=1,
            algorithm_type="content_based",
            page=1,
            page_size=5,
        )

        # 验证评分范围和排序
        scores = [item.score for item in response.items]

        # 评分应该在合理范围内
        for score in scores:
            assert 0 <= score <= 1, f"评分应该在0-1之间，实际为: {score}"

        # 评分应该按降序排列
        assert scores == sorted(scores, reverse=True), "推荐结果应该按评分降序排列"

    @pytest.mark.asyncio
    async def test_empty_database(
        self,
        async_session: AsyncSession,
        recommendation_service: UserRecommendationService,
    ):
        """测试空数据库情况"""
        # 创建一个用户但没有其他数据
        user = models.User(
            id=1,
            username="lonely_user",
            email="<EMAIL>",
            nickname="孤独用户",
            is_active=True,
            created_at=datetime.now(timezone.utc),
            updated_at=datetime.now(timezone.utc),
        )
        async_session.add(user)
        await async_session.commit()

        response = await recommendation_service.get_user_recommendations(
            db=async_session,
            user_id=1,
            algorithm_type="hybrid",
            page=1,
            page_size=5,
        )

        # 应该返回空结果而不是报错
        assert isinstance(response, schemas.PaginatedRecommendationResponse)
        assert len(response.items) == 0
        assert response.total_count == 0

    @pytest.mark.asyncio
    async def test_large_page_size(
        self,
        async_session: AsyncSession,
        recommendation_service: UserRecommendationService,
        sample_users: list[models.User],
    ):
        """测试大页面大小"""
        response = await recommendation_service.get_user_recommendations(
            db=async_session,
            user_id=1,
            algorithm_type="hybrid",
            page=1,
            page_size=100,  # 大页面大小
        )

        assert response.page_size == 100
        # 实际返回的数量不应超过可用用户数量
        assert len(response.items) <= len(sample_users) - 1  # 排除自己

    @pytest.mark.asyncio
    async def test_recommendation_reasons(
        self,
        async_session: AsyncSession,
        recommendation_service: UserRecommendationService,
        sample_users: list[models.User],
        sample_user_interests: None,
    ):
        """测试推荐理由"""
        response = await recommendation_service.get_user_recommendations(
            db=async_session,
            user_id=1,
            algorithm_type="content_based",
            page=1,
            page_size=5,
        )

        # 验证推荐理由存在且有意义
        for item in response.items:
            assert item.reason is not None
            assert len(item.reason) > 0
            assert isinstance(item.reason, str)

    @pytest.mark.asyncio
    async def test_concurrent_requests(
        self,
        async_session: AsyncSession,
        recommendation_service: UserRecommendationService,
        sample_users: list[models.User],
        sample_user_interactions: None,
    ):
        """测试并发请求"""
        import asyncio

        # 创建多个并发请求
        tasks = []
        for i in range(5):
            task = recommendation_service.get_user_recommendations(
                db=async_session,
                user_id=1,
                algorithm_type="hybrid",
                page=1,
                page_size=3,
            )
            tasks.append(task)

        # 等待所有请求完成
        responses = await asyncio.gather(*tasks)

        # 验证所有响应都成功
        for response in responses:
            assert isinstance(response, schemas.PaginatedRecommendationResponse)
            assert response.algorithm_type == "hybrid"

    @pytest.mark.asyncio
    async def test_cache_invalidation(
        self,
        async_session: AsyncSession,
        recommendation_service: UserRecommendationService,
        sample_users: list[models.User],
        mock_redis: MockRedis,
    ):
        """测试缓存失效"""
        # 第一次请求，建立缓存
        response1 = await recommendation_service.get_user_recommendations(
            db=async_session,
            user_id=1,
            algorithm_type="hybrid",
            page=1,
            page_size=5,
        )

        # 验证缓存存在
        cache_keys = [key for key in mock_redis.data.keys() if "user_rec" in key]
        assert len(cache_keys) > 0

        # 手动清除缓存
        for key in cache_keys:
            await mock_redis.delete(key)

        # 第二次请求，应该重新计算
        response2 = await recommendation_service.get_user_recommendations(
            db=async_session,
            user_id=1,
            algorithm_type="hybrid",
            page=1,
            page_size=5,
        )

        # 验证请求成功
        assert isinstance(response2, schemas.PaginatedRecommendationResponse)
        assert response2.algorithm_type == "hybrid"
