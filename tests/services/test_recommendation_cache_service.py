"""
推荐缓存服务测试

测试RecommendationCacheService的用户推荐缓存功能
"""

import pytest

from app import schemas
from app.db.redis import redis_master_client
from app.services.recommendation_cache_service import RecommendationCacheService


@pytest.fixture
def cache_service() -> RecommendationCacheService:
    """推荐缓存服务实例"""
    service = RecommendationCacheService()
    # 强制启用缓存用于测试
    service.cache_enabled = True
    return service


@pytest.fixture
def test_key_prefix() -> str:
    """测试键前缀，避免与生产数据冲突"""
    return "test_"


class TestRecommendationCacheService:
    """推荐缓存服务测试类"""

    @pytest.mark.asyncio
    async def test_set_and_get_user_recommendations_cache(
        self,
        cache_service: RecommendationCacheService,
        test_key_prefix: str,
    ):
        """测试设置和获取用户推荐缓存"""
        from app.db.redis import redis_master_client

        user_id = 1
        algorithm_type = "hybrid"

        # 临时修改缓存服务的键前缀用于测试
        original_get_key = cache_service._get_user_recommendation_key
        cache_service._get_user_recommendation_key = lambda uid, alg: f"{test_key_prefix}user_recs:{uid}:{alg}"

        try:
            # 创建用户推荐项（模拟用户推荐的RecommendationItem）
            user_recommendations = [
                schemas.RecommendationItem(
                    content_type="article",  # 使用article类型来模拟用户推荐
                    content_id=2,
                    score=0.95,
                    reason="相似用户",
                ),
                schemas.RecommendationItem(
                    content_type="video",
                    content_id=3,
                    score=0.87,
                    reason="共同兴趣",
                ),
            ]

            # 设置缓存
            await cache_service.set_user_recommendations_cache(
                user_id, algorithm_type, user_recommendations
            )

            # 验证缓存键存在
            cache_key = f"{test_key_prefix}user_recs:{user_id}:{algorithm_type}"
            assert await redis_master_client.exists(cache_key)

            # 获取缓存
            cached_items = await cache_service.get_user_recommendations_cache(
                user_id, algorithm_type, page=1, page_size=10
            )

            # 验证缓存内容
            assert cached_items is not None
            assert len(cached_items) == len(user_recommendations)

            # 验证数据格式（缓存中存储为user:content_id格式）
            for (item_data, score), original_item in zip(cached_items, user_recommendations):
                assert f"user:{original_item.content_id}" == item_data
                assert float(score) == original_item.score

        finally:
            # 恢复原始方法
            cache_service._get_user_recommendation_key = original_get_key
            # 清理测试数据
            await redis_master_client.delete(f"{test_key_prefix}user_recs:{user_id}:{algorithm_type}")

    @pytest.mark.asyncio
    async def test_user_similarity_cache(
        self,
        cache_service: RecommendationCacheService,
        test_key_prefix: str,
    ):
        """测试用户相似度缓存"""
        from app.db.redis import redis_master_client

        user_id1 = 1
        user_id2 = 2
        similarities = {
            "behavior": 0.85,
            "interest": 0.72,
            "social": 0.45,
            "overall": 0.67,
        }

        # 临时修改缓存服务的键方法用于测试
        original_get_similarity_key = cache_service._get_user_similarity_key
        cache_service._get_user_similarity_key = lambda uid1, uid2: f"{test_key_prefix}user_similarity:{min(uid1, uid2)}:{max(uid1, uid2)}"

        try:
            # 设置缓存
            await cache_service.set_user_similarity_cache(user_id1, user_id2, similarities)

            # 验证缓存键存在
            cache_key = f"{test_key_prefix}user_similarity:{min(user_id1, user_id2)}:{max(user_id1, user_id2)}"
            assert await redis_master_client.exists(cache_key)

            # 获取缓存
            cached_similarities = await cache_service.get_user_similarity_cache(user_id1, user_id2)

            # 验证缓存内容
            assert cached_similarities is not None
            assert cached_similarities == similarities

            # 测试顺序无关性
            cached_similarities_reverse = await cache_service.get_user_similarity_cache(user_id2, user_id1)
            assert cached_similarities_reverse == similarities

        finally:
            # 恢复原始方法
            cache_service._get_user_similarity_key = original_get_similarity_key
            # 清理测试数据
            await redis_master_client.delete(f"{test_key_prefix}user_similarity:{min(user_id1, user_id2)}:{max(user_id1, user_id2)}")

    @pytest.mark.asyncio
    async def test_cache_pagination(
        self,
        cache_service: RecommendationCacheService,
        test_key_prefix: str,
    ):
        """测试缓存分页功能"""
        from app.db.redis import redis_master_client

        user_id = 1
        algorithm_type = "test_pagination"

        # 临时修改缓存服务的键前缀用于测试
        original_get_key = cache_service._get_user_recommendation_key
        cache_service._get_user_recommendation_key = lambda uid, alg: f"{test_key_prefix}user_recs:{uid}:{alg}"

        try:
            # 创建多个推荐项
            recommendations = []
            content_types = ["article", "video", "scratch"]
            for i in range(10):
                item = schemas.RecommendationItem(
                    content_type=content_types[i % 3],
                    content_id=i + 1,
                    score=0.9 - (i * 0.05),
                    reason=f"推荐理由{i}",
                )
                recommendations.append(item)

            # 设置缓存
            await cache_service.set_user_recommendations_cache(
                user_id, algorithm_type, recommendations
            )

            # 测试第一页
            page1_items = await cache_service.get_user_recommendations_cache(
                user_id, algorithm_type, page=1, page_size=3
            )
            assert len(page1_items) == 3

            # 测试第二页
            page2_items = await cache_service.get_user_recommendations_cache(
                user_id, algorithm_type, page=2, page_size=3
            )
            assert len(page2_items) == 3

            # 验证分页结果不重复
            page1_ids = {item[0] for item in page1_items}
            page2_ids = {item[0] for item in page2_items}
            assert page1_ids.isdisjoint(page2_ids)

        finally:
            # 恢复原始方法
            cache_service._get_user_recommendation_key = original_get_key
            # 清理测试数据
            await redis_master_client.delete(f"{test_key_prefix}user_recs:{user_id}:{algorithm_type}")

    @pytest.mark.asyncio
    async def test_cache_invalidation(
        self,
        cache_service: RecommendationCacheService,
        test_key_prefix: str,
    ):
        """测试缓存失效功能"""
        from app.db.redis import redis_master_client

        user_id = 1
        algorithm_type = "test_invalidation"

        # 临时修改缓存服务的键前缀用于测试
        original_get_key = cache_service._get_user_recommendation_key
        cache_service._get_user_recommendation_key = lambda uid, alg: f"{test_key_prefix}user_recs:{uid}:{alg}"

        try:
            # 创建推荐项
            recommendations = [
                schemas.RecommendationItem(
                    content_type="article",
                    content_id=1,
                    score=0.9,
                    reason="测试推荐",
                )
            ]

            # 设置缓存
            await cache_service.set_user_recommendations_cache(
                user_id, algorithm_type, recommendations
            )

            # 验证缓存存在
            cache_key = f"{test_key_prefix}user_recs:{user_id}:{algorithm_type}"
            assert await redis_master_client.exists(cache_key)

            # 手动删除测试缓存（因为失效方法使用的是原始键格式）
            await redis_master_client.delete(cache_key)

            # 验证缓存已删除
            assert not await redis_master_client.exists(cache_key)

        finally:
            # 恢复原始方法
            cache_service._get_user_recommendation_key = original_get_key
