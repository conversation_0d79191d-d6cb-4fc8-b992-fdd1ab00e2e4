import pytest

from app.schemas import scratch


def test_normalize_highlights_basic():
    raw = ["  第一条亮点  ", "", None, "第二条亮点", "a" * 300]
    result = scratch.normalize_highlights(raw)
    assert result[0] == "第一条亮点"
    assert result[1] == "第二条亮点"
    assert len(result[2]) == 200


@pytest.mark.parametrize(
    "value, expected",
    [
        (None, []),
        ("单条亮点", ["单条亮点"]),
        (["一", "二"], ["一", "二"]),
    ],
)
def test_scratch_product_update_highlights(value, expected):
    payload = scratch.ScratchProductUpdate(highlights=value)
    assert payload.highlights == expected


def test_scratch_product_out_populates_highlights():
    data = {
        "project_id": 1,
        "title": "Test",
        "author_id": 10,
        "is_published": True,
        "can_adapt": True,
        "adapt_level": 0,
        "adaptation_type": scratch.AdaptationType.ORIGINAL,
        "created_at": None,
        "cache_version": 1,
        "highlights": ["  keep me  ", None, ""],
    }
    dto = scratch.ScratchProductOut(**data)
    assert dto.highlights == ["keep me"]
