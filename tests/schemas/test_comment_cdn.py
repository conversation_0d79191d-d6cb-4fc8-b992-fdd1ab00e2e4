from datetime import UTC, datetime

import pytest

from app.models.comment import CommentType
from app.schemas.comment import Comment, FlatComment
from app.utils import url


@pytest.fixture(autouse=True)
def _set_cdn_domain(monkeypatch):
    monkeypatch.setattr(url._SETTINGS, "OSS_CDN_DOMAIN", "https://cdn.example.com")


def test_comment_serialization_adds_cdn_prefix():
    comment = Comment(
        id=1,
        author_id=42,
        content="hello",
        comment_type=CommentType.ARTICLE,
        article_id=100,
        video_id=None,
        scratch_id=None,
        post_id=None,
        parent_id=None,
        is_visible=True,
        image_urls=["/steam/images/foo.webp"],
        reply_to_id=None,
        created_at=datetime.now(UTC),
        updated_at=datetime.now(UTC),
        author=None,
        like_count=0,
        is_liked=False,
    )

    dumped = comment.model_dump()

    assert dumped["image_urls"] == ["https://cdn.example.com/steam/images/foo.webp"]


def test_flat_comment_serialization_adds_cdn_prefix():
    flat_comment = FlatComment(
        id=1,
        content="flat",
        comment_type=CommentType.ARTICLE,
        article_id=100,
        video_id=None,
        scratch_id=None,
        post_id=None,
        image_urls=["steam/images/bar.png"],
        level=0,
        parent_id=None,
        reply_to_id=None,
        reply_to_user_id=None,
        reply_to_user=None,
        path="1",
        author_id=42,
        author=None,
        like_count=0,
        is_liked=False,
        reply_count=0,
        total_reply_count=0,
        is_visible=True,
        created_at=datetime.now(UTC),
        updated_at=datetime.now(UTC),
    )

    dumped = flat_comment.model_dump()

    assert dumped["image_urls"] == ["https://cdn.example.com/steam/images/bar.png"]
