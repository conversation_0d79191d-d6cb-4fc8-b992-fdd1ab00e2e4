import pytest
import pytest_asyncio
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.types import JSON
from unittest.mock import AsyncMock

from app import crud
from app.db.session import Base
from app.models.article import Article
from app.models.favorite import Favorite
from app.models.like import Like
from app.models.outbox import OutboxMessage
from app.models.user import User
from app.models.user_stats import UserStats
from app.models.video import Video
from app.models.video_folder import VideoFolder


@pytest.fixture(autouse=True)
def _mock_redis_connections(monkeypatch):
    """避免测试过程中初始化真实的 Redis 连接。"""
    mock_init = AsyncMock()
    mock_close = AsyncMock()
    monkeypatch.setattr("tests.conftest.init_redis", mock_init)
    monkeypatch.setattr("tests.conftest.close_redis", mock_close)
    return mock_init, mock_close


@pytest_asyncio.fixture
async def async_session() -> AsyncSession:
    """
    提供隔离的内存数据库会话，仅创建当前测试所需的最小表集合。
    """
    payload_column = OutboxMessage.__table__.c.payload
    original_type = payload_column.type
    payload_column.type = JSON()

    engine = create_async_engine("sqlite+aiosqlite:///:memory:")
    try:
        async with engine.begin() as conn:
            await conn.run_sync(
                Base.metadata.create_all,
                tables=[
                    User.__table__,
                    Article.__table__,
                    VideoFolder.__table__,
                    Video.__table__,
                    Like.__table__,
                    Favorite.__table__,
                    OutboxMessage.__table__,
                    UserStats.__table__,
                ],
            )
        async_session_factory = sessionmaker(engine, class_=AsyncSession, expire_on_commit=False)
        async with async_session_factory() as session:  # type: ignore[arg-type]
            yield session
    finally:
        await engine.dispose()
        payload_column.type = original_type


@pytest.mark.asyncio
async def test_toggle_like_creates_user_stats_event(async_session: AsyncSession):
    author = User(username="author-liked")
    liker = User(username="liker")
    async_session.add_all([author, liker])
    await async_session.commit()
    await async_session.refresh(author)
    await async_session.refresh(liker)

    article = Article(title="Test Article", content="content", author_id=author.id)
    async_session.add(article)
    await async_session.commit()
    await async_session.refresh(article)

    # 第一次点赞
    await crud.like.toggle_like(
        async_session,
        user_id=liker.id,
        content_type="article",
        content_id=article.id,
        commit=False,
    )
    await async_session.commit()

    stats_messages = (
        await async_session.execute(
            select(OutboxMessage)
            .where(OutboxMessage.topic == "stats.update")
            .order_by(OutboxMessage.id)
        )
    ).scalars().all()
    assert len(stats_messages) == 1
    assert stats_messages[0].payload == {
        "entity_type": "user",
        "entity_id": author.id,
        "field_name": "total_likes_count",
        "value_change": 1,
    }

    # 再次调用切换为取消点赞
    await crud.like.toggle_like(
        async_session,
        user_id=liker.id,
        content_type="article",
        content_id=article.id,
        commit=False,
    )
    await async_session.commit()

    stats_messages = (
        await async_session.execute(
            select(OutboxMessage)
            .where(OutboxMessage.topic == "stats.update")
            .order_by(OutboxMessage.id)
        )
    ).scalars().all()
    payload_changes = [message.payload for message in stats_messages]
    assert payload_changes == [
        {
            "entity_type": "user",
            "entity_id": author.id,
            "field_name": "total_likes_count",
            "value_change": 1,
        },
        {
            "entity_type": "user",
            "entity_id": author.id,
            "field_name": "total_likes_count",
            "value_change": -1,
        },
    ]


@pytest.mark.asyncio
async def test_toggle_favorite_creates_user_stats_event(async_session: AsyncSession):
    author = User(username="author-favorited")
    user = User(username="collector")
    async_session.add_all([author, user])
    await async_session.commit()
    await async_session.refresh(author)
    await async_session.refresh(user)

    article = Article(title="Favorite Article", content="content", author_id=author.id)
    async_session.add(article)
    await async_session.commit()
    await async_session.refresh(article)

    await crud.favorite.toggle_favorite(
        async_session,
        user_id=user.id,
        content_type="article",
        content_id=article.id,
        note=None,
        commit=False,
    )
    await async_session.commit()

    stats_messages = (
        await async_session.execute(
            select(OutboxMessage)
            .where(OutboxMessage.topic == "stats.update")
            .order_by(OutboxMessage.id)
        )
    ).scalars().all()
    assert len(stats_messages) == 1
    assert stats_messages[0].payload == {
        "entity_type": "user",
        "entity_id": author.id,
        "field_name": "total_favorites_count",
        "value_change": 1,
    }


@pytest.mark.asyncio
async def test_toggle_like_for_video_creates_user_stats_event(async_session: AsyncSession):
    author = User(username="video-author")
    liker = User(username="video-liker")
    async_session.add_all([author, liker])
    await async_session.commit()
    await async_session.refresh(author)
    await async_session.refresh(liker)

    folder = VideoFolder(name="默认文件夹", user_id=author.id, path="/video/default", is_default=True)
    async_session.add(folder)
    await async_session.commit()
    await async_session.refresh(folder)

    video = Video(
        title="Test Video",
        url="https://cdn.example.com/video.mp4",
        author_id=author.id,
        folder_id=folder.id,
        is_published=True,
    )
    async_session.add(video)
    await async_session.commit()
    await async_session.refresh(video)

    await crud.like.toggle_like(
        async_session,
        user_id=liker.id,
        content_type="video",
        content_id=video.id,
        commit=False,
    )
    await async_session.commit()

    stats_messages = (
        await async_session.execute(
            select(OutboxMessage)
            .where(OutboxMessage.topic == "stats.update")
            .order_by(OutboxMessage.id)
        )
    ).scalars().all()
    assert len(stats_messages) == 1
    assert stats_messages[0].payload == {
        "entity_type": "user",
        "entity_id": author.id,
        "field_name": "total_likes_count",
        "value_change": 1,
    }
