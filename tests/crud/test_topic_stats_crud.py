"""
话题统计CRUD操作测试用例

测试话题统计的数据库操作，包括：
- 创建和更新话题统计
- 热度分数计算
- 统计数据增减
- 查询操作
- 批量操作
"""

import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime, timedelta

from app.crud import topic_stats as crud_topic_stats
from app.models.topic_stats import TopicStats
from app.config.post_config import PostConfig


@pytest.fixture
def mock_db():
    """Mock数据库会话"""
    db = AsyncMock()
    db.commit = AsyncMock()
    db.refresh = AsyncMock()
    return db


@pytest.fixture
def config():
    """测试配置"""
    return PostConfig()


@pytest.fixture
def sample_topic_stats():
    """示例话题统计数据"""
    return TopicStats(
        id=1,
        topic="Python",
        post_count=50,
        total_likes=200,
        total_comments=100,
        total_reposts=25,
        total_views=1000,
        hot_score=45.5,
        trend_score=8.2,
        last_post_at=datetime.utcnow() - timedelta(hours=2),
        created_at=datetime.utcnow() - timedelta(days=10),
        updated_at=datetime.utcnow(),
    )


class TestTopicStatsCRUD:
    """话题统计CRUD测试"""

    async def test_get_by_topic_exists(self, mock_db, sample_topic_stats):
        """测试根据话题名获取统计数据 - 存在"""
        mock_db.execute.return_value.scalar_one_or_none.return_value = sample_topic_stats
        
        result = await crud_topic_stats.get_by_topic(mock_db, topic="Python")
        
        assert result == sample_topic_stats
        assert result.topic == "Python"
        mock_db.execute.assert_called_once()

    async def test_get_by_topic_not_exists(self, mock_db):
        """测试根据话题名获取统计数据 - 不存在"""
        mock_db.execute.return_value.scalar_one_or_none.return_value = None
        
        result = await crud_topic_stats.get_by_topic(mock_db, topic="NonExistent")
        
        assert result is None

    async def test_create_or_get_new_topic(self, mock_db):
        """测试创建新话题统计"""
        mock_db.execute.return_value.scalar_one_or_none.return_value = None
        
        with patch('app.crud.topic_stats.TopicStats') as mock_model:
            mock_instance = MagicMock()
            mock_model.return_value = mock_instance
            
            result = await crud_topic_stats.create_or_get(mock_db, topic="NewTopic")
            
            assert result == mock_instance
            mock_db.add.assert_called_once_with(mock_instance)

    async def test_create_or_get_existing_topic(self, mock_db, sample_topic_stats):
        """测试获取已存在的话题统计"""
        mock_db.execute.return_value.scalar_one_or_none.return_value = sample_topic_stats
        
        result = await crud_topic_stats.create_or_get(mock_db, topic="Python")
        
        assert result == sample_topic_stats
        mock_db.add.assert_not_called()

    async def test_increment_post_count(self, mock_db, sample_topic_stats):
        """测试增加帖子数量"""
        mock_db.execute.return_value.scalar_one_or_none.return_value = sample_topic_stats
        original_count = sample_topic_stats.post_count
        
        result = await crud_topic_stats.increment_post_count(mock_db, topic="Python")
        
        assert result.post_count == original_count + 1
        assert result.last_post_at is not None
        mock_db.commit.assert_called_once()

    async def test_decrement_post_count(self, mock_db, sample_topic_stats):
        """测试减少帖子数量"""
        mock_db.execute.return_value.scalar_one_or_none.return_value = sample_topic_stats
        original_count = sample_topic_stats.post_count
        
        result = await crud_topic_stats.decrement_post_count(mock_db, topic="Python")
        
        assert result.post_count == max(0, original_count - 1)
        mock_db.commit.assert_called_once()

    async def test_decrement_post_count_minimum_zero(self, mock_db):
        """测试帖子数量不能小于0"""
        stats = TopicStats(
            topic="Python",
            post_count=0,
            total_likes=0,
            total_comments=0,
            total_reposts=0,
            total_views=0,
            hot_score=0.0,
            trend_score=0.0,
        )
        mock_db.execute.return_value.scalar_one_or_none.return_value = stats
        
        result = await crud_topic_stats.decrement_post_count(mock_db, topic="Python")
        
        assert result.post_count == 0

    async def test_update_interaction_stats(self, mock_db, sample_topic_stats):
        """测试更新互动统计"""
        mock_db.execute.return_value.scalar_one_or_none.return_value = sample_topic_stats
        
        result = await crud_topic_stats.update_interaction_stats(
            mock_db,
            topic="Python",
            likes_delta=10,
            comments_delta=5,
            reposts_delta=2,
            views_delta=50,
        )
        
        assert result.total_likes == 210  # 200 + 10
        assert result.total_comments == 105  # 100 + 5
        assert result.total_reposts == 27  # 25 + 2
        assert result.total_views == 1050  # 1000 + 50
        mock_db.commit.assert_called_once()

    async def test_update_interaction_stats_negative_deltas(self, mock_db, sample_topic_stats):
        """测试负数增量（减少互动数）"""
        mock_db.execute.return_value.scalar_one_or_none.return_value = sample_topic_stats
        
        result = await crud_topic_stats.update_interaction_stats(
            mock_db,
            topic="Python",
            likes_delta=-5,
            comments_delta=-2,
            reposts_delta=-1,
            views_delta=-10,
        )
        
        assert result.total_likes == max(0, 195)  # 200 - 5
        assert result.total_comments == max(0, 98)  # 100 - 2
        assert result.total_reposts == max(0, 24)  # 25 - 1
        assert result.total_views == max(0, 990)  # 1000 - 10

    async def test_calculate_hot_score(self, config):
        """测试热度分数计算"""
        stats = TopicStats(
            topic="Python",
            post_count=100,
            total_likes=500,
            total_comments=200,
            total_reposts=50,
            total_views=2000,
            last_post_at=datetime.utcnow() - timedelta(hours=1),
        )
        
        score = crud_topic_stats.calculate_hot_score(stats, config)
        
        assert isinstance(score, float)
        assert score > 0
        
        # 验证分数计算逻辑
        expected_base_score = (
            100 * config.POST_WEIGHT +
            500 * config.LIKE_WEIGHT +
            200 * config.COMMENT_WEIGHT +
            50 * config.REPOST_WEIGHT +
            2000 * config.VIEW_WEIGHT
        )
        
        # 时间衰减因子应该接近1（因为是1小时前）
        time_factor = max(0.1, 1 - (1 / 24))  # 1小时 / 24小时
        expected_score = expected_base_score * time_factor
        
        assert abs(score - expected_score) < 0.1

    async def test_update_hot_score(self, mock_db, sample_topic_stats, config):
        """测试更新热度分数"""
        mock_db.execute.return_value.scalar_one_or_none.return_value = sample_topic_stats
        
        with patch('app.crud.topic_stats.calculate_hot_score', return_value=75.5) as mock_calc:
            result = await crud_topic_stats.update_hot_score(mock_db, topic="Python", config=config)
            
            assert result.hot_score == 75.5
            mock_calc.assert_called_once_with(sample_topic_stats, config)
            mock_db.commit.assert_called_once()

    async def test_get_hot_topics(self, mock_db, sample_topic_stats):
        """测试获取热门话题列表"""
        mock_result = MagicMock()
        mock_result.scalars.return_value.all.return_value = [sample_topic_stats]
        mock_db.execute.return_value = mock_result
        
        result = await crud_topic_stats.get_hot_topics(
            mock_db, limit=10, min_post_count=5, days=7
        )
        
        assert len(result) == 1
        assert result[0] == sample_topic_stats
        
        # 验证SQL查询参数
        call_args = mock_db.execute.call_args[0][0]
        assert "ORDER BY hot_score DESC" in str(call_args)

    async def test_get_trending_topics(self, mock_db, sample_topic_stats):
        """测试获取趋势话题列表"""
        mock_result = MagicMock()
        mock_result.scalars.return_value.all.return_value = [sample_topic_stats]
        mock_db.execute.return_value = mock_result
        
        result = await crud_topic_stats.get_trending_topics(
            mock_db, limit=10, min_post_count=5
        )
        
        assert len(result) == 1
        assert result[0] == sample_topic_stats

    async def test_batch_update_hot_scores(self, mock_db, config):
        """测试批量更新热度分数"""
        stats_list = [
            TopicStats(topic="Python", post_count=100, total_likes=500),
            TopicStats(topic="JavaScript", post_count=80, total_likes=400),
        ]
        
        mock_result = MagicMock()
        mock_result.scalars.return_value.all.return_value = stats_list
        mock_db.execute.return_value = mock_result
        
        with patch('app.crud.topic_stats.calculate_hot_score', side_effect=[85.5, 72.1]):
            result = await crud_topic_stats.batch_update_hot_scores(mock_db, config=config)
            
            assert result == 2
            assert stats_list[0].hot_score == 85.5
            assert stats_list[1].hot_score == 72.1
            mock_db.commit.assert_called_once()

    async def test_get_multi_with_pagination(self, mock_db, sample_topic_stats):
        """测试分页查询"""
        mock_result = MagicMock()
        mock_result.scalars.return_value.all.return_value = [sample_topic_stats]
        mock_db.execute.return_value = mock_result
        
        result = await crud_topic_stats.get_multi(mock_db, skip=10, limit=20)
        
        assert len(result) == 1
        assert result[0] == sample_topic_stats
        
        # 验证分页参数
        call_args = mock_db.execute.call_args[0][0]
        assert "OFFSET 10" in str(call_args) or "offset(10)" in str(call_args).lower()
        assert "LIMIT 20" in str(call_args) or "limit(20)" in str(call_args).lower()


class TestTopicStatsEdgeCases:
    """话题统计边界情况测试"""

    async def test_empty_topic_name(self, mock_db):
        """测试空话题名"""
        with pytest.raises(ValueError):
            await crud_topic_stats.get_by_topic(mock_db, topic="")

    async def test_very_long_topic_name(self, mock_db):
        """测试过长的话题名"""
        long_topic = "a" * 200  # 超过100字符限制
        
        with pytest.raises(ValueError):
            await crud_topic_stats.create_or_get(mock_db, topic=long_topic)

    async def test_calculate_hot_score_no_activity(self, config):
        """测试无活动的话题热度分数"""
        stats = TopicStats(
            topic="DeadTopic",
            post_count=0,
            total_likes=0,
            total_comments=0,
            total_reposts=0,
            total_views=0,
            last_post_at=None,
        )
        
        score = crud_topic_stats.calculate_hot_score(stats, config)
        assert score == 0.0

    async def test_calculate_hot_score_very_old_activity(self, config):
        """测试很久以前的活动"""
        stats = TopicStats(
            topic="OldTopic",
            post_count=100,
            total_likes=500,
            total_comments=200,
            total_reposts=50,
            total_views=2000,
            last_post_at=datetime.utcnow() - timedelta(days=30),  # 30天前
        )
        
        score = crud_topic_stats.calculate_hot_score(stats, config)
        
        # 应该有很大的时间衰减
        assert score > 0
        assert score < 10  # 应该比新活动的分数低很多
