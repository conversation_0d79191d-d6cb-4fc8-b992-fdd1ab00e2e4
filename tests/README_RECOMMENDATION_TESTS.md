# 用户推荐系统测试指南

本文档介绍如何运行和理解用户推荐系统的测试套件。

## 📋 测试概览

用户推荐系统包含以下测试模块：

### 🔧 服务层测试
- **`test_user_recommendation_service.py`** - 用户推荐服务测试
  - 测试4种推荐算法（协同过滤、基于内容、社交网络、热门用户）
  - 测试混合推荐算法
  - 测试缓存机制和分页功能
  - 测试边界条件和错误处理

- **`test_user_similarity_service.py`** - 用户相似度服务测试
  - 测试行为相似度计算
  - 测试兴趣相似度计算
  - 测试社交相似度计算
  - 测试余弦相似度和Jaccard相似度算法

- **`test_user_recommendation_tracking_service.py`** - 推荐追踪服务测试
  - 测试推荐展示、点击、关注事件追踪
  - 测试推荐效果指标计算（CTR、转化率等）
  - 测试算法效果对比
  - 测试缓存和错误处理

- **`test_recommendation_cache_service.py`** - 推荐缓存服务测试
  - 测试用户推荐结果缓存
  - 测试用户相似度缓存
  - 测试缓存失效和分页
  - 测试并发操作和错误处理

### 🌐 API层测试
- **`test_user_recommendations_api.py`** - 用户推荐API测试
  - 测试推荐获取端点
  - 测试推荐反馈端点
  - 测试相似度查询端点
  - 测试追踪端点和管理员端点

## 🚀 运行测试

### 方式一：使用测试脚本（推荐）

```bash
# 运行所有推荐系统测试
python tests/run_recommendation_tests.py all

# 只运行测试（不生成覆盖率报告）
python tests/run_recommendation_tests.py

# 生成测试覆盖率报告
python tests/run_recommendation_tests.py coverage

# 运行特定模块测试
python tests/run_recommendation_tests.py recommendation  # 推荐服务
python tests/run_recommendation_tests.py similarity     # 相似度服务
python tests/run_recommendation_tests.py tracking       # 追踪服务
python tests/run_recommendation_tests.py cache          # 缓存服务
python tests/run_recommendation_tests.py api            # API端点
```

### 方式二：直接使用pytest

```bash
# 运行所有推荐系统测试
pytest tests/services/test_user_recommendation_service.py \
       tests/services/test_user_similarity_service.py \
       tests/services/test_user_recommendation_tracking_service.py \
       tests/services/test_recommendation_cache_service.py \
       tests/api/test_user_recommendations_api.py -v

# 运行单个测试文件
pytest tests/services/test_user_recommendation_service.py -v

# 运行特定测试方法
pytest tests/services/test_user_recommendation_service.py::TestUserRecommendationService::test_collaborative_filtering_algorithm -v

# 生成覆盖率报告
pytest tests/services/test_user_recommendation_service.py \
       --cov=app.services.user_recommendation_service \
       --cov-report=html \
       --cov-report=term-missing
```

## 📊 测试覆盖范围

### 用户推荐服务测试
- ✅ 基本推荐功能
- ✅ 协同过滤算法
- ✅ 基于内容推荐算法
- ✅ 社交网络推荐算法
- ✅ 热门用户推荐算法
- ✅ 混合推荐算法
- ✅ 推荐结果缓存
- ✅ 分页功能
- ✅ 排除已关注用户
- ✅ 算法权重配置
- ✅ 并发请求处理
- ✅ 错误处理和边界条件

### 用户相似度服务测试
- ✅ 基本相似度计算
- ✅ 行为相似度算法
- ✅ 兴趣相似度算法
- ✅ 社交相似度算法
- ✅ 综合相似度计算
- ✅ 相似度缓存机制
- ✅ 相似用户列表获取
- ✅ 余弦相似度计算
- ✅ Jaccard相似度计算
- ✅ 权重配置和错误处理

### 推荐追踪服务测试
- ✅ 推荐展示事件追踪
- ✅ 推荐点击事件追踪
- ✅ 关注转化事件追踪
- ✅ 推荐效果指标计算
- ✅ 算法效果对比
- ✅ 指标缓存机制
- ✅ 时间范围过滤
- ✅ Redis计数器更新
- ✅ 追踪功能开关

### 缓存服务测试
- ✅ 用户推荐缓存设置和获取
- ✅ 推荐缓存分页
- ✅ 推荐缓存失效
- ✅ 用户相似度缓存
- ✅ 相似度缓存顺序无关性
- ✅ 缓存过期时间
- ✅ 并发缓存操作
- ✅ 缓存错误处理

### API端点测试
- ✅ 获取用户推荐API
- ✅ 推荐反馈提交API
- ✅ 用户相似度查询API
- ✅ 推荐追踪API
- ✅ 推荐指标API（管理员）
- ✅ 算法对比API（管理员）
- ✅ 参数验证和错误处理
- ✅ 权限控制测试

## 🔧 测试环境要求

### 依赖包
```bash
pip install pytest pytest-asyncio pytest-cov
```

### 数据库
测试使用内存SQLite数据库，无需额外配置。

### Redis
测试使用MockRedis，无需真实Redis服务。

## 📈 测试报告

### 覆盖率报告
运行覆盖率测试后，会生成HTML报告：
- 报告位置：`htmlcov/index.html`
- 在浏览器中打开查看详细覆盖率信息

### 测试结果解读
- ✅ **PASSED** - 测试通过
- ❌ **FAILED** - 测试失败，需要检查错误信息
- ⏰ **TIMEOUT** - 测试超时，可能存在性能问题
- 💥 **ERROR** - 测试运行异常，需要检查环境配置

## 🐛 常见问题

### 1. 导入错误
```
ModuleNotFoundError: No module named 'app'
```
**解决方案**：确保在项目根目录运行测试，或设置PYTHONPATH。

### 2. 数据库连接错误
```
sqlalchemy.exc.OperationalError
```
**解决方案**：测试使用内存数据库，检查SQLAlchemy配置。

### 3. Redis连接错误
```
redis.exceptions.ConnectionError
```
**解决方案**：测试使用MockRedis，检查mock配置。

### 4. 测试超时
**解决方案**：检查测试逻辑，优化性能或增加超时时间。

## 📝 编写新测试

### 测试文件命名
- 服务测试：`test_{service_name}_service.py`
- API测试：`test_{api_name}_api.py`

### 测试类命名
```python
class TestUserRecommendationService:
    """用户推荐服务测试类"""
```

### 测试方法命名
```python
async def test_get_user_recommendations_success(self):
    """测试获取用户推荐成功"""
```

### Fixture使用
```python
@pytest_asyncio.fixture
async def sample_users(async_session: AsyncSession) -> list[models.User]:
    """创建示例用户数据"""
```

## 🎯 测试最佳实践

1. **独立性**：每个测试应该独立运行，不依赖其他测试
2. **清晰性**：测试名称和文档应该清楚说明测试目的
3. **完整性**：覆盖正常流程、边界条件和异常情况
4. **性能**：避免不必要的数据库操作和网络请求
5. **维护性**：使用fixture和工具函数减少重复代码

## 📞 支持

如果在运行测试时遇到问题，请：
1. 检查错误信息和日志
2. 确认环境配置正确
3. 查看相关测试文档
4. 联系开发团队获取支持
