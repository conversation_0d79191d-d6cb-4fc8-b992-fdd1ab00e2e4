from __future__ import annotations

from datetime import date, datetime, timezone

DEFAULT_TIME_RANGE = "last_7_days"

MOCK_ANALYTICS_SUMMARY = {
    "period": "2025-10-05至2025-10-11",
    "total_users": 15420,
    "active_users": 8750,
    "new_users": 1250,
    "content_views": 45680,
    "error_rate": 0.8,
}

MOCK_DAILY_ACTIVE_USERS = [
    {"date": date(2025, 10, 5), "count": 1200},
    {"date": date(2025, 10, 6), "count": 1350},
    {"date": date(2025, 10, 7), "count": 1180},
    {"date": date(2025, 10, 8), "count": 1420},
    {"date": date(2025, 10, 9), "count": 1580},
    {"date": date(2025, 10, 10), "count": 1650},
    {"date": date(2025, 10, 11), "count": 1370},
]

MOCK_ANALYTICS_SNAPSHOT = {
    "snapshot_type": "dashboard",
    "time_range": DEFAULT_TIME_RANGE,
    "filters": {},
    "record_count": len(MOCK_DAILY_ACTIVE_USERS),
    "generated_at": datetime.now(timezone.utc),
    "generation_duration": 1520,
    "status": "completed",
}

MOCK_ANALYTICS_PAYLOAD = {
    "summary": MOCK_ANALYTICS_SUMMARY,
    "user_metrics": {"daily_active_users": MOCK_DAILY_ACTIVE_USERS},
    "content_metrics": {
        "top_content": [
            {"id": 1, "title": "热门视频标题1", "type": "video", "views": 5420, "likes": 320},
            {"id": 2, "title": "热门文章标题1", "type": "article", "views": 3850, "likes": 210},
        ]
    },
    "system_metrics": {
        "performance": {
            "avg_response_time": 145.2,
            "p95_response_time": 320.5,
            "error_rate": 0.8,
            "availability": 99.92,
        }
    },
}

__all__ = [
    "DEFAULT_TIME_RANGE",
    "MOCK_ANALYTICS_SUMMARY",
    "MOCK_DAILY_ACTIVE_USERS",
    "MOCK_ANALYTICS_SNAPSHOT",
    "MOCK_ANALYTICS_PAYLOAD",
]
