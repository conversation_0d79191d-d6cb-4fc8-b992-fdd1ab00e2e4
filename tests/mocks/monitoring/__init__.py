from __future__ import annotations

from datetime import datetime, timedelta, timezone
from decimal import Decimal

MOCK_MONITORING_WINDOW = timedelta(minutes=15)
MOCK_METRIC_NAME = "cpu"

def _ts(offset_minutes: int) -> datetime:
    return datetime.now(timezone.utc) - timedelta(minutes=offset_minutes)


MOCK_METRIC_POINTS: list[dict[str, float | datetime]] = [
    {"timestamp": _ts(15), "value": 41.2},
    {"timestamp": _ts(10), "value": 43.8},
    {"timestamp": _ts(5), "value": 55.1},
    {"timestamp": _ts(1), "value": 64.7},
]

MOCK_ALERT_RULE: dict[str, object] = {
    "name": "high_cpu_usage",
    "metric_name": MOCK_METRIC_NAME,
    "operator": "greater_than",
    "threshold": Decimal("80.0"),
    "duration": "5m",
    "severity": "warning",
    "channels": [
        {"type": "webhook", "target": "https://example.com/hook", "config": {"method": "POST"}},
    ],
    "enabled": True,
    "description": "CPU usage sustained above 80% for 5 minutes",
}

MOCK_ALERT_EVENT: dict[str, object] = {
    "event_id": "evt_high_cpu_sample",
    "status": "active",
    "trigger_value": 87.3,
    "triggered_at": _ts(0),
    "context": {"host": "app-1", "threshold": 80.0},
    "message": "CPU usage exceeded threshold",
    "notification_status": "pending",
}


__all__ = [
    "MOCK_METRIC_POINTS",
    "MOCK_ALERT_RULE",
    "MOCK_ALERT_EVENT",
    "MOCK_MONITORING_WINDOW",
    "MOCK_METRIC_NAME",
]
