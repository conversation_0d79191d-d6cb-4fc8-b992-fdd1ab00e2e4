from pathlib import Path

import pytest

from app.tasks import image_processing


TEST_VIDEO_NAME = "6970321557084261644-我现在花100块钱买你手上的东西是赚了还是怎样#麦田更多资源-XH1080.com.mp4"


@pytest.mark.asyncio
async def test_process_video_core_generates_hls(monkeypatch):
    """验证视频可以成功转码为 HLS (m3u8) 并上传目录"""

    video_path = Path(__file__).resolve().parents[1] / TEST_VIDEO_NAME
    assert video_path.exists(), "测试视频文件缺失"

    captured = {
        "called": False,
        "segment_count": 0,
    }

    class DummyPartialUpload:
        def upload_directory(self, local_directory_path: str, oss_target_path: str, retry_times: int = 3) -> bool:
            local_dir = Path(local_directory_path)
            master_playlist = local_dir / "master.m3u8"
            assert master_playlist.exists(), "未生成 master.m3u8 播放列表"

            content = master_playlist.read_text(encoding="utf-8")
            assert "#EXTM3U" in content, "播放列表文件格式不正确"
            assert "stream_0/index.m3u8" in content, "主播放表缺少低码率索引"
            assert "stream_1/index.m3u8" in content, "主播放表缺少高码率索引"

            segments = list(local_dir.glob("stream_*/*.ts"))
            assert segments, "未生成任何 TS 分片文件"
            variant_playlists = list(local_dir.glob("stream_*/index.m3u8"))
            assert variant_playlists, "未生成变体播放列表文件"

            captured["called"] = True
            captured["segment_count"] = len(segments)
            captured["oss_target_path"] = oss_target_path
            return True

    async def fake_process_and_upload_image(file_data: bytes, file_hash: str, quality: int = 80):
        return f"/steam/images/{file_hash}.webp"

    monkeypatch.setattr(image_processing, "PartialUpload", DummyPartialUpload)
    monkeypatch.setattr(image_processing, "process_and_upload_image", fake_process_and_upload_image)

    result = await image_processing._process_video_core(
        temp_file_path=str(video_path),
        original_file_hash="test_video_hash",
        video_quality="high",
        original_filename="test_source.mp4",
    )

    assert result is not None, "转码结果返回为空"
    assert captured["called"], "未触发 OSS 目录上传"
    assert captured["segment_count"] > 0, "TS 分片数量不正确"
    assert result["file_url"] == "/steam/hls/test_video_hash/master.m3u8"
    assert result["cover_url"] == "/steam/images/test_video_hash_cover.webp"
    assert result["duration"] > 0
    assert result["width"] > 0
    assert result["height"] > 0
