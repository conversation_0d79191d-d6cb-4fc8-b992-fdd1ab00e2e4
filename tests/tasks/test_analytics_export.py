from __future__ import annotations

from unittest.mock import AsyncMock, patch

import pytest

from app.tasks import analytics_export, analytics_refresh


@pytest.mark.asyncio
async def test_analytics_refresh_task_calls_pipeline():
    pipeline = AsyncMock()
    with patch.object(analytics_refresh, "get_pipeline", return_value=pipeline):
        with patch.object(analytics_refresh, "SessionLocal") as session_factory:
            session = AsyncMock()
            session_factory.return_value = session

            await analytics_refresh.task_refresh_snapshots()

            assert pipeline.refresh_snapshot.await_count == 4
            session.close.assert_awaited_once()


@pytest.mark.asyncio
async def test_analytics_export_task_returns_result():
    pipeline = AsyncMock()
    pipeline.export_snapshot.return_value = AsyncMock(path="/tmp/file.csv", rows=5, filename="file.csv")

    with patch.object(analytics_export, "get_pipeline", return_value=pipeline):
        with patch.object(analytics_export, "SessionLocal") as session_factory:
            session = AsyncMock()
            session_factory.return_value = session

            result = await analytics_export.task_generate_export("dashboard")
            assert result["rows"] == 5
            session.close.assert_awaited_once()
