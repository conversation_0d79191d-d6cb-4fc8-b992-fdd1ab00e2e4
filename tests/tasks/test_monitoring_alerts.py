from __future__ import annotations

from unittest.mock import AsyncMock, patch

import pytest

from app.tasks import monitoring_alerts


@pytest.mark.asyncio
async def test_monitoring_alert_task_invokes_engine():
    mock_engine = AsyncMock()
    with patch.object(monitoring_alerts, "get_monitoring_alert_engine", return_value=mock_engine):
        with patch.object(monitoring_alerts, "SessionLocal") as session_factory:
            session = AsyncMock()
            session_factory.return_value = session

            await monitoring_alerts.task_evaluate_alerts()

            mock_engine.evaluate_rules.assert_awaited_once_with(session)
            session.close.assert_awaited_once()
