#!/usr/bin/env python3
"""
用户推荐系统测试运行脚本

运行所有用户推荐相关的测试，并生成测试报告
"""

import subprocess
import sys
from pathlib import Path


def run_tests():
    """运行用户推荐系统的所有测试"""
    
    # 测试文件列表
    test_files = [
        "tests/services/test_user_recommendation_service.py",
        "tests/services/test_user_similarity_service.py", 
        "tests/services/test_user_recommendation_tracking_service.py",
        "tests/services/test_recommendation_cache_service.py",
        "tests/api/test_user_recommendations_api.py",
    ]
    
    print("🚀 开始运行用户推荐系统测试...")
    print("=" * 60)
    
    # 检查测试文件是否存在
    missing_files = []
    for test_file in test_files:
        if not Path(test_file).exists():
            missing_files.append(test_file)
    
    if missing_files:
        print("❌ 以下测试文件不存在:")
        for file in missing_files:
            print(f"   - {file}")
        return False
    
    # 运行每个测试文件
    all_passed = True
    results = {}
    
    for test_file in test_files:
        print(f"\n📋 运行测试: {test_file}")
        print("-" * 40)
        
        try:
            # 运行pytest
            result = subprocess.run(
                [sys.executable, "-m", "pytest", test_file, "-v", "--tb=short"],
                capture_output=True,
                text=True,
                timeout=300  # 5分钟超时
            )
            
            if result.returncode == 0:
                print(f"✅ {test_file} - 所有测试通过")
                results[test_file] = "PASSED"
            else:
                print(f"❌ {test_file} - 测试失败")
                print("错误输出:")
                print(result.stdout)
                print(result.stderr)
                results[test_file] = "FAILED"
                all_passed = False
                
        except subprocess.TimeoutExpired:
            print(f"⏰ {test_file} - 测试超时")
            results[test_file] = "TIMEOUT"
            all_passed = False
        except Exception as e:
            print(f"💥 {test_file} - 运行异常: {e}")
            results[test_file] = "ERROR"
            all_passed = False
    
    # 打印总结报告
    print("\n" + "=" * 60)
    print("📊 测试结果总结")
    print("=" * 60)
    
    for test_file, status in results.items():
        status_icon = {
            "PASSED": "✅",
            "FAILED": "❌", 
            "TIMEOUT": "⏰",
            "ERROR": "💥"
        }.get(status, "❓")
        
        print(f"{status_icon} {test_file}: {status}")
    
    if all_passed:
        print("\n🎉 所有测试都通过了！用户推荐系统测试完成。")
        return True
    else:
        print("\n⚠️  部分测试失败，请检查上述错误信息。")
        return False


def run_coverage_report():
    """运行测试覆盖率报告"""
    print("\n📈 生成测试覆盖率报告...")
    
    try:
        # 运行覆盖率测试
        result = subprocess.run([
            sys.executable, "-m", "pytest",
            "tests/services/test_user_recommendation_service.py",
            "tests/services/test_user_similarity_service.py", 
            "tests/services/test_user_recommendation_tracking_service.py",
            "tests/services/test_recommendation_cache_service.py",
            "tests/api/test_user_recommendations_api.py",
            "--cov=app.services.user_recommendation_service",
            "--cov=app.services.user_similarity_service",
            "--cov=app.services.user_recommendation_tracking_service", 
            "--cov=app.services.recommendation_cache_service",
            "--cov=app.api.endpoints.recommendations",
            "--cov-report=html",
            "--cov-report=term-missing"
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ 覆盖率报告生成成功")
            print("📁 HTML报告位置: htmlcov/index.html")
            print("\n覆盖率详情:")
            print(result.stdout)
        else:
            print("❌ 覆盖率报告生成失败")
            print(result.stderr)
            
    except Exception as e:
        print(f"💥 生成覆盖率报告时出错: {e}")


def run_specific_test(test_name: str):
    """运行特定的测试"""
    test_mapping = {
        "recommendation": "tests/services/test_user_recommendation_service.py",
        "similarity": "tests/services/test_user_similarity_service.py",
        "tracking": "tests/services/test_user_recommendation_tracking_service.py", 
        "cache": "tests/services/test_recommendation_cache_service.py",
        "api": "tests/api/test_user_recommendations_api.py",
    }
    
    if test_name not in test_mapping:
        print(f"❌ 未知的测试名称: {test_name}")
        print("可用的测试:")
        for name in test_mapping.keys():
            print(f"  - {name}")
        return False
    
    test_file = test_mapping[test_name]
    print(f"🚀 运行特定测试: {test_file}")
    
    try:
        result = subprocess.run([
            sys.executable, "-m", "pytest", test_file, "-v"
        ])
        return result.returncode == 0
    except Exception as e:
        print(f"💥 运行测试时出错: {e}")
        return False


def main():
    """主函数"""
    if len(sys.argv) > 1:
        command = sys.argv[1]
        
        if command == "all":
            success = run_tests()
            if success:
                run_coverage_report()
        elif command == "coverage":
            run_coverage_report()
        elif command in ["recommendation", "similarity", "tracking", "cache", "api"]:
            run_specific_test(command)
        else:
            print("用法:")
            print("  python run_recommendation_tests.py all        # 运行所有测试")
            print("  python run_recommendation_tests.py coverage   # 生成覆盖率报告")
            print("  python run_recommendation_tests.py recommendation  # 运行推荐服务测试")
            print("  python run_recommendation_tests.py similarity      # 运行相似度服务测试")
            print("  python run_recommendation_tests.py tracking        # 运行追踪服务测试")
            print("  python run_recommendation_tests.py cache           # 运行缓存服务测试")
            print("  python run_recommendation_tests.py api             # 运行API测试")
    else:
        # 默认运行所有测试
        run_tests()


if __name__ == "__main__":
    main()
