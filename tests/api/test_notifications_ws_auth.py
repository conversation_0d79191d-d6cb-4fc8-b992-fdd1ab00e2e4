"""WebSocket 鉴权依赖的单元测试"""

from unittest.mock import AsyncMock, MagicMock

import pytest
from fastapi import HTTPException

from app.api.deps import (
    get_current_user_websocket,
    get_token_from_websocket_protocols_or_query,
)
from app.models.user import User


class DummyWebSocket:
    """用于模拟 FastAPI WebSocket 对象的轻量测试桩"""

    def __init__(self):
        self.headers: dict[str, str] = {}
        self.cookies: dict[str, str] = {}
        self.close_calls: list[tuple[int, str]] = []

    async def close(self, code: int, reason: str):
        self.close_calls.append((code, reason))


@pytest.mark.asyncio
async def test_query_token_strips_bearer_prefix():
    """Query 参数中的 token 会自动去除 Bearer 前缀"""

    websocket = DummyWebSocket()
    raw_token = "Bearer eyJ..."
    expected_token = "eyJ..."

    token = await get_token_from_websocket_protocols_or_query(websocket, token=raw_token)

    assert token == expected_token


@pytest.mark.asyncio
async def test_get_current_user_websocket_accepts_numeric_username():
    """纯数字用户名应按用户名匹配，而非误判为用户ID"""
    websocket = DummyWebSocket()
    token = "fake-token"
    user = User(id=7, username="17604840253", is_active=True)

    mock_result = MagicMock()
    mock_result.scalar_one_or_none.return_value = user

    mock_db = AsyncMock()
    mock_db.execute = AsyncMock(return_value=mock_result)

    token_payload = {"sub": user.username}
    mock_token_service = AsyncMock()
    mock_token_service.verify_token = AsyncMock(return_value=token_payload)

    result = await get_current_user_websocket(
        websocket=websocket, token=token, db=mock_db, token_service=mock_token_service
    )

    assert result is user
    assert websocket.close_calls == []
    mock_token_service.verify_token.assert_awaited_once_with(token)
    mock_db.execute.assert_awaited_once()
    assert "users.username" in str(mock_db.execute.await_args.args[0]).lower()


@pytest.mark.asyncio
async def test_get_current_user_websocket_fallbacks_to_id_lookup():
    """当用户名查不到且 subject 可解析为数字时，仍应按用户ID兜底"""
    websocket = DummyWebSocket()
    token = "another-token"
    user = User(id=42, username="42", is_active=True)

    username_result = MagicMock()
    username_result.scalar_one_or_none.return_value = None

    id_result = MagicMock()
    id_result.scalar_one_or_none.return_value = user

    async def execute_side_effect(statement):
        criteria = list(getattr(statement, "_where_criteria", ()))
        if criteria:
            column = getattr(getattr(criteria[0], "left", None), "key", None)
            if column == "username":
                return username_result
            if column == "id":
                return id_result
        raise AssertionError(f"Unexpected statement: {statement}")

    mock_db = AsyncMock()
    mock_db.execute = AsyncMock(side_effect=execute_side_effect)

    token_payload = {"sub": user.username}
    mock_token_service = AsyncMock()
    mock_token_service.verify_token = AsyncMock(return_value=token_payload)

    result = await get_current_user_websocket(
        websocket=websocket, token=token, db=mock_db, token_service=mock_token_service
    )

    assert result is user
    assert websocket.close_calls == []
    assert mock_db.execute.await_count == 2
    calls = [str(call.args[0]).lower() for call in mock_db.execute.await_args_list]
    assert any("users.username" in c for c in calls)
    assert any("users.id" in c for c in calls)


@pytest.mark.asyncio
async def test_websocket_auth_succeeds_for_bearer_prefixed_query_token():
    """WebSocket 鉴权会正确剥离 Bearer 前缀，使 verify_token 接收到纯净的 token"""

    websocket = DummyWebSocket()
    prefixed_token = "Bearer eyJ.sample"
    clean_token = "eyJ.sample"
    user = User(id=1, username="testuser", is_active=True)

    mock_result = MagicMock()
    mock_result.scalar_one_or_none.return_value = user

    mock_db = AsyncMock()
    mock_db.execute = AsyncMock(return_value=mock_result)

    token_payload = {"sub": user.username}
    mock_token_service = AsyncMock()
    mock_token_service.verify_token = AsyncMock(return_value=token_payload)

    # 首先测试 token 前缀剥离
    processed_token = await get_token_from_websocket_protocols_or_query(websocket, token=prefixed_token)
    assert processed_token == clean_token

    # 然后测试完整的鉴权流程
    result = await get_current_user_websocket(
        websocket=websocket,
        token=processed_token,  # 使用处理后的 token
        db=mock_db,
        token_service=mock_token_service,
    )

    assert result is user
    assert websocket.close_calls == []  # 连接没有被关闭
    mock_token_service.verify_token.assert_awaited_once_with(clean_token)  # 验证时使用纯净 token


@pytest.mark.asyncio
async def test_cookie_token_strips_bearer_prefix():
    """Cookie 中的 token 会自动去除 Bearer 前缀"""

    websocket = DummyWebSocket()
    websocket.cookies = {"access_token": "Bearer eyJ.cookie"}
    expected_token = "eyJ.cookie"

    token = await get_token_from_websocket_protocols_or_query(websocket, token=None)

    assert token == expected_token


@pytest.mark.asyncio
async def test_websocket_auth_succeeds_for_bearer_prefixed_cookie_token():
    """WebSocket 鉴权会正确剥离 Cookie 中的 Bearer 前缀"""

    websocket = DummyWebSocket()
    websocket.cookies = {"access_token": "Bearer eyJ.cookie"}
    clean_token = "eyJ.cookie"
    user = User(id=1, username="testuser", is_active=True)

    mock_result = MagicMock()
    mock_result.scalar_one_or_none.return_value = user

    mock_db = AsyncMock()
    mock_db.execute = AsyncMock(return_value=mock_result)

    token_payload = {"sub": user.username}
    mock_token_service = AsyncMock()
    mock_token_service.verify_token = AsyncMock(return_value=token_payload)

    # 直接调用 get_token_from_websocket_protocols_or_query 来测试 token 处理
    token = await get_token_from_websocket_protocols_or_query(websocket, token=None)
    assert token == clean_token

    # 然后测试完整的鉴权流程
    result = await get_current_user_websocket(
        websocket=websocket,
        token=token,  # 使用处理后的 token
        db=mock_db,
        token_service=mock_token_service,
    )

    assert result is user
    assert websocket.close_calls == []  # 连接没有被关闭
    mock_token_service.verify_token.assert_awaited_once_with(clean_token)  # 验证时使用纯净 token


@pytest.mark.asyncio
async def test_token_without_bearer_prefix_unchanged():
    """没有 Bearer 前缀的 token 不会被修改"""

    websocket = DummyWebSocket()
    clean_token = "eyJ.clean"

    # 测试 Query 参数
    token = await get_token_from_websocket_protocols_or_query(websocket, token=clean_token)
    assert token == clean_token

    # 测试 Cookie
    websocket.cookies = {"access_token": clean_token}
    token = await get_token_from_websocket_protocols_or_query(websocket, token=None)
    assert token == clean_token


@pytest.mark.asyncio
async def test_websocket_auth_missing_token_closes_connection():
    websocket = DummyWebSocket()
    mock_db = AsyncMock()
    mock_db.execute = AsyncMock()
    mock_token_service = AsyncMock()

    with pytest.raises(HTTPException) as exc:
        await get_current_user_websocket(
            websocket=websocket,
            token=None,
            db=mock_db,
            token_service=mock_token_service,
        )

    assert exc.value.status_code == 401
    assert websocket.close_calls == [(1008, "No authentication token provided")]
    mock_token_service.verify_token.assert_not_called()
    mock_db.execute.assert_not_awaited()


@pytest.mark.asyncio
async def test_websocket_auth_invalid_token_closes_connection():
    websocket = DummyWebSocket()
    token = "invalid-token"
    mock_db = AsyncMock()
    mock_db.execute = AsyncMock()
    mock_token_service = AsyncMock()
    mock_token_service.verify_token = AsyncMock(return_value=None)

    with pytest.raises(HTTPException) as exc:
        await get_current_user_websocket(
            websocket=websocket,
            token=token,
            db=mock_db,
            token_service=mock_token_service,
        )

    assert exc.value.status_code == 401
    assert websocket.close_calls == [(1008, "Invalid token")]
    mock_token_service.verify_token.assert_awaited_once_with(token)
    mock_db.execute.assert_not_awaited()


@pytest.mark.asyncio
async def test_websocket_auth_user_not_found():
    websocket = DummyWebSocket()
    token = "valid-token"
    mock_db = AsyncMock()
    missing_result = MagicMock()
    missing_result.scalar_one_or_none.return_value = None
    mock_db.execute = AsyncMock(return_value=missing_result)

    payload = {"sub": "missing-user"}
    mock_token_service = AsyncMock()
    mock_token_service.verify_token = AsyncMock(return_value=payload)

    with pytest.raises(HTTPException) as exc:
        await get_current_user_websocket(
            websocket=websocket,
            token=token,
            db=mock_db,
            token_service=mock_token_service,
        )

    assert exc.value.status_code == 404
    assert websocket.close_calls == [(1008, "User not found")]
    mock_token_service.verify_token.assert_awaited_once_with(token)
    mock_db.execute.assert_awaited_once()


@pytest.mark.asyncio
async def test_websocket_auth_user_inactive():
    websocket = DummyWebSocket()
    token = "valid-token"
    inactive_user = User(id=9, username="inactive", is_active=False)

    result = MagicMock()
    result.scalar_one_or_none.return_value = inactive_user

    mock_db = AsyncMock()
    mock_db.execute = AsyncMock(return_value=result)

    payload = {"sub": inactive_user.username}
    mock_token_service = AsyncMock()
    mock_token_service.verify_token = AsyncMock(return_value=payload)

    with pytest.raises(HTTPException) as exc:
        await get_current_user_websocket(
            websocket=websocket,
            token=token,
            db=mock_db,
            token_service=mock_token_service,
        )

    assert exc.value.status_code == 400
    assert websocket.close_calls == [(1008, "User inactive")]
    mock_token_service.verify_token.assert_awaited_once_with(token)
    mock_db.execute.assert_awaited_once()
