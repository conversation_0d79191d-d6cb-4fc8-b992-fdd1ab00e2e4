from __future__ import annotations

from unittest.mock import AsyncMock

import pytest
from fastapi import FastAPI
from fastapi.testclient import TestClient

from app.api import deps
from app.api.endpoints import admin_analytics as analytics_endpoint
from app.schemas.admin_analytics import AnalyticsExportRequest, SnapshotType, TimeRange


@pytest.fixture
def test_app() -> FastAPI:
    app = FastAPI()
    app.include_router(analytics_endpoint.router, prefix="/api/admin/analytics", tags=["admin-analytics"])
    return app


@pytest.fixture
def client(test_app: FastAPI) -> TestClient:
    return TestClient(test_app)


@pytest.fixture(autouse=True)
def override_permissions(test_app: FastAPI):
    async def allow():
        return {"id": 1}

    read_guard = analytics_endpoint.analytics_read_guard
    export_guard = analytics_endpoint.analytics_export_guard
    test_app.dependency_overrides[read_guard] = allow
    test_app.dependency_overrides[export_guard] = allow
    yield
    test_app.dependency_overrides.pop(read_guard, None)
    test_app.dependency_overrides.pop(export_guard, None)


@pytest.fixture(autouse=True)
def override_db(test_app: FastAPI):
    async def _db():
        yield AsyncMock()

    test_app.dependency_overrides[deps.get_db] = _db
    yield
    test_app.dependency_overrides.pop(deps.get_db, None)


@pytest.fixture
def mock_service(test_app: FastAPI) -> AsyncMock:
    service = AsyncMock()
    def _parse(date_range, from_date, to_date, extra_filters=None):
        filters = extra_filters.copy() if extra_filters else {}
        return "last_7_days", filters

    service.parse_params = _parse  # type: ignore[assignment]
    test_app.dependency_overrides[analytics_endpoint.get_analytics_service] = lambda: service
    try:
        yield service
    finally:
        test_app.dependency_overrides.pop(analytics_endpoint.get_analytics_service, None)


def test_get_dashboard(client: TestClient, mock_service: AsyncMock):
    mock_service.get_dashboard.return_value = {"summary": {"total_users": 10}}

    response = client.get("/api/admin/analytics/dashboard")
    assert response.status_code == 200
    assert response.json()["data"]["summary"]["total_users"] == 10


def test_export_trigger(client: TestClient, mock_service: AsyncMock):
    mock_service.trigger_export.return_value = {
        "export_id": "dashboard_last_7_days.csv",
        "status": "completed",
    }
    payload = AnalyticsExportRequest(dataset=SnapshotType.DASHBOARD, time_range=TimeRange.LAST_7_DAYS)
    response = client.post("/api/admin/analytics/export", json=payload.model_dump(mode="json"))
    assert response.status_code == 200
    mock_service.trigger_export.assert_awaited_once()
