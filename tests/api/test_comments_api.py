import pytest
from fastapi import <PERSON><PERSON><PERSON><PERSON>x<PERSON>, status
from unittest.mock import Async<PERSON>ock, MagicMock, ANY

from app.api.endpoints import comments
from app.models.comment import CommentType
from app.schemas.comment import CommentCreate, CommentUpdate


@pytest.mark.asyncio
async def test_create_scratch_comment_success(monkeypatch):
    comment_in = CommentCreate(
        content="测试评论",
        comment_type=CommentType.SCRATCH,
        article_id=None,
        video_id=None,
        scratch_id=123,
        parent_id=None,
    )

    current_user = MagicMock()
    current_user.id = 1
    db = AsyncMock()

    scratch_project = MagicMock()
    scratch_project.is_published = True
    monkeypatch.setattr(
        comments.crud.scratch_product,
        "get",
        AsyncMock(return_value=scratch_project),
    )
    created_comment = MagicMock(
        comment_type=CommentType.SCRATCH,
        article_id=None,
        video_id=None,
        scratch_id=123,
    )
    monkeypatch.setattr(
        comments.crud.comment,
        "create",
        AsyncMock(return_value=created_comment),
    )

    content_stats_service = MagicMock()
    content_stats_service.update_comment_count = AsyncMock()

    result = await comments.create_comment(
        db=db,
        comment_in=comment_in,
        current_user=current_user,
        article_cache_service=MagicMock(),
        video_cache_service=MagicMock(),
        content_stats_service=content_stats_service,
    )

    assert result is created_comment
    comments.crud.scratch_product.get.assert_awaited_once_with(db=db, id=123)
    comments.crud.comment.create.assert_awaited_once_with(
        db,
        obj_in=comment_in,
        author_id=current_user.id,
    )
    content_stats_service.update_comment_count.assert_awaited_once_with(
        content_type="scratch",
        content_id=123,
        increment=1,
    )


@pytest.mark.asyncio
async def test_get_scratch_comments_not_found(monkeypatch):
    monkeypatch.setattr(
        comments.crud.scratch_product,
        "get",
        AsyncMock(return_value=None),
    )

    with pytest.raises(HTTPException) as exc:
        await comments.get_scratch_comments(
            db=AsyncMock(),
            project_id=404,
            cursor=None,
            size=20,
            sort_by="like_count",
            flat=False,
            max_level=10,
            current_user=None,
            content_stats_service=MagicMock(),
        )

    assert exc.value.status_code == status.HTTP_404_NOT_FOUND
    comments.crud.scratch_product.get.assert_awaited_once_with(db=ANY, id=404)


@pytest.mark.asyncio
async def test_create_comment_requires_content_or_image(monkeypatch):
    comment_in = CommentCreate(
        content="   ",
        comment_type=CommentType.ARTICLE,
        article_id=1,
        image_urls=[],
    )

    current_user = MagicMock()
    current_user.id = 1
    db = AsyncMock()

    monkeypatch.setattr(comments.crud.comment, "create", AsyncMock())

    with pytest.raises(HTTPException) as exc:
        await comments.create_comment(
            db=db,
            comment_in=comment_in,
            current_user=current_user,
            article_cache_service=MagicMock(),
            video_cache_service=MagicMock(),
            post_cache_service=MagicMock(),
            content_stats_service=MagicMock(),
        )

    assert exc.value.status_code == status.HTTP_400_BAD_REQUEST
    comments.crud.comment.create.assert_not_called()


@pytest.mark.asyncio
async def test_update_comment_requires_content_or_image(monkeypatch):
    comment_obj = MagicMock()
    comment_obj.id = 1
    comment_obj.author_id = 1
    comment_obj.content = "原始内容"
    comment_obj.image_urls = ["/steam/images/foo.webp"]

    monkeypatch.setattr(comments.crud.comment, "get", AsyncMock(return_value=comment_obj))
    monkeypatch.setattr(comments.crud.comment, "update", AsyncMock(return_value=comment_obj))
    monkeypatch.setattr(
        comments.PermissionChecker,
        "check_permission",
        AsyncMock(side_effect=[True, False]),
    )

    current_user = MagicMock()
    current_user.id = 1

    comment_in = CommentUpdate(content="   ", image_urls=[])

    with pytest.raises(HTTPException) as exc:
        await comments.update_comment(
            db=AsyncMock(),
            comment_id=1,
            comment_in=comment_in,
            current_user=current_user,
        )

    assert exc.value.status_code == status.HTTP_400_BAD_REQUEST
    comments.crud.comment.update.assert_not_awaited()
