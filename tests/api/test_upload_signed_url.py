import pytest
import pytest_asyncio
from httpx import ASGITransport, AsyncClient

from app.api import deps
from app.main import app
from app.services.service_factory import get_oss_access_service


@pytest_asyncio.fixture
async def client() -> AsyncClient:
    async with Async<PERSON>lient(transport=ASGITransport(app=app), base_url="http://test") as ac:
        yield ac


@pytest.mark.asyncio
async def test_get_signed_url_success(client: AsyncClient):
    class StubService:
        async def fetch_object(self, file_path: str):
            assert file_path == "steam/images/avatar.png"
            return b"fake-image-bytes", "image/png"

    app.dependency_overrides[deps.get_current_user_optional] = lambda: None
    app.dependency_overrides[get_oss_access_service] = lambda: StubService()

    try:
        response = await client.get(
            "/api/v1/upload/signed-url",
            params={"file_path": "steam/images/avatar.png"},
        )

        assert response.status_code == 200
        content = await response.aread()
        assert content == b"fake-image-bytes"
        assert response.headers["content-type"] == "image/png"
        assert response.headers["content-length"] == str(len(content))
    finally:
        app.dependency_overrides = {}


@pytest.mark.asyncio
async def test_get_signed_url_invalid_path(client: AsyncClient):
    class ErrorService:
        async def fetch_object(self, file_path: str):
            raise ValueError("file_path不能为空")

    app.dependency_overrides[deps.get_current_user_optional] = lambda: None
    app.dependency_overrides[get_oss_access_service] = lambda: ErrorService()

    try:
        response = await client.get("/api/v1/upload/signed-url", params={"file_path": ""})

        assert response.status_code == 400
        payload = response.json()
        assert payload["success"] is False
        assert payload["data"]["detail"] == "file_path不能为空"
    finally:
        app.dependency_overrides = {}


@pytest.mark.asyncio
async def test_get_signed_url_object_not_found(client: AsyncClient):
    class MissingService:
        async def fetch_object(self, file_path: str):
            raise FileNotFoundError("steam/images/missing.png")

    app.dependency_overrides[deps.get_current_user_optional] = lambda: None
    app.dependency_overrides[get_oss_access_service] = lambda: MissingService()

    try:
        response = await client.get(
            "/api/v1/upload/signed-url",
            params={"file_path": "steam/images/missing.png"},
        )

        assert response.status_code == 404
        payload = response.json()
        assert payload["success"] is False
        assert payload["data"]["detail"] == "请求的 OSS 对象不存在"
    finally:
        app.dependency_overrides = {}
