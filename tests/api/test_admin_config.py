from __future__ import annotations

from unittest.mock import AsyncMock

import pytest
from fastapi import FastAPI
from fastapi.testclient import TestClient

from app.api import deps
from app.api.endpoints import admin_config as admin_config_endpoint
from app.schemas.admin_config import (
    ConfigBatchUpdateResponse,
    ConfigHistoryEntry,
    ConfigHistoryResponse,
    ConfigListResponse,
    ConfigRollbackRequest,
    ConfigRollbackResponse,
    ConfigSetting,
    ConfigUpdateRequest,
    FeatureToggle,
)


@pytest.fixture
def test_app() -> FastAPI:
    app = FastAPI()
    app.include_router(admin_config_endpoint.router, prefix="/api/admin/config", tags=["admin-config"])
    return app


@pytest.fixture
def client(test_app: FastAPI) -> TestClient:
    return TestClient(test_app)


@pytest.fixture(autouse=True)
def override_permission_dependencies(test_app: FastAPI):
    async def allow_user():
        return {"id": 1}

    read_guard = admin_config_endpoint.admin_config_read_guard
    write_guard = admin_config_endpoint.admin_config_write_guard
    rollback_guard = admin_config_endpoint.admin_config_rollback_guard
    test_app.dependency_overrides[read_guard] = allow_user
    test_app.dependency_overrides[write_guard] = allow_user
    test_app.dependency_overrides[rollback_guard] = allow_user
    yield
    test_app.dependency_overrides.pop(read_guard, None)
    test_app.dependency_overrides.pop(write_guard, None)
    test_app.dependency_overrides.pop(rollback_guard, None)


def _override_db():
    async def _db():
        yield AsyncMock()

    return _db


@pytest.fixture(autouse=True)
def override_db_dependency(test_app: FastAPI):
    test_app.dependency_overrides[deps.get_db] = _override_db()
    yield
    test_app.dependency_overrides.pop(deps.get_db, None)


@pytest.fixture
def mock_service(test_app: FastAPI) -> AsyncMock:
    service = AsyncMock()
    override = admin_config_endpoint.get_config_service

    test_app.dependency_overrides[override] = lambda: service
    try:
        yield service
    finally:
        test_app.dependency_overrides.pop(override, None)


def test_get_configs_success(client: TestClient, mock_service: AsyncMock):
    mock_service.list_configs.return_value = ConfigListResponse(
        configs=[
            ConfigSetting(
                id=1,
                category="redis",
                key="redis.timeout",
                value=300,
                value_type="int",
                description="Redis超时时间",
                metadata={},
                version=2,
                is_sensitive=False,
                updated_at=None,
                updated_by=None,
            )
        ],
        features=[
            FeatureToggle(
                id=1,
                name="user_analytics",
                is_enabled=True,
                description="用户分析",
                scope="global",
                config={},
                updated_at=None,
                updated_by=None,
            )
        ],
    )

    response = client.get("/api/admin/config")
    assert response.status_code == 200
    payload = response.json()
    assert payload["status"] == "success"
    assert payload["data"]["configs"][0]["key"] == "redis.timeout"
    mock_service.list_configs.assert_awaited_once()


def test_update_configs_conflict(client: TestClient, mock_service: AsyncMock):
    mock_service.batch_update_configs.side_effect = admin_config_endpoint.ConfigConflictHttpError(
        detail="Version mismatch"
    )

    response = client.put(
        "/api/admin/config",
        json=ConfigUpdateRequest(
            configs=[
                {
                    "key": "redis.timeout",
                    "value": 500,
                    "value_type": "int",
                    "version": 1,
                }
            ]
        ).model_dump(mode="json"),
    )

    assert response.status_code == 409
    payload = response.json()
    assert payload["status"] == "error"
    assert payload["error"]["message"] == "Version mismatch"


def test_rollback_config_success(client: TestClient, mock_service: AsyncMock):
    mock_service.rollback_change.return_value = ConfigRollbackResponse(
        rollback_id="rbk_1",
        original_change_id="chg_1",
        status="completed",
        rollback_at="2025-01-01T00:00:00Z",
        rollback_by=1,
    )

    body = ConfigRollbackRequest(reason="restore", confirm=True).model_dump(mode="json")
    response = client.post("/api/admin/config/rollback/chg_1", json=body)

    assert response.status_code == 200
    payload = response.json()
    assert payload["data"]["status"] == "completed"
    mock_service.rollback_change.assert_awaited_once()


def test_get_history(client: TestClient, mock_service: AsyncMock):
    mock_service.list_history.return_value = ConfigHistoryResponse(
        total=1,
        page=1,
        page_size=20,
        changes=[
            ConfigHistoryEntry(
                change_id="chg_1",
                config_key="redis.timeout",
                category="redis",
                old_value="200",
                new_value="300",
                change_type="update",
                change_reason="test",
                changed_by=1,
                changed_at="2025-01-01T00:00:00Z",
                rollback_id=None,
                is_rollback=False,
            )
        ],
    )

    response = client.get("/api/admin/config/history?page=1&page_size=20")
    assert response.status_code == 200
    payload = response.json()
    assert payload["data"]["total"] == 1
    mock_service.list_history.assert_awaited_once()
