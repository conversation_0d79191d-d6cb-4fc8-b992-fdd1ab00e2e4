from unittest.mock import MagicMock

import pytest

from app.api.middleware import ResponseFormatterMiddleware
from app.config import settings
from app.services.oss_access_service import SignedUrlResult


class DummyService:
    async def generate_signed_urls(self, file_paths, expires=None, use_cdn=None, cache=None):
        return {
            path: SignedUrlResult(url=f"https://signed.example.com/{path}", expires_in=120, use_cdn=True)
            for path in file_paths
        }


@pytest.mark.asyncio
async def test_build_signed_assets_generates_meta(monkeypatch):
    monkeypatch.setattr(settings, "OSS_PRIVATE_BUCKET", True)
    monkeypatch.setattr(settings, "OSS_SIGN_URL_MAX_BATCH", 10)

    middleware = ResponseFormatterMiddleware(MagicMock())
    middleware._oss_service = DummyService()

    sample_data = {
        "avatar": "steam/images/avatar.png",
        "nested": {"cover": "/steam/covers/demo.jpg"},
        "ignored": "http://example.com/static.png",
    }

    result = await middleware._build_signed_assets(sample_data)

    assert "steam/images/avatar.png" in result
    assert result["steam/images/avatar.png"]["signed_url"].startswith(
        "https://signed.example.com/steam/images/avatar.png"
    )
    assert result["steam/images/avatar.png"]["expires_in"] == 120
    assert result["steam/images/avatar.png"]["cdn_enabled"] is True
    assert "/steam/covers/demo.jpg" in result
