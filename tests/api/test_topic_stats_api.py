"""
话题统计API测试用例

测试热门话题和标签相关的API端点，包括：
- 热门话题列表
- 趋势话题列表
- 话题详情
- 热门标签列表
- 分页功能
- 缓存功能
"""

import pytest
from fastapi import FastAPI
from fastapi.testclient import TestClient
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime, timedelta

from app.api.endpoints.posts import router as posts_router
from app.api.deps import get_current_user, get_current_user_optional, get_db
from app.schemas.topic_stats import (
    HotTopic,
    HotTopicsResponse,
    TrendingTopic,
    TrendingTopicsResponse,
    TopicDetail,
    TopicListQuery,
)
from app.schemas.post import PostOut, PostStats, PostType, PostVisibility, PostStatus
from app.schemas.user import UserAggregated
from app.services.service_factory import (
    get_post_aggregation_service,
    get_topic_stats_service,
    get_tag_hot_service,
)


@pytest.fixture
def test_app():
    """测试应用"""
    app = FastAPI()
    app.include_router(posts_router, prefix="/api/v1/posts", tags=["posts"])
    db = AsyncMock()
    topic_service = AsyncMock()
    post_service = AsyncMock()
    tag_service = AsyncMock()

    async def override_get_db():
        yield db

    async def override_current_user():
        return None

    app.dependency_overrides[get_db] = override_get_db
    app.dependency_overrides[get_current_user] = override_current_user
    app.dependency_overrides[get_current_user_optional] = override_current_user
    app.dependency_overrides[get_topic_stats_service] = lambda: topic_service
    app.dependency_overrides[get_post_aggregation_service] = lambda: post_service
    app.dependency_overrides[get_tag_hot_service] = lambda: tag_service
    app.state.test_services = {
        "db": db,
        "topic_service": topic_service,
        "post_service": post_service,
        "tag_service": tag_service,
    }

    topic_route = None
    for route in list(app.router.routes):
        if getattr(route, "path", None) == "/api/v1/posts/topics/{topic}":
            topic_route = route
            break
    if topic_route:
        app.router.routes.remove(topic_route)
        app.router.routes.append(topic_route)

    return app


@pytest.fixture
def client(test_app):
    """测试客户端"""
    return TestClient(test_app)


def _build_topic_service_responses():
    hot_topics = [
        HotTopic(
            topic="Python",
            post_count=150,
            hot_score=89.5,
            trend_score=12.3,
            rank=1,
            rank_change=0,
        ),
        HotTopic(
            topic="JavaScript",
            post_count=120,
            hot_score=76.2,
            trend_score=8.9,
            rank=2,
            rank_change=1,
        ),
        HotTopic(
            topic="AI",
            post_count=95,
            hot_score=68.7,
            trend_score=15.6,
            rank=3,
            rank_change=-1,
        ),
    ]

    trending_topics = [
        TrendingTopic(
            topic="ChatGPT",
            post_count=45,
            trend_score=25.8,
            growth_rate=2.5,
            rank=1,
        ),
        TrendingTopic(
            topic="Vue3",
            post_count=38,
            trend_score=18.9,
            growth_rate=1.8,
            rank=2,
        ),
    ]

    topic_detail = TopicDetail(
        topic="Python",
        post_count=150,
        total_likes=1250,
        total_comments=890,
        total_reposts=234,
        total_views=5670,
        hot_score=89.5,
        trend_score=12.3,
        last_post_at=datetime.utcnow() - timedelta(hours=2),
        peak_time=datetime.utcnow() - timedelta(days=1),
        created_at=datetime.utcnow() - timedelta(days=30),
    )

    return (
        HotTopicsResponse(
            topics=hot_topics,
            total=len(hot_topics),
            page=1,
            size=20,
            has_next=False,
        ),
        TrendingTopicsResponse(
            topics=trending_topics,
            total=len(trending_topics),
            page=1,
            size=20,
            has_next=False,
        ),
        topic_detail,
    )


@pytest.fixture
def topic_service(client):
    service: AsyncMock = client.app.state.test_services["topic_service"]
    service.reset_mock()
    hot_resp, trending_resp, detail_resp = _build_topic_service_responses()
    service.get_hot_topics.return_value = hot_resp
    service.get_trending_topics.return_value = trending_resp
    service.get_topic_detail.return_value = detail_resp
    return service


def _build_post_out(post_id: int = 1, topic: str = "Python") -> PostOut:
    """构造用于话题列表接口的 PostOut 数据"""
    now = datetime.utcnow()
    author = UserAggregated(
        id=post_id,
        username=f"user{post_id}",
        role_id=1,
        nickname=f"测试用户{post_id}",
        description=None,
        email=f"user{post_id}@example.com",
        avatar=None,
        cover=None,
        is_active=True,
        is_superuser=False,
        last_login=now,
        created_at=now,
        updated_at=now,
        likes_privacy_settings=3,
        favorites_privacy_settings=3,
        wechat_openid=None,
        wechat_unionid=None,
        login_type="password",
        stats=None,
    )
    stats = PostStats(
        content_id=post_id,
        like_count=10,
        comment_count=5,
        repost_count=1,
        view_count=100,
        is_liked_by_user=False,
        is_reposted_by_user=False,
        is_followed_author=False,
    )
    return PostOut(
        id=post_id,
        author_id=author.id,
        content=f"帖子内容{post_id}",
        post_type=PostType.TEXT,
        status=PostStatus.PUBLISHED,
        visibility=PostVisibility.PUBLIC,
        topic=topic,
        location=None,
        is_pinned=False,
        is_hot=False,
        hot_score=0,
        created_at=now,
        updated_at=now,
        author=author,
        stats=stats,
        media=[],
        mentions=[],
        review=None,
        original_post_id=None,
        original_post=None,
        repost_comment=None,
        link_url=None,
        link_title=None,
        link_description=None,
        link_image=None,
        poll_options=None,
        poll_expires_at=None,
        poll_multiple_choice=None,
        poll_votes=None,
        user_poll_votes=None,
    )


@pytest.fixture
def post_service(client):
    service: AsyncMock = client.app.state.test_services["post_service"]
    service.reset_mock()
    service.get_posts.return_value = [_build_post_out(1, "Python"), _build_post_out(2, "Python")]
    return service


@pytest.fixture
def tag_service(client):
    service: AsyncMock = client.app.state.test_services["tag_service"]
    service.reset_mock()
    service.get_hot_tags.return_value = []
    return service


class TestHotTopicsAPI:
    """热门话题API测试"""

    def test_get_hot_topics_success(self, client, topic_service):
        """测试获取热门话题成功"""
        response = client.get("/api/v1/posts/topics/hot")
        
        assert response.status_code == 200
        data = response.json()
        
        assert "topics" in data
        assert "total" in data
        assert "page" in data
        assert "size" in data
        assert "has_next" in data
        
        topics = data["topics"]
        assert len(topics) == 3
        assert topics[0]["topic"] == "Python"
        assert topics[0]["rank"] == 1
        assert topics[0]["hot_score"] == 89.5

    def test_get_hot_topics_with_filters(self, client, topic_service):
        """测试带过滤条件的热门话题查询"""
        response = client.get(
            "/api/v1/posts/topics/hot",
            params={
                "min_post_count": 100,
                "days": 7,
                "page": 1,
                "size": 10,
            }
        )
        
        assert response.status_code == 200
        
        # 验证服务被正确调用
        topic_service.get_hot_topics.assert_called_once()
        call_args = topic_service.get_hot_topics.call_args
        query_params = call_args[1]["query_params"]
        
        assert query_params.min_post_count == 100
        assert query_params.days == 7
        assert query_params.page == 1
        assert query_params.size == 10

    def test_get_hot_topics_invalid_params(self, client, topic_service):
        """测试无效参数"""
        # 测试无效的页码
        response = client.get("/api/v1/posts/topics/hot?page=0")
        assert response.status_code == 422

        # 测试无效的大小
        response = client.get("/api/v1/posts/topics/hot?size=0")
        assert response.status_code == 422
        
        # 测试无效的天数
        response = client.get("/api/v1/posts/topics/hot?days=0")
        assert response.status_code == 422


class TestTrendingTopicsAPI:
    """趋势话题API测试"""

    def test_get_trending_topics_success(self, client, topic_service):
        """测试获取趋势话题成功"""
        response = client.get("/api/v1/posts/topics/trending")
        
        assert response.status_code == 200
        data = response.json()
        
        assert "topics" in data
        topics = data["topics"]
        assert len(topics) == 2
        assert topics[0]["topic"] == "ChatGPT"
        assert topics[0]["growth_rate"] == 2.5

    def test_get_trending_topics_with_pagination(self, client, topic_service):
        """测试趋势话题分页"""
        response = client.get(
            "/api/v1/posts/topics/trending",
            params={"page": 2, "size": 5}
        )
        
        assert response.status_code == 200
        
        # 验证服务调用参数
        call_args = topic_service.get_trending_topics.call_args
        query_params = call_args[1]["query_params"]
        assert query_params.page == 2
        assert query_params.size == 5


class TestTopicDetailAPI:
    """话题详情API测试"""

    def test_get_topic_detail_success(self, client, topic_service):
        """测试获取话题详情成功"""
        response = client.get("/api/v1/posts/topics/Python/detail")
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["topic"] == "Python"
        assert data["post_count"] == 150
        assert data["total_likes"] == 1250
        assert data["hot_score"] == 89.5

    def test_get_topic_detail_not_found(self, client, topic_service):
        """测试话题不存在"""
        topic_service.get_topic_detail.return_value = None
        
        response = client.get("/api/v1/posts/topics/NonExistentTopic/detail")
        
        assert response.status_code == 404
        assert "话题不存在" in response.json()["detail"]


class TestTopicPostsAPI:
    """话题帖子列表API测试"""

    def test_get_topic_posts_success(self, client, post_service):
        with patch(
            "app.api.endpoints.posts.crud.post.count_posts_with_filters",
            new_callable=AsyncMock,
        ) as mock_count:
            mock_count.return_value = 5

            response = client.get(
                "/api/v1/posts/topics/Python",
                params={"page": 1, "size": 2},
            )

            assert response.status_code == 200
            payload = response.json()
            assert payload["total_count"] == 5
            assert len(payload["posts"]) == 2
            assert payload["posts"][0]["topic"] == "Python"

            post_service.get_posts.assert_awaited_once()
            query_params = post_service.get_posts.await_args.kwargs["query_params"]
            assert query_params.topic == "Python"
            assert query_params.page == 1
            assert query_params.size == 2

            mock_count.assert_awaited_once()
            assert mock_count.await_args.kwargs["topic"] == "Python"

    def test_get_topic_posts_empty(self, client, post_service):
        post_service.get_posts.return_value = []
        with patch(
            "app.api.endpoints.posts.crud.post.count_posts_with_filters",
            new_callable=AsyncMock,
        ) as mock_count:
            mock_count.return_value = 0

            response = client.get(
                "/api/v1/posts/topics/Unknown",
                params={"page": 1, "size": 10},
            )

            assert response.status_code == 200
            payload = response.json()
            assert payload["posts"] == []
            assert payload["total_count"] == 0
            assert payload["has_next"] is False
            assert payload["has_previous"] is False

            post_service.get_posts.assert_awaited_once()
            mock_count.assert_awaited_once()


class TestHotTagsAPI:
    """热门标签API测试"""

    def test_get_hot_tags_success(self, client, tag_service):
        """测试获取热门标签成功"""
        # Mock标签数据
        mock_tags = [
            MagicMock(model_dump=MagicMock(return_value={
                "tag_id": 1,
                "name": "Python",
                "content_type": "post",
                "score": 10.0,
                "window_score": 5.0,
                "unique_users": 3,
                "last_event_at": None,
            })),
            MagicMock(model_dump=MagicMock(return_value={
                "tag_id": 2,
                "name": "JavaScript",
                "content_type": "post",
                "score": 9.0,
                "window_score": 4.0,
                "unique_users": 2,
                "last_event_at": None,
            })),
        ]
        tag_service.get_hot_tags.return_value = mock_tags
        
        response = client.get("/api/v1/posts/tags/hot")
        
        assert response.status_code == 200
        data = response.json()
        
        assert len(data) == 2
        assert data[0]["name"] == "Python"
        assert data[0]["content_type"] == "post"
        tag_service.get_hot_tags.assert_awaited_once()

    def test_get_hot_tags_with_limit(self, client, tag_service):
        """测试限制返回数量"""
        mock_tags = [
            MagicMock(model_dump=MagicMock(return_value={
                "tag_id": i,
                "name": f"Tag{i}",
                "content_type": "post",
                "score": 1.0,
                "window_score": 0.5,
                "unique_users": 1,
                "last_event_at": None,
            }))
            for i in range(10)
        ]
        tag_service.get_hot_tags.return_value = mock_tags
        
        response = client.get("/api/v1/posts/tags/hot?limit=5")
        
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 10
        tag_service.get_hot_tags.assert_awaited_once()
        _, kwargs = tag_service.get_hot_tags.await_args
        assert kwargs["limit"] == 5

    def test_get_hot_tags_different_content_type(self, client, tag_service):
        """测试不同内容类型的标签"""
        tag_service.get_hot_tags.return_value = []
        
        response = client.get("/api/v1/posts/tags/hot?content_type=article")
        
        assert response.status_code == 200
        tag_service.get_hot_tags.assert_awaited_once()
        _, kwargs = tag_service.get_hot_tags.await_args
        assert kwargs["content_type"] == "article"
