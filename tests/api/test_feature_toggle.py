from __future__ import annotations

from unittest.mock import AsyncMock

import pytest
from fastapi import FastAP<PERSON>
from fastapi.testclient import TestClient

from app.api import deps
from app.api.endpoints import admin_config as admin_config_endpoint
from app.schemas.admin_config import FeatureToggle, FeatureToggleUpdateRequest


@pytest.fixture
def test_app() -> FastAPI:
    app = FastAPI()
    app.include_router(admin_config_endpoint.router, prefix="/api/admin/config", tags=["admin-config"])
    return app


@pytest.fixture
def client(test_app: FastAPI) -> TestClient:
    return TestClient(test_app)


@pytest.fixture(autouse=True)
def override_permissions(test_app: FastAPI):
    async def allow():
        return {"id": 1}

    read_guard = admin_config_endpoint.admin_config_read_guard
    write_guard = admin_config_endpoint.admin_config_write_guard
    test_app.dependency_overrides[read_guard] = allow
    test_app.dependency_overrides[write_guard] = allow
    yield
    test_app.dependency_overrides.pop(read_guard, None)
    test_app.dependency_overrides.pop(write_guard, None)


@pytest.fixture(autouse=True)
def override_db(test_app: FastAPI):
    async def _db():
        yield AsyncMock()

    test_app.dependency_overrides[deps.get_db] = _db
    yield
    test_app.dependency_overrides.pop(deps.get_db, None)


@pytest.fixture
def mock_service(test_app: FastAPI) -> AsyncMock:
    service = AsyncMock()
    override = admin_config_endpoint.get_config_service
    test_app.dependency_overrides[override] = lambda: service
    try:
        yield service
    finally:
        test_app.dependency_overrides.pop(override, None)


def test_list_feature_toggles(client: TestClient, mock_service: AsyncMock):
    mock_service.list_feature_toggles.return_value = [
        FeatureToggle(
            id=1,
            name="user_analytics",
            is_enabled=True,
            description="desc",
            scope="global",
            config={},
            updated_at=None,
            updated_by=None,
        )
    ]

    response = client.get("/api/admin/config/features")
    assert response.status_code == 200
    payload = response.json()
    assert payload["data"]["features"][0]["name"] == "user_analytics"
    mock_service.list_feature_toggles.assert_awaited_once()


def test_update_feature_toggle(client: TestClient, mock_service: AsyncMock):
    mock_service.update_feature_toggle.return_value = FeatureToggle(
        id=1,
        name="user_analytics",
        is_enabled=False,
        description="desc",
        scope="global",
        config={},
        updated_at=None,
        updated_by=None,
    )

    response = client.put(
        "/api/admin/config/features/user_analytics",
        json=FeatureToggleUpdateRequest(is_enabled=False, reason="test").model_dump(mode="json"),
    )

    assert response.status_code == 200
    payload = response.json()
    assert payload["data"]["name"] == "user_analytics"
    mock_service.update_feature_toggle.assert_awaited_once()
