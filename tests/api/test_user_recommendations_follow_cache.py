"""定向测试推荐接口的关注状态与缓存失效逻辑"""

from datetime import datetime, timezone

import pytest
from sqlalchemy import insert
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker

from app import models, schemas
from app.api.endpoints import recommendations
from app.db.session import Base
from app.models import load_all_models


@pytest.fixture
def event_loop():
    """为 asyncio 测试提供事件循环"""
    import asyncio

    loop = asyncio.new_event_loop()
    yield loop
    loop.close()


async def _create_async_session(*tables) -> AsyncSession:
    """创建内存 SQLite 异步会话并初始化指定表"""

    load_all_models()
    engine = create_async_engine("sqlite+aiosqlite:///:memory:", future=True)

    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all, tables=tables)

    async_session_factory = sessionmaker(
        engine,
        class_=AsyncSession,
        expire_on_commit=False,
    )
    return async_session_factory()  # type: ignore[return-value]


@pytest.mark.asyncio
async def test_get_user_recommendations_contains_follow_state():
    """验证推荐列表返回正确的关注状态"""

    async_session = await _create_async_session(
        models.User.__table__,
        models.user_follow,
    )

    try:
        # 准备关注数据：当前用户1关注用户2
        await async_session.execute(
            insert(models.user_follow).values(
                follower_id=1,
                followed_id=2,
                created_at=datetime.now(timezone.utc),
            )
        )
        await async_session.commit()

        # 构造推荐结果
        recommendation_response = schemas.PaginatedRecommendationResponse(
            items=[
                schemas.RecommendationItem(
                    content_type="user",
                    content_id=2,
                    score=0.95,
                    reason="test",
                    algorithm_type="hybrid",
                ),
                schemas.RecommendationItem(
                    content_type="user",
                    content_id=3,
                    score=0.85,
                    reason="test",
                    algorithm_type="hybrid",
                ),
            ],
            total_count=2,
            page=1,
            page_size=20,
            has_next=False,
            algorithm_type="hybrid",
        )

        class StubRecommendationService:
            async def get_user_recommendations(self, **_: object) -> schemas.PaginatedRecommendationResponse:
                return recommendation_response

        class StubAggregationService:
            async def get_users_by_ids(self, db: AsyncSession, user_ids: list[int]):
                now = datetime.now(timezone.utc)
                result: dict[int, schemas.UserAggregated] = {}
                for uid in user_ids:
                    result[uid] = schemas.UserAggregated(
                        id=uid,
                        username=f"user{uid}",
                        role_id=1,
                        nickname=f"用户{uid}",
                        description="测试",
                        email=f"user{uid}@test.com",
                        avatar=None,
                        cover=None,
                        is_active=True,
                        is_superuser=False,
                        last_login=None,
                        created_at=now,
                        updated_at=now,
                        likes_privacy_settings=3,
                        favorites_privacy_settings=3,
                        wechat_openid=None,
                        wechat_unionid=None,
                        login_type="password",
                        stats=schemas.UserStats(user_id=uid),
                    )
                return result

        current_user = models.User(id=1, username="user1")

        response = await recommendations.get_user_recommendations(
            db=async_session,
            algorithm_type=None,
            page=1,
            page_size=20,
            current_user=current_user,
            user_recommendation_service=StubRecommendationService(),
            user_aggregation_service=StubAggregationService(),
        )

        assert response.items[0].is_following is True
        assert response.items[1].is_following is False

    finally:
        engine = async_session.bind
        await async_session.close()
        if engine is not None:
            await engine.dispose()


@pytest.mark.asyncio
async def test_submit_feedback_triggers_cache_invalidation(monkeypatch):
    """验证关注反馈成功后会触发推荐缓存失效"""

    async_session = await _create_async_session(
        models.User.__table__,
        models.user_follow,
    )

    try:
        current_user = models.User(id=1, username="user1")
        target_user = models.User(id=2, username="user2")

        # Patch CRUD 操作
        async def mock_get(db: AsyncSession, id: int):
            return current_user if id == current_user.id else target_user

        async def mock_follow(db: AsyncSession, follower: models.User, followed: models.User):
            await db.execute(
                insert(models.user_follow).values(
                    follower_id=follower.id,
                    followed_id=followed.id,
                    created_at=datetime.now(timezone.utc),
                )
            )
            await db.commit()

        async def mock_create_log(db: AsyncSession, obj_in: schemas.RecommendationLogCreate, user_id: int):
            return None

        monkeypatch.setattr("app.crud.user.get", mock_get)
        monkeypatch.setattr("app.crud.user.follow", mock_follow)
        monkeypatch.setattr("app.crud.recommendation_log.create_with_user", mock_create_log)

        class StubTracker:
            def __init__(self) -> None:
                self.calls = 0

            async def track_follow_conversion(self, **_: object) -> None:
                self.calls += 1

        tracker = StubTracker()

        class StubCacheService:
            def __init__(self) -> None:
                self.invalidated_user_ids: list[int] = []

            async def invalidate_user_recommendations_cache(self, user_id: int) -> None:
                self.invalidated_user_ids.append(user_id)

        cache_service = StubCacheService()

        monkeypatch.setattr(
            "app.services.user_recommendation_tracking_service.UserRecommendationTrackingService",
            lambda: tracker,
        )

        feedback = schemas.UserRecommendationFeedback(
            user_id=2,
            feedback_type="follow",
            algorithm_type="hybrid",
        )

        result = await recommendations.submit_user_recommendation_feedback(
            db=async_session,
            feedback=feedback,
            current_user=current_user,
            recommendation_cache_service=cache_service,
        )

        assert result == {"message": "反馈提交成功"}
        assert cache_service.invalidated_user_ids == [current_user.id]
        assert tracker.calls == 1

    finally:
        engine = async_session.bind
        await async_session.close()
        if engine is not None:
            await engine.dispose()
