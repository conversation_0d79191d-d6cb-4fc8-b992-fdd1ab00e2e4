from datetime import datetime, timezone
from types import SimpleNamespace

import pytest
from fastapi import FastAPI
from fastapi.testclient import TestClient
from sqlalchemy.ext.asyncio import AsyncSession

from app import schemas
from app.api import deps
from app.api.endpoints.recommendations import router
from app.services.service_factory import (
    get_user_aggregation_service,
    get_user_recommendation_service,
)


class _FakeRecommendationItem:
    def __init__(self, *, content_id: int, score: float, reason: str, algorithm_type: str) -> None:
        self.content_id = content_id
        self.score = score
        self.reason = reason
        self.algorithm_type = algorithm_type


class _FakeRecommendationResponse:
    def __init__(self, *, items, total_count, page, page_size, has_next, algorithm_type) -> None:
        self.items = items
        self.total_count = total_count
        self.page = page
        self.page_size = page_size
        self.has_next = has_next
        self.algorithm_type = algorithm_type


class _FakeUserRecommendationService:
    async def get_user_recommendations(
        self,
        db: AsyncSession,
        user_id: int,
        page: int = 1,
        page_size: int = 20,
        algorithm_type: str | None = None,
    ) -> _FakeRecommendationResponse:
        algo = algorithm_type or "hybrid"
        items = [
            _FakeRecommendationItem(
                content_id=uid,
                score=1.0,
                reason="stub",
                algorithm_type=algo,
            )
            for uid in (2, 3)
        ]
        return _FakeRecommendationResponse(
            items=items,
            total_count=len(items),
            page=page,
            page_size=page_size,
            has_next=False,
            algorithm_type=algo,
        )


class _FakeUserAggregationService:
    async def get_users_by_ids(
        self, db: AsyncSession, user_ids: list[int]
    ) -> dict[int, schemas.UserAggregated]:
        now = datetime.now(timezone.utc)
        aggregated: dict[int, schemas.UserAggregated] = {}
        for uid in user_ids:
            base_payload = {
                "id": uid,
                "username": f"user{uid}",
                "role_id": 1,
                "nickname": f"用户{uid}",
                "description": "stub user",
                "email": f"user{uid}@example.com",
                "avatar": None,
                "cover": None,
                "is_active": True,
                "is_superuser": False,
                "last_login": None,
                "created_at": now,
                "updated_at": now,
                "likes_privacy_settings": 3,
                "favorites_privacy_settings": 3,
                "wechat_openid": None,
                "wechat_unionid": None,
                "login_type": "password",
                "stats": schemas.UserStats(user_id=uid),
            }
            aggregated[uid] = schemas.UserAggregated(**base_payload)
        return aggregated


class _DummyAsyncResult:
    def mappings(self):
        return self

    def all(self):
        return []


class _DummyAsyncSession:
    async def execute(self, *args, **kwargs):  # noqa: ARG002
        return _DummyAsyncResult()


@pytest.fixture
def injected_app(monkeypatch) -> FastAPI:
    app = FastAPI()
    app.include_router(router, prefix="/api/v1/recommendations")

    async def _get_db():
        yield _DummyAsyncSession()

    async def _get_current_user():
        return SimpleNamespace(id=1)

    app.dependency_overrides[deps.get_db] = _get_db
    app.dependency_overrides[deps.get_current_user] = _get_current_user
    app.dependency_overrides[get_user_recommendation_service] = lambda: _FakeUserRecommendationService()
    app.dependency_overrides[get_user_aggregation_service] = lambda: _FakeUserAggregationService()

    return app


def test_user_recommendations_endpoint_uses_injected_services(injected_app: FastAPI) -> None:
    client = TestClient(injected_app)

    response = client.get("/api/v1/recommendations/users")

    assert response.status_code == 200
    payload = response.json()
    data = payload.get("data", payload)
    assert data.get("items")
