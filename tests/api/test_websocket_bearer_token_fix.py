"""
WebSocket Bearer Token 剥离修复测试
直接测试 token 处理函数，避免复杂的数据库模拟
"""
import pytest
from unittest.mock import AsyncMock, MagicMock


class DummyWebSocket:
    """模拟 WebSocket 对象"""
    def __init__(self, headers=None, cookies=None):
        self.headers = headers or {}
        self.cookies = cookies or {}
        self.closed = False
        
    async def close(self, code=None, reason=None):
        self.closed = True


@pytest.mark.asyncio
async def test_bearer_token_stripping_in_query_parameter():
    """测试 Query 参数中的 Bearer token 剥离"""
    from app.api.deps import get_token_from_websocket_protocols_or_query
    
    # 创建模拟 WebSocket 对象
    websocket = DummyWebSocket()
    
    # 测试带 Bearer 前缀的 token
    raw_token = "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9"
    expected_token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9"
    
    # 调用函数
    result = await get_token_from_websocket_protocols_or_query(websocket, token=raw_token)
    
    # 验证结果
    assert result == expected_token


@pytest.mark.asyncio
async def test_bearer_token_stripping_in_cookie():
    """测试 Cookie 中的 Bearer token 剥离"""
    from app.api.deps import get_token_from_websocket_protocols_or_query
    
    # 创建模拟 WebSocket 对象，带有包含 Bearer token 的 cookie
    websocket = DummyWebSocket(cookies={"access_token": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9"})
    
    # 调用函数（不提供 Query 参数，强制使用 Cookie）
    result = await get_token_from_websocket_protocols_or_query(websocket, token=None)
    
    # 验证结果
    assert result == "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9"


@pytest.mark.asyncio
async def test_token_without_bearer_prefix_unchanged():
    """测试不带 Bearer 前缀的 token 保持不变"""
    from app.api.deps import get_token_from_websocket_protocols_or_query
    
    # 创建模拟 WebSocket 对象
    websocket = DummyWebSocket()
    
    # 测试不带 Bearer 前缀的 token
    raw_token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9"
    
    # 调用函数
    result = await get_token_from_websocket_protocols_or_query(websocket, token=raw_token)
    
    # 验证结果
    assert result == raw_token


@pytest.mark.asyncio
async def test_websocket_auth_with_stripped_bearer_token():
    """测试完整的 WebSocket 认证流程，使用带 Bearer 前缀的 token"""
    from app.api.deps import (
        get_current_user_websocket,
        get_token_from_websocket_protocols_or_query,
    )
    from app.models.user import User
    from datetime import datetime
    
    # 创建模拟 WebSocket 对象
    websocket = DummyWebSocket()
    websocket.close = AsyncMock()
    
    # 创建模拟用户
    user = User(
        id=123,
        username="testuser",
        email="<EMAIL>",
        nickname="测试用户",
        role_id=1,
        is_active=True,
        is_superuser=False,
        created_at=datetime.now(),
        updated_at=datetime.now()
    )
    
    # 创建模拟数据库
    mock_db = AsyncMock()
    mock_result = AsyncMock()
    mock_result.scalar_one_or_none = MagicMock(return_value=user)
    mock_db.execute = AsyncMock(return_value=mock_result)
    
    # 创建模拟 token 服务
    mock_token_service = AsyncMock()
    mock_token_service.verify_token.return_value = {"sub": user.username}
    
    # 测试带 Bearer 前缀的 token
    bearer_token = "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9"
    
    # 调用认证函数
    stripped_token = await get_token_from_websocket_protocols_or_query(
        websocket=websocket,
        token=bearer_token,
    )

    result = await get_current_user_websocket(
        websocket=websocket,
        token=stripped_token,
        db=mock_db,
        token_service=mock_token_service
    )
    
    # 验证结果
    assert result == user
    assert websocket.close.call_count == 0  # 连接没有被关闭
    
    # 验证 token 服务被调用时使用的是剥离后的 token
    mock_token_service.verify_token.assert_called_once_with("eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9")


@pytest.mark.asyncio
async def test_websocket_auth_with_bearer_token_in_cookie():
    """测试使用 Cookie 中的 Bearer token 进行 WebSocket 认证"""
    from app.api.deps import (
        get_current_user_websocket,
        get_token_from_websocket_protocols_or_query,
    )
    from app.models.user import User
    from datetime import datetime
    
    # 创建模拟 WebSocket 对象，带有包含 Bearer token 的 cookie
    websocket = DummyWebSocket(cookies={"access_token": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9"})
    websocket.close = AsyncMock()
    
    # 创建模拟用户
    user = User(
        id=123,
        username="testuser",
        email="<EMAIL>",
        nickname="测试用户",
        role_id=1,
        is_active=True,
        is_superuser=False,
        created_at=datetime.now(),
        updated_at=datetime.now()
    )
    
    # 创建模拟数据库
    mock_db = AsyncMock()
    mock_result = AsyncMock()
    mock_result.scalar_one_or_none = MagicMock(return_value=user)
    mock_db.execute = AsyncMock(return_value=mock_result)
    
    # 创建模拟 token 服务
    mock_token_service = AsyncMock()
    mock_token_service.verify_token.return_value = {"sub": user.username}
    
    # 调用认证函数（不提供 Query 参数，强制使用 Cookie）
    stripped_token = await get_token_from_websocket_protocols_or_query(
        websocket=websocket,
        token=None,
    )

    result = await get_current_user_websocket(
        websocket=websocket,
        token=stripped_token,
        db=mock_db,
        token_service=mock_token_service
    )
    
    # 验证结果
    assert result == user
    assert websocket.close.call_count == 0  # 连接没有被关闭
    
    # 验证 token 服务被调用时使用的是剥离后的 token
    mock_token_service.verify_token.assert_called_once_with("eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9")