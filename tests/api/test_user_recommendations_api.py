"""
用户推荐API端点集成测试

测试用户推荐相关的API端点功能
"""

import json
from datetime import datetime, timezone
from typing import AsyncGenerator
from unittest.mock import AsyncMock, patch

import pytest
import pytest_asyncio
from fastapi import FastAPI
from fastapi.testclient import TestClient
from sqlalchemy import BigInteger
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.ext.compiler import compiles
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker

from app import models, schemas
from app.api.deps import get_current_user, get_db
from app.api.endpoints.recommendations import router
from app.core.response_wrapper import ResponseCode
from app.db.session import Base
from app.models import load_all_models
from app.services.service_factory import (
    get_user_aggregation_service,
    get_user_recommendation_service,
)
from tests.utils.test_helpers import MockRedis, TestUserFactory, ResponseValidator


@compiles(JSONB, "sqlite")
def compile_jsonb(element, compiler, **kw):  # type: ignore[override]
    return "JSON"


@compiles(BigInteger, "sqlite")
def compile_bigint(element, compiler, **kw):  # type: ignore[override]
    return "INTEGER"


@pytest_asyncio.fixture
async def async_session() -> AsyncGenerator[AsyncSession, None]:
    """提供带有加载模型的异步内存SQLite会话"""
    load_all_models()
    engine = create_async_engine("sqlite+aiosqlite:///:memory:", future=True)
    
    async with engine.begin() as conn:
        await conn.run_sync(
            Base.metadata.create_all,
            tables=[
                models.User.__table__,
                models.UserProfile.__table__,
                models.UserInteraction.__table__,
                models.UserFollow.__table__,
                models.UserInterest.__table__,
                models.Article.__table__,
                models.Video.__table__,
                models.RecommendationLog.__table__,
            ],
        )

    async_session_factory = sessionmaker(
        engine,
        class_=AsyncSession,
        expire_on_commit=False,
    )

    async with async_session_factory() as session:  # type: ignore[call-arg]
        yield session
    await engine.dispose()


@pytest.fixture
def mock_redis() -> MockRedis:
    """Mock Redis客户端"""
    return MockRedis()


@pytest.fixture
def test_app(async_session: AsyncSession, mock_redis: MockRedis) -> FastAPI:
    """创建测试应用"""
    app = FastAPI()
    app.include_router(router, prefix="/api/v1/recommendations")
    
    # Mock依赖
    async def mock_get_db():
        yield async_session
    
    async def mock_get_current_user():
        return TestUserFactory.create_user(user_id=1, username="testuser")
    
    app.dependency_overrides[get_db] = mock_get_db
    app.dependency_overrides[get_current_user] = mock_get_current_user

    class DummyUserRecommendationService:
        async def get_user_recommendations(
            self,
            db: AsyncSession,
            user_id: int,
            page: int = 1,
            page_size: int = 20,
            algorithm_type: str | None = None,
        ) -> schemas.PaginatedRecommendationResponse:
            items = [
                schemas.RecommendationItem(
                    content_type="article",
                    content_id=uid,
                    score=0.8 + idx * 0.1,
                    reason="test",
                )
                for idx, uid in enumerate(range(2, 4))
            ]
            return schemas.PaginatedRecommendationResponse(
                items=items,
                total_count=len(items),
                page=page,
                page_size=page_size,
                has_next=False,
                algorithm_type=algorithm_type or "hybrid",
            )

    class DummyUserAggregationService:
        async def get_users_by_ids(
            self, db: AsyncSession, user_ids: list[int]
        ) -> dict[int, schemas.UserAggregated]:
            now = datetime.now(timezone.utc)
            aggregated: dict[int, schemas.UserAggregated] = {}
            for uid in user_ids:
                base_payload = {
                    "id": uid,
                    "username": f"user{uid}",
                    "role_id": 1,
                    "nickname": f"用户{uid}",
                    "description": "测试用户",
                    "email": f"user{uid}@test.com",
                    "avatar": None,
                    "cover": None,
                    "is_active": True,
                    "is_superuser": False,
                    "last_login": None,
                    "created_at": now,
                    "updated_at": now,
                    "likes_privacy_settings": 3,
                    "favorites_privacy_settings": 3,
                    "wechat_openid": None,
                    "wechat_unionid": None,
                    "login_type": "password",
                    "stats": schemas.UserStats(user_id=uid),
                }
                aggregated[uid] = schemas.UserAggregated(**base_payload)
            return aggregated

    app.dependency_overrides[get_user_recommendation_service] = (
        lambda: DummyUserRecommendationService()
    )
    app.dependency_overrides[get_user_aggregation_service] = (
        lambda: DummyUserAggregationService()
    )
    
    return app


@pytest.fixture
def client(test_app: FastAPI) -> TestClient:
    """测试客户端"""
    return TestClient(test_app)


@pytest_asyncio.fixture
async def sample_users(async_session: AsyncSession) -> list[models.User]:
    """创建示例用户数据"""
    users = []
    for i in range(1, 6):
        user = models.User(
            id=i,
            username=f"user{i}",
            email=f"user{i}@test.com",
            nickname=f"用户{i}",
            is_active=True,
            created_at=datetime.now(timezone.utc),
            updated_at=datetime.now(timezone.utc),
        )
        async_session.add(user)
        users.append(user)
    
    await async_session.commit()
    return users


@pytest_asyncio.fixture
async def sample_user_interests(
    async_session: AsyncSession, sample_users: list[models.User]
) -> None:
    """创建示例用户兴趣数据"""
    interests = [
        models.UserInterest(user_id=1, interest_tag="游戏", interest_weight=0.8),
        models.UserInterest(user_id=1, interest_tag="科技", interest_weight=0.6),
        models.UserInterest(user_id=2, interest_tag="游戏", interest_weight=0.9),
        models.UserInterest(user_id=2, interest_tag="动漫", interest_weight=0.7),
        models.UserInterest(user_id=3, interest_tag="科技", interest_weight=0.8),
        models.UserInterest(user_id=3, interest_tag="编程", interest_weight=0.9),
    ]
    
    for interest in interests:
        async_session.add(interest)
    
    await async_session.commit()


class TestUserRecommendationsAPI:
    """用户推荐API测试类"""

    def test_get_user_recommendations_success(
        self,
        client: TestClient,
        sample_users: list[models.User],
        sample_user_interests: None,
    ):
        """测试获取用户推荐成功"""
        response = client.get("/api/v1/recommendations/users")

        assert response.status_code == 200
        data = response.json()

        # 验证响应格式
        assert ResponseValidator.validate_success_response(data)
        assert "data" in data

        # 验证推荐数据结构
        recommendation_data = data["data"]
        assert "items" in recommendation_data
        assert "total_count" in recommendation_data
        assert "page" in recommendation_data
        assert "page_size" in recommendation_data
        assert "has_next" in recommendation_data
        assert "algorithm_type" in recommendation_data

    def test_get_user_recommendations_with_algorithm_type(
        self,
        client: TestClient,
        sample_users: list[models.User],
    ):
        """测试指定算法类型的用户推荐"""
        response = client.get(
            "/api/v1/recommendations/users",
            params={"algorithm_type": "collaborative", "page": 1, "page_size": 5},
        )

        assert response.status_code == 200
        data = response.json()
        assert ResponseValidator.validate_success_response(data)

    def test_get_user_recommendations_pagination(
        self,
        client: TestClient,
        sample_users: list[models.User],
    ):
        """测试用户推荐分页"""
        # 测试第一页
        response1 = client.get(
            "/api/v1/recommendations/users",
            params={"page": 1, "page_size": 2},
        )
        assert response1.status_code == 200

        # 测试第二页
        response2 = client.get(
            "/api/v1/recommendations/users",
            params={"page": 2, "page_size": 2},
        )
        assert response2.status_code == 200

    def test_get_user_recommendations_invalid_algorithm(
        self,
        client: TestClient,
        sample_users: list[models.User],
    ):
        """测试无效算法类型"""
        response = client.get(
            "/api/v1/recommendations/users",
            params={"algorithm_type": "invalid_algorithm"}
        )
        
        assert response.status_code == 400
        data = response.json()
        assert ResponseValidator.validate_error_response(data)

    def test_submit_user_recommendation_feedback_success(
        self,
        client: TestClient,
        sample_users: list[models.User],
    ):
        """测试提交用户推荐反馈成功"""
        feedback_data = {
            "recommended_user_id": 2,
            "feedback_type": "follow",
            "algorithm_type": "collaborative"
        }
        
        with patch('app.services.user_recommendation_service.UserRecommendationService'):
            response = client.post(
                "/api/v1/recommendations/users/feedback",
                json=feedback_data
            )
            
            assert response.status_code == 200
            data = response.json()
            assert ResponseValidator.validate_success_response(data)

    def test_submit_user_recommendation_feedback_invalid_type(
        self,
        client: TestClient,
        sample_users: list[models.User],
    ):
        """测试提交无效反馈类型"""
        feedback_data = {
            "recommended_user_id": 2,
            "feedback_type": "invalid_type",  # 无效的反馈类型
            "algorithm_type": "collaborative"
        }
        
        response = client.post(
            "/api/v1/recommendations/users/feedback",
            json=feedback_data
        )
        
        assert response.status_code == 422  # 验证错误

    def test_get_user_similarity_success(
        self,
        client: TestClient,
        sample_users: list[models.User],
        sample_user_interests: None,
    ):
        """测试获取用户相似度成功"""
        with patch('app.services.user_similarity_service.UserSimilarityService'):
            response = client.get("/api/v1/recommendations/users/similarity/2")
            
            assert response.status_code == 200
            data = response.json()
            assert ResponseValidator.validate_success_response(data)
            
            # 验证相似度数据结构
            similarity_data = data["data"]
            assert "user_id" in similarity_data
            assert "target_user_id" in similarity_data
            assert "similarities" in similarity_data

    def test_get_user_similarity_with_types(
        self,
        client: TestClient,
        sample_users: list[models.User],
    ):
        """测试指定相似度类型"""
        with patch('app.services.user_similarity_service.UserSimilarityService'):
            response = client.get(
                "/api/v1/recommendations/users/similarity/2",
                params={"similarity_types": ["behavior", "interest"]}
            )
            
            assert response.status_code == 200
            data = response.json()
            assert ResponseValidator.validate_success_response(data)

    def test_get_user_similarity_nonexistent_user(
        self,
        client: TestClient,
        sample_users: list[models.User],
    ):
        """测试获取不存在用户的相似度"""
        with patch('app.services.user_similarity_service.UserSimilarityService'):
            response = client.get("/api/v1/recommendations/users/similarity/999")
            
            # 应该返回成功但相似度为0
            assert response.status_code == 200

    def test_track_recommendation_display_success(
        self,
        client: TestClient,
        sample_users: list[models.User],
    ):
        """测试追踪推荐展示成功"""
        display_data = {
            "recommended_user_ids": [2, 3, 4],
            "algorithm_type": "collaborative",
            "position": "homepage"
        }
        
        with patch('app.services.user_recommendation_tracking_service.UserRecommendationTrackingService'):
            response = client.post(
                "/api/v1/recommendations/users/track/display",
                json=display_data
            )
            
            assert response.status_code == 200
            data = response.json()
            assert data["message"] == "展示事件追踪成功"

    def test_track_recommendation_click_success(
        self,
        client: TestClient,
        sample_users: list[models.User],
    ):
        """测试追踪推荐点击成功"""
        click_data = {
            "clicked_user_id": 2,
            "algorithm_type": "content_based",
            "position": 1
        }
        
        with patch('app.services.user_recommendation_tracking_service.UserRecommendationTrackingService'):
            response = client.post(
                "/api/v1/recommendations/users/track/click",
                json=click_data
            )
            
            assert response.status_code == 200
            data = response.json()
            assert data["message"] == "点击事件追踪成功"

    def test_track_recommendation_missing_data(
        self,
        client: TestClient,
        sample_users: list[models.User],
    ):
        """测试追踪缺少必要数据"""
        # 缺少clicked_user_id
        click_data = {
            "algorithm_type": "content_based",
            "position": 1
        }
        
        response = client.post(
            "/api/v1/recommendations/users/track/click",
            json=click_data
        )
        
        # 应该处理缺少数据的情况
        assert response.status_code in [200, 400, 500]

    def test_get_recommendation_metrics_admin_only(
        self,
        client: TestClient,
        sample_users: list[models.User],
    ):
        """测试获取推荐指标（仅管理员）"""
        # 这个测试需要模拟管理员权限
        with patch('app.api.deps.require_permission') as mock_permission:
            mock_permission.return_value = lambda: TestUserFactory.create_admin_user()
            
            with patch('app.services.user_recommendation_tracking_service.UserRecommendationTrackingService'):
                response = client.get("/api/v1/recommendations/users/metrics")
                
                assert response.status_code == 200
                data = response.json()
                
                # 验证指标数据结构
                metrics_data = data.get("data", data)  # 可能直接返回数据或包装在data中
                assert "period" in metrics_data
                assert "total_displays" in metrics_data
                assert "total_clicks" in metrics_data
                assert "total_follows" in metrics_data

    def test_get_recommendation_metrics_with_params(
        self,
        client: TestClient,
        sample_users: list[models.User],
    ):
        """测试带参数的推荐指标"""
        with patch('app.api.deps.require_permission') as mock_permission:
            mock_permission.return_value = lambda: TestUserFactory.create_admin_user()
            
            with patch('app.services.user_recommendation_tracking_service.UserRecommendationTrackingService'):
                response = client.get(
                    "/api/v1/recommendations/users/metrics",
                    params={"algorithm_type": "collaborative", "days": 30}
                )
                
                assert response.status_code == 200

    def test_get_algorithm_comparison_admin_only(
        self,
        client: TestClient,
        sample_users: list[models.User],
    ):
        """测试获取算法对比（仅管理员）"""
        with patch('app.api.deps.require_permission') as mock_permission:
            mock_permission.return_value = lambda: TestUserFactory.create_admin_user()
            
            with patch('app.services.user_recommendation_tracking_service.UserRecommendationTrackingService'):
                response = client.get("/api/v1/recommendations/users/metrics/comparison")
                
                assert response.status_code == 200
                data = response.json()
                
                # 验证对比数据结构
                comparison_data = data.get("data", data)
                assert "algorithms" in comparison_data
                assert "best_ctr_algorithm" in comparison_data
                assert "best_conversion_algorithm" in comparison_data

    def test_unauthorized_access_to_admin_endpoints(
        self,
        client: TestClient,
        sample_users: list[models.User],
    ):
        """测试非管理员访问管理员端点"""
        # 模拟普通用户（非管理员）
        with patch('app.api.deps.require_permission') as mock_permission:
            mock_permission.side_effect = Exception("权限不足")
            
            response = client.get("/api/v1/recommendations/users/metrics")
            assert response.status_code == 500  # 权限错误

    def test_api_error_handling(
        self,
        client: TestClient,
        sample_users: list[models.User],
    ):
        """测试API错误处理"""
        # 模拟服务异常
        with patch('app.services.user_recommendation_service.UserRecommendationService') as mock_service:
            mock_service.return_value.get_user_recommendations.side_effect = Exception("服务异常")
            
            response = client.get("/api/v1/recommendations/users")
            assert response.status_code == 500
            
            data = response.json()
            assert ResponseValidator.validate_error_response(data)

    def test_request_validation(
        self,
        client: TestClient,
        sample_users: list[models.User],
    ):
        """测试请求参数验证"""
        # 测试无效的页码
        response = client.get(
            "/api/v1/recommendations/users",
            params={"page": 0, "page_size": 10}  # 页码不能为0
        )
        assert response.status_code == 422
        
        # 测试无效的页面大小
        response = client.get(
            "/api/v1/recommendations/users",
            params={"page": 1, "page_size": 0}  # 页面大小不能为0
        )
        assert response.status_code == 422

    def test_concurrent_api_requests(
        self,
        client: TestClient,
        sample_users: list[models.User],
    ):
        """测试并发API请求"""
        import concurrent.futures
        
        with patch('app.services.user_recommendation_service.UserRecommendationService'):
            # 创建多个并发请求
            with concurrent.futures.ThreadPoolExecutor(max_workers=5) as executor:
                futures = []
                for i in range(10):
                    future = executor.submit(
                        client.get,
                        "/api/v1/recommendations/users",
                        params={"page": 1, "page_size": 5}
                    )
                    futures.append(future)
                
                # 等待所有请求完成
                responses = [future.result() for future in futures]
                
                # 验证所有请求都成功
                for response in responses:
                    assert response.status_code == 200

    def test_response_format_consistency(
        self,
        client: TestClient,
        sample_users: list[models.User],
    ):
        """测试响应格式一致性"""
        with patch('app.services.user_recommendation_service.UserRecommendationService'):
            # 测试多个端点的响应格式
            endpoints = [
                "/api/v1/recommendations/users",
                "/api/v1/recommendations/users/similarity/2",
            ]
            
            for endpoint in endpoints:
                response = client.get(endpoint)
                if response.status_code == 200:
                    data = response.json()
                    # 验证统一的响应格式
                    assert "success" in data or "data" in data
                    assert "timestamp" in data or isinstance(data, dict)
