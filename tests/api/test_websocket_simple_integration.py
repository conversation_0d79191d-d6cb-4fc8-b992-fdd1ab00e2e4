"""简单的 WebSocket 集成测试 - 验证 Bearer token 修复"""

import pytest
from unittest.mock import AsyncMock, patch

from app.api.deps import get_token_from_websocket_protocols_or_query


class TestWebSocketBearerTokenFix:
    """测试 WebSocket Bearer token 修复"""
    
    @pytest.mark.asyncio
    async def test_bearer_token_stripping_in_query_parameter(self):
        """测试 Query 参数中的 Bearer 前缀被正确剥离"""
        # 创建模拟 WebSocket 对象
        websocket = AsyncMock()
        websocket.headers = {}
        websocket.cookies = {}
        
        # 测试带 Bearer 前缀的 token
        bearer_token = "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9"
        expected_token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9"
        
        # 调用函数
        result = await get_token_from_websocket_protocols_or_query(websocket, token=bearer_token)
        
        # 验证结果
        assert result == expected_token
        
    @pytest.mark.asyncio
    async def test_bearer_token_stripping_in_cookie(self):
        """测试 Cookie 中的 Bearer 前缀被正确剥离"""
        # 创建模拟 WebSocket 对象
        websocket = AsyncMock()
        websocket.headers = {}
        websocket.cookies = {"access_token": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9"}
        
        expected_token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9"
        
        # 调用函数
        result = await get_token_from_websocket_protocols_or_query(websocket, token=None)
        
        # 验证结果
        assert result == expected_token
        
    @pytest.mark.asyncio
    async def test_token_without_bearer_prefix_unchanged(self):
        """测试不带 Bearer 前缀的 token 保持不变"""
        # 创建模拟 WebSocket 对象
        websocket = AsyncMock()
        websocket.headers = {}
        websocket.cookies = {}
        
        # 测试不带 Bearer 前缀的 token
        clean_token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9"
        
        # 测试 Query 参数
        result = await get_token_from_websocket_protocols_or_query(websocket, token=clean_token)
        assert result == clean_token
        
        # 测试 Cookie
        websocket.cookies = {"access_token": clean_token}
        result = await get_token_from_websocket_protocols_or_query(websocket, token=None)
        assert result == clean_token
        
    @pytest.mark.asyncio
    async def test_complete_websocket_auth_flow_with_bearer_token(self):
        """测试完整的 WebSocket 认证流程，使用带 Bearer 前缀的 token"""
        from app.api.deps import get_current_user_websocket
        from app.models.user import User
        from datetime import datetime
        
        # 创建模拟 WebSocket 对象
        websocket = AsyncMock()
        websocket.headers = {}
        websocket.cookies = {}
        websocket.close = AsyncMock()
        
        # 创建模拟用户
        user = User(
            id=123,
            username="testuser",
            email="<EMAIL>",
            nickname="测试用户",
            role_id=1,
            is_active=True,
            is_superuser=False,
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
        
        # 创建模拟数据库
        mock_db = AsyncMock()
        mock_result = AsyncMock()
        # scalar_one_or_none 应该直接返回用户对象，而不是协程
        mock_result.scalar_one_or_none.return_value = user
        mock_db.execute.return_value = mock_result
        
        # 创建模拟 token 服务
        mock_token_service = AsyncMock()
        mock_token_service.verify_token.return_value = {"sub": user.username}
        
        # 测试带 Bearer 前缀的 token
        bearer_token = "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9"
        
        # 调用认证函数
        result = await get_current_user_websocket(
            websocket=websocket,
            token=bearer_token,
            db=mock_db,
            token_service=mock_token_service
        )
        
        # 验证结果
        assert result == user
        assert websocket.close.call_count == 0  # 连接没有被关闭
        
        # 验证 token 服务被调用时使用的是剥离后的 token
        mock_token_service.verify_token.assert_called_once_with("eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9")


if __name__ == "__main__":
    pytest.main([__file__, "-v"])