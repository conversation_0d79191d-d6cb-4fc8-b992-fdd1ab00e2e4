"""WebSocket 集成测试 - 完整的登录后 WebSocket 连接测试"""

import asyncio
import json
import pytest
import pytest_asyncio
from typing import Dict, Any, Optional
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime

from fastapi import FastAPI, WebSocketDisconnect
from fastapi.testclient import TestClient
from sqlalchemy.ext.asyncio import AsyncSession

from app.api.api import api_router
from app.services.interfaces.token_service_interface import ITokenService
from app.models.user import User


class InMemoryTokenService(ITokenService):
    """简化版 TokenService，用于测试环境"""

    def __init__(self):
        self.tokens = {}
        self.revoked_tokens = set()

    async def create_token(
        self,
        username: str,
        device_id: Optional[int] = None,
        expires_delta: Optional[Any] = None,
        token_type: str = "access",
    ) -> str:
        """创建 token"""
        import uuid
        import time
        
        token_id = str(uuid.uuid4())
        
        token_data = {
            "sub": username,
            "iat": int(time.time()),
            "exp": int(time.time()) + (expires_delta.seconds if expires_delta else 3600),
            "jti": token_id,
            "type": token_type
        }
        
        if device_id:
            token_data["device_id"] = device_id
        
        # 简化的 token 编码（实际应用中应使用 JWT）
        token = f"mock_token_{token_id}_{hash(str(token_data)) % 1000000}"
        self.tokens[token] = token_data
        
        return token

    async def create_access_token(
        self, data: dict, expires_delta: Optional[Any] = None, device_id: Optional[int] = None
    ) -> str:
        """创建访问令牌"""
        username = data.get("sub")
        return await self.create_token(
            username=username,
            device_id=device_id,
            expires_delta=expires_delta,
            additional_claims=data,
            token_type="access"
        )

    async def verify_token(self, token: str) -> Optional[Dict[str, Any]]:
        """验证 token"""
        if token in self.revoked_tokens:
            return None
        
        token_data = self.tokens.get(token)
        if not token_data:
            return None
        
        # 检查是否过期
        import time
        if token_data.get("exp", 0) < int(time.time()):
            return None
        
        return token_data

    async def revoke_token(self, token: str) -> bool:
        """撤销 token"""
        self.revoked_tokens.add(token)
        return True

    async def revoke_user_tokens(self, username: str) -> int:
        """撤销用户的所有 token"""
        count = 0
        for token, data in self.tokens.items():
            if data.get("sub") == username and token not in self.revoked_tokens:
                self.revoked_tokens.add(token)
                count += 1
        return count

    async def revoke_device_token(self, username: str, device_id: int) -> bool:
        """撤销设备的 token"""
        for token, data in self.tokens.items():
            if (data.get("sub") == username and 
                data.get("device_id") == device_id and 
                token not in self.revoked_tokens):
                self.revoked_tokens.add(token)
                return True
        return False

    async def get_user_active_tokens(self, username: str) -> list:
        """获取用户的活跃 token"""
        active_tokens = []
        for token, data in self.tokens.items():
            if (data.get("sub") == username and 
                token not in self.revoked_tokens):
                active_tokens.append(data)
        return active_tokens

    async def refresh_token(self, refresh_token: str) -> str | None:
        """刷新令牌"""
        # 验证 refresh token
        token_data = await self.verify_token(refresh_token)
        if not token_data or token_data.get("type") != "refresh":
            return None
        
        # 撤销旧的 refresh token
        await self.revoke_token(refresh_token)
        
        # 创建新的 access token
        username = token_data.get("sub")
        device_id = token_data.get("device_id")
        return await self.create_access_token(
            data={"sub": username},
            device_id=device_id
        )

    async def validate_token(self, token: str) -> dict | None:
        """验证令牌"""
        return await self.verify_token(token)


class TestUserFactory:
    """测试用户工厂"""
    
    @staticmethod
    def create_user(
        user_id: int = 1,
        username: str = "testuser",
        email: str = "<EMAIL>",
        nickname: str = "测试用户",
        role_id: int = 1,
        is_active: bool = True,
        is_superuser: bool = False
    ) -> User:
        """创建测试用户"""
        user = User(
            id=user_id,
            username=username,
            email=email,
            nickname=nickname,
            role_id=role_id,
            is_active=is_active,
            is_superuser=is_superuser,
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
        return user


@pytest.fixture
def token_service() -> InMemoryTokenService:
    """创建测试用的 token 服务"""
    return InMemoryTokenService()


@pytest.fixture
def test_app(token_service: InMemoryTokenService) -> FastAPI:
    """创建测试应用"""
    app = FastAPI()
    
    # 替换 token 服务依赖
    from app.api import deps
    deps.get_token_service = lambda: token_service
    
    # 注册路由
    app.include_router(api_router, prefix="/api/v1")
    
    return app


@pytest.fixture
def client(test_app: FastAPI) -> TestClient:
    """创建测试客户端"""
    return TestClient(test_app)


@pytest_asyncio.fixture
async def test_user(token_service: InMemoryTokenService) -> User:
    """创建测试用户"""
    user = TestUserFactory.create_user(
        user_id=123,
        username="13800138000",
        email="<EMAIL>",
        nickname="测试用户"
    )
    
    # 创建有效的 token
    token = await token_service.create_token(
        username=str(user.username)
    )
    
    user.test_token = token
    return user


@pytest.fixture
def mock_db():
    """模拟数据库会话"""
    db = AsyncMock(spec=AsyncSession)
    db.execute = AsyncMock()
    db.scalars = AsyncMock()
    db.scalar_one_or_none = AsyncMock()
    return db


class TestWebSocketIntegration:
    """WebSocket 集成测试类"""
    
    @pytest.mark.asyncio
    async def test_bearer_token_in_query_parameter(
        self, test_app: FastAPI, test_user: User, mock_db: AsyncSession
    ):
        """测试使用 Query 参数传递带 Bearer 前缀的 token"""
        # 模拟数据库查询返回用户
        mock_db.scalar_one_or_none.return_value = test_user
        
        with patch('app.api.deps.get_db', return_value=mock_db):
            with patch('app.services.websocket_manager.websocket_handler') as mock_handler:
                # 模拟 WebSocket 处理器
                mock_handler.handle_connection = AsyncMock()
                
                # 使用 TestClient 的 WebSocket 连接
                with TestClient(test_app) as client:
                    try:
                        # 建立 WebSocket 连接，使用 Query 参数传递带 Bearer 前缀的 token
                        with client.websocket_connect(
                            "/api/v1/notifications/ws?token=Bearer%20" + test_user.test_token
                        ) as websocket:
                            # 验证连接成功
                            assert websocket is not None
                            
                            # 验证 WebSocket 处理器被调用
                            mock_handler.handle_connection.assert_called_once()
                            
                            # 获取调用参数
                            call_args = mock_handler.handle_connection.call_args
                            assert call_args[1]['user_id'] == test_user.id
                            assert call_args[1]['db'] == mock_db
                            
                    except Exception as e:
                        pytest.fail(f"WebSocket 连接失败: {str(e)}")
    
    @pytest.mark.asyncio
    async def test_bearer_token_in_cookie(
        self, test_app: FastAPI, test_user: User, mock_db: AsyncSession
    ):
        """测试使用 Cookie 传递带 Bearer 前缀的 token"""
        # 模拟数据库查询返回用户
        mock_db.scalar_one_or_none.return_value = test_user
        
        with patch('app.api.deps.get_db', return_value=mock_db):
            with patch('app.services.websocket_manager.websocket_handler') as mock_handler:
                # 模拟 WebSocket 处理器
                mock_handler.handle_connection = AsyncMock()
                
                # 使用 TestClient 的 WebSocket 连接
                with TestClient(test_app) as client:
                    try:
                        # 建立 WebSocket 连接，使用 Cookie 传递带 Bearer 前缀的 token
                        with client.websocket_connect(
                            "/api/v1/notifications/ws",
                            cookies={"access_token": "Bearer " + test_user.test_token}
                        ) as websocket:
                            # 验证连接成功
                            assert websocket is not None
                            
                            # 验证 WebSocket 处理器被调用
                            mock_handler.handle_connection.assert_called_once()
                            
                            # 获取调用参数
                            call_args = mock_handler.handle_connection.call_args
                            assert call_args[1]['user_id'] == test_user.id
                            
                    except Exception as e:
                        pytest.fail(f"WebSocket 连接失败: {str(e)}")
    
    @pytest.mark.asyncio
    async def test_token_without_bearer_prefix(
        self, test_app: FastAPI, test_user: User, mock_db: AsyncSession
    ):
        """测试使用不带 Bearer 前缀的 token（向后兼容性测试）"""
        # 模拟数据库查询返回用户
        mock_db.scalar_one_or_none.return_value = test_user
        
        with patch('app.api.deps.get_db', return_value=mock_db):
            with patch('app.services.websocket_manager.websocket_handler') as mock_handler:
                # 模拟 WebSocket 处理器
                mock_handler.handle_connection = AsyncMock()
                
                # 使用 TestClient 的 WebSocket 连接
                with TestClient(test_app) as client:
                    try:
                        # 建立 WebSocket 连接，使用不带 Bearer 前缀的 token
                        with client.websocket_connect(
                            "/api/v1/notifications/ws?token=" + test_user.test_token
                        ) as websocket:
                            # 验证连接成功
                            assert websocket is not None
                            
                            # 验证 WebSocket 处理器被调用
                            mock_handler.handle_connection.assert_called_once()
                            
                            # 获取调用参数
                            call_args = mock_handler.handle_connection.call_args
                            assert call_args[1]['user_id'] == test_user.id
                            
                    except Exception as e:
                        pytest.fail(f"WebSocket 连接失败: {str(e)}")
    
    @pytest.mark.asyncio
    async def test_invalid_token_rejected(
        self, test_app: FastAPI, mock_db: AsyncSession
    ):
        """测试使用无效 token 的 WebSocket 连接应该失败"""
        with patch('app.api.deps.get_db', return_value=mock_db):
            with patch('app.services.websocket_manager.websocket_handler') as mock_handler:
                # 模拟 WebSocket 处理器
                mock_handler.handle_connection = AsyncMock()
                
                # 使用 TestClient 的 WebSocket 连接
                with TestClient(test_app) as client:
                    # 使用无效的 token 应该导致连接失败
                    with pytest.raises(Exception):
                        with client.websocket_connect(
                            "/api/v1/notifications/ws?token=invalid_token"
                        ) as websocket:
                            pass  # 不应该到达这里


if __name__ == "__main__":
    pytest.main([__file__, "-v"])