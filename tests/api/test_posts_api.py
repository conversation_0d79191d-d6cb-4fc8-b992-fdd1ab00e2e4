"""Posts API 端点测试"""

from datetime import datetime
from types import SimpleNamespace
from unittest.mock import AsyncMock

import pytest
from fastapi import FastAPI, HTTPException
from fastapi.testclient import TestClient

from app.api.deps import (
    get_current_user,
    get_current_user_optional,
    get_db,
)
from app.api.endpoints import posts as posts_endpoint
from app.api.endpoints import upload as upload_endpoint
from app.api.endpoints.posts import router as posts_router
from app.core.pagination import CursorPaginationResponse
from app.models.comment import CommentType
from app.models.post import PostStatus
from app.schemas.post import (
    PostDraftOut,
    PostOut,
    PostPollOptionStat,
    PostPollSummary,
    PostStats,
    PostType,
    PostVisibility,
)
from app.schemas.user import UserAggregated
from app.services.service_factory import (
    get_content_stats_service,
    get_post_aggregation_service,
    get_post_cache_service,
    get_topic_stats_service,
)


def build_user(user_id: int = 1) -> UserAggregated:
    """构造用户聚合对象，确保响应可序列化"""
    now = datetime.utcnow()
    return UserAggregated(
        id=user_id,
        username=f"user{user_id}",
        role_id=1,
        nickname=f"测试用户{user_id}",
        description=None,
        email=f"user{user_id}@example.com",
        avatar=None,
        cover=None,
        is_active=True,
        is_superuser=False,
        last_login=now,
        created_at=now,
        updated_at=now,
        likes_privacy_settings=3,
        favorites_privacy_settings=3,
        wechat_openid=None,
        wechat_unionid=None,
        login_type="password",
        stats=None,
    )


def build_post(post_id: int = 1, author_id: int = 1) -> PostOut:
    """构造 PostOut，用于模拟服务层返回值"""
    author = build_user(author_id)
    now = datetime.utcnow()
    stats = PostStats(
        content_id=post_id,
        like_count=0,
        comment_count=0,
        repost_count=0,
        view_count=0,
        is_liked_by_user=False,
        is_reposted_by_user=False,
        is_followed_author=False,
    )

    return PostOut(
        id=post_id,
        author_id=author_id,
        content="测试沸点",
        post_type=PostType.TEXT,
        status=PostStatus.PUBLISHED,
        visibility=PostVisibility.PUBLIC,
        topic=None,
        location=None,
        is_pinned=False,
        is_hot=False,
        hot_score=0,
        created_at=now,
        updated_at=now,
        author=author,
        stats=stats,
        media=[],
        mentions=[],
        review=None,
        original_post_id=None,
        original_post=None,
        repost_comment=None,
        link_url=None,
        link_title=None,
        link_description=None,
        link_image=None,
        poll_options=None,
        poll_expires_at=None,
        poll_multiple_choice=None,
        poll_votes=None,
        user_poll_votes=None,
        poll_summary=None,
    )


def build_draft(draft_id: int = 1, author_id: int = 1) -> PostDraftOut:
    now = datetime.utcnow()
    return PostDraftOut(
        id=draft_id,
        author_id=author_id,
        content="草稿内容",
        post_type=PostType.TEXT,
        visibility=PostVisibility.PRIVATE,
        topic=None,
        location=None,
        status=PostStatus.DRAFT,
        created_at=now,
        updated_at=now,
    )
    return PostOut(
        id=post_id,
        author_id=author_id,
        content="测试沸点",
        post_type=PostType.TEXT,
        status=PostStatus.PUBLISHED,
        visibility=PostVisibility.PUBLIC,
        topic=None,
        location=None,
        is_pinned=False,
        is_hot=False,
        hot_score=0,
        created_at=now,
        updated_at=now,
        author=author,
        stats=stats,
        media=[],
        mentions=[],
        review=None,
        original_post_id=None,
        original_post=None,
        repost_comment=None,
        link_url=None,
        link_title=None,
        link_description=None,
        link_image=None,
        poll_options=None,
        poll_expires_at=None,
        poll_multiple_choice=None,
        poll_votes=None,
        user_poll_votes=None,
    )


def build_comment(comment_id: int = 1, parent_id: int | None = None) -> SimpleNamespace:
    """构造评论 ORM 模拟对象，满足序列化需求"""
    author = build_user(comment_id + 10)
    now = datetime.utcnow()
    return SimpleNamespace(
        id=comment_id,
        content=f"评论{comment_id}",
        comment_type=CommentType.POST,
        article_id=None,
        video_id=None,
        scratch_id=None,
        post_id=1,
        parent_id=parent_id,
        reply_to_id=parent_id,
        author_id=author.id,
        author=author,
        is_visible=True,
        created_at=now,
        updated_at=now,
        reply_to_user_id=None,
        reply_to_user=None,
    )


@pytest.fixture
def posts_api_context():
    """构建带依赖覆盖的测试客户端上下文"""
    app = FastAPI()
    app.include_router(posts_router, prefix="/api/v1/posts", tags=["posts"])

    db = AsyncMock()
    current_user = build_user(1)

    post_service = SimpleNamespace(
        create_post=AsyncMock(),
        get_posts=AsyncMock(),
        get_post_by_id=AsyncMock(),
        update_post=AsyncMock(),
        delete_post=AsyncMock(),
        get_following_posts=AsyncMock(),
        like_post=AsyncMock(),
        unlike_post=AsyncMock(),
        vote_poll=AsyncMock(),
        save_draft=AsyncMock(),
        list_drafts=AsyncMock(),
        delete_draft=AsyncMock(),
    )
    post_cache_service = AsyncMock()
    content_stats_service = AsyncMock()
    topic_stats_service = AsyncMock()

    async def override_get_db():
        yield db

    app.dependency_overrides[get_db] = override_get_db
    app.dependency_overrides[get_current_user] = lambda: current_user
    app.dependency_overrides[get_current_user_optional] = lambda: current_user
    app.dependency_overrides[get_post_aggregation_service] = lambda: post_service
    app.dependency_overrides[get_post_cache_service] = lambda: post_cache_service
    app.dependency_overrides[get_content_stats_service] = lambda: content_stats_service
    app.dependency_overrides[get_topic_stats_service] = lambda: topic_stats_service

    with TestClient(app) as client:
        yield {
            "app": app,
            "client": client,
            "db": db,
            "user": current_user,
            "post_service": post_service,
            "post_cache_service": post_cache_service,
            "content_stats_service": content_stats_service,
            "topic_stats_service": topic_stats_service,
        }

    app.dependency_overrides.clear()


class TestPostCreation:
    def test_create_post_success(self, posts_api_context):
        context = posts_api_context
        context["post_service"].create_post.return_value = build_post()

        response = context["client"].post(
            "/api/v1/posts/",
            json={"content": "测试", "post_type": "text", "visibility": "public"},
        )

        assert response.status_code == 201
        context["post_service"].create_post.assert_awaited_once()
        assert response.json()["id"] == 1

    def test_publish_post_from_draft(self, posts_api_context):
        context = posts_api_context
        context["post_service"].create_post.return_value = build_post()

        response = context["client"].post(
            "/api/v1/posts/",
            json={
                "draft_id": 10,
                "content": "正式内容",
                "post_type": "text",
                "visibility": "public",
            },
        )

        assert response.status_code == 201
        context["post_service"].create_post.assert_awaited_once()
        kwargs = context["post_service"].create_post.await_args.kwargs
        assert kwargs["post_in"].draft_id == 10

    def test_create_post_requires_poll_options(self, posts_api_context):
        context = posts_api_context

        response = context["client"].post(
            "/api/v1/posts/",
            json={"post_type": "poll", "poll_options": ["A"]},
        )

        assert response.status_code == 400
        assert response.json()["detail"] == "投票至少需要2个选项"
        context["post_service"].create_post.assert_not_called()

    def test_create_post_requires_auth(self, posts_api_context):
        context = posts_api_context

        def raise_unauthorized():
            raise HTTPException(status_code=401, detail="未授权")

        context["app"].dependency_overrides[get_current_user] = raise_unauthorized

        response = context["client"].post(
            "/api/v1/posts/",
            json={"content": "测试"},
        )

        assert response.status_code == 401


class TestPostListing:
    def test_get_posts_passes_filters(self, posts_api_context, monkeypatch):
        context = posts_api_context
        context["post_service"].get_posts.return_value = [build_post()]

        count_mock = AsyncMock(return_value=1)
        monkeypatch.setattr(
            posts_endpoint.crud.post,
            "count_posts_with_filters",
            count_mock,
            raising=False,
        )

        response = context["client"].get(
            "/api/v1/posts/",
            params={
                "page": 2,
                "size": 5,
                "sort_by": "hot_score",
                "sort_order": "asc",
                "topic": "Python",
            },
        )

        assert response.status_code == 200
        context["post_service"].get_posts.assert_awaited_once()
        query_params = context["post_service"].get_posts.await_args.kwargs["query_params"]
        assert query_params.page == 2
        assert query_params.sort_by == "hot_score"
        assert query_params.sort_order == "asc"
        count_mock.assert_awaited_once()
        payload = response.json()
        assert payload["page"] == 2
        assert payload["total_count"] == 1

    def test_get_hot_posts_forces_hot_filter(self, posts_api_context, monkeypatch):
        context = posts_api_context
        context["post_service"].get_posts.return_value = [build_post()]

        count_mock = AsyncMock(return_value=1)
        monkeypatch.setattr(
            posts_endpoint.crud.post,
            "count_posts_with_filters",
            count_mock,
            raising=False,
        )

        response = context["client"].get(
            "/api/v1/posts/hot",
            params={"page": 1, "size": 3},
        )

        assert response.status_code == 200
        query_params = context["post_service"].get_posts.await_args.kwargs["query_params"]
        assert query_params.is_hot is True
        assert query_params.sort_by == "hot_score"
        count_mock.assert_awaited_once()

    def test_get_following_posts(self, posts_api_context, monkeypatch):
        context = posts_api_context
        context["post_service"].get_following_posts.return_value = [build_post(author_id=context["user"].id)]

        count_mock = AsyncMock(return_value=2)
        monkeypatch.setattr(
            posts_endpoint.crud.post,
            "count_following_posts",
            count_mock,
            raising=False,
        )

        response = context["client"].get("/api/v1/posts/following", params={"page": 1, "size": 10})

        assert response.status_code == 200
        context["post_service"].get_following_posts.assert_awaited_once_with(
            context["db"],
            user_id=context["user"].id,
            page=1,
            size=10,
        )
        count_mock.assert_awaited_once()
        awaited = count_mock.await_args
        assert awaited.args[0] == context["db"]
        assert awaited.kwargs["user_id"] == context["user"].id

    def test_get_posts_by_topic(self, posts_api_context, monkeypatch):
        context = posts_api_context
        context["post_service"].get_posts.return_value = [build_post()]

        count_mock = AsyncMock(return_value=3)
        monkeypatch.setattr(posts_endpoint.crud.post, "count_posts_with_filters", count_mock)

        response = context["client"].get(
            "/api/v1/posts/topics/Python",
            params={"page": 1, "size": 2},
        )

        assert response.status_code == 200
        query_params = context["post_service"].get_posts.await_args.kwargs["query_params"]
        assert query_params.topic == "Python"
        assert query_params.page == 1
        count_mock.assert_awaited_once()

    def test_get_posts_returns_empty_page(self, posts_api_context, monkeypatch):
        context = posts_api_context
        context["post_service"].get_posts.return_value = []

        count_mock = AsyncMock(return_value=0)
        monkeypatch.setattr(
            posts_endpoint.crud.post,
            "count_posts_with_filters",
            count_mock,
            raising=False,
        )

        response = context["client"].get("/api/v1/posts/", params={"page": 1, "size": 10})

        assert response.status_code == 200
        payload = response.json()
        assert payload["posts"] == []
        assert payload["total_count"] == 0
        assert payload["has_next"] is False
        assert payload["has_previous"] is False


class TestPostDetailAndMutation:
    def test_get_post_not_found(self, posts_api_context):
        context = posts_api_context
        context["post_service"].get_post_by_id.return_value = None

        response = context["client"].get("/api/v1/posts/42")

        assert response.status_code == 404
        assert response.json()["detail"] == "沸点不存在"

    def test_update_post_success(self, posts_api_context):
        context = posts_api_context
        existing = build_post(post_id=1, author_id=context["user"].id)
        context["post_service"].get_post_by_id.return_value = existing
        context["post_service"].update_post.return_value = build_post(post_id=1)

        response = context["client"].put(
            "/api/v1/posts/1",
            json={"content": "更新"},
        )

        assert response.status_code == 200
        context["post_service"].update_post.assert_awaited_once()
        update_kwargs = context["post_service"].update_post.await_args.kwargs
        assert update_kwargs["post_id"] == 1
        assert update_kwargs["current_user_id"] == context["user"].id
        assert update_kwargs["post_in"].content == "更新"

    def test_update_post_forbidden(self, posts_api_context):
        context = posts_api_context
        context["post_service"].get_post_by_id.return_value = SimpleNamespace(author_id=999)

        response = context["client"].put(
            "/api/v1/posts/1",
            json={"content": "更新"},
        )

        assert response.status_code == 403
        assert response.json()["detail"] == "无权限修改此沸点"
        context["post_service"].update_post.assert_not_called()

    def test_delete_post_success(self, posts_api_context):
        context = posts_api_context
        context["post_service"].get_post_by_id.return_value = build_post(
            post_id=1, author_id=context["user"].id
        )

        response = context["client"].delete("/api/v1/posts/1")

        assert response.status_code == 204
        context["post_service"].delete_post.assert_awaited_once_with(
            context["db"], post_id=1, current_user_id=context["user"].id
        )


class TestPostInteractions:
    def test_like_post(self, posts_api_context):
        context = posts_api_context

        response = context["client"].post("/api/v1/posts/1/like")

        assert response.status_code == 204
        context["post_service"].like_post.assert_awaited_once_with(
            context["db"], post_id=1, user_id=context["user"].id
        )

    def test_unlike_post(self, posts_api_context):
        context = posts_api_context

        response = context["client"].delete("/api/v1/posts/1/like")

        assert response.status_code == 204
        context["post_service"].unlike_post.assert_awaited_once_with(
            context["db"], post_id=1, user_id=context["user"].id
        )

    def test_vote_poll(self, posts_api_context):
        context = posts_api_context
        context["post_service"].vote_poll.return_value = PostPollSummary(
            post_id=1,
            total_votes=10,
            option_stats=[
                PostPollOptionStat(index=0, text="A", vote_count=6, vote_ratio=0.6),
                PostPollOptionStat(index=1, text="B", vote_count=4, vote_ratio=0.4),
            ],
            user_choices=[0],
            multiple_choice=False,
            expires_at=None,
        )

        response = context["client"].post(
            "/api/v1/posts/1/poll/vote",
            json={"option_indexes": [0]},
        )

        assert response.status_code == 200
        payload = response.json()
        assert payload["post_id"] == 1
        assert payload["total_votes"] == 10
        assert payload["user_choices"] == [0]
        context["post_service"].vote_poll.assert_awaited_once_with(
            context["db"],
            post_id=1,
            user_id=context["user"].id,
            option_indexes=[0],
        )


class TestPostDrafts:
    def test_save_draft_create(self, posts_api_context):
        context = posts_api_context
        draft = build_draft(draft_id=5)
        context["post_service"].save_draft.return_value = draft

        response = context["client"].post(
            "/api/v1/posts/drafts",
            json={"content": "草稿内容"},
        )

        assert response.status_code == 201
        context["post_service"].save_draft.assert_awaited_once()
        kwargs = context["post_service"].save_draft.await_args.kwargs
        assert kwargs["draft_in"].content == "草稿内容"
        assert response.json()["id"] == 5

    def test_save_draft_update(self, posts_api_context):
        context = posts_api_context
        draft = build_draft(draft_id=2)
        context["post_service"].save_draft.return_value = draft

        response = context["client"].post(
            "/api/v1/posts/drafts",
            json={"id": 2, "content": "更新草稿"},
        )

        assert response.status_code == 201
        kwargs = context["post_service"].save_draft.await_args.kwargs
        assert kwargs["draft_in"].id == 2
        assert kwargs["draft_in"].content == "更新草稿"

    def test_list_drafts(self, posts_api_context):
        context = posts_api_context
        context["post_service"].list_drafts.return_value = [build_draft(draft_id=1)]

        response = context["client"].get(
            "/api/v1/posts/drafts",
            params={"page": 1, "size": 10},
        )

        assert response.status_code == 200
        context["post_service"].list_drafts.assert_awaited_once()
        payload = response.json()
        assert "items" in payload and len(payload["items"]) == 1

    def test_delete_draft(self, posts_api_context):
        context = posts_api_context

        response = context["client"].delete("/api/v1/posts/drafts/7")

        assert response.status_code == 204
        context["post_service"].delete_draft.assert_awaited_once_with(
            context["db"], draft_id=7, author_id=context["user"].id
        )


class TestPostComments:
    def test_create_comment_success(self, posts_api_context, monkeypatch):
        context = posts_api_context
        context["post_cache_service"].get_by_id.return_value = SimpleNamespace(status=PostStatus.PUBLISHED)

        create_mock = AsyncMock(
            return_value=SimpleNamespace(
                id=1,
                comment_type=CommentType.POST,
                article_id=None,
                video_id=None,
                scratch_id=None,
                post_id=1,
                parent_id=None,
                reply_to_id=None,
                author_id=context["user"].id,
                author=context["user"],
                content="评论内容",
                is_visible=True,
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow(),
            )
        )
        monkeypatch.setattr(posts_endpoint.crud.comment, "create", create_mock)
        monkeypatch.setattr(posts_endpoint.crud.comment, "get", AsyncMock(return_value=None))

        response = context["client"].post(
            "/api/v1/posts/1/comments",
            json={"content": "评论内容"},
        )

        assert response.status_code == 201
        create_mock.assert_awaited_once()
        context["content_stats_service"].update_comment_count.assert_awaited_once_with(
            content_type="post",
            content_id=1,
            increment=1,
        )

    def test_create_comment_parent_not_found(self, posts_api_context, monkeypatch):
        context = posts_api_context
        context["post_cache_service"].get_by_id.return_value = SimpleNamespace(status=PostStatus.PUBLISHED)
        monkeypatch.setattr(posts_endpoint.crud.comment, "get", AsyncMock(return_value=None))

        response = context["client"].post(
            "/api/v1/posts/1/comments",
            json={"content": "回复", "parent_id": 999},
        )

        assert response.status_code == 404
        assert response.json()["detail"] == "父评论不存在"

    def test_get_comments_flat_success(self, posts_api_context, monkeypatch):
        context = posts_api_context
        context["post_cache_service"].get_by_id.return_value = SimpleNamespace(status=PostStatus.PUBLISHED)

        parent = build_comment(1)
        child = build_comment(2, parent_id=1)
        pagination_result = CursorPaginationResponse(
            items=[parent, child],
            has_next=False,
            has_previous=False,
            next_cursor=None,
            previous_cursor=None,
            total_count=2,
        )
        monkeypatch.setattr(
            posts_endpoint.crud.comment,
            "get_by_post_cursor",
            AsyncMock(return_value=pagination_result),
        )
        context["content_stats_service"].batch_get_stats.return_value = {
            ("comment", 1): {"like_count": 1, "is_liked_by_user": True},
            ("comment", 2): {"like_count": 0, "is_liked_by_user": False},
        }

        response = context["client"].get(
            "/api/v1/posts/1/comments",
            params={"sort_by": "invalid", "flat": "true"},
        )

        assert response.status_code == 200
        body = response.json()
        assert len(body["items"]) == 2
        context["content_stats_service"].batch_get_stats.assert_awaited_once()

    def test_get_comments_post_not_found(self, posts_api_context):
        context = posts_api_context
        context["post_cache_service"].get_by_id.return_value = None

        response = context["client"].get("/api/v1/posts/1/comments")

        assert response.status_code == 404
        assert response.json()["detail"] == "沸点不存在或未发布"


class TestPostMediaUpload:
    def test_upload_image_success(self, posts_api_context, monkeypatch):
        context = posts_api_context
        upload_result = SimpleNamespace(
            file_url="/image.png",
            file_hash="hash",
            width=800,
            height=600,
        )
        monkeypatch.setattr(
            upload_endpoint,
            "handle_single_image_upload",
            AsyncMock(return_value=upload_result),
        )

        response = context["client"].post(
            "/api/v1/posts/upload/media",
            files={"file": ("image.png", b"data", "image/png")},
        )

        assert response.status_code == 200
        payload = response.json()
        assert payload["media_type"] == "image"
        assert payload["file_url"] == upload_result.file_url

    def test_upload_video_success(self, posts_api_context, monkeypatch):
        context = posts_api_context
        upload_result = SimpleNamespace(
            file_hash="hash",
            file_url="/steam/post-videos/hash.mp4",
            cover_url="/steam/images/hash_cover.webp",
            duration=12,
            width=1920,
            height=1080,
            size=12345,
        )

        mock_upload = AsyncMock(return_value=upload_result)

        monkeypatch.setattr(
            "app.services.service_factory.get_post_video_upload_service",
            lambda: SimpleNamespace(upload=mock_upload),
        )

        response = context["client"].post(
            "/api/v1/posts/upload/media",
            files={"file": ("video.mp4", b"data", "video/mp4")},
        )

        assert response.status_code == 200
        payload = response.json()
        assert payload["media_type"] == "video"
        assert payload["file_url"] == upload_result.file_url
        assert payload["cover_url"] == upload_result.cover_url
        mock_upload.assert_awaited()

    def test_upload_invalid_media_type(self, posts_api_context):
        context = posts_api_context

        response = context["client"].post(
            "/api/v1/posts/upload/media",
            files={"file": ("file.txt", b"data", "text/plain")},
        )

        assert response.status_code == 400
        assert "不支持的文件类型" in response.json()["detail"]
