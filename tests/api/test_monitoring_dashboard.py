from __future__ import annotations

from unittest.mock import AsyncMock

import pytest
from fastapi import FastAPI
from fastapi.testclient import Test<PERSON>lient

from app.api import deps
from app.api.endpoints import admin_monitoring as monitoring_endpoint


@pytest.fixture
def test_app() -> FastAPI:
    app = FastAPI()
    app.include_router(
        monitoring_endpoint.router, prefix="/api/admin/monitoring", tags=["admin-monitoring"]
    )
    return app


@pytest.fixture
def client(test_app: FastAPI) -> TestClient:
    return TestClient(test_app)


@pytest.fixture(autouse=True)
def override_permissions(test_app: FastAPI):
    async def allow():
        return {"id": 1}

    read_guard = monitoring_endpoint.monitoring_read_guard
    manage_guard = monitoring_endpoint.monitoring_manage_guard
    test_app.dependency_overrides[read_guard] = allow
    test_app.dependency_overrides[manage_guard] = allow
    yield
    test_app.dependency_overrides.pop(read_guard, None)
    test_app.dependency_overrides.pop(manage_guard, None)


@pytest.fixture(autouse=True)
def override_db(test_app: FastAPI):
    async def _db():
        yield AsyncMock()

    test_app.dependency_overrides[deps.get_db] = _db
    yield
    test_app.dependency_overrides.pop(deps.get_db, None)


@pytest.fixture
def mock_service(test_app: FastAPI) -> AsyncMock:
    service = AsyncMock()
    test_app.dependency_overrides[monitoring_endpoint.get_monitoring_service] = lambda: service
    try:
        yield service
    finally:
        test_app.dependency_overrides.pop(monitoring_endpoint.get_monitoring_service, None)


def test_get_dashboard(client: TestClient, mock_service: AsyncMock):
    mock_service.get_dashboard.return_value = {
        "system_metrics": {"cpu": {"current": 42.0}},
        "application_metrics": {},
        "alerts": {"active": 0, "recent": []},
    }

    response = client.get("/api/admin/monitoring/dashboard")
    assert response.status_code == 200
    data = response.json()
    assert data["data"]["system_metrics"]["cpu"]["current"] == 42.0
    mock_service.get_dashboard.assert_awaited_once()


def test_create_alert_rule(client: TestClient, mock_service: AsyncMock):
    mock_service.create_alert_rule.return_value = {"id": 1, "name": "cpu"}

    response = client.post(
        "/api/admin/monitoring/alerts",
        json={
            "name": "CPU high",
            "metric_name": "cpu",
            "operator": "greater_than",
            "threshold": 80,
            "duration": "5m",
            "severity": "warning",
            "channels": [],
        },
    )
    assert response.status_code == 200
    mock_service.create_alert_rule.assert_awaited_once()


def test_test_alert_endpoint(client: TestClient, mock_service: AsyncMock):
    mock_service.test_channel.return_value = {"status": "success"}
    response = client.post("/api/admin/monitoring/test-alert", json={"channel": "dingtalk"})
    assert response.status_code == 200
    mock_service.test_channel.assert_awaited_once()
