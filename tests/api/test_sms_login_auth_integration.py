"""SMS 登录流程与鉴权 API 集成测试"""

from __future__ import annotations

from dataclasses import dataclass
from datetime import datetime, timedelta
from typing import Any, Async<PERSON>enerator, Dict, Optional
from uuid import uuid4

import pytest
from fastapi import Depends, FastAPI, HTTPException, Request, status
from fastapi.testclient import TestClient

from app.api import deps
from app.api.endpoints import token as token_endpoint
from app.api.endpoints import users as users_endpoint
from app.api.endpoints.auth import router as auth_router
from app.core.limiter import setup_limiter
from app.services.interfaces.token_service_interface import ITokenService
from app.services.service_factory import (
    get_auth_orchestrator_service,
    get_token_service,
    get_user_aggregation_service,
)
from app.schemas.auth import AuthCompletionResponse, AuthInitiationResponse
from app.schemas.user import UserAggregated
from app.schemas.user_stats import UserStats


@dataclass
class DummyUser:
    """测试用的精简用户对象"""

    id: int
    username: str
    is_active: bool = True
    is_superuser: bool = False


class DummyUserAggregationService:
    """返回固定用户资料的聚合服务"""

    def __init__(self, username: str, user_id: int):
        self._username = username
        self._user_id = user_id

    async def get_user_profile(self, db: Any, user_id: int) -> UserAggregated | None:
        if user_id != self._user_id:
            return None

        now = datetime.utcnow()
        stats = UserStats.model_construct(
            user_id=user_id,
            total_likes_count=0,
            total_favorites_count=0,
            following_count=0,
            follower_count=0,
            article_count=0,
            video_count=0,
        )

        return UserAggregated.model_construct(
            id=user_id,
            username=self._username,
            role_id=1,
            nickname="测试用户0253",
            description=None,
            email=f"{self._username}@test.local",
            avatar=None,
            cover=None,
            is_active=True,
            is_superuser=False,
            last_login=None,
            created_at=now,
            updated_at=now,
            likes_privacy_settings=3,
            favorites_privacy_settings=3,
            wechat_openid=None,
            wechat_unionid=None,
            login_type="sms",
            stats=stats,
        )


class InMemoryTokenService(ITokenService):
    """简化版 TokenService，用于测试环境"""

    def __init__(self):
        self._tokens: Dict[str, Dict[str, Any]] = {}
        self._user_index: Dict[str, int] = {}
        self._user_tokens: Dict[str, set[str]] = {}

    def _next_user_id(self, username: str) -> int:
        if username not in self._user_index:
            self._user_index[username] = len(self._user_index) + 1
        return self._user_index[username]

    async def create_token(
        self,
        username: str,
        device_id: int | None = None,
        expires_delta: timedelta | None = None,
        token_type: str = "access",
    ) -> str:
        token = f"token-{uuid4().hex}"
        user_id = self._next_user_id(username)
        expires_at = datetime.utcnow() + (expires_delta or timedelta(minutes=60))
        payload = {
            "sub": username,
            "username": username,
            "user_id": user_id,
            "device_id": device_id,
            "type": token_type,
            "token_type": token_type,
            "created_at": datetime.utcnow().isoformat(),
            "expires_at": expires_at.isoformat(),
        }
        self._tokens[token] = payload
        self._user_tokens.setdefault(username, set()).add(token)
        return token

    async def verify_token(self, token: str) -> Dict[str, Any] | None:
        return self._tokens.get(token)

    async def revoke_token(self, token: str) -> bool:
        payload = self._tokens.pop(token, None)
        if not payload:
            return False
        username = payload["username"]
        if username in self._user_tokens:
            self._user_tokens[username].discard(token)
        return True

    async def revoke_user_tokens(self, username: str) -> int:
        tokens = list(self._user_tokens.get(username, set()))
        for token in tokens:
            await self.revoke_token(token)
        return len(tokens)

    async def revoke_device_token(self, username: str, device_id: int) -> bool:
        for token in list(self._user_tokens.get(username, set())):
            payload = self._tokens.get(token)
            if payload and payload.get("device_id") == device_id:
                await self.revoke_token(token)
                return True
        return False

    async def get_user_active_tokens(self, username: str) -> list[dict]:
        return [self._tokens[token] for token in self._user_tokens.get(username, set())]

    async def create_access_token(
        self, data: dict, expires_delta: timedelta | None = None, device_id: int | None = None
    ) -> str:
        username = data.get("sub")
        if not username:
            raise ValueError("Token data 必须包含 sub")
        return await self.create_token(username, device_id=device_id, expires_delta=expires_delta)

    async def refresh_token(self, refresh_token: str) -> str | None:
        payload = await self.verify_token(refresh_token)
        if not payload:
            return None
        return await self.create_access_token({"sub": payload["username"]}, device_id=payload.get("device_id"))

    async def validate_token(self, token: str) -> dict | None:
        return await self.verify_token(token)


class FakeAuthOrchestratorService:
    """仅支持特定手机号 + 验证码的认证服务"""

    valid_phone = "17604840253"
    expected_code = "000000"

    def __init__(self, token_service: InMemoryTokenService):
        self._token_service = token_service

    async def initiate_authentication(self, auth_type: str, request_data: Dict[str, Any]) -> AuthInitiationResponse:
        return AuthInitiationResponse(
            success=True,
            message="验证码已发送 (测试环境虚拟)",
            data={"phone": request_data.get("phone")},
            auth_type=auth_type,
        )

    async def complete_authentication(
        self,
        auth_type: str,
        verification_data: Dict[str, Any],
        request: Request,
        db: Any,
    ) -> AuthCompletionResponse:
        phone = verification_data.get("phone")
        code = verification_data.get("code")

        if phone == self.valid_phone and code == self.expected_code:
            access_token = await self._token_service.create_access_token(
                data={"sub": phone},
                device_id=1,
            )
            return AuthCompletionResponse(
                success=True,
                user=None,
                access_token=access_token,
                refresh_token=None,
                token_type="bearer",
                requires_device_verification=False,
                verification_token=None,
                message="登录成功",
                auth_method=auth_type,
            )

        return AuthCompletionResponse(
            success=False,
            user=None,
            access_token=None,
            refresh_token=None,
            token_type="bearer",
            requires_device_verification=False,
            verification_token=None,
            message="验证码错误",
            auth_method=auth_type,
        )

    async def handle_device_verification(self, verification_token: str, verification_code: str, db: Any) -> AuthCompletionResponse:
        raise NotImplementedError("测试桩未实现设备验证流程")


@pytest.fixture
def token_service() -> InMemoryTokenService:
    return InMemoryTokenService()


@pytest.fixture
def test_app(token_service: InMemoryTokenService) -> FastAPI:
    app = FastAPI()
    app.include_router(auth_router, prefix="/api/v1/auth", tags=["auth"])
    app.include_router(token_endpoint.router, prefix="/api/v1/token", tags=["token"])
    app.include_router(users_endpoint.router, prefix="/api/v1/users", tags=["users"])
    setup_limiter(app)

    app.dependency_overrides[get_token_service] = lambda: token_service
    app.dependency_overrides[get_auth_orchestrator_service] = lambda: FakeAuthOrchestratorService(token_service)
    app.dependency_overrides[get_user_aggregation_service] = lambda: DummyUserAggregationService(
        username=FakeAuthOrchestratorService.valid_phone,
        user_id=1,
    )

    async def _override_db() -> AsyncGenerator[Any, None]:
        yield None

    async def _override_current_user(
        token: str | None = Depends(deps.get_token_from_header_or_cookie),
        token_service: ITokenService = Depends(get_token_service),
    ) -> DummyUser:
        if not token:
            raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="缺少认证令牌")

        payload = await token_service.verify_token(token)
        if not payload:
            raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="无效的认证令牌")

        return DummyUser(id=payload.get("user_id", 1), username=payload.get("username", "unknown"))

    app.dependency_overrides[deps.get_db] = _override_db
    app.dependency_overrides[deps.get_current_user] = _override_current_user

    return app


@pytest.fixture
def client(test_app: FastAPI) -> TestClient:
    return TestClient(test_app)


def test_sms_login_and_authenticated_apis(client: TestClient):
    """验证短信登录成功后，可调用需要鉴权的 API"""

    login_payload = {"phone": "17604840253", "code": "000000"}
    login_response = client.post("/api/v1/auth/sms/login", json=login_payload)
    assert login_response.status_code == 200

    login_data = login_response.json()
    assert login_data["success"] is True
    access_token: Optional[str] = login_data.get("access_token")
    assert access_token

    auth_headers = {"Authorization": f"Bearer {access_token}"}

    me_response = client.get("/api/v1/users/me", headers=auth_headers)
    assert me_response.status_code == 200
    me_data = me_response.json()
    assert me_data["username"].startswith("176")

    tokens_response = client.get("/api/v1/token/tokens", headers=auth_headers)
    assert tokens_response.status_code == 200
    tokens_data = tokens_response.json()
    assert tokens_data["total"] >= 1
    assert any(item["token_type"] == "access" for item in tokens_data["tokens"])


def test_sms_login_failure_with_wrong_code(client: TestClient):
    """验证码错误时应返回失败，不应颁发令牌"""

    response = client.post(
        "/api/v1/auth/sms/login",
        json={"phone": "17604840253", "code": "111111"},
    )
    assert response.status_code == 200
    payload = response.json()
    assert payload["success"] is False
    assert payload.get("access_token") is None
