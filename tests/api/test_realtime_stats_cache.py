import pytest
from types import SimpleNamespace
from unittest.mock import AsyncMock, patch

from app.api.endpoints.favorites import toggle_favorite
from app.api.endpoints.likes import toggle_like
from app.schemas.favorite import FavoriteToggle
from app.schemas.like import LikeToggle


@pytest.fixture(autouse=True)
def _mock_redis_connections(monkeypatch):
    """避免测试中初始化真实的Redis连接。"""
    mock_init = AsyncMock()
    mock_close = AsyncMock()
    monkeypatch.setattr("tests.conftest.init_redis", mock_init)
    monkeypatch.setattr("tests.conftest.close_redis", mock_close)
    return mock_init, mock_close


@pytest.mark.asyncio
async def test_toggle_like_updates_content_stats_for_video():
    db = AsyncMock()
    db.commit = AsyncMock()
    like_data = LikeToggle(content_type="video", content_id=123)
    current_user = SimpleNamespace(id=456)
    stats_service = SimpleNamespace(update_like_status=AsyncMock())

    with patch(
        "app.api.endpoints.likes.crud.video.get",
        new=AsyncMock(return_value=SimpleNamespace(id=like_data.content_id)),
    ), patch(
        "app.api.endpoints.likes.crud.like.toggle_like",
        new=AsyncMock(return_value=(SimpleNamespace(id=1), True)),
    ), patch(
        "app.api.endpoints.likes.crud.like.get_content_like_count",
        new=AsyncMock(return_value=10),
    ):
        result = await toggle_like(
            db=db,
            like_data=like_data,
            current_user=current_user,
            content_stats_service=stats_service,
        )

    stats_service.update_like_status.assert_awaited_once_with(
        "video", like_data.content_id, current_user.id, True
    )
    db.commit.assert_awaited_once()
    assert result.is_liked is True


@pytest.mark.asyncio
async def test_toggle_favorite_updates_content_stats_for_video():
    db = AsyncMock()
    db.commit = AsyncMock()
    favorite_data = FavoriteToggle(content_type="video", content_id=789, note=None)
    current_user = SimpleNamespace(id=321)
    stats_service = SimpleNamespace(update_favorite_status=AsyncMock())

    with patch(
        "app.api.endpoints.favorites.crud.video.get",
        new=AsyncMock(return_value=SimpleNamespace(id=favorite_data.content_id)),
    ), patch(
        "app.api.endpoints.favorites.crud.favorite.toggle_favorite",
        new=AsyncMock(return_value=(SimpleNamespace(id=1), True)),
    ), patch(
        "app.api.endpoints.favorites.crud.favorite.get_content_favorite_count",
        new=AsyncMock(return_value=5),
    ):
        result = await toggle_favorite(
            db=db,
            favorite_data=favorite_data,
            current_user=current_user,
            content_stats_service=stats_service,
        )

    stats_service.update_favorite_status.assert_awaited_once_with(
        "video", favorite_data.content_id, current_user.id, True
    )
    db.commit.assert_awaited_once()
    assert result.is_favorited is True
