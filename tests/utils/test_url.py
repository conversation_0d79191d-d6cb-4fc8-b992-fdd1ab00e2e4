import pytest

from app.utils.url import prepend_cdn, prepend_cdn_list


@pytest.mark.parametrize(
    "url,expected",
    [
        (None, None),
        ("", None),
        ("   ", None),
        ("https://example.com/path.jpg", "https://example.com/path.jpg"),
        ("http://example.com/abc.png", "http://example.com/abc.png"),
        ("/steam/path/img.png", "https://cdn.example.com/steam/path/img.png"),
        ("steam/path/img.png", "https://cdn.example.com/steam/path/img.png"),
        ("uploads/image.png", "https://cdn.example.com/uploads/image.png"),
    ],
)
def test_prepend_cdn(url, expected):
    assert (
        prepend_cdn(url, cdn_domain="https://cdn.example.com", prefixes=["steam/", "uploads/"])
        == expected
    )


def test_prepend_cdn_list():
    urls = ["steam/a.png", "/steam/b.png", "https://already/ok"]
    result = prepend_cdn_list(urls, cdn_domain="https://cdn.example.com", prefixes=["steam/"])
    assert result == [
        "https://cdn.example.com/steam/a.png",
        "https://cdn.example.com/steam/b.png",
        "https://already/ok",
    ]
