"""游标分页实现

使用 fastapi-pagination 库实现游标分页，提供更好的性能和用户体验。
游标分页特别适合大数据集和实时数据更新的场景。
"""

from datetime import date, datetime, time
from decimal import Decimal
from typing import Any, TypeVar

from fastapi_pagination import add_pagination, set_page
from fastapi_pagination.cursor import CursorPage, CursorParams
from fastapi_pagination.ext.sqlalchemy import paginate
from pydantic import BaseModel, Field
from sqlalchemy import asc, desc, func
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.inspection import inspect as sa_inspect
from sqlalchemy.orm.attributes import InstrumentedAttribute
from sqlalchemy.schema import Column

from app.core.logging import logger

T = TypeVar("T")


class CursorPaginationParams(BaseModel):
    """游标分页参数"""

    cursor: str | None = Field(None, description="游标位置，用于获取下一页数据")
    size: int = Field(20, ge=1, le=100, description="每页大小，范围1-100")
    order_by: str = Field("id", description="排序字段")
    order_direction: str = Field("desc", pattern="^(asc|desc)$", description="排序方向：asc或desc")


class CursorPaginationResponse[T](BaseModel):
    """游标分页响应"""

    items: list[T] = Field(..., description="数据列表")
    has_next: bool = Field(..., description="是否有下一页")
    has_previous: bool = Field(..., description="是否有上一页")
    next_cursor: str | None = Field(None, description="下一页游标")
    previous_cursor: str | None = Field(None, description="上一页游标")
    total_count: int | None = Field(None, description="总数量（可选，计算成本较高）")



class CursorPaginator:
    """游标分页器"""

    @staticmethod
    def _serialize_cursor_value(value: Any) -> str | None:
        """将游标值序列化为字符串"""
        if value is None:
            return None
        if isinstance(value, (datetime, date, time)):
            return value.isoformat()
        return str(value)

    @staticmethod
    def _parse_cursor_value(raw_value: str | None, cursor_column: Any) -> Any | None:
        """根据列类型解析游标字符串"""
        if raw_value in (None, '', '0'):
            return None

        column_type = getattr(cursor_column, 'type', None)
        python_type = None

        if column_type is not None and hasattr(column_type, 'python_type'):
            try:
                python_type = column_type.python_type  # type: ignore[attr-defined]
            except (NotImplementedError, AttributeError):
                python_type = None

        try:
            if python_type is datetime:
                return datetime.fromisoformat(raw_value)
            if python_type is date:
                return date.fromisoformat(raw_value)
            if python_type is time:
                return time.fromisoformat(raw_value)
            if python_type is bool:
                return raw_value.lower() in {'true', '1'}
            if python_type in (int, float):
                return python_type(raw_value)  # type: ignore[misc]
            if python_type is Decimal:
                return Decimal(raw_value)
        except Exception:
            return raw_value

        return raw_value

    @staticmethod
    async def paginate_query(
        db: AsyncSession,
        query: Any,
        params: CursorPaginationParams,
        cursor_field: Any = 'id',
        include_total: bool = False,
        cursor_label: str | None = None,
    ) -> CursorPaginationResponse:
        """对查询进行游标分页"""
        order_func = desc if params.order_direction == 'desc' else asc

        model_class = query.column_descriptions[0].get('entity')
        is_scalar_query = len(query.column_descriptions) == 1 and isinstance(
            query.column_descriptions[0]['expr'], Column
        )

        cursor_attr_name = cursor_label
        requires_extra_column = False
        cursor_column: Any | None = None

        if is_scalar_query:
            cursor_column = query.column_descriptions[0]['expr']
        else:
            if isinstance(cursor_field, InstrumentedAttribute):
                cursor_column = cursor_field
                owner = getattr(cursor_field, 'class_', None)
                if owner == model_class and cursor_label is None:
                    cursor_attr_name = cursor_field.key
                else:
                    requires_extra_column = True
            elif isinstance(cursor_field, Column):
                cursor_column = cursor_field
                if (
                    getattr(cursor_column, 'table', None)
                    == getattr(model_class, '__table__', None)
                    and cursor_label is None
                ):
                    cursor_attr_name = getattr(cursor_column, 'key', None)
                else:
                    requires_extra_column = True
            elif isinstance(cursor_field, str):
                if not model_class:
                    raise AttributeError(f"Cursor field '{cursor_field}' 无法在未知模型上解析")
                if hasattr(model_class, cursor_field):
                    cursor_column = getattr(model_class, cursor_field)
                    if cursor_label is None:
                        cursor_attr_name = cursor_field
                else:
                    raise AttributeError(
                        f"Cursor field '{cursor_field}' 未在模型 {model_class} 上定义"
                    )
            else:
                cursor_column = cursor_field
                requires_extra_column = True

        if cursor_label and cursor_attr_name is None:
            cursor_attr_name = cursor_label

        if cursor_column is None:
            cursor_column = query.column_descriptions[0]['expr']

        if is_scalar_query:
            requires_extra_column = False

        column_label = cursor_attr_name or '_cursor_value'

        base_query = query
        if requires_extra_column:
            base_query = base_query.add_columns(cursor_column.label(column_label))

        base_query = base_query.order_by(None)

        cursor_filter_value = CursorPaginator._parse_cursor_value(params.cursor, cursor_column)

        ordered_query = base_query.order_by(order_func(cursor_column))
        if cursor_filter_value is not None:
            if params.order_direction == 'desc':
                ordered_query = ordered_query.where(cursor_column < cursor_filter_value)
            else:
                ordered_query = ordered_query.where(cursor_column > cursor_filter_value)

        limit_query = ordered_query.limit(params.size + 1)

        result = await db.execute(limit_query)

        cursor_values: list[Any] = []
        if requires_extra_column:
            rows = result.all()
            has_next = len(rows) > params.size
            if has_next:
                rows = rows[:-1]
            items = [row[0] for row in rows]
            cursor_values = [row[-1] for row in rows]
            if cursor_attr_name:
                for item, value in zip(items, cursor_values):
                    if hasattr(item, '__dict__'):
                        setattr(item, cursor_attr_name, value)
        else:
            items = result.scalars().all()
            has_next = len(items) > params.size
            if has_next:
                items = items[:-1]

        next_cursor: str | None = None
        previous_cursor: str | None = None

        first_cursor_value: Any | None = None
        if items:
            if cursor_values:
                first_cursor_value = cursor_values[0]
            elif cursor_attr_name and hasattr(items[0], cursor_attr_name):
                first_cursor_value = getattr(items[0], cursor_attr_name)
            elif hasattr(items[0], 'id'):
                first_cursor_value = items[0].id
            else:
                first_cursor_value = items[0]

        if has_next and items:
            if cursor_values:
                cursor_value = cursor_values[-1]
            elif cursor_attr_name and hasattr(items[-1], cursor_attr_name):
                cursor_value = getattr(items[-1], cursor_attr_name)
            elif hasattr(items[-1], 'id'):
                cursor_value = items[-1].id
            else:
                cursor_value = items[-1]
            next_cursor = CursorPaginator._serialize_cursor_value(cursor_value)

        if params.cursor and items:
            previous_value = await CursorPaginator._get_previous_cursor(
                db=db,
                base_query=base_query,
                params=params,
                cursor_column=cursor_column,
                first_cursor_value=first_cursor_value,
                cursor_attr_name=cursor_attr_name,
                requires_extra_column=requires_extra_column,
            )
            previous_cursor = CursorPaginator._serialize_cursor_value(previous_value)

        total_count = None
        if include_total:
            try:
                expr = base_query.column_descriptions[0]['expr']
                mapper_info = sa_inspect(expr, raiseerr=False) if expr is not None else None

                if mapper_info and getattr(mapper_info, 'primary_key', None):
                    # 统计 ORM 实体时使用主键列，兼容非 id 命名的主键
                    count_column = mapper_info.primary_key[0]
                    count_query = base_query.with_only_columns(func.count(count_column)).order_by(None)
                elif hasattr(expr, 'type'):
                    count_query = base_query.with_only_columns(func.count()).order_by(None)
                else:
                    count_query = base_query.with_only_columns(func.count()).order_by(None)

                count_result = await db.execute(count_query)
                total_count = count_result.scalar()
            except Exception as count_error:
                logger.warning(f'计数查询失败，跳过总数计算: {count_error}')
                total_count = None

        return CursorPaginationResponse(
            items=items,
            has_next=has_next,
            has_previous=bool(params.cursor),
            next_cursor=next_cursor,
            previous_cursor=previous_cursor,
            total_count=total_count,
        )

    @staticmethod
    async def _get_previous_cursor(
        db: AsyncSession,
        base_query: Any,
        params: CursorPaginationParams,
        cursor_column: Any,
        first_cursor_value: Any,
        cursor_attr_name: str | None,
        requires_extra_column: bool,
    ) -> Any:
        """获取上一页游标值"""
        if first_cursor_value is None:
            return None

        reverse_order_func = asc if params.order_direction == 'desc' else desc

        if params.order_direction == 'desc':
            reverse_query = base_query.where(cursor_column > first_cursor_value)
        else:
            reverse_query = base_query.where(cursor_column < first_cursor_value)

        reverse_query = reverse_query.order_by(reverse_order_func(cursor_column)).limit(params.size)

        result = await db.execute(reverse_query)

        if requires_extra_column:
            rows = result.all()
            if not rows:
                return None
            return rows[-1][-1]

        reverse_items = result.scalars().all()
        if not reverse_items:
            return None

        last_item = reverse_items[-1]
        if cursor_attr_name and hasattr(last_item, cursor_attr_name):
            return getattr(last_item, cursor_attr_name)
        if hasattr(last_item, 'id'):
            return last_item.id
        return last_item


class FastAPIPaginationIntegration:
    """FastAPI Pagination 库集成"""

    @staticmethod
    def setup_pagination(app):
        """设置 FastAPI Pagination"""
        add_pagination(app)
        # 设置默认的游标分页
        set_page(CursorPage[Any])

    @staticmethod
    async def paginate_sqlalchemy_query(
        db: AsyncSession, query: Any, params: CursorParams | None = None
    ) -> CursorPage:
        """使用 fastapi-pagination 对 SQLAlchemy 查询进行游标分页"""
        return await paginate(db, query, params)


# 兼容性适配器，用于现有代码的平滑迁移
class PaginationAdapter:
    """分页适配器，提供从传统分页到游标分页的迁移支持"""

    @staticmethod
    def convert_offset_to_cursor(
        offset: int, limit: int, total: int, items: list[Any], cursor_field: str = "id"
    ) -> CursorPaginationResponse:
        """将传统的 offset/limit 分页结果转换为游标分页格式"""
        has_next = (offset + limit) < total
        has_previous = offset > 0

        next_cursor = None
        previous_cursor = None

        if items:
            if has_next:
                next_cursor = str(getattr(items[-1], cursor_field))
            if has_previous and len(items) > 0:
                previous_cursor = str(getattr(items[0], cursor_field))

        return CursorPaginationResponse(
            items=items,
            has_next=has_next,
            has_previous=has_previous,
            next_cursor=next_cursor,
            previous_cursor=previous_cursor,
            total_count=total,
        )

    @staticmethod
    def cursor_params_to_offset(
        cursor: str | None, size: int, model_class: Any, cursor_field: str = "id"
    ) -> tuple[int, int]:
        """将游标参数转换为 offset/limit 参数（用于向后兼容）"""
        if not cursor:
            return 0, size

        try:
            int(cursor)
            # 这里需要根据具体业务逻辑计算 offset
            # 简化实现，实际使用中可能需要更复杂的逻辑
            offset = 0  # 游标分页通常不需要 offset
            return offset, size
        except ValueError:
            return 0, size
