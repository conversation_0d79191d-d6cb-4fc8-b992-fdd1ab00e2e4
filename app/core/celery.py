import os

from celery import Celery
from celery.schedules import crontab
from kombu import Queue

from app.config import settings

task_modules = []
for filename in os.listdir("app/tasks"):
    if filename.endswith(".py") and not filename.startswith("__"):
        module_name = f"app.tasks.{filename[:-3]}"  # 去掉 .py 后缀
        task_modules.append(module_name)

IMAGE_QUEUE_NAME = "media_image"
VIDEO_QUEUE_NAME = "media_video"
OUTBOX_QUEUE_NAME = "outbox"

app = Celery(
    "steam_aggregation",
    broker=settings.CELERY_BROKER_URL,
    backend=settings.CELERY_RESULT_BACKEND,
    include=task_modules,
)

app.conf.update(
    task_serializer="json",
    accept_content=["json"],
    result_serializer="json",
    timezone="UTC",
    enable_utc=True,
    # 启用异步任务支持
    task_always_eager=False,
    worker_concurrency=4,
    # 支持异步任务结果
    result_backend_transport_options={
        "visibility_timeout": 3600,
        # 移除 master_name 配置，因为结果后端现在使用直连Redis
    },
    broker_transport_options={
        "master_name": settings.REDIS_SENTINEL_MASTER_NAME,
    },
    beat_schedule={
        # --- 高频增量任务 ---
        "update-item-profiles-incremental-hourly": {
            "task": "app.tasks.recommendation_tasks.task_update_item_profiles_incremental",
            "schedule": crontab(minute=0),  # 每小时的0分执行
        },
        "update-user-profiles-incremental-hourly": {
            "task": "app.tasks.recommendation_tasks.task_update_user_profiles_incremental",
            "schedule": crontab(minute=5),  # 每小时的5分执行
        },
        # --- 低频全量任务 ---
        "recluster-users-full-daily": {
            "task": "app.tasks.recommendation_tasks.task_recluster_users_full",
            "schedule": crontab(hour=2, minute=0),  # 每天凌晨2点执行
        },
        "regenerate-group-recommendations-full-daily": {
            "task": "app.tasks.recommendation_tasks.task_regenerate_group_recommendations_full",
            "schedule": crontab(hour=3, minute=0),  # 每天凌晨3点执行
        },
        "calculate-hot-content-hourly": {
            "task": "app.tasks.recommendation_tasks.task_calculate_hot_content",
            "schedule": 3600.0,
        },
        "calculate-item-similarity-daily": {
            "task": "app.tasks.recommendation_tasks.task_calculate_item_similarity",
            "schedule": crontab(hour=1, minute=0),
        },
        "cache-latest-content-hourly": {
            "task": "app.tasks.recommendation_tasks.task_cache_latest_content",
            "schedule": 3600.0,
        },
        # --- 用户缓存预热任务 ---
        "warm-up-user-cache-daily": {
            "task": "app.tasks.cache_tasks.task_warm_up_user_cache",
            "schedule": crontab(hour=4, minute=0),  # 每天凌晨4点执行
        },
        # --- 发件箱中继任务 ---
        "relay-outbox-messages-every-5-seconds": {
            "task": "app.tasks.cache_tasks.task_relay_outbox_messages",
            "schedule": 5.0,  # 每5秒执行一次
        },
        "cleanup-outbox-history-daily": {
            "task": "app.tasks.cache_tasks.cleanup_outbox_messages",
            "schedule": crontab(hour=3, minute=30),  # 每天凌晨3:30清理历史发件箱
            "kwargs": {"days": 30, "batch_size": 1000},
        },
        # --- 布隆过滤器维护任务 ---
        "rebuild-article-bloom-daily": {
            "task": "app.tasks.bloom_tasks.task_rebuild_article_bloom_filter",
            "schedule": crontab(hour=4, minute=15),  # 每天 04:15 UTC 执行
            "kwargs": {"drop_existing": True, "page_size": 2000},
        },
        # --- 通知清理任务 ---
        "cleanup-expired-notifications-daily": {
            "task": "app.tasks.notification_tasks.cleanup_expired_notifications",
            "schedule": crontab(hour=5, minute=0),  # 每天凌晨5点执行
        },
        # --- 热门标签任务 ---
        "rollup-tag-hot-rankings": {
            "task": "app.tasks.tag_hot_tasks.rollup_tag_hot_rankings",
            "schedule": 300.0,  # 每5分钟执行
        },
        "cleanup-tag-hot-windows": {
            "task": "app.tasks.tag_hot_tasks.cleanup_tag_hot_windows",
            "schedule": crontab(minute=45),  # 每小时45分清理一次
        },
        # --- 搜索统计聚合任务 ---
        "aggregate-search-stats-daily": {
            "task": "app.tasks.search_tasks.aggregate_search_stats",
            "schedule": crontab(hour=1, minute=30),  # 每天凌晨1:30执行
        },
        # --- 搜索建议更新任务 ---
        "update-search-suggestions-daily": {
            "task": "app.tasks.search_tasks.update_search_suggestions",
            "schedule": crontab(hour=2, minute=30),  # 每天凌晨2:30执行
            "kwargs": {"content_type": "all"},
        },
        # --- 搜索历史清理任务 ---
        "cleanup-old-search-history-daily": {
            "task": "app.tasks.search_tasks.cleanup_old_search_history",
            "schedule": crontab(hour=3, minute=15),  # 每天凌晨3:15执行
            "kwargs": {"days": 90},  # 保留90天
        },
        # --- 搜索索引维护任务 ---
        "vacuum-search-indexes-weekly": {
            "task": "app.tasks.search_tasks.vacuum_search_indexes",
            "schedule": crontab(hour=4, minute=30, day_of_week=0),  # 每周日凌晨4:30执行
        },
        # --- 热门搜索缓存刷新任务 ---
        "refresh-trending-cache-10min": {
            "task": "app.tasks.search_tasks.refresh_trending_cache",
            "schedule": 600.0,  # 每10分钟执行一次
        },
        # --- 搜索分析计算任务 ---
        "calculate-search-analytics-daily": {
            "task": "app.tasks.search_tasks.calculate_search_analytics",
            "schedule": crontab(hour=8, minute=0),  # 每天早上8点执行
        },
    },
)

app.conf.task_default_queue = "celery"
app.conf.task_queues = (
    Queue("celery", routing_key="celery"),
    Queue(IMAGE_QUEUE_NAME, routing_key=IMAGE_QUEUE_NAME),
    Queue(VIDEO_QUEUE_NAME, routing_key=VIDEO_QUEUE_NAME),
    Queue(OUTBOX_QUEUE_NAME, routing_key=OUTBOX_QUEUE_NAME),
)

app.autodiscover_tasks()
