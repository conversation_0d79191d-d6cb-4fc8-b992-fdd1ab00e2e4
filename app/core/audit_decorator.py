"""审计日志装饰器"""

import asyncio
import time
from functools import wraps
from typing import Any, Callable, Dict, Optional

from fastapi import Request
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.logging import logger
from app.models.audit_log import AuditAction, AuditResourceType
from app.models.user import User
from app.services.audit_log_service import AuditLogService


def audit_log(
    action: AuditAction,
    resource_type: AuditResourceType,
    description: Optional[str] = None,
    get_resource_id: Optional[Callable] = None,
    get_resource_name: Optional[Callable] = None,
    get_old_values: Optional[Callable] = None,
    get_new_values: Optional[Callable] = None,
    async_log: bool = True
):
    """
    审计日志装饰器
    
    Args:
        action: 操作类型
        resource_type: 资源类型
        description: 操作描述
        get_resource_id: 获取资源ID的函数
        get_resource_name: 获取资源名称的函数
        get_old_values: 获取操作前值的函数
        get_new_values: 获取操作后值的函数
        async_log: 是否异步记录日志
    """
    
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            start_time = time.time()
            success = True
            error_message = None
            result = None
            
            # 提取参数
            db: Optional[AsyncSession] = None
            current_user: Optional[User] = None
            request: Optional[Request] = None
            
            # 从kwargs中提取常见参数
            for key, value in kwargs.items():
                if key == "db" and isinstance(value, AsyncSession):
                    db = value
                elif key == "current_user" and isinstance(value, User):
                    current_user = value
                elif key == "request" and isinstance(value, Request):
                    request = value
            
            # 从args中提取（按FastAPI常见顺序）
            if not db and args:
                for arg in args:
                    if isinstance(arg, AsyncSession):
                        db = arg
                        break
            
            if not current_user and args:
                for arg in args:
                    if isinstance(arg, User):
                        current_user = arg
                        break
            
            if not request and args:
                for arg in args:
                    if isinstance(arg, Request):
                        request = arg
                        break
            
            # 获取操作前的值
            old_values = None
            if get_old_values:
                try:
                    old_values = await get_old_values(*args, **kwargs) if asyncio.iscoroutinefunction(get_old_values) else get_old_values(*args, **kwargs)
                except Exception as e:
                    logger.warning(f"获取操作前值失败: {str(e)}")
            
            try:
                # 执行原函数
                result = await func(*args, **kwargs)
                
            except Exception as e:
                success = False
                error_message = str(e)
                raise
            
            finally:
                # 记录审计日志
                if db and current_user:
                    try:
                        duration_ms = int((time.time() - start_time) * 1000)
                        
                        # 获取资源信息
                        resource_id = None
                        if get_resource_id:
                            try:
                                resource_id = await get_resource_id(result, *args, **kwargs) if asyncio.iscoroutinefunction(get_resource_id) else get_resource_id(result, *args, **kwargs)
                            except Exception as e:
                                logger.warning(f"获取资源ID失败: {str(e)}")
                        
                        resource_name = None
                        if get_resource_name:
                            try:
                                resource_name = await get_resource_name(result, *args, **kwargs) if asyncio.iscoroutinefunction(get_resource_name) else get_resource_name(result, *args, **kwargs)
                            except Exception as e:
                                logger.warning(f"获取资源名称失败: {str(e)}")
                        
                        # 获取操作后的值
                        new_values = None
                        if get_new_values:
                            try:
                                new_values = await get_new_values(result, *args, **kwargs) if asyncio.iscoroutinefunction(get_new_values) else get_new_values(result, *args, **kwargs)
                            except Exception as e:
                                logger.warning(f"获取操作后值失败: {str(e)}")
                        
                        # 记录日志
                        log_task = AuditLogService.log_action(
                            db=db,
                            user=current_user,
                            action=action,
                            resource_type=resource_type,
                            resource_id=resource_id,
                            resource_name=resource_name,
                            description=description or f"{action.value} {resource_type.value}",
                            old_values=old_values,
                            new_values=new_values,
                            request=request,
                            success=success,
                            error_message=error_message,
                            duration_ms=duration_ms
                        )
                        
                        if async_log:
                            # 异步记录，不阻塞主流程
                            asyncio.create_task(log_task)
                        else:
                            # 同步记录
                            await log_task
                            
                    except Exception as e:
                        logger.error(f"记录审计日志失败: {str(e)}")
                        # 不影响主业务流程
            
            return result
        
        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            # 对于同步函数的处理
            logger.warning(f"审计装饰器应用于同步函数 {func.__name__}，建议使用异步函数")
            return func(*args, **kwargs)
        
        return async_wrapper if asyncio.iscoroutinefunction(func) else sync_wrapper
    
    return decorator


# 常用的审计装饰器预设
def audit_create(resource_type: AuditResourceType, **kwargs):
    """创建操作审计"""
    return audit_log(AuditAction.CREATE, resource_type, **kwargs)


def audit_update(resource_type: AuditResourceType, **kwargs):
    """更新操作审计"""
    return audit_log(AuditAction.UPDATE, resource_type, **kwargs)


def audit_delete(resource_type: AuditResourceType, **kwargs):
    """删除操作审计"""
    return audit_log(AuditAction.DELETE, resource_type, **kwargs)


def audit_approve(resource_type: AuditResourceType, **kwargs):
    """审批操作审计"""
    return audit_log(AuditAction.APPROVE, resource_type, **kwargs)


def audit_reject(resource_type: AuditResourceType, **kwargs):
    """拒绝操作审计"""
    return audit_log(AuditAction.REJECT, resource_type, **kwargs)


def audit_block(resource_type: AuditResourceType, **kwargs):
    """封禁操作审计"""
    return audit_log(AuditAction.BLOCK, resource_type, **kwargs)
