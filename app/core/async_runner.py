"""Celery 任务统一的异步执行器."""

from __future__ import annotations

import asyncio
import threading
from collections.abc import Awaitable
from typing import TypeVar

from celery import signals

_T = TypeVar("_T")

_runner: asyncio.Runner | None = None
_runner_lock = threading.Lock()


def _ensure_runner() -> asyncio.Runner:
    global _runner
    if _runner is None:
        with _runner_lock:
            if _runner is None:
                _runner = asyncio.Runner()
    return _runner


def run_async(coro: Awaitable[_T], *, timeout: float | None = 300.0) -> _T:
    """在复用的事件循环中同步执行协程."""

    async def _run_with_optional_timeout() -> _T:
        if timeout is None:
            return await coro
        return await asyncio.wait_for(coro, timeout=timeout)

    runner = _ensure_runner()
    return runner.run(_run_with_optional_timeout())


@signals.worker_process_init.connect
def _init_runner(**_: object) -> None:
    _ensure_runner()


@signals.worker_process_shutdown.connect
def _close_runner(**_: object) -> None:
    global _runner
    with _runner_lock:
        if _runner is not None:
            _runner.close()
            _runner = None
