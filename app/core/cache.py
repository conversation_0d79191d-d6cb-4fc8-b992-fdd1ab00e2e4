"""缓存工具"""

from __future__ import annotations

import json
from dataclasses import dataclass
from typing import Any


@dataclass
class ConfigCache:
    """配置中心缓存封装"""

    master: Any
    reader: Any | None = None

    async def set_config(self, key: str, value: str | None) -> None:
        if not self.master:
            return
        try:
            await self.master.set(f"admin:config:{key}", value or "")
        except Exception:
            pass

    async def get_config(self, key: str) -> str | None:
        client = self.reader or self.master
        if not client:
            return None
        try:
            return await client.get(f"admin:config:{key}")
        except Exception:
            return None

    async def set_feature(self, name: str, enabled: bool) -> None:
        if not self.master:
            return
        try:
            await self.master.set(f"admin:feature:{name}", "true" if enabled else "false")
        except Exception:
            pass

    async def get_feature(self, name: str) -> bool | None:
        client = self.reader or self.master
        if not client:
            return None
        try:
            value = await client.get(f"admin:feature:{name}")
        except Exception:
            return None
        if value is None:
            return None
        return value == "true"

    async def set_payload(self, key: str, payload: dict[str, Any]) -> None:
        if not self.master:
            return
        try:
            await self.master.set(key, json.dumps(payload))
        except Exception:
            pass
