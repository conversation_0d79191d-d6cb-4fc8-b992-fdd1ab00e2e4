from datetime import datetime
from typing import Literal

from pydantic import BaseModel


class LikeBase(BaseModel):
    """点赞基础模型"""

    content_type: Literal["article", "video", "comment", "scratch", "post"]
    content_id: int


class LikeCreate(LikeBase):
    """创建点赞的请求模型"""

    pass


class LikeToggle(LikeBase):
    """切换点赞状态的请求模型"""

    pass


class LikeInDBBase(LikeBase):
    """数据库中点赞的基础模型"""

    id: int
    user_id: int
    is_active: bool
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class Like(LikeInDBBase):
    """API响应中的点赞模型"""

    pass


class LikeStatus(BaseModel):
    """点赞状态响应模型"""

    content_type: Literal["article", "video", "comment", "scratch", "post"]
    content_id: int
    is_liked: bool
    like_count: int


class LikeStats(BaseModel):
    """点赞统计模型"""

    total_likes: int
    today_likes: int
    this_week_likes: int
    this_month_likes: int


class ContentLikeInfo(BaseModel):
    """内容点赞信息模型"""

    content_type: Literal["article", "video", "comment", "scratch", "post"]
    content_id: int
    like_count: int
    is_liked_by_user: bool = False  # 当前用户是否已点赞
