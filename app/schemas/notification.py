from datetime import datetime, timezone
from enum import Enum
from typing import Any

from pydantic import BaseModel, ConfigDict, Field, field_validator


class NotificationChannel(str, Enum):
    """广播通知支持的渠道"""

    IN_APP = "in_app"
    EMAIL = "email"
    PUSH = "push"


class NotificationBroadcastRequest(BaseModel):
    """广播通知请求模型"""

    title: str = Field(..., description="通知标题")
    message: str = Field(..., description="通知内容")
    type: str | None = Field(None, description="通知类型枚举值")
    priority: str | None = Field(None, description="通知优先级枚举值")
    data: dict[str, Any] | None = Field(None, description="附加扩展数据")
    action_url: str | None = Field(None, description="通知跳转链接")
    expires_in_hours: int | None = Field(
        None, ge=1, description="通知有效期（小时），默认 168 小时"
    )
    target_user_type: str | None = Field(
        None, description="目标用户类别，例如 all/active"
    )
    schedule_at: datetime | None = Field(
        None, description="定时发送时间（ISO8601，需晚于当前时间）"
    )
    channels: list[NotificationChannel] = Field(
        default_factory=lambda: [NotificationChannel.IN_APP],
        description="通知渠道列表，当前支持 in_app/email/push",
    )

    model_config = ConfigDict(from_attributes=True)

    @field_validator("schedule_at", mode="after")
    @classmethod
    def validate_schedule_at(cls, value: datetime | None) -> datetime | None:
        """确保定时发送时间有效并统一为UTC"""
        if value is None:
            return value

        if value.tzinfo is None:
            value = value.replace(tzinfo=timezone.utc)

        now_utc = datetime.now(timezone.utc)
        if value <= now_utc:
            raise ValueError("定时发送时间必须晚于当前时间")

        return value.astimezone(timezone.utc)

    @field_validator("channels", mode="after")
    @classmethod
    def validate_channels(cls, value: list[NotificationChannel]) -> list[NotificationChannel]:
        """去重并保证至少包含一个渠道"""
        if not value:
            return [NotificationChannel.IN_APP]

        seen: set[NotificationChannel] = set()
        unique_channels: list[NotificationChannel] = []
        for channel in value:
            if channel not in seen:
                unique_channels.append(channel)
                seen.add(channel)

        return unique_channels
