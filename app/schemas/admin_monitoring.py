from datetime import datetime
from decimal import Decimal
from enum import Enum
from typing import Any

from pydantic import BaseModel, ConfigDict, Field, field_validator


class MetricPoint(BaseModel):
    """单个指标点"""

    timestamp: datetime
    value: float


class MetricSeriesResponse(BaseModel):
    """指标序列响应"""

    metric_name: str
    interval: str
    points: list[MetricPoint] = Field(default_factory=list)


class AlertOperator(str, Enum):
    GREATER_THAN = "greater_than"
    GREATER_OR_EQUAL = "greater_or_equal"
    LESS_THAN = "less_than"
    LESS_OR_EQUAL = "less_or_equal"
    EQUAL = "equal"
    NOT_EQUAL = "not_equal"


class AlertSeverity(str, Enum):
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


class AlertChannel(BaseModel):
    """告警渠道配置"""

    type: str
    target: str | None = None
    config: dict[str, Any] = Field(default_factory=dict)


class AlertRuleBase(BaseModel):
    """告警规则基础信息"""

    name: str
    metric_name: str
    operator: AlertOperator
    threshold: Decimal
    duration: str
    severity: AlertSeverity
    channels: list[AlertChannel] = Field(default_factory=list)
    enabled: bool = True
    description: str | None = None
    created_at: datetime | None = None
    updated_at: datetime | None = None
    created_by: int | None = None
    updated_by: int | None = None

    model_config = ConfigDict(from_attributes=True)

    @field_validator("threshold", mode="before")
    @classmethod
    def _parse_threshold(cls, value: Any) -> Decimal:
        if value is None:
            return Decimal(0)
        if isinstance(value, Decimal):
            return value
        return Decimal(str(value))


class AlertRule(AlertRuleBase):
    """告警规则响应"""

    id: int


class AlertRuleCreateRequest(BaseModel):
    """创建告警规则请求"""

    name: str
    metric_name: str
    operator: AlertOperator
    threshold: Decimal
    duration: str
    severity: AlertSeverity
    channels: list[AlertChannel]
    description: str | None = None

    @field_validator("threshold", mode="before")
    @classmethod
    def _parse_threshold(cls, value: Any) -> Decimal:
        if isinstance(value, Decimal):
            return value
        return Decimal(str(value))


class AlertRuleUpdateRequest(BaseModel):
    """更新告警规则请求"""

    metric_name: str | None = None
    operator: AlertOperator | None = None
    threshold: Decimal | None = None
    duration: str | None = None
    severity: AlertSeverity | None = None
    channels: list[AlertChannel] | None = None
    enabled: bool | None = None
    description: str | None = None

    @field_validator("threshold", mode="before")
    @classmethod
    def _parse_threshold(cls, value: Any) -> Decimal | None:
        if value is None:
            return None
        if isinstance(value, Decimal):
            return value
        return Decimal(str(value))


class AlertEvent(BaseModel):
    """告警事件响应"""

    id: int
    rule_id: int | None = None
    event_id: str
    status: str
    trigger_value: float
    triggered_at: datetime
    resolved_at: datetime | None = None
    context: dict[str, Any] = Field(default_factory=dict)
    message: str | None = None
    notification_status: str

    model_config = ConfigDict(from_attributes=True)
