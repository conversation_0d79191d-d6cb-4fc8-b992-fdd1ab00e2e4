from datetime import datetime
from enum import Enum
from typing import Optional

from pydantic import BaseModel, ConfigDict, Field, model_serializer

from app.schemas.review import ReviewBase
from app.schemas.user import UserAggregated
from app.utils.url import prepend_cdn


class PostType(str, Enum):
    """沸点类型枚举"""

    TEXT = "text"
    IMAGE = "image"
    VIDEO = "video"
    LINK = "link"
    POLL = "poll"
    REPOST = "repost"


class PostStatus(str, Enum):
    """沸点状态枚举"""

    DRAFT = "draft"
    PUBLISHED = "published"
    DELETED = "deleted"
    HIDDEN = "hidden"


class PostVisibility(str, Enum):
    """沸点可见性枚举"""

    PUBLIC = "public"
    FOLLOWERS = "followers"
    PRIVATE = "private"


class PostMediaBase(BaseModel):
    """沸点媒体基础模型"""

    media_type: str = Field(..., description="媒体类型: image, video, audio")
    media_url: str = Field(..., description="媒体文件URL")
    thumbnail_url: str | None = Field(None, description="缩略图URL")
    media_size: int | None = Field(None, description="文件大小（字节）")
    media_width: int | None = Field(None, description="媒体宽度")
    media_height: int | None = Field(None, description="媒体高度")
    media_duration: int | None = Field(None, description="媒体时长（秒）")
    sort_order: int = Field(0, description="排序顺序")

    model_config = ConfigDict(from_attributes=True)


class PostMediaCreate(PostMediaBase):
    """沸点媒体创建模型"""

    post_id: int = Field(..., description="沸点ID")


class PostMediaOut(PostMediaBase):
    """沸点媒体输出模型"""

    id: int = Field(..., description="媒体ID")
    post_id: int = Field(..., description="沸点ID")
    created_at: datetime = Field(..., description="创建时间")

    @model_serializer(mode="wrap")
    def ser_model(self, nxt):
        data = nxt(self)

        for url_field in ["media_url", "thumbnail_url"]:
            data[url_field] = prepend_cdn(data.get(url_field))

        return data


class PostMentionBase(BaseModel):
    """沸点@提及基础模型"""

    mentioned_user_id: int = Field(..., description="被提及用户ID")
    mention_text: str = Field(..., description="@提及的文本")

    model_config = ConfigDict(from_attributes=True)


class PostMentionCreate(PostMentionBase):
    """沸点@提及创建模型"""

    post_id: int = Field(..., description="沸点ID")


class PostMentionOut(PostMentionBase):
    """沸点@提及输出模型"""

    id: int = Field(..., description="提及ID")
    mentioned_user: UserAggregated | None = Field(None, description="被提及用户信息")
    created_at: datetime = Field(..., description="创建时间")


class PostPollOption(BaseModel):
    """投票选项模型"""

    text: str = Field(..., description="选项文本")
    vote_count: int = Field(0, description="投票数")
    vote_ratio: float | None = Field(None, description="投票占比，范围 0-1")


class PostPollOptionStat(PostPollOption):
    """投票选项的实时统计信息"""

    index: int = Field(..., description="选项索引")


class PostPollSummary(BaseModel):
    """投票实时统计摘要"""

    post_id: int = Field(..., description="沸点ID")
    total_votes: int = Field(0, description="总投票数")
    option_stats: list[PostPollOptionStat] = Field(
        default_factory=list, description="各选项实时统计"
    )
    user_choices: list[int] = Field(default_factory=list, description="当前用户所选选项索引")
    multiple_choice: bool = Field(False, description="是否允许多选")
    expires_at: datetime | None = Field(None, description="投票截止时间")


class PostPollVoteOut(BaseModel):
    """投票记录输出模型"""

    id: int = Field(..., description="投票ID")
    user_id: int = Field(..., description="投票用户ID")
    option_index: int = Field(..., description="投票选项索引")
    created_at: datetime = Field(..., description="投票时间")

    model_config = ConfigDict(from_attributes=True)


class PostPollVoteCreate(BaseModel):
    """投票记录创建模型"""

    post_id: int = Field(..., description="沸点ID")
    user_id: int = Field(..., description="投票用户ID")
    option_index: int = Field(..., description="投票选项索引")

    model_config = ConfigDict(from_attributes=True)


class PostStats(BaseModel):
    """沸点统计信息"""

    content_id: int = Field(..., description="内容ID")
    like_count: int = Field(0, description="点赞数")
    comment_count: int = Field(0, description="评论数")
    repost_count: int = Field(0, description="转发数")
    view_count: int = Field(0, description="浏览数")

    # 用户相关统计
    is_liked_by_user: bool = Field(False, description="当前用户是否已点赞")
    is_reposted_by_user: bool = Field(False, description="当前用户是否已转发")
    is_followed_author: bool = Field(False, description="当前用户是否已关注作者")


class PostBase(BaseModel):
    """沸点基础模型"""

    id: int = Field(..., description="沸点ID")
    author_id: int = Field(..., description="作者ID")
    content: str | None = Field(None, description="沸点文本内容")
    post_type: PostType = Field(PostType.TEXT, description="沸点类型")
    status: PostStatus = Field(PostStatus.PUBLISHED, description="沸点状态")
    visibility: PostVisibility = Field(PostVisibility.PUBLIC, description="可见性")
    topic: str | None = Field(None, description="话题标签")
    location: str | None = Field(None, description="位置信息")
    poll_options: list[PostPollOption] | None = Field(None, description="投票选项")
    poll_expires_at: datetime | None = Field(None, description="投票截止时间")
    poll_multiple_choice: bool | None = Field(None, description="是否允许多选")
    is_pinned: bool = Field(False, description="是否置顶")
    is_hot: bool = Field(False, description="是否热门")
    hot_score: int = Field(0, description="热度分数")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")

    model_config = ConfigDict(from_attributes=True)


class PostCreate(BaseModel):
    """创建沸点的请求模型"""

    draft_id: int | None = Field(None, description="草稿ID，用于发布草稿时关联")
    content: str | None = Field(None, description="沸点文本内容", max_length=500)
    post_type: PostType = Field(PostType.TEXT, description="沸点类型")
    visibility: PostVisibility = Field(PostVisibility.PUBLIC, description="可见性")
    topic: str | None = Field(None, description="话题标签", max_length=100)
    location: str | None = Field(None, description="位置信息", max_length=200)

    # 转发相关
    original_post_id: int | None = Field(None, description="原始沸点ID（转发时使用）")
    repost_comment: str | None = Field(None, description="转发评论", max_length=200)

    # 链接分享相关
    link_url: str | None = Field(None, description="分享链接URL", max_length=500)
    link_title: str | None = Field(None, description="链接标题", max_length=200)
    link_description: str | None = Field(None, description="链接描述")
    link_image: str | None = Field(None, description="链接预览图", max_length=500)

    # 投票相关
    poll_options: list[str] | None = Field(None, description="投票选项列表")
    poll_expires_hours: int | None = Field(
        None, description="投票有效期（小时）", ge=1, le=168
    )  # 最多7天
    poll_multiple_choice: bool = Field(False, description="是否允许多选")

    # 媒体文件
    media_files: list[str] | None = Field(None, description="媒体文件URL列表")

    # @提及
    mentions: list[str] | None = Field(None, description="@提及的用户名列表")


class PostUpdate(BaseModel):
    """更新沸点的请求模型"""

    content: str | None = Field(None, description="沸点文本内容", max_length=500)
    visibility: PostVisibility | None = Field(None, description="可见性")
    topic: str | None = Field(None, description="话题标签", max_length=100)
    location: str | None = Field(None, description="位置信息", max_length=200)


class PostOut(PostBase):
    """统一的沸点响应模型，用于API输出"""

    author: UserAggregated = Field(..., description="作者信息")
    stats: PostStats | None = Field(None, description="统计信息")
    media: list[PostMediaOut] | None = Field(None, description="媒体文件列表")
    mentions: list[PostMentionOut] | None = Field(None, description="@提及列表")
    review: ReviewBase | None = Field(None, description="审核信息")

    # 转发相关
    original_post_id: int | None = Field(None, description="原始沸点ID")
    original_post: Optional["PostOut"] = Field(None, description="原始沸点信息")
    repost_comment: str | None = Field(None, description="转发评论")

    # 链接分享相关
    link_url: str | None = Field(None, description="分享链接URL")
    link_title: str | None = Field(None, description="链接标题")
    link_description: str | None = Field(None, description="链接描述")
    link_image: str | None = Field(None, description="链接预览图")

    # 投票相关
    poll_options: list[PostPollOption] | None = Field(None, description="投票选项")
    poll_expires_at: datetime | None = Field(None, description="投票截止时间")
    poll_multiple_choice: bool | None = Field(None, description="是否允许多选")
    poll_votes: list[PostPollVoteOut] | None = Field(None, description="投票记录")
    user_poll_votes: list[int] | None = Field(None, description="当前用户的投票选项索引")
    poll_summary: PostPollSummary | None = Field(None, description="投票实时统计摘要")

    model_config = ConfigDict(from_attributes=True)

    @model_serializer(mode="wrap")
    def ser_model(self, nxt):
        data = nxt(self)

        data["link_url"] = prepend_cdn(data.get("link_url"))
        data["link_image"] = prepend_cdn(data.get("link_image"))

        if data.get("media"):
            for media in data["media"]:
                if isinstance(media, dict):
                    media["media_url"] = prepend_cdn(media.get("media_url"))
                    media["thumbnail_url"] = prepend_cdn(media.get("thumbnail_url"))

        if data.get("original_post") and isinstance(data["original_post"], dict):
            original = data["original_post"]
            original["link_url"] = prepend_cdn(original.get("link_url"))
            original["link_image"] = prepend_cdn(original.get("link_image"))

        return data


class PostsPageResponse(BaseModel):
    """分页沸点响应模型"""

    posts: list[PostOut] = Field(default_factory=list, description="沸点列表")
    has_next: bool = Field(False, description="是否还有下一页")
    has_previous: bool = Field(False, description="是否有上一页")
    total_count: int = Field(0, description="总数量")
    page: int = Field(1, description="当前页码")
    size: int = Field(20, description="每页数量")


class PostListQuery(BaseModel):
    """沸点列表查询参数"""

    author_id: int | None = Field(None, description="作者ID")
    post_type: PostType | None = Field(None, description="沸点类型")
    topic: str | None = Field(None, description="话题标签")
    visibility: PostVisibility | None = Field(None, description="可见性")
    is_hot: bool | None = Field(None, description="是否热门")
    page: int = Field(1, description="页码", ge=1)
    size: int = Field(20, description="每页数量", ge=1, le=100)
    sort_by: str = Field("created_at", description="排序字段")
    sort_order: str = Field("desc", description="排序方向", pattern="^(asc|desc)$")


class PostPollVoteRequest(BaseModel):
    """投票请求模型"""

    option_indexes: list[int] = Field(..., description="投票选项索引列表")


class PostDraftSave(BaseModel):
    """保存沸点草稿的请求模型"""

    id: int | None = Field(None, description="草稿ID，存在时表示更新草稿")
    content: str | None = Field(None, description="草稿内容", max_length=500)
    post_type: PostType | None = Field(None, description="草稿沸点类型")
    visibility: PostVisibility | None = Field(None, description="草稿可见性")
    topic: str | None = Field(None, description="草稿话题标签", max_length=100)
    location: str | None = Field(None, description="草稿位置信息", max_length=200)
    media_files: list[str] | None = Field(None, description="草稿媒体文件URL列表")


class PostDraftOut(BaseModel):
    """沸点草稿输出模型"""

    id: int = Field(..., description="草稿ID")
    author_id: int = Field(..., description="作者ID")
    content: str | None = Field(None, description="草稿内容")
    post_type: PostType = Field(PostType.TEXT, description="草稿沸点类型")
    visibility: PostVisibility = Field(PostVisibility.PRIVATE, description="草稿可见性")
    topic: str | None = Field(None, description="草稿话题标签")
    location: str | None = Field(None, description="草稿位置信息")
    status: PostStatus = Field(PostStatus.DRAFT, description="草稿状态")
    created_at: datetime = Field(..., description="草稿创建时间")
    updated_at: datetime = Field(..., description="草稿更新时间")
    media: list[PostMediaOut] | None = Field(None, description="草稿媒体文件列表")

    model_config = ConfigDict(from_attributes=True)

    @model_serializer(mode="wrap")
    def ser_model(self, nxt):
        data = nxt(self)
        if data.get("media"):
            for media in data["media"]:
                if isinstance(media, dict):
                    media["media_url"] = prepend_cdn(media.get("media_url"))
                    media["thumbnail_url"] = prepend_cdn(media.get("thumbnail_url"))
        return data


class PostDraftListResponse(BaseModel):
    """沸点草稿列表响应"""

    items: list[PostDraftOut] = Field(default_factory=list, description="草稿列表")


# 解决循环引用问题
PostOut.model_rebuild()
