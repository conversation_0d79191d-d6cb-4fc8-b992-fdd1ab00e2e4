from datetime import datetime
from enum import Enum
from typing import Any

from pydantic import BaseModel, ConfigDict, Field


class ConfigValueType(str, Enum):
    STRING = "string"
    INT = "int"
    FLOAT = "float"
    BOOLEAN = "boolean"
    JSON = "json"
    SECRET = "secret"


class ConfigSettingBase(BaseModel):
    """配置项基础信息"""

    category: str
    key: str
    value: Any
    value_type: ConfigValueType
    description: str | None = None
    metadata: dict[str, Any] = Field(default_factory=dict, alias="settings_metadata")
    version: int = 1
    is_sensitive: bool = False
    updated_at: datetime | None = None
    updated_by: int | None = None

    model_config = ConfigDict(from_attributes=True, populate_by_name=True)


class ConfigSetting(ConfigSettingBase):
    """配置项响应模型"""

    id: int


class ConfigMutation(BaseModel):
    """配置变更请求项"""

    key: str
    value: Any
    value_type: ConfigValueType | None = None
    reason: str | None = None
    version: int | None = Field(
        default=None, description="乐观锁版本；为空则使用当前版本"
    )
    is_sensitive: bool | None = None


class ConfigUpdateRequest(BaseModel):
    """批量更新配置请求"""

    configs: list[ConfigMutation]


class ConfigUpdateResultEntry(BaseModel):
    """配置更新结果"""

    key: str
    old_value: Any | None = None
    new_value: Any | None = None
    status: str


class ConfigBatchUpdateResponse(BaseModel):
    """批量更新响应"""

    change_id: str
    updated_configs: list[ConfigUpdateResultEntry] = Field(default_factory=list)
    broadcast_status: str = "pending"


class ConfigHistoryEntry(BaseModel):
    """配置变更历史条目"""

    change_id: str
    config_key: str
    category: str | None = None
    old_value: Any | None = None
    new_value: Any | None = None
    change_type: str
    change_reason: str | None = None
    changed_by: int | None = None
    changed_at: datetime
    rollback_id: str | None = None
    is_rollback: bool = False

    model_config = ConfigDict(from_attributes=True)


class ConfigHistoryResponse(BaseModel):
    """配置历史分页响应"""

    total: int
    page: int
    page_size: int
    changes: list[ConfigHistoryEntry] = Field(default_factory=list)


class ConfigRollbackRequest(BaseModel):
    """配置回滚请求"""

    reason: str
    confirm: bool = Field(..., description="必须为True以确认回滚")


class ConfigRollbackResponse(BaseModel):
    """配置回滚响应"""

    rollback_id: str
    original_change_id: str
    status: str
    rollback_at: datetime
    rollback_by: int | None = None


class FeatureToggleBase(BaseModel):
    """功能开关基础信息"""

    name: str
    is_enabled: bool
    description: str | None = None
    scope: str = "global"
    config: dict[str, Any] = Field(default_factory=dict, alias="toggle_config")
    updated_at: datetime | None = None
    updated_by: int | None = None

    model_config = ConfigDict(from_attributes=True, populate_by_name=True)


class FeatureToggle(FeatureToggleBase):
    """功能开关响应"""

    id: int


class FeatureToggleUpdateRequest(BaseModel):
    """功能开关更新请求"""

    is_enabled: bool
    reason: str | None = None
    config: dict[str, Any] | None = None


class FeatureToggleUpdateResponse(BaseModel):
    """功能开关更新响应"""

    name: str
    is_enabled: bool
    updated_at: datetime
    broadcast_status: str = "pending"


class ConfigListResponse(BaseModel):
    """配置与功能开关列表"""

    configs: list[ConfigSetting] = Field(default_factory=list)
    features: list[FeatureToggle] = Field(default_factory=list)
