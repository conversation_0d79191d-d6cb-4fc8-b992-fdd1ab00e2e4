from datetime import date, datetime
from enum import Enum
from typing import Any

from pydantic import BaseModel, ConfigDict, Field


class SnapshotType(str, Enum):
    DASHBOARD = "dashboard"
    USERS = "users"
    CONTENT = "content"
    SYSTEM = "system"


class TimeRange(str, Enum):
    TODAY = "today"
    YESTERDAY = "yesterday"
    LAST_7_DAYS = "last_7_days"
    LAST_30_DAYS = "last_30_days"
    LAST_90_DAYS = "last_90_days"
    CUSTOM = "custom"


class AnalyticsSnapshotBase(BaseModel):
    """分析快照基础信息"""

    snapshot_type: SnapshotType
    time_range: str
    filters: dict[str, Any] = Field(default_factory=dict)
    record_count: int = 0
    generated_at: datetime | None = None
    generation_duration: int = 0
    status: str = "pending"

    model_config = ConfigDict(from_attributes=True)


class AnalyticsSnapshot(AnalyticsSnapshotBase):
    """分析快照响应"""

    id: int


class AnalyticsSnapshotData(BaseModel):
    """分析快照数据记录"""

    id: int
    snapshot_id: int
    data_type: str = "summary"
    payload: dict[str, Any] = Field(default_factory=dict)
    created_at: datetime

    model_config = ConfigDict(from_attributes=True)


class AnalyticsQuery(BaseModel):
    """通用分析查询参数"""

    date_range: TimeRange = TimeRange.LAST_7_DAYS
    from_date: date | None = None
    to_date: date | None = None
    filters: dict[str, Any] | None = None


class AnalyticsExportRequest(BaseModel):
    """数据导出请求"""

    dataset: SnapshotType
    time_range: TimeRange = TimeRange.LAST_7_DAYS
    filters: dict[str, Any] = Field(default_factory=dict)
    async_mode: bool = True


class AnalyticsExportResponse(BaseModel):
    """数据导出响应"""

    export_id: str
    status: str
    download_url: str | None = None
    estimated_rows: int | None = None
