from __future__ import annotations

import logging
from datetime import datetime

from pydantic import BaseModel, ConfigDict, EmailStr, Field, model_serializer, model_validator

from app.utils.url import prepend_cdn

from .user_stats import UserStats

logger = logging.getLogger(__name__)




class UserRoleBase(BaseModel):
    id: int
    name: str
    desc: str
    is_default: bool
    is_active: bool
    created_at: datetime
    updated_at: datetime

    model_config = {"from_attributes": True}


class UserRoleResponse(BaseModel):
    name: str

    model_config = {"from_attributes": True}


class UserFollow(BaseModel):
    user_id: int  # 被关注的用户ID
    follow_id: int  # 关注的用户ID


class UserBase(BaseModel):
    id: int = Field(..., description="用户ID")
    username: str = Field(..., description="用户名")
    role_id: int = Field(..., description="角色ID")
    nickname: str = Field(..., description="昵称")
    description: str | None = Field(None, description="描述")
    email: str | None = Field(None, description="邮箱")
    avatar: str | None = Field(None, description="头像")
    cover: str | None = Field(None, description="用户封面")
    is_active: bool = Field(True, description="是否激活")
    is_superuser: bool = Field(False, description="是否超级用户")
    last_login: datetime | None = Field(None, description="上次登录时间")
    created_at: datetime | None = Field(..., description="创建时间")
    updated_at: datetime | None = Field(..., description="更新时间")
    likes_privacy_settings: int = Field(3, description="点赞隐私设置")
    favorites_privacy_settings: int = Field(3, description="收藏隐私设置")
    wechat_openid: str | None = Field(None, description="微信OpenID")
    wechat_unionid: str | None = Field(None, description="微信UnionID")
    login_type: str = Field("password", description="登录方式")

    model_config = ConfigDict(
        from_attributes=True,
        exclude={"videos", "articles", "comments", "reviews", "video_folders", "devices"},
    )


class UserUpdate(BaseModel):
    email: EmailStr | None = None
    nickname: str | None = None
    description: str | None = None
    avatar: str | None = None
    is_active: bool | None = None
    role_id: int | None = None
    likes_privacy_settings: int | None = None
    favorites_privacy_settings: int | None = None


class UserBanRequest(BaseModel):
    """用户封禁请求"""
    user_id: int = Field(..., description="用户ID")
    reason: str = Field(..., min_length=1, max_length=500, description="封禁原因")


class UserUnbanRequest(BaseModel):
    """用户解封请求"""
    user_id: int = Field(..., description="用户ID")
    reason: str | None = Field(None, max_length=500, description="解封原因")


class User(UserBase):
    @model_serializer(mode="wrap")
    def ser_model(self, nxt):
        data = nxt(self)

        # 对 username 进行脱敏处理
        if "username" in data and len(data["username"]) == 11 and data["username"].isdigit():
            data["username"] = data["username"][:3] + "****" + data["username"][7:]

        # 对 email 进行脱敏处理
        if "email" in data and data["email"]:
            try:
                username, domain = data["email"].split("@")
                if len(username) <= 2:
                    data["email"] = username[0] + "*" * (len(username) - 1) + "@" + domain
                else:
                    data["email"] = username[:2] + "*" * (len(username) - 2) + "@" + domain
            except ValueError:
                pass  # 保持原样

        # 对微信ID进行脱敏处理
        sensitive_fields = ["wechat_openid", "wechat_unionid"]
        for field in sensitive_fields:
            if field in data and data[field] and len(data[field]) >= 8:
                data[field] = data[field][:4] + "*" * (len(data[field]) - 8) + data[field][-4:]

        data["avatar"] = prepend_cdn(data.get("avatar"))
        data["cover"] = prepend_cdn(data.get("cover"))

        return data

    model_config = ConfigDict(from_attributes=True)


class UserAggregated(UserBase):
    """聚合后的用户完整信息，包含统计数据"""

    stats: UserStats | None = Field(None, description="用户统计数据")

    @model_validator(mode="before")
    @classmethod
    def validate_user_fields(cls, values):
        """
        处理 UserAggregated 中的字段转换，确保特殊字段正确处理
        """
        if isinstance(values, dict):
            # 记录输入数据的结构
            logger.debug(f"UserAggregated验证前 - 输入字段: {list(values.keys())}")
            logger.debug(f"UserAggregated验证前 - stats类型: {type(values.get('stats'))}")

            # 处理 stats 字段的转换（如果需要的话）
            if "stats" in values and values["stats"] is not None:
                stats_data = values["stats"]
                logger.debug(f"处理stats - 类型: {type(stats_data)}, 值: {stats_data}")

        return values

    @model_serializer(mode="wrap")
    def ser_model(self, nxt):
        data = nxt(self)

        # 对 username 进行脱敏处理
        if "username" in data and len(data["username"]) == 11 and data["username"].isdigit():
            data["username"] = data["username"][:3] + "****" + data["username"][7:]

        # 对 email 进行脱敏处理
        if "email" in data and data["email"]:
            try:
                username, domain = data["email"].split("@")
                if len(username) <= 2:
                    data["email"] = username[0] + "*" * (len(username) - 1) + "@" + domain
                else:
                    data["email"] = username[:2] + "*" * (len(username) - 2) + "@" + domain
            except ValueError:
                pass  # 保持原样

        # 对微信ID进行脱敏处理
        sensitive_fields = ["wechat_openid", "wechat_unionid"]
        for field in sensitive_fields:
            if field in data and data[field] and len(data[field]) >= 8:
                data[field] = data[field][:4] + "*" * (len(data[field]) - 8) + data[field][-4:]

        data["avatar"] = prepend_cdn(data.get("avatar"))
        data["cover"] = prepend_cdn(data.get("cover"))

        return data

    model_config = ConfigDict(from_attributes=True)


class UserAuthResponse(BaseModel):
    """用户认证响应模型"""

    access_token: str | None = Field(None, description="访问令牌")
    token_type: str | None = Field(None, description="令牌类型")
    user: UserAggregated | None = Field(None, description="用户信息")
    requires_device_verification: bool = Field(False, description="是否需要设备验证")
    message: str | None = Field(None, description="操作结果消息")
    device_info: dict | None = Field(None, description="设备信息")
    verification_token: str | None = Field(None, description="验证令牌")
    auth_method: str | None = Field(None, description="认证方法")


class UserWechatResponse(UserAuthResponse):
    """扫码登录相应模型"""

    status: str | None = Field(None, description="扫码状态")
    openid: str | None = Field(None, description="微信OpenID")


class QRCodeResponse(BaseModel):
    """二维码响应模型"""

    scene_str: str = Field(..., description="场景字符串")
    qr_url: str = Field(..., description="二维码图片URL")
    expire_seconds: int = Field(..., description="过期时间（秒）")


class WeChatUserInfo(BaseModel):
    """微信用户信息模型"""

    openid: str | None = Field(None, description="微信OpenID")
    nickname: str | None = Field(None, description="昵称")
    sex: int | None = Field(None, description="性别")
    province: str | None = Field(None, description="省份")
    city: str | None = Field(None, description="城市")
    country: str | None = Field(None, description="国家")
    headimgurl: str | None = Field(None, description="头像URL")
    privilege: list | None = Field(None, description="权限列表")
    unionid: str | None = Field(None, description="微信UnionID")


class UserCreate(WeChatUserInfo):
    username: str = Field(..., description="用户名", min_length=11, max_length=11)
