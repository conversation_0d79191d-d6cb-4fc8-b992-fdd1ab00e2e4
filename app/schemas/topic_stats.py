"""话题统计相关的Pydantic模型"""

from datetime import datetime
from typing import Optional

from pydantic import BaseModel, Field, ConfigDict


class TopicStatsBase(BaseModel):
    """话题统计基础模型"""
    
    topic: str = Field(..., description="话题标签", max_length=100)
    post_count: int = Field(default=0, description="使用该话题的帖子数量", ge=0)
    total_likes: int = Field(default=0, description="该话题下所有帖子的总点赞数", ge=0)
    total_comments: int = Field(default=0, description="该话题下所有帖子的总评论数", ge=0)
    total_reposts: int = Field(default=0, description="该话题下所有帖子的总转发数", ge=0)
    total_views: int = Field(default=0, description="该话题下所有帖子的总浏览数", ge=0)
    hot_score: float = Field(default=0.0, description="热度分数", ge=0.0)
    trend_score: float = Field(default=0.0, description="趋势分数（近期增长）")
    last_post_at: Optional[datetime] = Field(None, description="最后一次使用该话题的时间")
    peak_time: Optional[datetime] = Field(None, description="话题热度峰值时间")

    model_config = ConfigDict(from_attributes=True)


class TopicStatsCreate(BaseModel):
    """创建话题统计的请求模型"""
    
    topic: str = Field(..., description="话题标签", max_length=100)
    post_count: int = Field(default=1, description="初始帖子数量", ge=1)

    model_config = ConfigDict(from_attributes=True)


class TopicStatsUpdate(BaseModel):
    """更新话题统计的请求模型"""
    
    post_count_delta: int = Field(default=0, description="帖子数量变化")
    likes_delta: int = Field(default=0, description="点赞数变化")
    comments_delta: int = Field(default=0, description="评论数变化")
    reposts_delta: int = Field(default=0, description="转发数变化")
    views_delta: int = Field(default=0, description="浏览数变化")

    model_config = ConfigDict(from_attributes=True)


class TopicStats(TopicStatsBase):
    """话题统计的响应模型"""
    
    id: int = Field(..., description="统计记录ID")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")

    model_config = ConfigDict(from_attributes=True)


class HotTopic(BaseModel):
    """热门话题模型"""
    
    topic: str = Field(..., description="话题标签")
    post_count: int = Field(..., description="帖子数量")
    hot_score: float = Field(..., description="热度分数")
    trend_score: float = Field(..., description="趋势分数")
    rank: int = Field(..., description="排名")
    rank_change: Optional[int] = Field(None, description="排名变化（相比上次）")

    model_config = ConfigDict(from_attributes=True)


class TrendingTopic(BaseModel):
    """趋势话题模型"""
    
    topic: str = Field(..., description="话题标签")
    post_count: int = Field(..., description="帖子数量")
    trend_score: float = Field(..., description="趋势分数")
    growth_rate: float = Field(..., description="增长率")
    rank: int = Field(..., description="排名")

    model_config = ConfigDict(from_attributes=True)


class TopicDetail(BaseModel):
    """话题详情模型"""
    
    topic: str = Field(..., description="话题标签")
    post_count: int = Field(..., description="帖子数量")
    total_likes: int = Field(..., description="总点赞数")
    total_comments: int = Field(..., description="总评论数")
    total_reposts: int = Field(..., description="总转发数")
    total_views: int = Field(..., description="总浏览数")
    hot_score: float = Field(..., description="热度分数")
    trend_score: float = Field(..., description="趋势分数")
    last_post_at: Optional[datetime] = Field(None, description="最后发帖时间")
    peak_time: Optional[datetime] = Field(None, description="热度峰值时间")
    created_at: datetime = Field(..., description="话题首次出现时间")

    model_config = ConfigDict(from_attributes=True)


class TopicTrendBase(BaseModel):
    """话题趋势基础模型"""
    
    topic: str = Field(..., description="话题标签")
    period_type: str = Field(..., description="统计周期类型", pattern="^(hour|day)$")
    period_start: datetime = Field(..., description="统计周期开始时间")
    post_count: int = Field(default=0, description="新增帖子数", ge=0)
    likes_count: int = Field(default=0, description="新增点赞数", ge=0)
    comments_count: int = Field(default=0, description="新增评论数", ge=0)
    reposts_count: int = Field(default=0, description="新增转发数", ge=0)
    views_count: int = Field(default=0, description="新增浏览数", ge=0)
    period_score: float = Field(default=0.0, description="该时间段的热度分数", ge=0.0)

    model_config = ConfigDict(from_attributes=True)


class TopicTrendCreate(TopicTrendBase):
    """创建话题趋势的请求模型"""
    pass


class TopicTrend(TopicTrendBase):
    """话题趋势的响应模型"""
    
    id: int = Field(..., description="趋势记录ID")
    created_at: datetime = Field(..., description="创建时间")

    model_config = ConfigDict(from_attributes=True)


class TopicListQuery(BaseModel):
    """话题列表查询参数"""
    
    sort_by: str = Field(default="hot_score", description="排序字段")
    sort_order: str = Field(default="desc", description="排序方向", pattern="^(asc|desc)$")
    min_post_count: Optional[int] = Field(None, description="最小帖子数量", ge=1)
    days: Optional[int] = Field(None, description="时间范围（天数）", ge=1, le=365)
    page: int = Field(default=1, description="页码", ge=1)
    size: int = Field(default=20, description="每页数量", ge=1, le=100)

    model_config = ConfigDict(from_attributes=True)


class HotTopicsResponse(BaseModel):
    """热门话题响应模型"""
    
    topics: list[HotTopic] = Field(..., description="热门话题列表")
    total: int = Field(..., description="总数量")
    page: int = Field(..., description="当前页码")
    size: int = Field(..., description="每页数量")
    has_next: bool = Field(..., description="是否有下一页")

    model_config = ConfigDict(from_attributes=True)


class TrendingTopicsResponse(BaseModel):
    """趋势话题响应模型"""
    
    topics: list[TrendingTopic] = Field(..., description="趋势话题列表")
    total: int = Field(..., description="总数量")
    page: int = Field(..., description="当前页码")
    size: int = Field(..., description="每页数量")
    has_next: bool = Field(..., description="是否有下一页")

    model_config = ConfigDict(from_attributes=True)
