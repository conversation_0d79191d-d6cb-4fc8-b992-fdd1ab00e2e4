from datetime import datetime

from pydantic import BaseModel, ConfigDict, model_serializer

from app.schemas.video import VideoBase
from app.utils.url import prepend_cdn


class BannerBase(BaseModel):
    """轮播图基础模型"""

    title: str
    description: str | None = None
    image_url: str
    link_url: str | None = None
    video_id: int | None = None
    sort_order: int = 0
    is_active: bool = True
    banner_type: str = "home"


class BannerCreate(BannerBase):
    """创建轮播图模型"""

    pass


class BannerUpdate(BaseModel):
    """更新轮播图模型"""

    title: str | None = None
    description: str | None = None
    image_url: str | None = None
    link_url: str | None = None
    video_id: int | None = None
    sort_order: int | None = None
    is_active: bool | None = None
    banner_type: str | None = None


class BannerInDBBase(BannerBase):
    """数据库中的轮播图模型"""

    id: int
    created_at: datetime | None = None
    updated_at: datetime | None = None

    class Config:
        from_attributes = True


class Banner(BannerInDBBase):
    """API响应中的轮播图模型"""

    @model_serializer(mode="wrap")
    def ser_model(self, nxt):
        data = nxt(self)

        data["image_url"] = prepend_cdn(data.get("image_url"))
        data["link_url"] = prepend_cdn(data.get("link_url"))

        return data


class BannerWithVideo(Banner):
    """包含视频信息的轮播图模型"""

    video: VideoBase | None = None

    model_config = ConfigDict(from_attributes=True)
