"""审计日志Pydantic模型"""

from datetime import datetime
from typing import Any

from pydantic import BaseModel, Field

from app.models.audit_log import AuditAction, AuditResourceType


class AuditLogBase(BaseModel):
    """审计日志基础模型"""

    action: AuditAction
    resource_type: AuditResourceType
    resource_id: int | None = None
    resource_name: str | None = None
    description: str | None = None
    reason: str | None = None


class AuditLogCreate(AuditLogBase):
    """创建审计日志模型"""

    user_id: int
    old_values: dict[str, Any] | None = None
    new_values: dict[str, Any] | None = None


class AuditLogOut(AuditLogBase):
    """审计日志输出模型"""

    id: int
    timestamp: datetime
    user_id: int
    user_name: str
    user_email: str | None = None
    ip_address: str | None = None
    user_agent: str | None = None
    request_id: str | None = None
    session_id: str | None = None
    old_values: dict[str, Any] | None = None
    new_values: dict[str, Any] | None = None
    success: bool
    error_message: str | None = None
    endpoint: str | None = None
    method: str | None = None
    duration_ms: int | None = None

    class Config:
        from_attributes = True


class AuditLogSummary(BaseModel):
    """审计日志摘要模型（不包含敏感信息）"""

    id: int
    timestamp: datetime
    user_name: str
    action: AuditAction
    resource_type: AuditResourceType
    resource_id: int | None = None
    resource_name: str | None = None
    description: str | None = None
    success: bool
    endpoint: str | None = None
    method: str | None = None
    duration_ms: int | None = None

    class Config:
        from_attributes = True


class AuditLogQuery(BaseModel):
    """审计日志查询参数"""

    user_id: int | None = None
    action: AuditAction | None = None
    resource_type: AuditResourceType | None = None
    resource_id: int | None = None
    start_date: datetime | None = None
    end_date: datetime | None = None
    success: bool | None = None
    keyword: str | None = None
    skip: int = Field(0, ge=0)
    limit: int = Field(100, ge=1, le=1000)


class AuditLogStats(BaseModel):
    """审计日志统计模型"""

    total_logs: int
    success_count: int
    failure_count: int
    success_rate: float
    top_users: list[dict[str, Any]]
    top_actions: list[dict[str, Any]]
    top_resources: list[dict[str, Any]]
    daily_stats: list[dict[str, Any]]


class AuditLogListResponse(BaseModel):
    """审计日志列表响应"""

    items: list[AuditLogOut]
    total: int
    skip: int
    limit: int
    has_more: bool


class AuditLogExportRequest(BaseModel):
    """审计日志导出请求"""

    query: AuditLogQuery
    format: str = Field("csv", pattern="^(csv|excel|json)$")
    include_sensitive: bool = False


class UserActivitySummary(BaseModel):
    """用户活动摘要"""

    user_id: int
    user_name: str
    total_actions: int
    success_actions: int
    failure_actions: int
    last_activity: datetime | None = None
    most_common_action: AuditAction | None = None
    most_common_resource: AuditResourceType | None = None


class ResourceActivitySummary(BaseModel):
    """资源活动摘要"""

    resource_type: AuditResourceType
    resource_id: int | None = None
    resource_name: str | None = None
    total_actions: int
    create_count: int
    update_count: int
    delete_count: int
    last_activity: datetime | None = None
    most_active_user: str | None = None


class DailyActivityStats(BaseModel):
    """每日活动统计"""

    date: str
    total_actions: int
    unique_users: int
    success_rate: float
    top_action: AuditAction | None = None
    top_resource: AuditResourceType | None = None
