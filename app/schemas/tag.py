from datetime import datetime
from typing import Literal

from pydantic import BaseModel


class TagBase(BaseModel):
    """标签基础模型"""

    name: str
    is_default: bool = False
    content_type: Literal["article", "video", "scratch", "post", "global"] | None = None

    model_config = {"from_attributes": True}


class TagCreate(TagBase):
    """创建标签的请求模型"""

    pass


class TagUpdate(TagBase):
    """更新标签的请求模型"""

    name: str | None = None
    is_default: bool | None = None
    content_type: Literal["article", "video", "scratch", "post", "global"] | None = None

    model_config = {"from_attributes": True}


class Tag(TagBase):
    """标签的响应模型"""

    id: int
    is_default: bool
    created_at: datetime | None = None
    updated_at: datetime | None = None

    model_config = {"from_attributes": True}
