"""热门标签相关的响应模型"""

from datetime import datetime

from pydantic import BaseModel, Field


class HotTag(BaseModel):
    """热门标签模型"""

    tag_id: int = Field(..., description="标签ID")
    name: str = Field(..., description="标签名称")
    content_type: str = Field(..., description="内容类型")
    score: float = Field(..., description="热度累计分数")
    window_score: float = Field(..., description="最近窗口热度变化值")
    unique_users: int = Field(..., description="独立用户估算数")
    last_event_at: datetime | None = Field(None, description="最近一次事件时间")

    model_config = {"from_attributes": True}
