from datetime import datetime

from pydantic import BaseModel, Field, model_serializer, validator

from app.models.comment import CommentType
from app.schemas.user import User
from app.utils.url import prepend_cdn

MAX_COMMENT_IMAGES = 9


def _normalize_image_urls(value: list[str] | None) -> list[str]:
    if value is None:
        return []
    if not isinstance(value, list):
        raise ValueError("image_urls 必须为字符串数组")
    if len(value) > MAX_COMMENT_IMAGES:
        raise ValueError(f"单条评论最多 {MAX_COMMENT_IMAGES} 张图片")

    normalized: list[str] = []
    for item in value:
        if not isinstance(item, str):
            raise ValueError("image_urls 必须为字符串数组")
        stripped = item.strip()
        if not stripped:
            raise ValueError("图片URL不能为空")
        if not (stripped.startswith("http://") or stripped.startswith("https://") or stripped.startswith("/")):
            raise ValueError("图片URL必须为OSS或HTTP(S)路径")
        normalized.append(stripped)
    return normalized


def _prepend_cdn_to_images(values: list[str] | None) -> list[str]:
    if not values:
        return []

    prefixed: list[str] = []
    for item in values:
        prefixed.append(prepend_cdn(item) or item)
    return prefixed


class CommentBase(BaseModel):
    """评论基础模型"""

    content: str = Field(..., description="评论内容")
    comment_type: CommentType = Field(..., description="评论类型：article、video、scratch 或 post")
    article_id: int | None = Field(None, description="文章ID，当comment_type为article时必填")
    video_id: int | None = Field(None, description="视频ID，当comment_type为video时必填")
    scratch_id: int | None = Field(None, description="Scratch项目ID，当comment_type为scratch时必填")
    post_id: int | None = Field(None, description="沸点ID，当comment_type为post时必填")
    parent_id: int | None = Field(None, description="父评论ID，用于回复")
    is_visible: bool | None = Field(True, description="是否可见")
    image_urls: list[str] = Field(default_factory=list, description="评论图片URL列表，最多9张")

    @validator("image_urls", pre=True, always=True)
    def validate_image_urls(cls, value: list[str] | None) -> list[str]:  # noqa: D401
        """规范评论图片 URL 列表"""
        return _normalize_image_urls(value)

    @model_serializer(mode="wrap")
    def serialize_with_cdn(self, nxt):  # type: ignore[override]
        data = nxt(self)
        data["image_urls"] = _prepend_cdn_to_images(data.get("image_urls"))
        return data


class CommentCreate(CommentBase):
    """创建评论模型"""

    reply_to_id: int | None = Field(None, description="回复目标评论ID")


class CommentUpdate(BaseModel):
    """更新评论模型"""

    content: str | None = Field(None, description="评论内容")
    is_visible: bool | None = Field(None, description="是否可见")
    image_urls: list[str] | None = Field(None, description="评论图片URL列表，最多9张")

    @validator("image_urls", pre=True)
    def validate_update_image_urls(cls, value: list[str] | None) -> list[str] | None:  # noqa: D401
        """校验更新时的图片 URL 列表"""
        if value is None:
            return []
        return _normalize_image_urls(value)


class CommentInDBBase(CommentBase):
    """数据库中的评论模型"""

    id: int
    author_id: int
    author: User | None = None
    reply_to_id: int | None = None
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class Comment(CommentInDBBase):
    """返回给API的评论模型"""

    comment_id: int = Field(..., alias="id")
    author: User | None = None
    like_count: int = Field(0, description="点赞数量")
    is_liked: bool = Field(False, description="当前用户是否已点赞")

    class Config:
        from_attributes = True
        populate_by_name = True


class Reply(Comment):
    """返回给API的回复模型"""

    reply_id: int = Field(..., alias="id")
    reply_to_comment_id: int | None = Field(..., alias="parent_id")
    reply_to_user_id: int | None = None  # 添加回复目标用户ID
    reply_to_user: User | None = None  # 添加回复目标用户信息
    replies: list["Reply"] = []  # 支持嵌套回复
    reply_count: int = 0  # 回复数量

    class Config:
        from_attributes = True
        populate_by_name = True


class CommentWithReplies(Comment):
    """包含回复的评论模型"""

    replies: list[Reply] = []
    reply_count: int = 0  # 添加回复数量字段


class CommentList(BaseModel):
    """评论列表模型"""

    total: int
    items: list[CommentWithReplies]


class FlatComment(BaseModel):
    """扁平化评论模型"""

    id: int = Field(..., description="评论ID")
    content: str = Field(..., description="评论内容")
    comment_type: CommentType = Field(..., description="评论类型")
    article_id: int | None = Field(None, description="文章ID")
    video_id: int | None = Field(None, description="视频ID")
    scratch_id: int | None = Field(None, description="Scratch项目ID")
    post_id: int | None = Field(None, description="沸点ID")
    image_urls: list[str] = Field(default_factory=list, description="评论图片URL列表，最多9张")
    level: int = Field(..., description="评论层级，0=顶层，1=一级回复，2=二级回复")
    parent_id: int | None = Field(None, description="父评论ID")
    reply_to_id: int | None = Field(None, description="回复目标评论ID")
    reply_to_user_id: int | None = Field(None, description="回复目标用户ID")
    reply_to_user: User | None = Field(None, description="回复目标用户信息")
    path: str = Field(..., description="评论层级路径，如 '1.5.12'")
    author_id: int = Field(..., description="作者ID")
    author: User | None = Field(None, description="作者信息")
    like_count: int = Field(0, description="点赞数量")
    is_liked: bool = Field(False, description="当前用户是否已点赞")
    reply_count: int = Field(0, description="直接回复数量")
    total_reply_count: int = Field(0, description="总回复数量（包括子回复）")
    is_visible: bool = Field(True, description="是否可见")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")

    class Config:
        from_attributes = True

    @model_serializer(mode="wrap")
    def serialize_with_cdn(self, nxt):  # type: ignore[override]
        data = nxt(self)
        data["image_urls"] = _prepend_cdn_to_images(data.get("image_urls"))
        return data


class FlatCommentList(BaseModel):
    """扁平化评论列表模型"""

    items: list[FlatComment] = Field(..., description="扁平化评论列表")
    has_next: bool = Field(..., description="是否有下一页")
    has_previous: bool = Field(..., description="是否有上一页")
    next_cursor: str | None = Field(None, description="下一页游标")
    previous_cursor: str | None = Field(None, description="上一页游标")
    total_count: int | None = Field(None, description="总数量（可选）")


class CommentCursorList(BaseModel):
    """评论游标分页列表模型"""

    items: list[CommentWithReplies] = Field(..., description="评论列表")
    has_next: bool = Field(..., description="是否有下一页")
    has_previous: bool = Field(..., description="是否有上一页")
    next_cursor: str | None = Field(None, description="下一页游标")
    previous_cursor: str | None = Field(None, description="上一页游标")
    total_count: int | None = Field(None, description="总数量（可选）")
