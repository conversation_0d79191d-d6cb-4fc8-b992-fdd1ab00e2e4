"""
搜索功能的 Schema 定义

包含：
- 搜索请求参数
- 搜索响应模型
- 搜索建议
- 搜索历史
"""

from datetime import datetime
from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field, validator


# ==================== 请求 Schema ====================

class SearchParams(BaseModel):
    """搜索参数"""
    
    q: str = Field(..., min_length=1, max_length=100, description="搜索关键词")
    type: str = Field("all", pattern="^(all|article|video|scratch)$", description="内容类型")
    category_id: Optional[int] = Field(None, description="类别ID过滤")
    tag_ids: Optional[str] = Field(None, description="标签ID列表，逗号分隔")
    author_id: Optional[int] = Field(None, description="作者ID过滤")
    sort_by: str = Field("relevance", pattern="^(relevance|hot|recent)$", description="排序方式")
    date_from: Optional[str] = Field(None, description="起始日期")
    date_to: Optional[str] = Field(None, description="结束日期")
    difficulty: Optional[str] = Field(None, pattern="^(easy|medium|hard)$", description="难度级别（仅Scratch）")
    cursor: Optional[str] = Field(None, description="游标位置")
    size: int = Field(20, ge=1, le=50, description="每页大小")
    highlight: bool = Field(True, description="是否返回高亮片段")
    
    @validator("tag_ids")
    def validate_tag_ids(cls, v):
        """验证标签ID格式"""
        if v:
            try:
                ids = [int(x.strip()) for x in v.split(",")]
                return ids
            except ValueError:
                raise ValueError("tag_ids 必须是逗号分隔的整数列表")
        return None


class SearchSuggestionParams(BaseModel):
    """搜索建议参数"""
    
    q: str = Field(..., min_length=1, description="搜索前缀")
    type: str = Field("all", pattern="^(all|article|video|scratch)$", description="内容类型")
    limit: int = Field(10, ge=1, le=20, description="返回数量")


class SearchHistoryParams(BaseModel):
    """搜索历史参数"""
    
    type: str = Field("all", pattern="^(all|article|video|scratch)$", description="内容类型过滤")
    limit: int = Field(20, ge=1, le=100, description="返回数量")


# ==================== 响应 Schema ====================

class SearchHighlight(BaseModel):
    """搜索结果高亮"""
    
    title: Optional[str] = Field(None, description="高亮的标题")
    content: Optional[str] = Field(None, description="高亮的内容片段")
    description: Optional[str] = Field(None, description="高亮的描述")


class AuthorInfo(BaseModel):
    """作者信息"""
    
    id: int
    username: str
    avatar_url: Optional[str] = None


class CategoryInfo(BaseModel):
    """类别信息"""
    
    id: int
    name: str


class TagInfo(BaseModel):
    """标签信息"""
    
    id: int
    name: str


class ContentStats(BaseModel):
    """内容统计"""
    
    visit_count: int = 0
    like_count: int = 0
    comment_count: int = 0


class SearchResultBase(BaseModel):
    """搜索结果基础模型"""
    
    id: int
    type: str
    title: str
    description: Optional[str] = None
    cover_url: Optional[str] = None
    author: AuthorInfo
    category: Optional[CategoryInfo] = None
    tags: List[TagInfo] = []
    stats: ContentStats
    highlight: Optional[SearchHighlight] = None
    relevance_score: float = Field(0.0, description="相关度分数")
    created_at: datetime
    updated_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True


class ArticleSearchResult(SearchResultBase):
    """文章搜索结果"""
    
    type: str = "article"


class VideoSearchResult(SearchResultBase):
    """视频搜索结果"""
    
    type: str = "video"
    duration: Optional[int] = None
    width: Optional[int] = None
    height: Optional[int] = None


class ScratchSearchResult(SearchResultBase):
    """Scratch 搜索结果"""
    
    type: str = "scratch"
    difficulty: Optional[str] = None
    can_adapt: bool = True
    adapt_count: int = 0
    original_project_id: Optional[int] = None


class SearchResponse(BaseModel):
    """搜索响应"""
    
    items: List[SearchResultBase]
    has_next: bool
    has_previous: bool
    next_cursor: Optional[str] = None
    previous_cursor: Optional[str] = None
    total_count: Optional[int] = None
    search_time_ms: int = Field(0, description="搜索耗时（毫秒）")
    suggestions: List[str] = Field([], description="相关搜索建议")


class SearchSuggestionItem(BaseModel):
    """搜索建议项"""
    
    keyword: str
    type: str = Field(..., description="trending/popular/related")
    count: int = Field(0, description="搜索次数或相关度")


class SearchSuggestionResponse(BaseModel):
    """搜索建议响应"""
    
    suggestions: List[SearchSuggestionItem]


class TrendingKeyword(BaseModel):
    """热门搜索词"""
    
    keyword: str
    search_count: int
    trend: str = Field(..., description="up/down/stable")
    change_rate: float = Field(0.0, description="变化率")


class TrendingResponse(BaseModel):
    """热门搜索响应"""
    
    trending_keywords: List[TrendingKeyword]
    period: str
    updated_at: datetime


class SearchHistoryItem(BaseModel):
    """搜索历史项"""
    
    id: int
    query: str
    content_type: str
    result_count: int
    searched_at: datetime
    
    class Config:
        from_attributes = True


class SearchHistoryResponse(BaseModel):
    """搜索历史响应"""
    
    history: List[SearchHistoryItem]
    total: int


class SearchStatsResponse(BaseModel):
    """搜索统计响应（管理员）"""
    
    total_searches: int
    unique_users: int
    avg_results_per_search: float
    avg_response_time_ms: float
    top_keywords: List[Dict[str, Any]]
    search_by_type: Dict[str, int]
    timeline: List[Dict[str, Any]]


# ==================== 操作响应 ====================

class MessageResponse(BaseModel):
    """通用消息响应"""
    
    message: str
    deleted_count: Optional[int] = None


class ErrorResponse(BaseModel):
    """错误响应"""
    
    detail: str
    error_code: Optional[str] = None
