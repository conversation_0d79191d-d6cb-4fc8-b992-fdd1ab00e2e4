from datetime import UTC, datetime
from enum import Enum

from sqlalchemy import (
    <PERSON><PERSON><PERSON>,
    Column,
    ForeignKey,
    Integer,
    String,
    Text,
    and_,
    Index,
)
from sqlalchemy.dialects.postgresql import TIMESTAMP as PostgresTIMESTAMP, JSONB
from sqlalchemy.orm import foreign, relationship
from sqlalchemy.types import TypeDecorator

from app.db.session import Base
from app.models.review import ContentType, Review


class Timestamp(TypeDecorator):
    """支持时区的DateTime类型"""

    impl = PostgresTIMESTAMP(timezone=True)
    cache_ok = True


def now_utc():
    """获取当前UTC时间"""
    return datetime.now(UTC)


class PostType(str, Enum):
    """沸点类型枚举"""
    
    TEXT = "text"           # 纯文本
    IMAGE = "image"         # 图片
    VIDEO = "video"         # 视频
    LINK = "link"           # 链接分享
    POLL = "poll"           # 投票
    REPOST = "repost"       # 转发


class PostStatus(str, Enum):
    """沸点状态枚举"""
    
    DRAFT = "draft"         # 草稿
    PUBLISHED = "published" # 已发布
    DELETED = "deleted"     # 已删除
    HIDDEN = "hidden"       # 隐藏（管理员操作）


class PostVisibility(str, Enum):
    """沸点可见性枚举"""
    
    PUBLIC = "public"       # 公开
    FOLLOWERS = "followers" # 仅关注者可见
    PRIVATE = "private"     # 私密（仅自己可见）


class Post(Base):
    """沸点数据模型"""

    __tablename__ = "posts"

    id = Column(Integer, primary_key=True, index=True)
    author_id = Column(Integer, ForeignKey("users.id"), index=True, nullable=False)
    
    # 内容相关
    content = Column(Text, nullable=True, comment="沸点文本内容")
    post_type = Column(String(20), nullable=False, default=PostType.TEXT, comment="沸点类型")
    status = Column(String(20), nullable=False, default=PostStatus.PUBLISHED, comment="沸点状态")
    visibility = Column(String(20), nullable=False, default=PostVisibility.PUBLIC, comment="可见性")
    
    # 转发相关
    original_post_id = Column(Integer, ForeignKey("posts.id"), nullable=True, comment="原始沸点ID（转发时使用）")
    repost_comment = Column(Text, nullable=True, comment="转发评论")
    
    # 话题和位置
    topic = Column(String(100), nullable=True, comment="话题标签")
    location = Column(String(200), nullable=True, comment="位置信息")
    
    # 链接分享相关
    link_url = Column(String(500), nullable=True, comment="分享链接URL")
    link_title = Column(String(200), nullable=True, comment="链接标题")
    link_description = Column(Text, nullable=True, comment="链接描述")
    link_image = Column(String(500), nullable=True, comment="链接预览图")
    
    # 投票相关
    poll_options = Column(JSONB, nullable=True, comment="投票选项JSON")
    poll_expires_at = Column(Timestamp, nullable=True, comment="投票截止时间")
    poll_multiple_choice = Column(Boolean, default=False, comment="是否允许多选")
    
    # 统计数据
    like_count = Column(Integer, default=0, comment="点赞数")
    comment_count = Column(Integer, default=0, comment="评论数")
    repost_count = Column(Integer, default=0, comment="转发数")
    view_count = Column(Integer, default=0, comment="浏览数")
    
    # 系统字段
    is_pinned = Column(Boolean, default=False, comment="是否置顶")
    is_hot = Column(Boolean, default=False, comment="是否热门")
    hot_score = Column(Integer, default=0, comment="热度分数")
    created_at = Column(Timestamp, default=now_utc, nullable=False)
    updated_at = Column(Timestamp, default=now_utc, onupdate=now_utc, nullable=False)
    cache_version = Column(Integer, nullable=False, server_default="1", comment="缓存版本号")

    # 关联关系
    author = relationship("User", back_populates="posts")
    original_post = relationship("Post", remote_side=[id], backref="reposts")
    media = relationship("PostMedia", back_populates="post", cascade="all, delete-orphan")
    mentions = relationship("PostMention", back_populates="post", cascade="all, delete-orphan")
    poll_votes = relationship("PostPollVote", back_populates="post", cascade="all, delete-orphan")
    comments = relationship("Comment", back_populates="post", cascade="all, delete-orphan")
    
    # 关联到审核记录
    reviews = relationship(
        "Review",
        primaryjoin=lambda: and_(
            Post.id == foreign(Review.content_id),
            Review.content_type == ContentType.POST,
        ),
        cascade="all, delete-orphan",
        viewonly=True,
    )

    # 索引
    __table_args__ = (
        Index('idx_posts_author_created', 'author_id', 'created_at'),
        Index('idx_posts_status_created', 'status', 'created_at'),
        Index('idx_posts_type_created', 'post_type', 'created_at'),
        Index('idx_posts_topic', 'topic'),
        Index('idx_posts_hot_score', 'hot_score'),
        Index('idx_posts_visibility_status', 'visibility', 'status'),
    )

    def __repr__(self):
        return f"<Post {self.id}: {self.content[:50] if self.content else 'No content'}>"


class PostMedia(Base):
    """沸点媒体文件模型"""

    __tablename__ = "post_media"

    id = Column(Integer, primary_key=True, index=True)
    post_id = Column(Integer, ForeignKey("posts.id"), nullable=False)
    media_type = Column(String(20), nullable=False, comment="媒体类型: image, video, audio")
    media_url = Column(String(500), nullable=False, comment="媒体文件URL")
    thumbnail_url = Column(String(500), nullable=True, comment="缩略图URL")
    media_size = Column(Integer, nullable=True, comment="文件大小（字节）")
    media_width = Column(Integer, nullable=True, comment="媒体宽度")
    media_height = Column(Integer, nullable=True, comment="媒体高度")
    media_duration = Column(Integer, nullable=True, comment="媒体时长（秒）")
    sort_order = Column(Integer, default=0, comment="排序顺序")
    created_at = Column(Timestamp, default=now_utc, nullable=False)

    # 关联关系
    post = relationship("Post", back_populates="media")

    __table_args__ = (
        Index('idx_post_media_post_id', 'post_id'),
        Index('idx_post_media_type', 'media_type'),
    )


class PostMention(Base):
    """沸点@提及模型"""

    __tablename__ = "post_mentions"

    id = Column(Integer, primary_key=True, index=True)
    post_id = Column(Integer, ForeignKey("posts.id"), nullable=False)
    mentioned_user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    mention_text = Column(String(100), nullable=False, comment="@提及的文本")
    created_at = Column(Timestamp, default=now_utc, nullable=False)

    # 关联关系
    post = relationship("Post", back_populates="mentions")
    mentioned_user = relationship("User")

    __table_args__ = (
        Index('idx_post_mentions_post_id', 'post_id'),
        Index('idx_post_mentions_user_id', 'mentioned_user_id'),
    )


class PostPollVote(Base):
    """沸点投票记录模型"""

    __tablename__ = "post_poll_votes"

    id = Column(Integer, primary_key=True, index=True)
    post_id = Column(Integer, ForeignKey("posts.id"), nullable=False)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    option_index = Column(Integer, nullable=False, comment="投票选项索引")
    created_at = Column(Timestamp, default=now_utc, nullable=False)

    # 关联关系
    post = relationship("Post", back_populates="poll_votes")
    user = relationship("User")

    __table_args__ = (
        Index('idx_post_poll_votes_post_id', 'post_id'),
        Index('idx_post_poll_votes_user_id', 'user_id'),
        # 确保用户对同一个投票选项只能投一次票
        Index('idx_post_poll_votes_unique', 'post_id', 'user_id', 'option_index', unique=True),
    )
