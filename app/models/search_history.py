"""搜索历史数据模型"""

from datetime import UTC, datetime

from sqlalchemy import Column, ForeignKey, Integer, String, Text, CheckConstraint, Index
from sqlalchemy.dialects.postgresql import INET, JSONB
from sqlalchemy.orm import relationship

from app.db.session import Base
from app.db.timestamp import Timestamp


class SearchHistory(Base):
    """搜索历史记录"""

    __tablename__ = "search_history"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id", ondelete="CASCADE"), nullable=True, index=True)
    query = Column(Text, nullable=False, comment="搜索关键词")
    content_type = Column(String(32), nullable=False, comment="搜索内容类型")
    filters = Column(JSONB, nullable=True, comment="搜索过滤条件（JSON格式）")
    result_count = Column(Integer, default=0, comment="搜索结果数量")
    clicked_result_id = Column(Integer, nullable=True, comment="用户点击的结果ID")
    clicked_result_position = Column(Integer, nullable=True, comment="点击结果的位置")
    session_id = Column(String(128), nullable=True, comment="会话ID")
    ip_address = Column(INET, nullable=True, comment="IP地址")
    user_agent = Column(Text, nullable=True, comment="用户代理")
    created_at = Column(Timestamp, default=datetime.now(UTC), nullable=False)

    # 关系
    user = relationship("User", back_populates="search_history")

    # 约束
    __table_args__ = (
        CheckConstraint(
            "content_type IN ('article', 'video', 'scratch', 'all')",
            name='check_content_type'
        ),
        Index('idx_search_history_user', 'user_id', 'created_at'),
        Index('idx_search_history_query', 'query', 'content_type'),
        Index('idx_search_history_created', 'created_at'),
    )

    def __repr__(self):
        return f"<SearchHistory {self.id}: '{self.query}' by user {self.user_id}>"
