"""搜索统计数据模型"""

from datetime import UTC, datetime, date

from sqlalchemy import (
    Column,
    Integer,
    String,
    Numeric,
    Date,
    Boolean,
    UniqueConstraint,
    Index,
    CheckConstraint,
)

from app.db.session import Base
from app.db.timestamp import Timestamp


class SearchKeywordStats(Base):
    """搜索关键词统计"""

    __tablename__ = "search_keywords_stats"

    id = Column(Integer, primary_key=True, index=True)
    keyword = Column(String(255), nullable=False, comment="搜索关键词")
    content_type = Column(String(32), nullable=False, comment="内容类型")
    search_count = Column(Integer, default=0, comment="搜索次数")
    result_count_avg = Column(Numeric(10, 2), nullable=True, comment="平均搜索结果数")
    click_through_rate = Column(Numeric(5, 4), nullable=True, comment="点击率")
    last_searched_at = Column(Timestamp, nullable=True, comment="最后搜索时间")
    stat_date = Column(Date, nullable=False, default=date.today, comment="统计日期")
    created_at = Column(Timestamp, default=datetime.now(UTC), nullable=False)
    updated_at = Column(
        Timestamp, default=datetime.now(UTC), onupdate=datetime.now(UTC), nullable=False
    )

    __table_args__ = (
        UniqueConstraint('keyword', 'content_type', 'stat_date', name='uq_keyword_type_date'),
        CheckConstraint(
            "content_type IN ('article', 'video', 'scratch', 'all')",
            name='check_keywords_content_type'
        ),
        Index('idx_search_keywords_stats_count', 'search_count', 'stat_date'),
        Index('idx_search_keywords_stats_date', 'stat_date'),
        Index('idx_search_keywords_stats_keyword', 'keyword', 'content_type'),
    )

    def __repr__(self):
        return f"<SearchKeywordStats '{self.keyword}' - {self.search_count} searches>"


class SearchSuggestion(Base):
    """搜索建议（自动补全）"""

    __tablename__ = "search_suggestions"

    id = Column(Integer, primary_key=True, index=True)
    keyword = Column(String(255), nullable=False, comment="搜索关键词")
    content_type = Column(String(32), nullable=False, comment="内容类型")
    suggestion_type = Column(String(32), nullable=False, comment="建议类型")
    weight = Column(Integer, default=0, comment="权重值，用于排序")
    is_active = Column(Boolean, default=True, comment="是否激活")
    created_at = Column(Timestamp, default=datetime.now(UTC), nullable=False)
    updated_at = Column(
        Timestamp, default=datetime.now(UTC), onupdate=datetime.now(UTC), nullable=False
    )

    __table_args__ = (
        UniqueConstraint(
            'keyword', 'content_type', 'suggestion_type', name='uq_keyword_type_suggestion'
        ),
        CheckConstraint(
            "content_type IN ('article', 'video', 'scratch', 'all')",
            name='check_suggestions_content_type'
        ),
        CheckConstraint(
            "suggestion_type IN ('trending', 'popular', 'related')",
            name='check_suggestion_type'
        ),
        Index('idx_search_suggestions_keyword', 'keyword', 'content_type', 'is_active'),
        Index('idx_search_suggestions_weight', 'weight', 'content_type'),
    )

    def __repr__(self):
        return f"<SearchSuggestion '{self.keyword}' ({self.suggestion_type})>"
