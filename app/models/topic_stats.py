"""话题统计数据模型"""

from datetime import UTC, datetime

from sqlalchemy import Column, Float, Index, Integer, String, Text
from sqlalchemy.orm import relationship

from app.db.session import Base
from app.db.timestamp import Timestamp, now_utc


class TopicStats(Base):
    """话题统计数据模型"""

    __tablename__ = "topic_stats"

    id = Column(Integer, primary_key=True, index=True)
    topic = Column(String(100), unique=True, index=True, nullable=False, comment="话题标签")
    
    # 统计数据
    post_count = Column(Integer, default=0, nullable=False, comment="使用该话题的帖子数量")
    total_likes = Column(Integer, default=0, nullable=False, comment="该话题下所有帖子的总点赞数")
    total_comments = Column(Integer, default=0, nullable=False, comment="该话题下所有帖子的总评论数")
    total_reposts = Column(Integer, default=0, nullable=False, comment="该话题下所有帖子的总转发数")
    total_views = Column(Integer, default=0, nullable=False, comment="该话题下所有帖子的总浏览数")
    
    # 热度计算
    hot_score = Column(Float, default=0.0, nullable=False, comment="热度分数")
    trend_score = Column(Float, default=0.0, nullable=False, comment="趋势分数（近期增长）")
    
    # 时间统计
    last_post_at = Column(Timestamp, nullable=True, comment="最后一次使用该话题的时间")
    peak_time = Column(Timestamp, nullable=True, comment="话题热度峰值时间")
    
    # 元数据
    created_at = Column(Timestamp, default=now_utc, nullable=False)
    updated_at = Column(Timestamp, default=now_utc, onupdate=now_utc, nullable=False)
    
    # 索引优化
    __table_args__ = (
        Index('idx_topic_stats_hot_score', 'hot_score'),
        Index('idx_topic_stats_trend_score', 'trend_score'),
        Index('idx_topic_stats_post_count', 'post_count'),
        Index('idx_topic_stats_last_post_at', 'last_post_at'),
        Index('idx_topic_stats_updated_at', 'updated_at'),
    )

    def __repr__(self):
        return f"<TopicStats {self.topic}: posts={self.post_count}, hot_score={self.hot_score}>"


class TopicTrend(Base):
    """话题趋势数据模型 - 用于记录话题的历史趋势"""

    __tablename__ = "topic_trends"

    id = Column(Integer, primary_key=True, index=True)
    topic = Column(String(100), index=True, nullable=False, comment="话题标签")
    
    # 时间段统计（按小时/天统计）
    period_type = Column(String(10), nullable=False, comment="统计周期类型: hour/day")
    period_start = Column(Timestamp, nullable=False, comment="统计周期开始时间")
    
    # 该时间段内的统计数据
    post_count = Column(Integer, default=0, nullable=False, comment="新增帖子数")
    likes_count = Column(Integer, default=0, nullable=False, comment="新增点赞数")
    comments_count = Column(Integer, default=0, nullable=False, comment="新增评论数")
    reposts_count = Column(Integer, default=0, nullable=False, comment="新增转发数")
    views_count = Column(Integer, default=0, nullable=False, comment="新增浏览数")
    
    # 计算得出的分数
    period_score = Column(Float, default=0.0, nullable=False, comment="该时间段的热度分数")
    
    created_at = Column(Timestamp, default=now_utc, nullable=False)
    
    # 索引优化
    __table_args__ = (
        Index('idx_topic_trends_topic_period', 'topic', 'period_type', 'period_start'),
        Index('idx_topic_trends_period_start', 'period_start'),
        Index('idx_topic_trends_period_score', 'period_score'),
    )

    def __repr__(self):
        return f"<TopicTrend {self.topic}: {self.period_type} {self.period_start}>"
