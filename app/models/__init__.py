# 使models目录成为一个Python包
from app.models.admin_analytics import AdminAnalyticsSnapshot, AdminAnalyticsSnapshotData
from app.models.admin_config import (
    AdminConfigChangeLog,
    AdminConfigSetting,
    AdminFeatureToggle,
)
from app.models.admin_monitoring import AdminAlertEvent, AdminAlertRule, AdminMonitoringMetric
from app.models.article import Article
from app.models.audit_log import AuditAction, AuditLog, AuditResourceType
from app.models.backpack import BackpackItem, BackpackItemType
from app.models.banner import Banner
from app.models.category import Category
from app.models.comment import Comment
from app.models.favorite import Favorite
from app.models.file_hash import FileHash
from app.models.history import History
from app.models.like import Like
from app.models.post import (
    Post,
    PostMedia,
    PostMention,
    PostPollVote,
    PostStatus,
    PostType,
    PostVisibility,
)
from app.models.review import Review, ReviewStatus
from app.models.scratch import ScratchProduct
from app.models.search_history import SearchHistory
from app.models.search_stats import SearchKeywordStats, SearchSuggestion
from app.models.tag import Tag
from app.models.tag_stats import TagStats
from app.models.user import Permission, User, UserRole, UserFollow, user_follow
from app.models.user_behavior import (
    ContentSimilarity,
    RecommendationLog,
    UserBrowseHistory,
    UserInteraction,
    UserInterest,
    UserProfile,
)
from app.models.user_device import UserDevice
from app.models.user_stats import UserStats
from app.models.video import Video
from app.models.video_folder import VideoFolder
from app.notifications.models import (
    Notification,
    NotificationPriority,
    NotificationStatus,
    NotificationType,
)

from .outbox import OutboxMessage

# 在这里导入所有模型，以便在其他地方可以通过app.models导入


def load_all_models():
    """
    This function doesn't do anything, but by importing and calling it,
    we ensure that all model modules are loaded and registered with SQLAlchemy's
    metadata before Alembic tries to access it. This is a workaround for
    aggressive linters that might remove "unused" model imports.
    """
    pass


__all__ = [
    "Category",
    "Article",
    "AdminConfigSetting",
    "AdminFeatureToggle",
    "AdminConfigChangeLog",
    "AdminMonitoringMetric",
    "AdminAlertRule",
    "AdminAlertEvent",
    "AdminAnalyticsSnapshot",
    "AdminAnalyticsSnapshotData",
    "BackpackItem",
    "BackpackItemType",
    "Comment",
    "Favorite",
    "FileHash",
    "Like",
    "Post",
    "PostMedia",
    "PostMention",
    "PostPollVote",
    "PostStatus",
    "PostType",
    "PostVisibility",
    "Review",
    "ReviewStatus",
    "ScratchProduct",
    "SearchHistory",
    "SearchKeywordStats",
    "SearchSuggestion",
    "Tag",
    "TagStats",
    "User",
    "UserRole",
    "UserFollow",
    "Video",
    "VideoFolder",
    "Permission",
    "UserBrowseHistory",
    "UserInteraction",
    "UserInterest",
    "UserProfile",
    "RecommendationLog",
    "ContentSimilarity",
    "UserDevice",
    "History",
    "Banner",
    "UserStats",
    "OutboxMessage",
    "Notification",
    "NotificationType",
    "NotificationPriority",
    "NotificationStatus",
    "user_follow",
]
