"""审计日志模型"""

from datetime import datetime
from enum import Enum
from typing import Any

from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON>olean, DateTime, Integer, String, Text, func
from sqlalchemy.orm import Mapped, mapped_column

from app.db.session import Base


class AuditAction(str, Enum):
    """审计操作类型"""

    CREATE = "CREATE"
    UPDATE = "UPDATE"
    DELETE = "DELETE"
    APPROVE = "APPROVE"
    REJECT = "REJECT"
    BLOCK = "BLOCK"
    UNBLOCK = "UNBLOCK"
    LOGIN = "LOGIN"
    LOGOUT = "LOGOUT"
    EXPORT = "EXPORT"
    IMPORT = "IMPORT"
    BATCH_UPDATE = "BATCH_UPDATE"
    BATCH_DELETE = "BATCH_DELETE"


class AuditResourceType(str, Enum):
    """审计资源类型"""

    USER = "USER"
    ARTICLE = "ARTICLE"
    VIDEO = "VIDEO"
    COMMENT = "COMMENT"
    CATEGORY = "CATEGORY"
    TAG = "TAG"
    DEVICE = "DEVICE"
    NOTIFICATION = "NOTIFICATION"
    SYSTEM = "SYSTEM"


class AuditLog(Base):
    """审计日志表"""

    __tablename__ = "audit_logs"

    # 基础信息
    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    timestamp: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), server_default=func.now(), index=True, comment="操作时间"
    )

    # 操作用户信息
    user_id: Mapped[int] = mapped_column(Integer, index=True, comment="操作用户ID")
    user_name: Mapped[str] = mapped_column(String(100), comment="操作用户名")
    user_email: Mapped[str | None] = mapped_column(String(255), comment="用户邮箱")

    # 请求信息
    ip_address: Mapped[str | None] = mapped_column(String(45), comment="IP地址")
    user_agent: Mapped[str | None] = mapped_column(Text, comment="用户代理")
    request_id: Mapped[str | None] = mapped_column(String(100), index=True, comment="请求ID")
    session_id: Mapped[str | None] = mapped_column(String(100), comment="会话ID")

    # 操作信息
    action: Mapped[AuditAction] = mapped_column(String(50), index=True, comment="操作类型")
    resource_type: Mapped[AuditResourceType] = mapped_column(
        String(50), index=True, comment="资源类型"
    )
    resource_id: Mapped[int | None] = mapped_column(Integer, index=True, comment="资源ID")
    resource_name: Mapped[str | None] = mapped_column(String(255), comment="资源名称")

    # 操作详情
    description: Mapped[str | None] = mapped_column(Text, comment="操作描述")
    reason: Mapped[str | None] = mapped_column(Text, comment="操作原因")
    old_values: Mapped[dict[str, Any] | None] = mapped_column(JSON, comment="操作前的值")
    new_values: Mapped[dict[str, Any] | None] = mapped_column(JSON, comment="操作后的值")

    # 结果信息
    success: Mapped[bool] = mapped_column(Boolean, default=True, comment="操作是否成功")
    error_message: Mapped[str | None] = mapped_column(Text, comment="错误信息")

    # 元数据
    endpoint: Mapped[str | None] = mapped_column(String(255), comment="API端点")
    method: Mapped[str | None] = mapped_column(String(10), comment="HTTP方法")
    duration_ms: Mapped[int | None] = mapped_column(Integer, comment="执行时长(毫秒)")

    def __repr__(self) -> str:
        return f"<AuditLog(id={self.id}, user={self.user_name}, action={self.action}, resource={self.resource_type})>"
