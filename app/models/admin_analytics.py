from sqlalchemy import (
    BigInteger,
    Column,
    DateTime,
    ForeignKey,
    Index,
    JSON,
    String,
    UniqueConstraint,
)
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func, text

from app.db.session import Base


class AdminAnalyticsSnapshot(Base):
    """分析快照元信息"""

    __tablename__ = "admin_analytics_snapshots"
    __table_args__ = (
        UniqueConstraint(
            "snapshot_type",
            "time_range",
            name="uq_admin_analytics_snapshots_type_range",
        ),
        Index("ix_admin_analytics_snapshots_type", "snapshot_type"),
        Index("ix_admin_analytics_snapshots_time_range", "time_range"),
        Index("ix_admin_analytics_snapshots_generated_at", "generated_at"),
        Index("ix_admin_analytics_snapshots_status", "status"),
        {"comment": "后台运营分析快照概览"},
    )

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    snapshot_type = Column(String(64), nullable=False)
    time_range = Column(String(32), nullable=False, server_default=text("'last_7d'"))
    filters = Column(
        JSONB().as_generic(JSON),
        nullable=False,
        server_default=text("'{}'"),
    )
    record_count = Column(BigInteger, nullable=False, server_default=text("0"))
    generated_at = Column(DateTime(timezone=True), nullable=False, server_default=func.now())
    generation_duration = Column(BigInteger, nullable=False, server_default=text("0"))
    status = Column(String(32), nullable=False, server_default=text("'pending'"))

    data_entries = relationship(
        "AdminAnalyticsSnapshotData",
        back_populates="snapshot",
        cascade="all, delete-orphan",
        passive_deletes=True,
        lazy="selectin",
    )


class AdminAnalyticsSnapshotData(Base):
    """分析快照载荷"""

    __tablename__ = "admin_analytics_snapshot_data"
    __table_args__ = (
        Index("ix_admin_analytics_snapshot_data_snapshot_id", "snapshot_id"),
        Index("ix_admin_analytics_snapshot_data_data_type", "data_type"),
        Index("ix_admin_analytics_snapshot_data_created_at", "created_at"),
        {"comment": "后台运营分析快照具体数据载荷"},
    )

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    snapshot_id = Column(
        BigInteger,
        ForeignKey("admin_analytics_snapshots.id", ondelete="CASCADE"),
        nullable=False,
    )
    payload = Column(
        JSONB().as_generic(JSON),
        nullable=False,
        server_default=text("'{}'"),
    )
    data_type = Column(String(64), nullable=False, server_default=text("'summary'"))
    created_at = Column(DateTime(timezone=True), nullable=False, server_default=func.now())

    snapshot = relationship("AdminAnalyticsSnapshot", back_populates="data_entries", lazy="joined")
