from sqlalchemy import (
    BigInteger,
    Boolean,
    Column,
    DateTime,
    ForeignKey,
    Index,
    Integer,
    Numeric,
    JSON,
    String,
    Text,
    UniqueConstraint,
)
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func, text

from app.db.session import Base


class AdminMonitoringMetric(Base):
    """监控指标记录"""

    __tablename__ = "admin_monitoring_metrics"
    __table_args__ = (
        Index("ix_admin_monitoring_metrics_metric_name", "metric_name"),
        Index("ix_admin_monitoring_metrics_timestamp", "timestamp"),
        Index("ix_admin_monitoring_metrics_metric_timestamp", "metric_name", "timestamp"),
        Index("ix_admin_monitoring_metrics_source", "source"),
        {"comment": "监控指标时序数据"},
    )

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    metric_name = Column(String(150), nullable=False)
    value = Column(Numeric(18, 6), nullable=False)
    labels = Column(
        JSONB().as_generic(JSON),
        nullable=False,
        server_default=text("'{}'"),
    )
    timestamp = Column(DateTime(timezone=True), nullable=False, server_default=func.now())
    source = Column(String(64), nullable=False, server_default=text("'system'"))
    metric_metadata = Column(
        "metadata",
        JSONB().as_generic(JSON),
        nullable=False,
        server_default=text("'{}'"),
    )


class AdminAlertRule(Base):
    """告警规则"""

    __tablename__ = "admin_alert_rules"
    __table_args__ = (
        UniqueConstraint("name", name="uq_admin_alert_rules_name"),
        Index("ix_admin_alert_rules_metric_name", "metric_name"),
        Index("ix_admin_alert_rules_enabled", "enabled"),
        Index("ix_admin_alert_rules_severity", "severity"),
        Index("ix_admin_alert_rules_created_at", "created_at"),
        {"comment": "系统监控告警规则定义"},
    )

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    name = Column(String(150), nullable=False)
    metric_name = Column(String(150), nullable=False)
    operator = Column(String(32), nullable=False)
    threshold = Column(Numeric(18, 6), nullable=False)
    duration = Column(String(32), nullable=False, server_default=text("'0m'"))
    severity = Column(String(32), nullable=False, server_default=text("'info'"))
    channels = Column(
        JSONB().as_generic(JSON),
        nullable=False,
        server_default=text("'[]'"),
    )
    enabled = Column(Boolean, nullable=False, server_default=text("true"))
    description = Column(Text)
    created_at = Column(DateTime(timezone=True), nullable=False, server_default=func.now())
    updated_at = Column(
        DateTime(timezone=True),
        nullable=False,
        server_default=func.now(),
        onupdate=func.now(),
    )
    created_by = Column(Integer, ForeignKey("users.id", ondelete="SET NULL"))
    updated_by = Column(Integer, ForeignKey("users.id", ondelete="SET NULL"))

    events = relationship(
        "AdminAlertEvent",
        back_populates="rule",
        lazy="selectin",
    )


class AdminAlertEvent(Base):
    """告警事件"""

    __tablename__ = "admin_alert_events"
    __table_args__ = (
        UniqueConstraint("event_id", name="uq_admin_alert_events_event_id"),
        Index("ix_admin_alert_events_rule_id", "rule_id"),
        Index("ix_admin_alert_events_status", "status"),
        Index("ix_admin_alert_events_triggered_at", "triggered_at"),
        Index("ix_admin_alert_events_notification_status", "notification_status"),
        {"comment": "监控告警触发事件"},
    )

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    rule_id = Column(BigInteger, ForeignKey("admin_alert_rules.id", ondelete="SET NULL"))
    event_id = Column(String(128), nullable=False)
    status = Column(String(32), nullable=False, server_default=text("'active'"))
    trigger_value = Column(Numeric(18, 6), nullable=False)
    triggered_at = Column(DateTime(timezone=True), nullable=False, server_default=func.now())
    resolved_at = Column(DateTime(timezone=True))
    context = Column(
        JSONB().as_generic(JSON),
        nullable=False,
        server_default=text("'{}'"),
    )
    message = Column(Text)
    notification_status = Column(String(32), nullable=False, server_default=text("'pending'"))

    rule = relationship("AdminAlertRule", back_populates="events", lazy="joined")
