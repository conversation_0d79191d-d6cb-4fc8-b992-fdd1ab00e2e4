from datetime import UTC, datetime

from sqlalchemy import (
    Boolean,
    Column,
    Float,
    ForeignKey,
    Integer,
    String,
    Text,
    UniqueConstraint,
)
from sqlalchemy.orm import relationship, synonym

from app.db.session import Base
from app.db.timestamp import Timestamp, now_utc


class UserBrowseHistory(Base):
    """用户浏览历史数据模型"""

    __tablename__ = "user_browse_history"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), index=True, nullable=False)

    # 内容类型：article 或 video
    content_type = Column(String(20), nullable=False, index=True)

    # 内容ID（文章ID或视频ID）
    content_id = Column(Integer, nullable=False, index=True)

    # 浏览时长（秒）
    duration = Column(Integer, nullable=True, comment="浏览时长（秒）")

    # 浏览来源：search, recommend, category, hot, author等
    source = Column(String(50), nullable=True, index=True, comment="浏览来源")

    # 设备信息
    device_type = Column(String(20), nullable=True, comment="设备类型：mobile, desktop, tablet")

    # IP地址（用于地理位置分析）
    ip_address = Column(String(45), nullable=True, comment="IP地址")

    # 用户代理
    user_agent = Column(Text, nullable=True, comment="用户代理字符串")

    created_at = Column(Timestamp, default=datetime.now(UTC), nullable=True, index=True)

    # 关联关系
    user = relationship("User")

    def __repr__(self):
        return f"<UserBrowseHistory {self.user_id}:{self.content_type}:{self.content_id}>"


class UserInteraction(Base):
    """用户交互行为数据模型"""

    __tablename__ = "user_interactions"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), index=True, nullable=False)

    # 内容类型：article 或 video
    content_type = Column(String(20), nullable=False, index=True)

    # 内容ID（文章ID或视频ID）
    content_id = Column(Integer, nullable=False, index=True)

    # 交互类型：view, like, favorite, comment, share, click等
    interaction_type = Column(String(20), nullable=False, index=True)

    # 交互权重（用于计算用户兴趣度）
    weight = Column(Float, default=1.0, comment="交互权重")

    # 额外数据（JSON格式存储）
    extra_data = Column(Text, nullable=True, comment="额外数据")

    created_at = Column(Timestamp, default=datetime.now(UTC), nullable=True, index=True)

    # 关联关系
    user = relationship("User")

    # 为兼容旧字段命名，提供 interaction_weight 同义名
    interaction_weight = synonym("weight")

    def __repr__(self):
        return f"<UserInteraction {self.user_id}:{self.interaction_type}:{self.content_type}:{self.content_id}>"


class UserInterest(Base):
    """用户兴趣标签数据模型"""

    __tablename__ = "user_interests"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), index=True, nullable=False)
    interest_tag = Column(String(100), nullable=False, index=True)
    interest_weight = Column(Float, default=0.0, nullable=False)
    created_at = Column(Timestamp, default=datetime.now(UTC), nullable=True)
    updated_at = Column(Timestamp, default=datetime.now(UTC), onupdate=datetime.now(UTC), nullable=True)

    user = relationship("User")

    def __repr__(self):
        return f"<UserInterest {self.user_id}:{self.interest_tag}:{self.interest_weight}>"


class UserProfile(Base):
    """用户画像数据模型"""

    __tablename__ = "user_profiles"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), index=True, nullable=False, unique=True)

    # 兴趣标签（JSON格式存储标签及权重）
    interest_tags = Column(Text, nullable=True, comment="兴趣标签JSON")

    # 偏好分类（JSON格式存储分类及权重）
    preferred_categories = Column(Text, nullable=True, comment="偏好分类JSON")

    # 活跃时间段（JSON格式存储）
    active_hours = Column(Text, nullable=True, comment="活跃时间段JSON")

    # 设备偏好
    preferred_device = Column(String(20), nullable=True, comment="偏好设备类型")

    # 内容偏好：article, video, both
    content_preference = Column(String(20), default="both", comment="内容偏好")

    # 推荐多样性偏好（0-1之间，越高越多样化）
    diversity_preference = Column(Float, default=0.5, comment="多样性偏好")

    # 最后更新时间
    last_updated = Column(Timestamp, default=now_utc, onupdate=now_utc)

    created_at = Column(Timestamp, default=now_utc)

    # 关联关系
    user = relationship("User")

    def __repr__(self):
        return f"<UserProfile {self.user_id}>"


class RecommendationLog(Base):
    """推荐记录数据模型"""

    __tablename__ = "recommendation_logs"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), index=True, nullable=False)

    # 推荐算法类型：collaborative, content_based, hot, hybrid等
    algorithm_type = Column(String(50), nullable=False, index=True)

    # 推荐内容列表（JSON格式存储）
    recommended_items = Column(Text, nullable=False, comment="推荐内容JSON")

    # 推荐原因（JSON格式存储）
    recommendation_reason = Column(Text, nullable=True, comment="推荐原因JSON")

    # 推荐位置：homepage, category_page, article_detail等
    position = Column(String(50), nullable=True, index=True, comment="推荐位置")

    # 是否被点击
    is_clicked = Column(Boolean, default=False, index=True)

    # 点击的内容ID
    clicked_content_id = Column(Integer, nullable=True, index=True)

    # 点击时间
    clicked_at = Column(Timestamp, nullable=True)

    created_at = Column(Timestamp, default=datetime.now(UTC), nullable=True, index=True)

    # 关联关系
    user = relationship("User")

    def __repr__(self):
        return f"<RecommendationLog {self.user_id}:{self.algorithm_type}>"


class ContentSimilarity(Base):
    """内容相似度数据模型"""

    __tablename__ = "content_similarities"

    id = Column(Integer, primary_key=True, index=True)

    # 源内容
    source_content_type = Column(String(20), nullable=False, index=True)
    source_content_id = Column(Integer, nullable=False, index=True)

    # 目标内容
    target_content_type = Column(String(20), nullable=False, index=True)
    target_content_id = Column(Integer, nullable=False, index=True)

    # 相似度分数（0-1之间）
    similarity_score = Column(Float, nullable=False, index=True)

    # 相似度类型：tag_based, category_based, content_based等
    similarity_type = Column(String(50), nullable=False, index=True)

    # 计算时间
    calculated_at = Column(Timestamp, default=datetime.now(UTC), nullable=True)

    # 联合唯一约束
    __table_args__ = (
        UniqueConstraint(
            "source_content_type",
            "source_content_id",
            "target_content_type",
            "target_content_id",
            "similarity_type",
            name="uq_content_similarity",
        ),
    )

    def __repr__(self):
        return f"<ContentSimilarity {self.source_content_type}:{self.source_content_id} -> {self.target_content_type}:{self.target_content_id}>"
