from sqlalchemy import (
    <PERSON><PERSON><PERSON>,
    <PERSON>umn,
    <PERSON><PERSON><PERSON>,
    Integer,
    String,
    Table,
    Text,
)
from sqlalchemy.orm import relationship, synonym
from sqlalchemy.sql import text

from app.db.session import Base
from app.db.timestamp import Timestamp, now_utc

# 用户关注关系表
user_follow = Table(
    "user_follow",
    Base.metadata,
    Column(
        "follower_id",
        Integer,
        ForeignKey("users.id", ondelete="CASCADE"),
        primary_key=True,
    ),
    Column(
        "followed_id",
        Integer,
        ForeignKey("users.id", ondelete="CASCADE"),
        primary_key=True,
    ),
    Column("created_at", Timestamp, nullable=False, server_default=text("CURRENT_TIMESTAMP")),
)

# 角色-权限关联表
role_permission = Table(
    "role_permission",
    Base.metadata,
    Column("role_id", Integer, ForeignKey("user_role.id"), primary_key=True),
    Column("permission_id", Integer, ForeignKey("permission.id"), primary_key=True),
)


class Permission(Base):
    """权限表"""

    __tablename__ = "permission"
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(50), unique=True, index=True, comment="权限名称")
    code = Column(String(50), unique=True, index=True, comment="权限代码")
    resource = Column(String(50), index=True, comment="资源类型")
    action = Column(String(50), comment="操作类型：read, write, delete, admin等")
    description = Column(Text, comment="权限描述")
    is_active = Column(Boolean, default=True, comment="是否启用")
    created_at = Column(Timestamp, default=now_utc)
    updated_at = Column(Timestamp, default=now_utc, onupdate=now_utc)

    # 关联关系
    roles = relationship("UserRole", secondary=role_permission, back_populates="permissions")

    def __repr__(self):
        return f"<Permission {self.name}>"


class UserRole(Base):
    """用户角色"""

    __tablename__ = "user_role"
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(50), unique=True, index=True)
    desc = Column(Text)
    is_default = Column(Boolean, default=False, comment="是否为默认角色")
    is_active = Column(Boolean, default=True, comment="是否启用")
    created_at = Column(Timestamp, default=now_utc)
    updated_at = Column(Timestamp, default=now_utc, onupdate=now_utc)

    # 关联关系
    permissions = relationship("Permission", secondary=role_permission, back_populates="roles")
    users = relationship("User", back_populates="role")

    def __repr__(self):
        return f"<UserRole {self.name}>"


class User(Base):
    """用户"""

    __tablename__ = "users"

    cache_version = Column(Integer, nullable=False, server_default="1", comment="缓存版本号")

    # 关注关系
    following = relationship(
        "User",
        secondary=user_follow,
        primaryjoin=(user_follow.c.follower_id == id),
        secondaryjoin=(user_follow.c.followed_id == id),
        backref="followers",  # 反向引用，可以通过user.followers访问关注该用户的用户列表
    )
    id = Column(Integer, primary_key=True, index=True)
    username = Column(String(50), unique=True, index=True, nullable=False)  # 手机号码
    password = Column(String(100), nullable=True)  # 微信登录用户可能没有密码
    role_id = Column(Integer, ForeignKey("user_role.id"), index=True)
    nickname = Column(String(50))
    description = Column(Text, nullable=True)
    email = Column(String(100), unique=True, index=True, nullable=True)
    avatar = Column(String(255), nullable=True)
    cover = Column(String(255), nullable=True)  # 用户封面
    is_active = Column(Boolean, default=True)
    is_superuser = Column(Boolean, default=False)
    last_login = Column(Timestamp, nullable=True)
    created_at = Column(Timestamp, default=now_utc)
    updated_at = Column(Timestamp, default=now_utc, onupdate=now_utc)
    is_deleted = Column(Boolean, default=False, nullable=False, index=True)

    # 隐私设置
    likes_privacy_settings = Column(
        Integer,
        nullable=False,
        default=3,  # 默认 1 | 2 = 3，即默认全部公开
        comment="点赞隐私设置 (位掩码: 1=文章, 2=视频)",
    )
    favorites_privacy_settings = Column(
        Integer,
        nullable=False,
        default=3,  # 默认 1 | 2 = 3，即默认全部公开
        comment="收藏隐私设置 (位掩码: 1=文章, 2=视频)",
    )

    # 微信相关字段
    wechat_openid = Column(
        String(100), unique=True, index=True, nullable=True, comment="微信OpenID"
    )
    wechat_unionid = Column(
        String(100), unique=True, index=True, nullable=True, comment="微信UnionID"
    )
    login_type = Column(String(20), default="password", comment="登录方式：password, wechat")

    # 关联关系
    role = relationship("UserRole", back_populates="users")
    articles = relationship("Article", back_populates="author")
    videos = relationship("Video", back_populates="author")
    comments = relationship("Comment", back_populates="author", foreign_keys="[Comment.author_id]")
    reviews = relationship("Review", back_populates="reviewer")
    video_folders = relationship("VideoFolder", back_populates="user")
    devices = relationship("UserDevice", back_populates="user")
    stats = relationship(
        "UserStats", back_populates="user", uselist=False, cascade="all, delete-orphan"
    )
    notifications = relationship(
        "Notification", back_populates="user", cascade="all, delete-orphan"
    )
    scratch_products = relationship("ScratchProduct", back_populates="author")
    posts = relationship("Post", back_populates="author")
    search_history = relationship(
        "SearchHistory", back_populates="user", cascade="all, delete-orphan"
    )

    # 关注关系
    following = relationship(
        "User",
        secondary=user_follow,
        primaryjoin=(id == user_follow.c.follower_id),
        secondaryjoin=(id == user_follow.c.followed_id),
        backref="followers",
    )

    def __repr__(self):
        return f"<User {self.username}>"

    @property
    def permissions(self):
        """获取用户所有权限"""
        if self.is_superuser:
            return ["*"]  # 超级用户拥有所有权限
        if not self.role:
            return []
        return [p.code for p in self.role.permissions if p.is_active]

    def check_privacy_setting(self, setting_type: str, content_type: str) -> bool:
        """
        检查用户的隐私设置是否公开

        Args:
            setting_type: 隐私设置类型 ('likes', 'favorites')
            content_type: 内容类型 ('article', 'video')

        Returns:
            True 如果该项设置为公开, False 如果为私密
        """
        from app.core.enums import PrivacySetting

        privacy_map = {
            "likes": self.likes_privacy_settings,
            "favorites": self.favorites_privacy_settings,
        }
        content_map = {
            "article": PrivacySetting.ARTICLE,
            "video": PrivacySetting.VIDEO,
        }

        privacy_field = privacy_map.get(setting_type)
        content_flag = content_map.get(content_type)

        if privacy_field is None or content_flag is None:
            return False  # 默认为不公开

        # 使用位与操作检查特定位是否被设置
        return (privacy_field & content_flag.value) > 0


class UserFollow(Base):
    """用户关注关系映射类（基于 user_follow 中间表）"""

    __table__ = user_follow

    # 提供 following_id 同义名以兼容旧代码命名
    following_id = synonym("followed_id")
