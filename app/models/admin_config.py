from sqlalchemy import (
    BigInteger,
    Boolean,
    CheckConstraint,
    Column,
    DateTime,
    ForeignKey,
    Index,
    Integer,
    JSON,
    String,
    Text,
    UniqueConstraint,
)
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.orm import backref, relationship
from sqlalchemy.sql import func, text

from app.db.session import Base


class AdminConfigSetting(Base):
    """系统配置项"""

    __tablename__ = "admin_config_settings"
    __table_args__ = (
        UniqueConstraint("key", name="uq_admin_config_settings_key"),
        CheckConstraint("version >= 1", name="ck_admin_config_settings_version_positive"),
        Index("ix_admin_config_settings_category", "category"),
        Index("ix_admin_config_settings_category_key", "category", "key"),
        Index("ix_admin_config_settings_is_sensitive", "is_sensitive"),
        {"comment": "系统配置项存储表"},
    )

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    category = Column(String(100), nullable=False)
    key = Column(String(255), nullable=False)
    value = Column(Text, nullable=False)
    value_type = Column(String(32), nullable=False)
    description = Column(String(255))
    settings_metadata = Column(
        "metadata",
        JSONB().as_generic(JSON),
        nullable=False,
        server_default=text("'{}'"),
    )
    version = Column(BigInteger, nullable=False, server_default=text("1"))
    updated_at = Column(
        DateTime(timezone=True),
        nullable=False,
        server_default=func.now(),
        onupdate=func.now(),
    )
    updated_by = Column(Integer, ForeignKey("users.id", ondelete="SET NULL"))
    is_sensitive = Column(Boolean, nullable=False, server_default=text("false"))


class AdminFeatureToggle(Base):
    """功能开关定义"""

    __tablename__ = "admin_feature_toggles"
    __table_args__ = (
        UniqueConstraint("name", name="uq_admin_feature_toggles_name"),
        Index("ix_admin_feature_toggles_scope", "scope"),
        Index("ix_admin_feature_toggles_is_enabled", "is_enabled"),
        {"comment": "后台功能开关管理表"},
    )

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    name = Column(String(100), nullable=False)
    is_enabled = Column(Boolean, nullable=False, server_default=text("false"))
    description = Column(String(255))
    scope = Column(String(50), nullable=False, server_default=text("'global'"))
    toggle_config = Column(
        "config",
        JSONB().as_generic(JSON),
        nullable=False,
        server_default=text("'{}'"),
    )
    updated_at = Column(
        DateTime(timezone=True),
        nullable=False,
        server_default=func.now(),
        onupdate=func.now(),
    )
    updated_by = Column(Integer, ForeignKey("users.id", ondelete="SET NULL"))


class AdminConfigChangeLog(Base):
    """配置变更历史"""

    __tablename__ = "admin_config_change_logs"
    __table_args__ = (
        UniqueConstraint("change_id", name="uq_admin_config_change_logs_change_id"),
        Index("ix_admin_config_change_logs_config_key", "config_key"),
        Index("ix_admin_config_change_logs_category_changed_at", "category", "changed_at"),
        Index("ix_admin_config_change_logs_changed_by", "changed_by"),
        Index("ix_admin_config_change_logs_changed_at", "changed_at"),
        Index("ix_admin_config_change_logs_rollback_id", "rollback_id"),
        {"comment": "配置变更历史与回滚记录"},
    )

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    change_id = Column(String(128), nullable=False)
    config_key = Column(String(255), nullable=False)
    category = Column(String(100))
    old_value = Column(Text)
    new_value = Column(Text)
    change_type = Column(String(32), nullable=False)
    change_reason = Column(Text)
    changed_by = Column(Integer, ForeignKey("users.id", ondelete="SET NULL"))
    changed_at = Column(DateTime(timezone=True), nullable=False, server_default=func.now())
    rollback_id = Column(
        String(128),
        ForeignKey("admin_config_change_logs.change_id", ondelete="SET NULL"),
    )
    is_rollback = Column(Boolean, nullable=False, server_default=text("false"))

    rollback_change = relationship(
        "AdminConfigChangeLog",
        remote_side=[change_id],
        foreign_keys=[rollback_id],
        backref=backref("rolled_back_entries", lazy="selectin"),
        uselist=False,
    )
