"""标签热度统计模型"""

from sqlalchemy import Column, Float, ForeignKey, Integer, String, UniqueConstraint
from sqlalchemy.orm import relationship

from app.db.session import Base
from app.db.timestamp import Timestamp, now_utc


class TagStats(Base):
    """标签热度统计数据"""

    __tablename__ = "tag_stats"

    id = Column(Integer, primary_key=True, index=True)
    tag_id = Column(Integer, ForeignKey("tags.id", ondelete="CASCADE"), nullable=False, index=True)
    content_type = Column(
        String(32),
        nullable=False,
        index=True,
        comment="标签所属内容类型: article/video/scratch/post/global",
    )
    total_score = Column(Float, nullable=False, default=0.0, comment="累计热度分数")
    unique_users_est = Column(Integer, nullable=False, default=0, comment="独立用户估算值")
    window_delta = Column(Float, nullable=False, default=0.0, comment="最近窗口热度变化")
    last_seen_at = Column(Timestamp, nullable=True, comment="最后一次热度更新的时间")
    created_at = Column(Timestamp, default=now_utc, nullable=False)
    updated_at = Column(Timestamp, default=now_utc, onupdate=now_utc, nullable=False)

    tag = relationship("Tag", back_populates="stats", lazy="joined")

    __table_args__ = (
        UniqueConstraint("tag_id", "content_type", name="uq_tag_stats_tag_content_type"),
    )

    def __repr__(self) -> str:
        return (
            f"<TagStats tag_id={self.tag_id} content_type={self.content_type} "
            f"total_score={self.total_score:.2f}>"
        )
