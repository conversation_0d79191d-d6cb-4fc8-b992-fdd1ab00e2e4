import enum
from datetime import UTC, datetime

from sqlalchemy import (
    Column,
    Enum,
    ForeignKey,
    Integer,
    Text,
)
from sqlalchemy.orm import relationship

from app.db.session import Base
from app.db.timestamp import Timestamp


class ContentType(str, enum.Enum):
    """内容类型枚举"""

    ARTICLE = "article"
    VIDEO = "video"
    POST = "post"


class ReviewStatus(str, enum.Enum):
    """审核状态枚举"""

    PENDING = "pending"  # 待审核
    APPROVED = "approved"  # 已通过
    REJECTED = "rejected"  # 已拒绝


class Review(Base):
    """内容审核表模型"""

    __tablename__ = "reviews"

    id = Column(Integer, primary_key=True, index=True)
    # 内容类型：文章、视频或沸点
    content_type = Column(
        Enum("article", "video", "post", name="content_type"), nullable=False, index=True
    )
    # 内容ID（文章ID或视频ID）
    content_id = Column(Integer, nullable=False, index=True)
    # 审核状态
    status = Column(
        Enum("pending", "approved", "rejected", name="review_status"),
        default="pending",
        index=True,
    )
    # 审核人ID
    reviewer_id = Column(Integer, ForeignKey("users.id"), index=True, nullable=True)
    # 审核意见
    comment = Column(Text, nullable=True)
    # 审核时间
    reviewed_at = Column(Timestamp, nullable=True)
    # 创建和更新时间
    created_at = Column(Timestamp, default=datetime.now(UTC), nullable=True)
    updated_at = Column(
        Timestamp, default=datetime.now(UTC), onupdate=datetime.now(UTC), nullable=True
    )

    # 关联关系
    reviewer = relationship("User", back_populates="reviews")

    def __repr__(self):
        return f"<Review {self.id} {self.content_type}:{self.content_id} {self.status}>"
