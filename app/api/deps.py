import contextlib
from datetime import datetime
from pathlib import Path
from typing import Annotated

from fastapi import Depends, HTTPException, Query, Request, WebSocket, status
from fastapi.security import OAuth2PasswordBearer
from pydantic import BaseModel
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from app.config import get_settings
from app.core.logging import logger
from app.db.session import get_db
from app.models.user import User, UserRole
from app.services.interfaces.token_service_interface import ITokenService
from app.services.service_factory import get_token_service

settings = get_settings()

DEBUG_LOG_PATH = Path("/tmp/ws_auth_debug.log")


def _debug_ws(message: str, *, force: bool = False) -> None:
    if not force and not getattr(settings, "WS_AUTH_DEBUG_ENABLED", False):
        return

    timestamp = datetime.utcnow().isoformat()
    line = f"[{timestamp}] {message}\n"
    try:
        DEBUG_LOG_PATH.parent.mkdir(parents=True, exist_ok=True)
        with DEBUG_LOG_PATH.open("a", encoding="utf-8") as fp:
            fp.write(line)
    except Exception:
        pass

    try:
        _log_ws("debug", message)
    except Exception:
        pass


def _log_ws(level: str, message: str) -> None:
    log_method = getattr(logger, level, logger.info)
    try:
        log_method("WS auth: {}", message)
    except Exception:
        pass


_debug_ws("deps module loaded")

# OAuth2密码流的令牌URL
oauth2_scheme = OAuth2PasswordBearer(tokenUrl=f"{settings.API_V1_STR}/auth/login")

# 可选的OAuth2密码流（用于支持游客访问）
oauth2_scheme_optional = OAuth2PasswordBearer(
    tokenUrl=f"{settings.API_V1_STR}/auth/login", auto_error=False
)


class TokenData(BaseModel):
    username: str | None = None
    permissions: list[str] = []


async def get_token_from_header_or_cookie(
    request: Request, token_from_header: str | None = Depends(oauth2_scheme_optional)
) -> str | None:
    """
    从 Authorization 头或 Cookie 中获取 token
    优先级: Authorization Header > Cookie
    """
    if token_from_header:
        return token_from_header

    token_from_cookie = request.cookies.get("access_token")
    return token_from_cookie


async def get_current_user(
    db: AsyncSession = Depends(get_db),
    token: str | None = Depends(get_token_from_header_or_cookie),
    token_service: ITokenService = Depends(get_token_service),
) -> User:
    """获取当前用户"""
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="无法验证凭据",
        headers={"WWW-Authenticate": "Bearer"},
    )

    if not token:
        raise credentials_exception

    # 使用 TokenService 验证 token
    token_info = await token_service.verify_token(token)
    if not token_info:
        raise credentials_exception

    username = token_info.get("sub")
    if not username:
        raise credentials_exception

    # 查询用户（预加载 role 和 role.permissions 关系）
    from sqlalchemy.orm import selectinload

    result = await db.execute(
        select(User)
        .options(selectinload(User.role).selectinload(UserRole.permissions))
        .where(User.username == username)
    )
    user = result.scalar_one_or_none()
    if user is None:
        raise credentials_exception
    if not user.is_active:
        raise HTTPException(status_code=400, detail="用户未激活")

    return user


async def get_current_active_user(
    current_user: Annotated[User, Depends(get_current_user)],
) -> User:
    """获取当前激活用户"""
    if not current_user.is_active:
        raise HTTPException(status_code=400, detail="用户未激活")
    return current_user


async def get_current_active_superuser(
    current_user: Annotated[User, Depends(get_current_user)],
) -> User:
    """获取当前超级管理员用户"""
    if not current_user.is_superuser:
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="用户权限不足")
    return current_user


async def get_current_user_optional(
    db: AsyncSession = Depends(get_db),
    token: str | None = Depends(get_token_from_header_or_cookie),
    token_service: ITokenService = Depends(get_token_service),
) -> User | None:
    """
    获取当前用户（可选）
    用于支持游客访问的端点

    Args:
        db: 数据库会话
        token: 可选的访问令牌

    Returns:
        用户对象或None（游客）
    """
    if not token:
        return None

    # 验证 token，但失败时返回 None 而不是抛出异常
    token_info = await token_service.verify_token(token)
    if not token_info:
        return None

    username = token_info.get("sub")
    if not username:
        return None

    # 查询用户
    from sqlalchemy.orm import selectinload

    result = await db.execute(
        select(User)
        .options(selectinload(User.role).selectinload(UserRole.permissions))
        .where(User.username == username)
    )
    user = result.scalar_one_or_none()

    # 如果用户不存在或未激活，也视为游客
    if user is None or not user.is_active:
        return None

    return user


def _strip_bearer_prefix(token: str) -> str:
    """剥离 token 中的 Bearer 前缀"""
    if token.startswith("Bearer "):
        return token[7:]  # 移除 'Bearer ' 前缀（注意空格）
    return token


def _mask_token(token: str | None) -> str:
    """对token做脱敏处理，避免在日志中输出完整凭证"""
    if not token:
        return "<empty>"

    token = token.strip()
    if len(token) <= 10:
        return token

    return f"{token[:4]}...{token[-4:]}"


async def extract_token_from_websocket_protocols(websocket) -> str | None:
    """从WebSocket协议中提取token

    支持的协议格式：
    - Bearer.{token}
    """
    if hasattr(websocket, "headers") and "sec-websocket-protocol" in websocket.headers:
        protocol_header = websocket.headers["sec-websocket-protocol"]

        # 解析协议列表（可能有多个协议用逗号分隔）
        protocols = [p.strip() for p in protocol_header.split(",")]
        with contextlib.suppress(Exception):
            _debug_ws(f"Sec-WebSocket-Protocol present candidates={len(protocols)}")

        for index, protocol in enumerate(protocols):
            if protocol.startswith("Bearer"):
                token = protocol[7:]  # 移除 'Bearer.' 或 'Bearer ' 前缀
                token = token.strip()
                with contextlib.suppress(Exception):
                    _debug_ws(
                        f"token obtained from subprotocol index={index} masked={_mask_token(token)}"
                    )
                return token

    return None


async def get_token_from_websocket_protocols_or_query(
    websocket: WebSocket,
    token: str = Query(None, description="访问令牌（Query参数）"),
) -> str | None:
    """从 WebSocket 协议、Query 参数或 Cookie 中获取 token

    优先级: Sec-WebSocket-Protocol > Query 参数 > Cookie(access_token)
    """
    # 1) 首先尝试从协议中提取 token
    token_from_protocol = await extract_token_from_websocket_protocols(websocket)
    if token_from_protocol:
        return token_from_protocol

    # 2) 回退到 Query 参数
    if token is None:
        query_params = getattr(websocket, "query_params", None)
        if query_params is not None:
            token = query_params.get("token")

    if token:
        # 剥离 Bearer 前缀（如果存在）
        token = _strip_bearer_prefix(token)
        with contextlib.suppress(Exception):
            _debug_ws(f"token obtained from query masked={_mask_token(token)}")
        return token

    # 3) 最后尝试从 Cookie 中读取 access_token
    try:
        if hasattr(websocket, "cookies") and isinstance(websocket.cookies, dict):
            cookie_token = websocket.cookies.get("access_token")
            if cookie_token:
                # 剥离 Bearer 前缀（如果存在）
                cookie_token = _strip_bearer_prefix(cookie_token)
                with contextlib.suppress(Exception):
                    _debug_ws(f"token obtained from cookie masked={_mask_token(cookie_token)}")
                return cookie_token
    except Exception:
        # 读取 cookie 不应影响后续逻辑
        pass

    with contextlib.suppress(Exception):
        _debug_ws("token not found in protocol, query or cookie")
    return None


async def _resolve_ws_user(
    db: AsyncSession,
    subject_str: str,
    payload_user_id: str | int | None,
    subject_can_be_int: bool,
    subject_int: int | None,
) -> User | None:
    """根据 token 载荷中的 subject 信息解析用户"""
    try:
        result = await db.execute(select(User).where(User.username == subject_str))
        user = result.scalar_one_or_none()
        if user is not None:
            return user
    except Exception as exc:  # pragma: no cover - 仅用于日志记录
        _log_ws("warning", f"username lookup error subject={subject_str} error={exc}")
        _debug_ws(f"username lookup failed subject={subject_str} error={exc}")

    if payload_user_id is not None:
        try:
            user_id = int(payload_user_id)
        except (TypeError, ValueError):
            user_id = None
        if user_id is not None:
            try:
                result = await db.execute(select(User).where(User.id == user_id))
                user = result.scalar_one_or_none()
                if user is not None:
                    return user
            except Exception as exc:  # pragma: no cover - 仅用于日志记录
                _log_ws(
                    "warning", f"user_id lookup error payload_user_id={payload_user_id} error={exc}"
                )
                _debug_ws(f"user_id lookup failed payload_user_id={payload_user_id} error={exc}")

    if subject_can_be_int and subject_int is not None:
        try:
            result = await db.execute(select(User).where(User.id == subject_int))
            user = result.scalar_one_or_none()
            if user is not None:
                return user
        except Exception as exc:  # pragma: no cover - 仅用于日志记录
            _log_ws("warning", f"fallback id lookup error subject={subject_str} error={exc}")
            _debug_ws(f"fallback id lookup failed subject={subject_str} error={exc}")

    return None


async def get_current_user_websocket(
    websocket: WebSocket,
    token: str | None = Depends(get_token_from_websocket_protocols_or_query),
    db: AsyncSession = Depends(get_db),
    token_service: ITokenService = Depends(get_token_service),
) -> User:
    """WebSocket 连接的用户认证

    支持三种认证方式（按优先级）：
    1. 通过 Sec-WebSocket-Protocol: protocols: ['Bearer.{token}']
    2. 通过 Query 参数: ?token={token}
    3. 通过 Cookie: access_token={token}

    兼容 token 中 `sub` 为用户名或用户ID 两种情况。
    """
    try:
        try:
            origin = websocket.headers.get('origin') if hasattr(websocket, 'headers') else None
            proto = (
                websocket.headers.get('sec-websocket-protocol')
                if hasattr(websocket, 'headers')
                else None
            )
            client = getattr(websocket, 'client', None)
            path_value = getattr(websocket, 'url', None)
            has_cookie = (
                hasattr(websocket, 'cookies')
                and isinstance(websocket.cookies, dict)
                and 'access_token' in websocket.cookies
            )
            _log_ws(
                'info',
                'begin origin=%s path=%s client=%s has_subprotocol=%s has_cookie=%s'
                % (origin, path_value, client, bool(proto), bool(has_cookie)),
            )
            _debug_ws(
                'begin origin=%s path=%s client=%s has_subprotocol=%s has_cookie=%s'
                % (origin, path_value, client, bool(proto), bool(has_cookie))
            )
        except Exception:
            pass

        if not token:
            await websocket.close(code=1008, reason='No authentication token provided')
            _log_ws('warning', 'missing token from all sources')
            _debug_ws('missing token from all sources')
            raise HTTPException(status_code=401, detail='未提供认证令牌')

        clean_token = _strip_bearer_prefix(token)
        masked_token = _mask_token(clean_token)
        _log_ws('info', f'token prepared masked={masked_token}')
        _debug_ws(f'token prepared masked={masked_token}')

        payload = await token_service.verify_token(clean_token)
        if not payload:
            await websocket.close(code=1008, reason='Invalid token')
            _log_ws(
                'warning',
                f'token verification failed length={len(clean_token) if clean_token else 0}',
            )
            _debug_ws(
                f'token verification failed length={len(clean_token) if clean_token else 0}'
            )
            raise HTTPException(status_code=401, detail='无效的令牌')

        _log_ws('info', f'token payload keys={sorted(payload.keys())}')
        _debug_ws(f'token payload keys={sorted(payload.keys())}')

        subject = payload.get('sub') or payload.get('username')
        if subject is None:
            await websocket.close(code=1008, reason='Invalid token payload')
            _log_ws('warning', 'payload missing subject')
            _debug_ws('payload missing subject')
            raise HTTPException(status_code=401, detail='令牌载荷无效')

        subject_str = str(subject)
        payload_user_id = payload.get('user_id') or payload.get('id')
        subject_int = None
        subject_can_be_int = False
        try:
            subject_int = int(subject_str)
            subject_can_be_int = True
        except (TypeError, ValueError):
            pass

        user = await _resolve_ws_user(
            db=db,
            subject_str=subject_str,
            payload_user_id=payload_user_id,
            subject_can_be_int=subject_can_be_int,
            subject_int=subject_int,
        )

        if user is None:
            await websocket.close(code=1008, reason='User not found')
            _log_ws(
                'warning',
                f'user not found subject={subject_str} payload_user_id={payload_user_id}',
            )
            _debug_ws(
                f'user not found subject={subject_str} payload_user_id={payload_user_id}'
            )
            raise HTTPException(status_code=404, detail='用户不存在')

        if not user.is_active:
            await websocket.close(code=1008, reason='User inactive')
            _log_ws('warning', f'user inactive user_id={user.id}')
            _debug_ws(f'user inactive user_id={user.id}')
            raise HTTPException(status_code=400, detail='用户未激活')

        _log_ws('info', f'success user_id={user.id} username={user.username}')
        _debug_ws(f'auth success user_id={user.id} username={user.username}')
        return user

    except HTTPException as exc:
        _log_ws(
            'warning',
            f'handshake rejected status={exc.status_code} detail={exc.detail}',
        )
        _debug_ws(f'handshake rejected status={exc.status_code} detail={exc.detail}')
        raise
    except Exception as error:
        await websocket.close(code=1008, reason=str(error))
        _log_ws('error', f'exception during handshake error={error}')
        _debug_ws(f'exception during handshake: {error}')
        raise

async def get_current_user_websocket_debug(
    websocket: WebSocket,
    token: str = Query(..., description="调试模式下必须提供 token"),
    db: AsyncSession = Depends(get_db),
    token_service: ITokenService = Depends(get_token_service),
) -> User:
    """简化版 WebSocket 鉴权，仅用于调试。

    - 强制要求 query 参数提供 token
    - 只做 token 验证 + 用户查询
    - 将关键步骤写入调试日志
    """

    _debug_ws(f"[debug] raw token={token}", force=True)

    token = _strip_bearer_prefix(token)
    _debug_ws(f"[debug] stripped token={_mask_token(token)}", force=True)

    payload = await token_service.verify_token(token)
    if not payload:
        _debug_ws("[debug] token verification failed", force=True)
        raise HTTPException(status_code=401, detail="token 验证失败")

    _debug_ws(f"[debug] payload={payload}", force=True)

    username = payload.get("sub") or payload.get("username")
    if not username:
        _debug_ws("[debug] payload missing username", force=True)
        raise HTTPException(status_code=401, detail="payload 无有效 subject")

    result = await db.execute(select(User).where(User.username == username))
    user = result.scalar_one_or_none()
    if not user:
        _debug_ws(f"[debug] user not found username={username}", force=True)
        raise HTTPException(status_code=404, detail="用户不存在")

    if not user.is_active:
        _debug_ws(f"[debug] user inactive user_id={user.id}", force=True)
        raise HTTPException(status_code=400, detail="用户未激活")

    _debug_ws(
        f"[debug] auth success user_id={user.id} username={user.username}",
        force=True,
    )
    return user
