import json
from typing import Any, Iterable

from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware

from app.core.logging import logger
from app.core.response_wrapper import success_response
from app.config import settings
from app.services.oss_access_service import SignedUrlResult
from app.services.service_factory import get_oss_access_service


class ResponseFormatterMiddleware(BaseHTTPMiddleware):
    """统一响应格式化中间件"""

    def __init__(self, app) -> None:
        super().__init__(app)
        self._oss_service = None

    def _is_formatted_response(self, content: dict) -> bool:
        """检测是否已经是格式化的响应"""
        if not isinstance(content, dict):
            return False

        # 检查是否包含新的APIResponse格式关键字段
        required_fields = {"success", "code", "message", "timestamp"}
        content_fields = set(content.keys())

        return len(content_fields.intersection(required_fields)) >= 3

    async def dispatch(self, request: Request, call_next) -> Any:
        response = await call_next(request)

        # 对于OPTIONS请求（CORS预检）和204状态码，直接返回原始响应
        if request.method == "OPTIONS" or response.status_code == 204:
            return response

        # 只对API v1路径且返回JSON的成功响应进行格式化
        media_type = response.media_type or response.headers.get("content-type", "")
        if (
            request.url.path.startswith("/api/v1")
            and response.status_code < 400
            and response.status_code != 204
            and request.url.path not in ["/api/v1/health", "/api/v1/wechat/callback"]
            and str(media_type).lower().startswith("application/json")
        ):
            try:
                body = b""
                async for chunk in response.body_iterator:
                    body += chunk
                content = json.loads(body.decode("utf-8")) if body else {}

                # 检查是否已经是格式化的响应
                if self._is_formatted_response(content):
                    # 已经是格式化响应，直接返回
                    formatted_content = content
                else:
                    # 普通数据，使用新的格式进行包装
                    formatted_content = success_response(content)

                if self._should_enrich_assets(formatted_content):
                    signed_assets = await self._build_signed_assets(
                        formatted_content.get("data")
                    )
                    if signed_assets:
                        meta = formatted_content.setdefault("meta", {})
                        meta["signed_assets"] = signed_assets

                response_body = json.dumps(
                    formatted_content, ensure_ascii=False, default=str
                ).encode("utf-8")

                # 创建新的headers字典，排除Content-Length让FastAPI自动计算
                new_headers = {
                    k: v for k, v in response.headers.items() if k.lower() != "content-length"
                }
                return Response(
                    content=response_body,
                    status_code=response.status_code,
                    headers=new_headers,
                    media_type=response.media_type or "application/json",
                )
            except Exception as e:
                logger.error(f"响应格式化失败: {str(e)}", exc_info=True)
                # 异常情况下也要排除Content-Length头部
                new_headers = {
                    k: v for k, v in response.headers.items() if k.lower() != "content-length"
                }
                return Response(
                    content=json.dumps(
                        success_response({"error": "响应格式化失败"}), ensure_ascii=False
                    ).encode("utf-8"),
                    status_code=500,
                    headers=new_headers,
                    media_type="application/json",
                )
        return response

    def _should_enrich_assets(self, content: dict[str, Any]) -> bool:
        if not settings.OSS_PRIVATE_BUCKET:
            return False
        if not isinstance(content, dict):
            return False
        if not content.get("data"):
            return False
        return True

    def _looks_like_asset(self, value: str) -> bool:
        if not value or not isinstance(value, str):
            return False
        if value.startswith(("http://", "https://", "ws://", "wss://", "data:")):
            return False
        normalized = value.lstrip("/")
        if not normalized:
            return False
        if normalized.lower().startswith("api/"):
            return False
        if "." not in normalized.split("/")[-1]:
            return False
        return True

    def _extract_asset_paths(self, node: Any, collector: set[str]) -> None:
        if isinstance(node, dict):
            for value in node.values():
                self._extract_asset_paths(value, collector)
        elif isinstance(node, (list, tuple, set)):
            for item in node:
                self._extract_asset_paths(item, collector)
        elif isinstance(node, str) and self._looks_like_asset(node):
            collector.add(node)

    def _get_oss_service(self):
        if self._oss_service is None:
            self._oss_service = get_oss_access_service()
        return self._oss_service

    async def _build_signed_assets(self, data: Any) -> dict[str, dict[str, Any]]:
        collector: set[str] = set()
        self._extract_asset_paths(data, collector)

        if not collector:
            return {}

        max_batch = max(settings.OSS_SIGN_URL_MAX_BATCH, 1)
        selected = list(collector)[:max_batch]

        service = self._get_oss_service()
        results = await service.generate_signed_urls(selected)

        payload: dict[str, dict[str, Any]] = {}
        for original in selected:
            result = results.get(original)
            if isinstance(result, SignedUrlResult):
                payload[original] = {
                    "signed_url": result.url,
                    "expires_in": result.expires_in,
                    "cdn_enabled": result.use_cdn,
                }

        return payload
