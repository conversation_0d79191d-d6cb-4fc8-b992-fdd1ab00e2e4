from typing import Literal

from fastapi import APIRouter, Depends, Query
from fastapi_cache.decorator import cache
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import Session, selectinload

from app import crud, schemas
from app.api import deps
from app.api.permission_deps import require_permission
from app.core.permissions import Permissions
from app.models.category import Category

router = APIRouter()


@router.get(
    "/{category_id}/related_info",
    response_model=schemas.CategoryRelatedInfo,
    summary="获取分类相关的推荐信息",
)
@cache(expire=3600)
async def get_category_related_info(
    category_id: int,
    db: AsyncSession = Depends(deps.get_db),
):
    """
    获取指定分类的详情信息和相关推荐。
    - **分类详情**: 分类的基本信息和统计数据。
    - **相关作者**: 在该分类下发布视频最多的作者。
    - **热门标签**: 在该分类下视频中使用最频繁的标签。
    - 结果会缓存1小时以提高性能。
    """
    # 获取分类详情，递归预加载 children 关系
    from sqlalchemy import select

    result = await db.execute(
        select(Category)
        .options(
            selectinload(Category.children).selectinload(Category.children).selectinload(Category.children)
        )
        .where(Category.id == category_id)
    )
    category = result.scalar_one_or_none()
    if not category:
        from fastapi import HTTPException, status

        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="分类不存在")

    # 获取相关作者和热门标签
    authors = await crud.user.get_top_authors_in_category(db, category_id=category_id)
    tags = await crud.tag.get_top_tags_in_category(db, category_id=category_id)

    return schemas.CategoryRelatedInfo(category=category, related_authors=authors, hot_tags=tags)


@router.get(
    "/tree",
    response_model=list[schemas.Category],
    summary="获取分类层级树",
)
async def get_category_tree(
    db: Session = Depends(deps.get_db),
    category_type: Literal["article", "video", "scratch"] | None = Query(
        None, description="分类类型"
    ),
) -> list[schemas.Category]:
    """
    获取所有分类的层级结构树。
    这对于构建分类导航菜单非常有用。
    """
    return await crud.category.get_category_tree(db, category_type=category_type)


# ==================== 管理端接口 ====================


@router.post(
    "/admin", response_model=schemas.Category, summary="创建分类", description="管理员创建新分类"
)
async def create_category_admin(
    category_data: schemas.CategoryCreate,
    db: AsyncSession = Depends(deps.get_db),
    current_user=Depends(require_permission(Permissions.CATEGORY_MANAGE_ALL)),
):
    """创建新分类"""
    from fastapi import HTTPException
    from sqlalchemy import select

    # 检查分类名称是否已存在
    result = await db.execute(select(Category).where(Category.name == category_data.name))
    if result.scalar_one_or_none():
        raise HTTPException(status_code=400, detail="分类名称已存在")

    # 创建分类
    category = Category(**category_data.model_dump())
    db.add(category)
    await db.commit()
    
    # 重新加载以确保所有关系都已加载
    result = await db.execute(
        select(Category)
        .options(
            selectinload(Category.children).selectinload(Category.children).selectinload(Category.children)
        )
        .where(Category.id == category.id)
    )
    category = result.scalar_one_or_none()

    return category


@router.put(
    "/{category_id}/admin",
    response_model=schemas.Category,
    summary="更新分类",
    description="管理员更新分类信息",
)
async def update_category_admin(
    category_id: int,
    category_data: schemas.CategoryUpdate,
    db: AsyncSession = Depends(deps.get_db),
    current_user=Depends(require_permission(Permissions.CATEGORY_MANAGE_ALL)),
):
    """更新分类信息"""
    from fastapi import HTTPException
    from sqlalchemy import select

    # 获取分类（递归加载所有层级的 children）
    result = await db.execute(
        select(Category)
        .options(
            selectinload(Category.children).selectinload(Category.children).selectinload(Category.children)
        )
        .where(Category.id == category_id)
    )
    category = result.scalar_one_or_none()
    if not category:
        raise HTTPException(status_code=404, detail="分类不存在")

    # 检查名称冲突
    if category_data.name and category_data.name != category.name:
        result = await db.execute(
            select(Category).where(Category.name == category_data.name, Category.id != category_id)
        )
        if result.scalar_one_or_none():
            raise HTTPException(status_code=400, detail="分类名称已存在")

    # 更新分类
    for field, value in category_data.model_dump(exclude_unset=True).items():
        setattr(category, field, value)

    await db.commit()
    # 重新加载以确保所有关系都已加载
    await db.refresh(category)
    result = await db.execute(
        select(Category)
        .options(
            selectinload(Category.children).selectinload(Category.children).selectinload(Category.children)
        )
        .where(Category.id == category_id)
    )
    category = result.scalar_one_or_none()

    return category


@router.delete("/{category_id}/admin", summary="删除分类", description="管理员软删除分类")
async def delete_category_admin(
    category_id: int,
    db: AsyncSession = Depends(deps.get_db),
    current_user=Depends(require_permission(Permissions.CATEGORY_MANAGE_ALL)),
):
    """软删除分类"""
    from fastapi import HTTPException
    from sqlalchemy import select

    # 获取分类
    result = await db.execute(select(Category).where(Category.id == category_id))
    category = result.scalar_one_or_none()
    if not category:
        raise HTTPException(status_code=404, detail="分类不存在")

    # 软删除
    category.is_deleted = True
    await db.commit()

    return {"message": "分类删除成功"}


@router.put("/{category_id}/sort", summary="更新分类排序", description="管理员更新分类排序权重")
async def update_category_sort(
    category_id: int,
    sort_order: int = Query(..., description="排序权重"),
    db: AsyncSession = Depends(deps.get_db),
    current_user=Depends(require_permission(Permissions.CATEGORY_MANAGE_ALL)),
):
    """更新分类排序"""
    from fastapi import HTTPException
    from sqlalchemy import select

    # 获取分类
    result = await db.execute(select(Category).where(Category.id == category_id))
    category = result.scalar_one_or_none()
    if not category:
        raise HTTPException(status_code=404, detail="分类不存在")

    # 更新排序
    category.sort_order = sort_order
    await db.commit()

    return {"message": "排序更新成功"}
