"""动态配置中心 API"""

from __future__ import annotations

from datetime import datetime
from typing import Any

from fastapi import APIRouter, Depends, HTTPException, Query, Request, status
from fastapi.responses import JSONResponse
from sqlalchemy.ext.asyncio import AsyncSession

from app.api import deps
from app.api.permission_deps import require_permission
from app.core.cache import ConfigCache
from app.core.permissions import Permissions
from app.db.redis import get_redis_master, get_redis_slave
from app.schemas.admin_config import (
    ConfigListResponse,
    ConfigRollbackRequest,
    ConfigUpdateRequest,
    FeatureToggleUpdateRequest,
)
from app.services.config_service import ConfigService

router = APIRouter()

admin_config_read_guard = require_permission(Permissions.ADMIN_CONFIG_READ_ALL)
admin_config_write_guard = require_permission(Permissions.ADMIN_CONFIG_WRITE_ALL)
admin_config_rollback_guard = require_permission(Permissions.ADMIN_CONFIG_ROLLBACK_ALL)


class ConfigConflictHttpError(Exception):
    """配置冲突异常"""

    def __init__(self, detail: str) -> None:
        self.detail = detail


class ConfigMissingHttpError(Exception):
    """配置缺失异常"""

    def __init__(self, detail: str) -> None:
        self.detail = detail


async def get_config_service() -> ConfigService:
    master = await get_redis_master()
    reader = await get_redis_slave()
    cache = ConfigCache(master=master, reader=reader)
    return ConfigService(cache=cache)


def _success(data: Any) -> dict[str, Any]:
    return {"status": "success", "data": data}


def _error_response(status_code: int, message: str, code: str = "ERROR") -> JSONResponse:
    payload = {"status": "error", "error": {"code": code, "message": message}}
    return JSONResponse(status_code=status_code, content=payload)


@router.get("/", summary="获取配置与功能开关列表")
async def list_configs(
    request: Request,
    category: str | None = Query(None, description="配置分类过滤"),
    db: AsyncSession = Depends(deps.get_db),
    current_user=Depends(admin_config_read_guard),
    service: ConfigService = Depends(get_config_service),
):
    """返回配置项与功能开关状态"""
    result: ConfigListResponse = await service.list_configs(db, category=category)
    return _success(result.model_dump(mode="json"))


@router.put("/", summary="批量更新配置")
async def batch_update_configs(
    update_request: ConfigUpdateRequest,
    request: Request,
    db: AsyncSession = Depends(deps.get_db),
    current_user=Depends(admin_config_write_guard),
    service: ConfigService = Depends(get_config_service),
):
    try:
        result = await service.batch_update_configs(
            db,
            current_user=current_user,
            update_request=update_request,
            request=request,
        )
        return _success(result.model_dump(mode="json"))
    except ConfigService.ConfigConflictError as exc:
        return _error_response(status.HTTP_409_CONFLICT, str(exc), code="CONFLICT")
    except ConfigService.ConfigNotFoundError as exc:
        return _error_response(status.HTTP_404_NOT_FOUND, str(exc), code="NOT_FOUND")
    except ConfigConflictHttpError as exc:
        return _error_response(status.HTTP_409_CONFLICT, exc.detail, code="CONFLICT")


@router.get("/history", summary="查询配置变更历史")
async def list_config_history(
    request: Request,
    config_key: str | None = Query(None, description="配置键"),
    from_date: datetime | None = Query(None, description="开始时间"),
    to_date: datetime | None = Query(None, description="结束时间"),
    page: int = Query(1, ge=1),
    page_size: int = Query(20, ge=1, le=100),
    db: AsyncSession = Depends(deps.get_db),
    current_user=Depends(admin_config_read_guard),
    service: ConfigService = Depends(get_config_service),
):
    result = await service.list_history(
        db,
        page=page,
        page_size=page_size,
        config_key=config_key,
        from_date=from_date,
        to_date=to_date,
    )
    return _success(result.model_dump(mode="json"))


@router.post("/rollback/{change_id}", summary="回滚指定变更")
async def rollback_change(
    change_id: str,
    rollback_request: ConfigRollbackRequest,
    request: Request,
    db: AsyncSession = Depends(deps.get_db),
    current_user=Depends(admin_config_rollback_guard),
    service: ConfigService = Depends(get_config_service),
):
    if not rollback_request.confirm:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="回滚操作需要确认"
        )
    try:
        result = await service.rollback_change(
            db,
            change_id=change_id,
            current_user=current_user,
            reason=rollback_request.reason,
            request=request,
        )
        return _success(result.model_dump(mode="json"))
    except ConfigService.ConfigNotFoundError as exc:
        return _error_response(status.HTTP_404_NOT_FOUND, str(exc), code="NOT_FOUND")
    except ConfigMissingHttpError as exc:
        return _error_response(status.HTTP_404_NOT_FOUND, exc.detail, code="NOT_FOUND")


@router.get("/features", summary="获取功能开关列表")
async def list_feature_toggles(
    request: Request,
    db: AsyncSession = Depends(deps.get_db),
    current_user=Depends(admin_config_read_guard),
    service: ConfigService = Depends(get_config_service),
):
    toggles = await service.list_feature_toggles(db)
    data = {"features": [toggle.model_dump(mode="json") for toggle in toggles]}
    return _success(data)


@router.put("/features/{name}", summary="更新功能开关")
async def update_feature_toggle(
    name: str,
    update_request: FeatureToggleUpdateRequest,
    request: Request,
    db: AsyncSession = Depends(deps.get_db),
    current_user=Depends(admin_config_write_guard),
    service: ConfigService = Depends(get_config_service),
):
    try:
        toggle = await service.update_feature_toggle(
            db,
            name=name,
            current_user=current_user,
            update_request=update_request,
            request=request,
        )
        return _success(toggle.model_dump(mode="json"))
    except ConfigService.ConfigNotFoundError as exc:
        return _error_response(status.HTTP_404_NOT_FOUND, str(exc), code="NOT_FOUND")
    except ConfigMissingHttpError as exc:
        return _error_response(status.HTTP_404_NOT_FOUND, exc.detail, code="NOT_FOUND")
