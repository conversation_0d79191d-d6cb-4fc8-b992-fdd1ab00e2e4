"""审计日志API接口"""

from datetime import datetime, timedelta

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy import and_, desc, func, select
from sqlalchemy.ext.asyncio import AsyncSession

from app import models
from app.api import deps
from app.core.permission_system import (
    Action,
    PermissionChecker,
    ResourceType,
    Scope,
)
from app.core.permission_system import Permission as PermissionObject
from app.models.audit_log import AuditAction, AuditLog, AuditResourceType
from app.schemas.audit_log import (
    AuditLogListResponse,
    AuditLogOut,
    AuditLogStats,
    ResourceActivitySummary,
    UserActivitySummary,
)
from app.services.audit_log_service import AuditLogService

router = APIRouter()


@router.get("/", response_model=AuditLogListResponse)
async def get_audit_logs(
    *,
    db: AsyncSession = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_user),
    user_id: int | None = Query(None, description="用户ID过滤"),
    action: AuditAction | None = Query(None, description="操作类型过滤"),
    resource_type: AuditResourceType | None = Query(None, description="资源类型过滤"),
    resource_id: int | None = Query(None, description="资源ID过滤"),
    start_date: datetime | None = Query(None, description="开始时间"),
    end_date: datetime | None = Query(None, description="结束时间"),
    success: bool | None = Query(None, description="成功状态过滤"),
    keyword: str | None = Query(None, description="关键词搜索"),
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
) -> AuditLogListResponse:
    """获取审计日志列表"""

    # 权限检查
    permission_checker = PermissionChecker()

    # 检查是否有查看所有日志的权限
    has_all_permission = await permission_checker.check_permission(
        db, current_user, PermissionObject(ResourceType.SYSTEM, Action.READ, Scope.ALL)
    )

    # 如果没有查看所有日志的权限，只能查看自己的日志
    if not has_all_permission:
        user_id = current_user.id

    # 查询日志
    logs = await AuditLogService.get_logs(
        db=db,
        user_id=user_id,
        action=action,
        resource_type=resource_type,
        resource_id=resource_id,
        start_date=start_date,
        end_date=end_date,
        success=success,
        keyword=keyword,
        skip=skip,
        limit=limit,
    )

    # 获取总数
    total = await AuditLogService.get_log_count(
        db=db,
        user_id=user_id,
        action=action,
        resource_type=resource_type,
        start_date=start_date,
        end_date=end_date,
        success=success,
    )

    # 转换为输出模型
    log_items = [AuditLogOut.from_orm(log) for log in logs]

    return AuditLogListResponse(
        items=log_items, total=total, skip=skip, limit=limit, has_more=skip + limit < total
    )


@router.get("/{log_id}", response_model=AuditLogOut)
async def get_audit_log_detail(
    *,
    db: AsyncSession = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_user),
    log_id: int,
) -> AuditLogOut:
    """获取审计日志详情"""

    # 权限检查
    permission_checker = PermissionChecker()
    has_all_permission = await permission_checker.check_permission(
        db, current_user, PermissionObject(ResourceType.SYSTEM, Action.READ, Scope.ALL)
    )

    # 查询日志
    query = select(AuditLog).where(AuditLog.id == log_id)

    # 如果没有查看所有日志的权限，只能查看自己的日志
    if not has_all_permission:
        query = query.where(AuditLog.user_id == current_user.id)

    result = await db.execute(query)
    log = result.scalar_one_or_none()

    if not log:
        raise HTTPException(status_code=404, detail="审计日志不存在或无权限访问")

    return AuditLogOut.from_orm(log)


@router.get("/stats/overview", response_model=AuditLogStats)
async def get_audit_stats(
    *,
    db: AsyncSession = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_user),
    days: int = Query(30, ge=1, le=365, description="统计天数"),
) -> AuditLogStats:
    """获取审计日志统计信息"""

    # 权限检查
    permission_checker = PermissionChecker()
    has_permission = await permission_checker.check_permission(
        db, current_user, PermissionObject(ResourceType.SYSTEM, Action.READ, Scope.ALL)
    )
    if not has_permission:
        raise HTTPException(status_code=403, detail="权限不足")

    start_date = datetime.utcnow() - timedelta(days=days)

    # 基础统计
    total_query = select(func.count(AuditLog.id)).where(AuditLog.timestamp >= start_date)
    success_query = select(func.count(AuditLog.id)).where(
        and_(AuditLog.timestamp >= start_date, AuditLog.success == True)
    )
    failure_query = select(func.count(AuditLog.id)).where(
        and_(AuditLog.timestamp >= start_date, AuditLog.success == False)
    )

    total_result = await db.execute(total_query)
    success_result = await db.execute(success_query)
    failure_result = await db.execute(failure_query)

    total_logs = total_result.scalar() or 0
    success_count = success_result.scalar() or 0
    failure_count = failure_result.scalar() or 0
    success_rate = (success_count / total_logs * 100) if total_logs > 0 else 0

    # Top用户统计
    top_users_query = (
        select(AuditLog.user_name, func.count(AuditLog.id).label("count"))
        .where(AuditLog.timestamp >= start_date)
        .group_by(AuditLog.user_name)
        .order_by(desc("count"))
        .limit(10)
    )

    top_users_result = await db.execute(top_users_query)
    top_users = [{"name": row[0], "count": row[1]} for row in top_users_result.fetchall()]

    # Top操作统计
    top_actions_query = (
        select(AuditLog.action, func.count(AuditLog.id).label("count"))
        .where(AuditLog.timestamp >= start_date)
        .group_by(AuditLog.action)
        .order_by(desc("count"))
        .limit(10)
    )

    top_actions_result = await db.execute(top_actions_query)
    top_actions = [{"action": row[0], "count": row[1]} for row in top_actions_result.fetchall()]

    # Top资源统计
    top_resources_query = (
        select(AuditLog.resource_type, func.count(AuditLog.id).label("count"))
        .where(AuditLog.timestamp >= start_date)
        .group_by(AuditLog.resource_type)
        .order_by(desc("count"))
        .limit(10)
    )

    top_resources_result = await db.execute(top_resources_query)
    top_resources = [
        {"resource_type": row[0], "count": row[1]} for row in top_resources_result.fetchall()
    ]

    # 每日统计（简化版）
    daily_stats = []  # 这里可以根据需要实现更详细的每日统计

    return AuditLogStats(
        total_logs=total_logs,
        success_count=success_count,
        failure_count=failure_count,
        success_rate=round(success_rate, 2),
        top_users=top_users,
        top_actions=top_actions,
        top_resources=top_resources,
        daily_stats=daily_stats,
    )


@router.get("/users/activity", response_model=list[UserActivitySummary])
async def get_user_activity_summary(
    *,
    db: AsyncSession = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_user),
    days: int = Query(30, ge=1, le=365, description="统计天数"),
    limit: int = Query(50, ge=1, le=200),
) -> list[UserActivitySummary]:
    """获取用户活动摘要"""

    # 权限检查
    permission_checker = PermissionChecker()
    has_permission = await permission_checker.check_permission(
        db, current_user, PermissionObject(ResourceType.SYSTEM, Action.READ, Scope.ALL)
    )
    if not has_permission:
        raise HTTPException(status_code=403, detail="权限不足")

    start_date = datetime.utcnow() - timedelta(days=days)

    # 用户活动统计查询
    query = (
        select(
            AuditLog.user_id,
            AuditLog.user_name,
            func.count(AuditLog.id).label("total_actions"),
            func.sum(func.case((AuditLog.success == True, 1), else_=0)).label("success_actions"),
            func.sum(func.case((AuditLog.success == False, 1), else_=0)).label("failure_actions"),
            func.max(AuditLog.timestamp).label("last_activity"),
        )
        .where(AuditLog.timestamp >= start_date)
        .group_by(AuditLog.user_id, AuditLog.user_name)
        .order_by(desc("total_actions"))
        .limit(limit)
    )

    result = await db.execute(query)
    rows = result.fetchall()

    summaries = []
    for row in rows:
        summary = UserActivitySummary(
            user_id=row.user_id,
            user_name=row.user_name,
            total_actions=row.total_actions,
            success_actions=row.success_actions or 0,
            failure_actions=row.failure_actions or 0,
            last_activity=row.last_activity,
        )
        summaries.append(summary)

    return summaries


@router.get("/resources/activity", response_model=list[ResourceActivitySummary])
async def get_resource_activity_summary(
    *,
    db: AsyncSession = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_user),
    resource_type: AuditResourceType | None = Query(None, description="资源类型过滤"),
    days: int = Query(30, ge=1, le=365, description="统计天数"),
    limit: int = Query(50, ge=1, le=200),
) -> list[ResourceActivitySummary]:
    """获取资源活动摘要"""

    # 权限检查
    permission_checker = PermissionChecker()
    has_permission = await permission_checker.check_permission(
        db, current_user, PermissionObject(ResourceType.SYSTEM, Action.READ, Scope.ALL)
    )
    if not has_permission:
        raise HTTPException(status_code=403, detail="权限不足")

    start_date = datetime.utcnow() - timedelta(days=days)

    # 构建查询
    query = select(
        AuditLog.resource_type,
        AuditLog.resource_id,
        AuditLog.resource_name,
        func.count(AuditLog.id).label("total_actions"),
        func.sum(func.case((AuditLog.action == AuditAction.CREATE, 1), else_=0)).label(
            "create_count"
        ),
        func.sum(func.case((AuditLog.action == AuditAction.UPDATE, 1), else_=0)).label(
            "update_count"
        ),
        func.sum(func.case((AuditLog.action == AuditAction.DELETE, 1), else_=0)).label(
            "delete_count"
        ),
        func.max(AuditLog.timestamp).label("last_activity"),
    ).where(and_(AuditLog.timestamp >= start_date, AuditLog.resource_id.isnot(None)))

    if resource_type:
        query = query.where(AuditLog.resource_type == resource_type)

    query = (
        query.group_by(AuditLog.resource_type, AuditLog.resource_id, AuditLog.resource_name)
        .order_by(desc("total_actions"))
        .limit(limit)
    )

    result = await db.execute(query)
    rows = result.fetchall()

    summaries = []
    for row in rows:
        summary = ResourceActivitySummary(
            resource_type=row.resource_type,
            resource_id=row.resource_id,
            resource_name=row.resource_name,
            total_actions=row.total_actions,
            create_count=row.create_count or 0,
            update_count=row.update_count or 0,
            delete_count=row.delete_count or 0,
            last_activity=row.last_activity,
        )
        summaries.append(summary)

    return summaries


@router.post("/export")
async def export_audit_logs(
    *,
    db: AsyncSession = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_user),
    user_id: int | None = None,
    action: AuditAction | None = None,
    resource_type: AuditResourceType | None = None,
    start_date: datetime | None = None,
    end_date: datetime | None = None,
    format: str = "csv",
) -> dict:
    """导出审计日志"""

    # 权限检查
    permission_checker = PermissionChecker()
    has_permission = await permission_checker.check_permission(
        db, current_user, PermissionObject(ResourceType.SYSTEM, Action.READ, Scope.ALL)
    )
    if not has_permission:
        raise HTTPException(status_code=403, detail="权限不足")

    # 这里可以实现具体的导出逻辑
    # 由于导出可能是耗时操作，建议使用异步任务队列

    return {
        "message": "导出任务已创建",
        "task_id": "export_task_123",  # 实际应该返回真实的任务ID
        "status": "pending",
    }
