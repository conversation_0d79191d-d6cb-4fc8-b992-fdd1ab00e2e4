from typing import Any

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.ext.asyncio import AsyncSession

from app import crud, models, schemas
from app.api import deps
from app.api.permission_deps import require_permission
from app.core.logging import logger
from app.core.permission_system import Action, ResourceType, Scope
from app.core.permission_system import Permission as PermissionObject
from app.services.content_stats_service import ContentStatsService
from app.services.service_factory import get_content_stats_service

router = APIRouter()


@router.post("/toggle", response_model=schemas.FavoriteStatus)
async def toggle_favorite(
    *,
    db: AsyncSession = Depends(deps.get_db),
    favorite_data: schemas.FavoriteToggle,
    current_user: models.User = Depends(deps.get_current_active_user),
    content_stats_service: ContentStatsService = Depends(get_content_stats_service),
) -> Any:
    """切换收藏状态"""
    # 验证内容是否存在
    if favorite_data.content_type == "article":
        content = await crud.article.get(db, id=favorite_data.content_id)
        if not content:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="文章不存在",
            )
    elif favorite_data.content_type == "video":
        content = await crud.video.get(db, id=favorite_data.content_id)
        if not content:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="视频不存在",
            )
    elif favorite_data.content_type == "scratch":
        content = await crud.scratch_product.get(db, id=favorite_data.content_id)
        if not content:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Scratch项目不存在",
            )
    else:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="不支持的内容类型",
        )

    # 切换收藏状态
    if favorite_data.content_type == "scratch":
        # 对于Scratch项目，使用专门的服务
        from app.services.scratch_stats_service import ScratchStatsService

        scratch_service = ScratchStatsService()
        _, is_favorited = await scratch_service.toggle_favorite(
            db=db,
            user_id=current_user.id,
            content_id=favorite_data.content_id,
            note=favorite_data.note,
        )
    else:
        # 对于其他内容类型，使用通用的CRUD方法
        _, is_favorited = await crud.favorite.toggle_favorite(
            db,
            user_id=current_user.id,
            content_type=favorite_data.content_type,
            content_id=favorite_data.content_id,
            note=favorite_data.note,
            commit=False,  # 明确指出不由CRUD方法提交
        )

    # 在API层统一提交事务
    await db.commit()

    if favorite_data.content_type in {"article", "video"}:
        try:
            await content_stats_service.update_favorite_status(
                favorite_data.content_type,
                favorite_data.content_id,
                current_user.id,
                is_favorited,
            )
        except Exception as exc:  # pragma: no cover - 日志记录
            logger.warning(
                "收藏缓存更新失败 content_type=%s content_id=%s user_id=%s err=%s",
                favorite_data.content_type,
                favorite_data.content_id,
                current_user.id,
                exc,
            )

    # 统计数据将由后台任务异步更新，这里直接返回当前的收藏状态
    # 为了获取最新的收藏总数，我们可以直接查询数据库
    if favorite_data.content_type == "scratch":
        # 对于Scratch项目，从scratch_products表获取收藏数量
        scratch_project = await crud.scratch_product.get(db, id=favorite_data.content_id)
        favorite_count = scratch_project.favorite_count if scratch_project else 0
    else:
        favorite_count = await crud.favorite.get_content_favorite_count(
            db,
            content_type=favorite_data.content_type,
            content_id=favorite_data.content_id,
        )

    return schemas.FavoriteStatus(
        content_type=favorite_data.content_type,
        content_id=favorite_data.content_id,
        is_favorited=is_favorited,
        favorite_count=favorite_count,
    )


@router.put("/{favorite_id}/note", response_model=schemas.Favorite)
async def update_favorite_note(
    *,
    db: AsyncSession = Depends(deps.get_db),
    favorite_id: int,
    note_data: schemas.FavoriteUpdate,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """更新收藏备注"""
    favorite = await crud.favorite.get(db, id=favorite_id)
    if not favorite:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="收藏记录不存在",
        )

    if favorite.user_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限修改此收藏记录",
        )

    updated_favorite = await crud.favorite.update(db=db, db_obj=favorite, obj_in=note_data)
    return updated_favorite


@router.get("/stats", response_model=schemas.FavoriteStats)
async def get_favorite_stats(
    *,
    db: AsyncSession = Depends(deps.get_db),
    content_type: str | None = Query(None, description="内容类型筛选"),
    current_user: models.User = Depends(
        require_permission(
            PermissionObject(resource=ResourceType.SYSTEM, action=Action.READ, scope=Scope.ALL)
        )
    ),
) -> Any:
    """获取收藏统计信息（仅管理员）"""
    stats = await crud.favorite.get_favorite_stats(db, content_type=content_type)
    return schemas.FavoriteStats(**stats)


@router.post("/batch-status", response_model=list[schemas.ContentFavoriteInfo])
async def get_batch_favorite_status(
    *,
    db: AsyncSession = Depends(deps.get_db),
    content_items: list[schemas.FavoriteBase],
    current_user: models.User = Depends(deps.get_current_active_user),
    content_stats_service: ContentStatsService = Depends(get_content_stats_service),
) -> Any:
    """批量获取内容收藏信息"""
    if not content_items:
        return []

    items = [(item.content_type, item.content_id) for item in content_items]

    # 从新服务批量获取统计信息
    stats_dict = await content_stats_service.batch_get_stats(
        db, content_items=items, user_id=current_user.id
    )

    # 构建响应
    result = []
    for content_type, content_id in items:
        stats = stats_dict.get((content_type, content_id), {})
        result.append(
            schemas.ContentFavoriteInfo(
                content_type=content_type,
                content_id=content_id,
                favorite_count=stats.get("favorite_count", 0),
                is_favorited_by_user=stats.get("is_favorited_by_user", False),
            )
        )

    return result
