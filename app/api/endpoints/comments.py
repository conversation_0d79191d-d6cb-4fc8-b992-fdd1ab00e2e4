from typing import Any

from fastapi import API<PERSON>outer, Depends, HTTPException, Request, status
from sqlalchemy.ext.asyncio import AsyncSession

from app import crud, models
from app.api import deps
from app.api.permission_deps import require_permission
from app.core.pagination import <PERSON>urs<PERSON><PERSON>agination<PERSON>arams
from app.core.permission_system import (
    Action,
    PermissionChecker,
    ResourceType,
    Scope,
)
from app.core.permission_system import (
    Permission as PermissionObject,
)
from app.models.comment import CommentType
from app.schemas.comment import (
    Comment,
    CommentCreate,
    CommentCursorList,
    CommentUpdate,
    CommentWithReplies,
    FlatComment,
    FlatCommentList,
    Reply,
)
from app.schemas.like import LikeStatus
from app.core.logging import logger
from app.services.article_cache_service import ArticleCacheService
from app.services.content_stats_service import ContentStatsService
from app.services.service_factory import (
    get_article_cache_service,
    get_content_stats_service,
    get_video_cache_service,
    get_post_cache_service,
)
from app.services.video_cache_service import VideoCacheService
from app.services.post_cache_service import PostCacheService
from app.models.post import PostStatus

router = APIRouter()


@router.post("/", response_model=Comment, status_code=status.HTTP_201_CREATED)
async def create_comment(
    *,
    db: AsyncSession = Depends(deps.get_db),
    comment_in: CommentCreate,
    current_user: models.User = Depends(
        require_permission(
            PermissionObject(resource=ResourceType.COMMENT, action=Action.CREATE, scope=Scope.OWN)
        )
    ),
    article_cache_service: ArticleCacheService = Depends(get_article_cache_service),
    video_cache_service: VideoCacheService = Depends(get_video_cache_service),
    post_cache_service: PostCacheService = Depends(get_post_cache_service),
    content_stats_service: ContentStatsService = Depends(get_content_stats_service),
) -> Any:
    """创建评论或回复"""
    # 验证回复的父评论
    if comment_in.parent_id:
        parent_comment = await crud.comment.get(db, id=comment_in.parent_id)
        if not parent_comment:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="回复的父评论不存在",
            )
        # 确保回复与父评论属于同一内容实体
        if parent_comment.comment_type != comment_in.comment_type:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="回复必须与父评论属于同一内容类型",
            )
        if (
            parent_comment.article_id != comment_in.article_id
            or parent_comment.video_id != comment_in.video_id
            or parent_comment.scratch_id != comment_in.scratch_id
            or parent_comment.post_id != comment_in.post_id
        ):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="回复必须与父评论属于同一内容实体",
            )
        # 设置reply_to_id为被回复的评论ID，parent_id保持不变用于标识这是一个回复
        comment_in.reply_to_id = comment_in.parent_id

    # 验证评论类型和关联ID
    content_empty = not comment_in.content or not comment_in.content.strip()
    if content_empty and not comment_in.image_urls:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="评论内容和图片不能同时为空",
        )

    if comment_in.comment_type == CommentType.ARTICLE:
        if not comment_in.article_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="文章评论必须提供article_id",
            )
        # 检查文章是否存在且已发布
        article = await article_cache_service.get_entity(db, entity_id=comment_in.article_id)
        if not article or not article.is_published:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="文章不存在或未发布",
            )
    elif comment_in.comment_type == CommentType.VIDEO:
        if not comment_in.video_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="视频评论必须提供video_id",
            )
        # 检查视频是否存在且已发布
        video = await video_cache_service.get_entity(db, entity_id=comment_in.video_id)
        if not video or not video.is_published:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="视频不存在或未发布",
            )
    elif comment_in.comment_type == CommentType.SCRATCH:
        if not comment_in.scratch_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Scratch评论必须提供scratch_id",
            )
        scratch_project = await crud.scratch_product.get(db=db, id=comment_in.scratch_id)
        if not scratch_project or not scratch_project.is_published:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Scratch项目不存在或未发布",
            )
    elif comment_in.comment_type == CommentType.POST:
        if not comment_in.post_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="沸点评论必须提供post_id",
            )
        post = await post_cache_service.get_by_id(db, entity_id=comment_in.post_id)
        if not post or post.status != PostStatus.PUBLISHED:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="沸点不存在或未发布",
            )

    # 创建评论
    comment = await crud.comment.create(db, obj_in=comment_in, author_id=current_user.id)

    # 更新相关内容的评论数
    content_type = comment.comment_type.value
    content_id = comment.article_id or comment.video_id or comment.scratch_id or comment.post_id
    if content_id:
        await content_stats_service.update_comment_count(
            content_type=content_type, content_id=content_id, increment=1
        )

    return comment


@router.get("/article/{article_id}", response_model=CommentCursorList | FlatCommentList)
async def get_article_comments(
    *,
    db: AsyncSession = Depends(deps.get_db),
    article_id: int,
    cursor: str | None = None,
    size: int = 20,
    sort_by: str = "like_count",
    flat: bool = True,  # 是否返回扁平化结构
    max_level: int = 10,  # 扁平化时的最大显示层级
    current_user: models.User | None = Depends(deps.get_current_user_optional),
    article_cache_service: ArticleCacheService = Depends(get_article_cache_service),
    content_stats_service: ContentStatsService = Depends(get_content_stats_service),
) -> Any:
    """获取文章评论列表

    Args:
        sort_by: 排序方式，支持 'like_count'(点赞数) 和 'created_at'(时间)
        flat: 是否返回扁平化结构，默认False返回嵌套结构
        max_level: 扁平化时的最大显示层级，0=仅顶层，1=顶层+一级回复，2=顶层+二级回复
    """
    # 检查文章是否存在且已发布
    article = await article_cache_service.get_entity(db, entity_id=article_id)
    if not article:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="文章不存",
        )
    if not article.is_approved or not article.is_published:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="该文章未发布",
        )

    # 验证排序参数
    if sort_by not in ["like_count", "created_at"]:
        sort_by = "like_count"  # 默认使用点赞数排序

    # 创建分页参数
    pagination_params = CursorPaginationParams(
        cursor=cursor,
        size=min(size, 100),  # 限制最大页面大小
        order_by=sort_by,
        order_direction="desc",
    )

    # 获取评论列表
    pagination_result = await crud.comment.get_by_article_cursor(
        db, article_id=article_id, params=pagination_params
    )

    # 获取所有评论的点赞信息
    comments_orm = pagination_result.items
    comment_ids = [comment_orm.id for comment_orm in comments_orm]
    like_info = {}
    if comment_ids:
        content_items = [("comment", comment_id) for comment_id in comment_ids]
        user_id = current_user.id if current_user else None
        stats_dict = await content_stats_service.batch_get_stats(
            db, content_items=content_items, user_id=user_id
        )
        # 转换为更方便的格式
        for (_content_type, content_id), stats in stats_dict.items():
            like_info[content_id] = {
                "like_count": stats.get("like_count", 0),
                "is_liked": stats.get("is_liked_by_user", False),
            }

    # 根据flat参数决定返回格式
    if flat:
        # 返回扁平化结构
        flat_comments = []
        comment_map = {}  # 用于快速查找评论
        reply_counts = {}  # 统计每个评论的回复数量

        # 第一步：创建评论映射和统计回复数量
        for comment_orm in comments_orm:
            comment_map[comment_orm.id] = comment_orm
            if comment_orm.parent_id:
                reply_counts[comment_orm.parent_id] = reply_counts.get(comment_orm.parent_id, 0) + 1

        # 第二步：计算层级和路径
        def get_comment_level_and_path(comment_orm):
            if not comment_orm.parent_id:
                return 0, str(comment_orm.id)

            parent = comment_map.get(comment_orm.parent_id)
            if not parent:
                return 1, f"unknown.{comment_orm.id}"

            parent_level, parent_path = get_comment_level_and_path(parent)
            return parent_level + 1, f"{parent_path}.{comment_orm.id}"

        # 第三步：创建扁平化评论对象
        for comment_orm in comments_orm:
            level, path = get_comment_level_and_path(comment_orm)

            # 跳过超过最大层级的评论
            if level > max_level:
                continue

            comment_like_info = like_info.get(comment_orm.id, {"like_count": 0, "is_liked": False})

            # 获取回复目标用户信息
            reply_to_user_id = None
            reply_to_user = None
            if comment_orm.reply_to_id and comment_orm.reply_to_id in comment_map:
                reply_to_comment = comment_map[comment_orm.reply_to_id]
                reply_to_user_id = reply_to_comment.author_id
                reply_to_user = reply_to_comment.author

            # 计算总回复数量（递归统计所有子回复）
            def count_total_replies(comment_id):
                total = reply_counts.get(comment_id, 0)
                for child_comment in comments_orm:
                    if child_comment.parent_id == comment_id:
                        total += count_total_replies(child_comment.id)
                return total

            flat_comment = FlatComment(
                id=comment_orm.id,
                content=comment_orm.content,
                comment_type=comment_orm.comment_type,
                article_id=comment_orm.article_id,
                video_id=comment_orm.video_id,
                scratch_id=comment_orm.scratch_id,
                image_urls=list(comment_orm.image_urls or []),
                level=level,
                parent_id=comment_orm.parent_id,
                reply_to_id=comment_orm.reply_to_id,
                reply_to_user_id=reply_to_user_id,
                reply_to_user=reply_to_user,
                path=path,
                author_id=comment_orm.author_id,
                author=comment_orm.author,
                like_count=comment_like_info["like_count"],
                is_liked=comment_like_info["is_liked"],
                reply_count=reply_counts.get(comment_orm.id, 0),
                total_reply_count=count_total_replies(comment_orm.id),
                is_visible=comment_orm.is_visible,
                created_at=comment_orm.created_at,
                updated_at=comment_orm.updated_at,
            )
            flat_comments.append(flat_comment)

        # 第四步：排序
        if sort_by == "like_count":
            # 按层级分组排序：同层级内按点赞数排序
            flat_comments.sort(
                key=lambda x: (x.path.count("."), -x.like_count, -x.created_at.timestamp())
            )
        else:  # sort_by == "created_at"
            # 按层级分组排序：同层级内按时间排序
            flat_comments.sort(key=lambda x: (x.path.count("."), -x.created_at.timestamp()))

        return FlatCommentList(
            items=flat_comments,
            has_next=pagination_result.has_next,
            has_previous=pagination_result.has_previous,
            next_cursor=pagination_result.next_cursor,
            previous_cursor=pagination_result.previous_cursor,
            total_count=pagination_result.total_count,
        )
    else:
        # 返回嵌套结构（原有逻辑）
        all_comments_map = {}  # 存储所有评论的映射
        top_level_comments = []  # 存储顶层评论

        # 第一步：创建所有评论的基础对象
        for comment_orm in comments_orm:
            comment_like_info = like_info.get(comment_orm.id, {"like_count": 0, "is_liked": False})

            if not comment_orm.parent_id:
                # 顶层评论
                comment_dict = {
                    "id": comment_orm.id,
                    "content": comment_orm.content,
                    "comment_type": comment_orm.comment_type,
                    "article_id": comment_orm.article_id,
                    "video_id": comment_orm.video_id,
                    "scratch_id": comment_orm.scratch_id,
                    "image_urls": list(comment_orm.image_urls or []),
                    "parent_id": comment_orm.parent_id,
                    "is_visible": comment_orm.is_visible,
                    "author_id": comment_orm.author_id,
                    "author": comment_orm.author,
                    "created_at": comment_orm.created_at,
                    "updated_at": comment_orm.updated_at,
                    "like_count": comment_like_info["like_count"],
                    "is_liked": comment_like_info["is_liked"],
                }
                pydantic_comment = CommentWithReplies.model_validate(comment_dict)
                pydantic_comment.replies = []
                pydantic_comment.reply_count = 0
                all_comments_map[comment_orm.id] = pydantic_comment
                top_level_comments.append(pydantic_comment)
            else:
                # 回复评论
                # 获取回复目标用户信息
                reply_to_user_id = None
                reply_to_user = None
                if comment_orm.reply_to_id:
                    # 从已处理的评论中查找回复目标
                    for existing_comment_orm in comments_orm:
                        if existing_comment_orm.id == comment_orm.reply_to_id:
                            reply_to_user_id = existing_comment_orm.author_id
                            reply_to_user = existing_comment_orm.author
                            break

                reply_dict = {
                    "id": comment_orm.id,
                    "content": comment_orm.content,
                    "comment_type": comment_orm.comment_type,
                    "article_id": comment_orm.article_id,
                    "video_id": comment_orm.video_id,
                    "scratch_id": comment_orm.scratch_id,
                    "image_urls": list(comment_orm.image_urls or []),
                    "parent_id": comment_orm.parent_id,
                    "reply_to_id": comment_orm.reply_to_id,
                    "is_visible": comment_orm.is_visible,
                    "author_id": comment_orm.author_id,
                    "author": comment_orm.author,
                    "created_at": comment_orm.created_at,
                    "updated_at": comment_orm.updated_at,
                    "like_count": comment_like_info["like_count"],
                    "is_liked": comment_like_info["is_liked"],
                    "reply_to_user_id": reply_to_user_id,
                    "reply_to_user": reply_to_user,
                }
                pydantic_reply = Reply.model_validate(reply_dict)
                all_comments_map[comment_orm.id] = pydantic_reply

        # 第二步：构建树形结构
        def build_comment_tree():
            # 将回复添加到对应的父评论下
            for comment_orm in comments_orm:
                if comment_orm.parent_id:
                    parent_comment = all_comments_map.get(comment_orm.parent_id)
                    current_reply = all_comments_map.get(comment_orm.id)
                    if parent_comment and current_reply:
                        if hasattr(parent_comment, "replies"):
                            parent_comment.replies.append(current_reply)
                        else:
                            # 如果父评论是Reply对象，需要给它添加replies属性
                            parent_comment.replies = [current_reply]

            # 递归计算回复数量并排序
            def process_comment(comment):
                if hasattr(comment, "replies") and comment.replies:
                    # 按时间排序回复
                    comment.replies.sort(key=lambda x: x.created_at)
                    # 递归处理子回复
                    for reply in comment.replies:
                        process_comment(reply)
                    # 计算总回复数量（包括子回复的回复）
                    if hasattr(comment, "reply_count"):
                        comment.reply_count = len(comment.replies)

            for comment in top_level_comments:
                process_comment(comment)

        build_comment_tree()

        # 根据排序参数对顶层评论进行排序
        if sort_by == "like_count":
            # 按点赞数降序排序，点赞数相同时按创建时间降序
            top_level_comments.sort(key=lambda x: (x.like_count, x.created_at), reverse=True)
        else:  # sort_by == "created_at"
            # 按创建时间降序排序
            top_level_comments.sort(key=lambda x: x.created_at, reverse=True)

        return CommentCursorList(
            items=top_level_comments,
            has_next=pagination_result.has_next,
            has_previous=pagination_result.has_previous,
            next_cursor=pagination_result.next_cursor,
            previous_cursor=pagination_result.previous_cursor,
            total_count=pagination_result.total_count,
        )


@router.get("/video/{video_id}", response_model=CommentCursorList | FlatCommentList)
async def get_video_comments(
    *,
    db: AsyncSession = Depends(deps.get_db),
    video_id: int,
    cursor: str | None = None,
    size: int = 20,
    sort_by: str = "like_count",
    flat: bool = False,  # 是否返回扁平化结构
    max_level: int = 10,  # 扁平化时的最大显示层级
    current_user: models.User | None = Depends(deps.get_current_user_optional),
    video_cache_service: VideoCacheService = Depends(get_video_cache_service),
    content_stats_service: ContentStatsService = Depends(get_content_stats_service),
) -> Any:
    """获取视频评论列表

    Args:
        sort_by: 排序方式，支持 'like_count'(点赞数) 和 'created_at'(时间)
        flat: 是否返回扁平化结构，默认False返回嵌套结构
        max_level: 扁平化时的最大显示层级，0=仅顶层，1=顶层+一级回复，2=顶层+二级回复
    """
    # 检查视频是否存在且已发布
    video = await video_cache_service.get_entity(db, entity_id=video_id)
    if not video or not video.is_published:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="视频不存在或未发布",
        )

    # 验证排序参数
    if sort_by not in ["like_count", "created_at"]:
        sort_by = "like_count"  # 默认使用点赞数排序

    # 创建分页参数
    pagination_params = CursorPaginationParams(
        cursor=cursor,
        size=min(size, 100),  # 限制最大页面大小
        order_by=sort_by,
        order_direction="desc",
    )

    # 获取评论列表
    pagination_result = await crud.comment.get_by_video_cursor(
        db, video_id=video_id, params=pagination_params
    )

    # 获取所有评论的点赞信息
    comments_orm = pagination_result.items
    comment_ids = [comment_orm.id for comment_orm in comments_orm]
    like_info = {}
    if comment_ids:
        content_items = [("comment", comment_id) for comment_id in comment_ids]
        user_id = current_user.id if current_user else None
        stats_dict = await content_stats_service.batch_get_stats(
            db, content_items=content_items, user_id=user_id
        )
        # 转换为更方便的格式
        for (_content_type, content_id), stats in stats_dict.items():
            like_info[content_id] = {
                "like_count": stats.get("like_count", 0),
                "is_liked": stats.get("is_liked_by_user", False),
            }

    # 根据flat参数决定返回格式
    if flat:
        # 返回扁平化结构
        flat_comments = []
        comment_map = {}  # 用于快速查找评论
        reply_counts = {}  # 统计每个评论的回复数量

        # 第一步：创建评论映射和统计回复数量
        for comment_orm in comments_orm:
            comment_map[comment_orm.id] = comment_orm
            if comment_orm.parent_id:
                reply_counts[comment_orm.parent_id] = reply_counts.get(comment_orm.parent_id, 0) + 1

        # 第二步：计算层级和路径
        def get_comment_level_and_path(comment_orm):
            if not comment_orm.parent_id:
                return 0, str(comment_orm.id)

            parent = comment_map.get(comment_orm.parent_id)
            if not parent:
                return 1, f"unknown.{comment_orm.id}"

            parent_level, parent_path = get_comment_level_and_path(parent)
            return parent_level + 1, f"{parent_path}.{comment_orm.id}"

        # 第三步：创建扁平化评论对象
        for comment_orm in comments_orm:
            level, path = get_comment_level_and_path(comment_orm)

            # 跳过超过最大层级的评论
            if level > max_level:
                continue

            comment_like_info = like_info.get(comment_orm.id, {"like_count": 0, "is_liked": False})

            # 获取回复目标用户信息
            reply_to_user_id = None
            reply_to_user = None
            if comment_orm.reply_to_id and comment_orm.reply_to_id in comment_map:
                reply_to_comment = comment_map[comment_orm.reply_to_id]
                reply_to_user_id = reply_to_comment.author_id
                reply_to_user = reply_to_comment.author

            # 计算总回复数量（递归统计所有子回复）
            def count_total_replies(comment_id):
                total = reply_counts.get(comment_id, 0)
                for child_comment in comments_orm:
                    if child_comment.parent_id == comment_id:
                        total += count_total_replies(child_comment.id)
                return total

            flat_comment = FlatComment(
                id=comment_orm.id,
                content=comment_orm.content,
                comment_type=comment_orm.comment_type,
                article_id=comment_orm.article_id,
                video_id=comment_orm.video_id,
                scratch_id=comment_orm.scratch_id,
                image_urls=list(comment_orm.image_urls or []),
                level=level,
                parent_id=comment_orm.parent_id,
                reply_to_id=comment_orm.reply_to_id,
                reply_to_user_id=reply_to_user_id,
                reply_to_user=reply_to_user,
                path=path,
                author_id=comment_orm.author_id,
                author=comment_orm.author,
                like_count=comment_like_info["like_count"],
                is_liked=comment_like_info["is_liked"],
                reply_count=reply_counts.get(comment_orm.id, 0),
                total_reply_count=count_total_replies(comment_orm.id),
                is_visible=comment_orm.is_visible,
                created_at=comment_orm.created_at,
                updated_at=comment_orm.updated_at,
            )
            flat_comments.append(flat_comment)

        # 第四步：排序
        if sort_by == "like_count":
            # 按层级分组排序：同层级内按点赞数排序
            flat_comments.sort(
                key=lambda x: (x.path.count("."), -x.like_count, -x.created_at.timestamp())
            )
        else:  # sort_by == "created_at"
            # 按层级分组排序：同层级内按时间排序
            flat_comments.sort(key=lambda x: (x.path.count("."), -x.created_at.timestamp()))

        return FlatCommentList(
            items=flat_comments,
            has_next=pagination_result.has_next,
            has_previous=pagination_result.has_previous,
            next_cursor=pagination_result.next_cursor,
            previous_cursor=pagination_result.previous_cursor,
            total_count=pagination_result.total_count,
        )
    else:
        # 返回嵌套结构（原有逻辑）
        all_comments_map = {}  # 存储所有评论的映射
        top_level_comments = []  # 存储顶层评论

        # 第一步：创建所有评论的基础对象
        for comment_orm in comments_orm:
            comment_like_info = like_info.get(comment_orm.id, {"like_count": 0, "is_liked": False})

            if not comment_orm.parent_id:
                # 顶层评论
                comment_dict = {
                    "id": comment_orm.id,
                    "content": comment_orm.content,
                    "comment_type": comment_orm.comment_type,
                    "article_id": comment_orm.article_id,
                    "video_id": comment_orm.video_id,
                    "scratch_id": comment_orm.scratch_id,
                    "image_urls": list(comment_orm.image_urls or []),
                    "parent_id": comment_orm.parent_id,
                    "is_visible": comment_orm.is_visible,
                    "author_id": comment_orm.author_id,
                    "author": comment_orm.author,
                    "created_at": comment_orm.created_at,
                    "updated_at": comment_orm.updated_at,
                    "like_count": comment_like_info["like_count"],
                    "is_liked": comment_like_info["is_liked"],
                }
                pydantic_comment = CommentWithReplies.model_validate(comment_dict)
                pydantic_comment.replies = []
                pydantic_comment.reply_count = 0
                all_comments_map[comment_orm.id] = pydantic_comment
                top_level_comments.append(pydantic_comment)
            else:
                # 回复评论
                # 获取回复目标用户信息
                reply_to_user_id = None
                reply_to_user = None
                if comment_orm.reply_to_id:
                    # 从已处理的评论中查找回复目标
                    for existing_comment_orm in comments_orm:
                        if existing_comment_orm.id == comment_orm.reply_to_id:
                            reply_to_user_id = existing_comment_orm.author_id
                            reply_to_user = existing_comment_orm.author
                            break

                reply_dict = {
                    "id": comment_orm.id,
                    "content": comment_orm.content,
                    "comment_type": comment_orm.comment_type,
                    "article_id": comment_orm.article_id,
                    "video_id": comment_orm.video_id,
                    "scratch_id": comment_orm.scratch_id,
                    "image_urls": list(comment_orm.image_urls or []),
                    "parent_id": comment_orm.parent_id,
                    "reply_to_id": comment_orm.reply_to_id,
                    "is_visible": comment_orm.is_visible,
                    "author_id": comment_orm.author_id,
                    "author": comment_orm.author,
                    "created_at": comment_orm.created_at,
                    "updated_at": comment_orm.updated_at,
                    "like_count": comment_like_info["like_count"],
                    "is_liked": comment_like_info["is_liked"],
                    "reply_to_user_id": reply_to_user_id,
                    "reply_to_user": reply_to_user,
                }
                pydantic_reply = Reply.model_validate(reply_dict)
                all_comments_map[comment_orm.id] = pydantic_reply

        # 第二步：构建树形结构
        def build_comment_tree():
            # 将回复添加到对应的父评论下
            for comment_orm in comments_orm:
                if comment_orm.parent_id:
                    parent_comment = all_comments_map.get(comment_orm.parent_id)
                    current_reply = all_comments_map.get(comment_orm.id)
                    if parent_comment and current_reply:
                        if hasattr(parent_comment, "replies"):
                            parent_comment.replies.append(current_reply)
                        else:
                            # 如果父评论是Reply对象，需要给它添加replies属性
                            parent_comment.replies = [current_reply]

            # 递归计算回复数量并排序
            def process_comment(comment):
                if hasattr(comment, "replies") and comment.replies:
                    # 按时间排序回复
                    comment.replies.sort(key=lambda x: x.created_at)
                    # 递归处理子回复
                    for reply in comment.replies:
                        process_comment(reply)
                    # 计算总回复数量（包括子回复的回复）
                    if hasattr(comment, "reply_count"):
                        comment.reply_count = len(comment.replies)

            for comment in top_level_comments:
                process_comment(comment)

        build_comment_tree()

        # 根据排序参数对顶层评论进行排序
        if sort_by == "like_count":
            # 按点赞数降序排序，点赞数相同时按创建时间降序
            top_level_comments.sort(key=lambda x: (x.like_count, x.created_at), reverse=True)
        else:  # sort_by == "created_at"
            # 按创建时间降序排序
            top_level_comments.sort(key=lambda x: x.created_at, reverse=True)

        return CommentCursorList(
            items=top_level_comments,
            has_next=pagination_result.has_next,
            has_previous=pagination_result.has_previous,
            next_cursor=pagination_result.next_cursor,
            previous_cursor=pagination_result.previous_cursor,
            total_count=pagination_result.total_count,
        )


@router.get("/scratch/{project_id}", response_model=CommentCursorList | FlatCommentList)
async def get_scratch_comments(
    *,
    db: AsyncSession = Depends(deps.get_db),
    project_id: int,
    cursor: str | None = None,
    size: int = 20,
    sort_by: str = "like_count",
    flat: bool = False,  # 是否返回扁平化结构
    max_level: int = 10,  # 扁平化时的最大显示层级
    current_user: models.User | None = Depends(deps.get_current_user_optional),
    content_stats_service: ContentStatsService = Depends(get_content_stats_service),
) -> Any:
    """获取 Scratch 项目评论列表"""
    scratch_project = await crud.scratch_product.get(db=db, id=project_id)
    if not scratch_project or not scratch_project.is_published:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Scratch项目不存在或未发布",
        )

    if sort_by not in ["like_count", "created_at"]:
        sort_by = "like_count"

    pagination_params = CursorPaginationParams(
        cursor=cursor,
        size=min(size, 100),
        order_by=sort_by,
        order_direction="desc",
    )

    pagination_result = await crud.comment.get_by_scratch_cursor(
        db, scratch_id=project_id, params=pagination_params
    )

    comments_orm = pagination_result.items
    comment_ids = [comment_orm.id for comment_orm in comments_orm]
    like_info = {}
    if comment_ids:
        content_items = [("comment", comment_id) for comment_id in comment_ids]
        user_id = current_user.id if current_user else None
        stats_dict = await content_stats_service.batch_get_stats(
            db, content_items=content_items, user_id=user_id
        )
        for (_content_type, content_id), stats in stats_dict.items():
            like_info[content_id] = {
                "like_count": stats.get("like_count", 0),
                "is_liked": stats.get("is_liked_by_user", False),
            }

    if flat:
        flat_comments = []
        comment_map = {}
        reply_counts = {}

        for comment_orm in comments_orm:
            comment_map[comment_orm.id] = comment_orm
            if comment_orm.parent_id:
                reply_counts[comment_orm.parent_id] = reply_counts.get(comment_orm.parent_id, 0) + 1

        def get_comment_level_and_path(comment_orm):
            if not comment_orm.parent_id:
                return 0, str(comment_orm.id)

            parent = comment_map.get(comment_orm.parent_id)
            if not parent:
                return 1, f"unknown.{comment_orm.id}"

            parent_level, parent_path = get_comment_level_and_path(parent)
            return parent_level + 1, f"{parent_path}.{comment_orm.id}"

        for comment_orm in comments_orm:
            level, path = get_comment_level_and_path(comment_orm)

            if level > max_level:
                continue

            comment_like_info = like_info.get(comment_orm.id, {"like_count": 0, "is_liked": False})

            reply_to_user_id = None
            reply_to_user = None
            if comment_orm.reply_to_id and comment_orm.reply_to_id in comment_map:
                reply_to_comment = comment_map[comment_orm.reply_to_id]
                reply_to_user_id = reply_to_comment.author_id
                reply_to_user = reply_to_comment.author

            def count_total_replies(comment_id):
                total = reply_counts.get(comment_id, 0)
                for child_comment in comments_orm:
                    if child_comment.parent_id == comment_id:
                        total += count_total_replies(child_comment.id)
                return total

            flat_comment = FlatComment(
                id=comment_orm.id,
                content=comment_orm.content,
                comment_type=comment_orm.comment_type,
                article_id=comment_orm.article_id,
                video_id=comment_orm.video_id,
                scratch_id=comment_orm.scratch_id,
            image_urls=list(comment_orm.image_urls or []),
                level=level,
                parent_id=comment_orm.parent_id,
                reply_to_id=comment_orm.reply_to_id,
                reply_to_user_id=reply_to_user_id,
                reply_to_user=reply_to_user,
                path=path,
                author_id=comment_orm.author_id,
                author=comment_orm.author,
                like_count=comment_like_info["like_count"],
                is_liked=comment_like_info["is_liked"],
                reply_count=reply_counts.get(comment_orm.id, 0),
                total_reply_count=count_total_replies(comment_orm.id),
                is_visible=comment_orm.is_visible,
                created_at=comment_orm.created_at,
                updated_at=comment_orm.updated_at,
            )
            flat_comments.append(flat_comment)

        if sort_by == "like_count":
            flat_comments.sort(
                key=lambda x: (x.path.count("."), -x.like_count, -x.created_at.timestamp())
            )
        else:
            flat_comments.sort(key=lambda x: (x.path.count("."), -x.created_at.timestamp()))

        return FlatCommentList(
            items=flat_comments,
            has_next=pagination_result.has_next,
            has_previous=pagination_result.has_previous,
            next_cursor=pagination_result.next_cursor,
            previous_cursor=pagination_result.previous_cursor,
            total_count=pagination_result.total_count,
        )

    all_comments_map = {}
    top_level_comments = []

    for comment_orm in comments_orm:
        comment_like_info = like_info.get(comment_orm.id, {"like_count": 0, "is_liked": False})

        if not comment_orm.parent_id:
            comment_dict = {
                "id": comment_orm.id,
                "content": comment_orm.content,
                "comment_type": comment_orm.comment_type,
                "article_id": comment_orm.article_id,
                "video_id": comment_orm.video_id,
                "scratch_id": comment_orm.scratch_id,
                "image_urls": list(comment_orm.image_urls or []),
                "parent_id": comment_orm.parent_id,
                "is_visible": comment_orm.is_visible,
                "author_id": comment_orm.author_id,
                "author": comment_orm.author,
                "created_at": comment_orm.created_at,
                "updated_at": comment_orm.updated_at,
                "like_count": comment_like_info["like_count"],
                "is_liked": comment_like_info["is_liked"],
            }
            pydantic_comment = CommentWithReplies.model_validate(comment_dict)
            pydantic_comment.replies = []
            pydantic_comment.reply_count = 0
            all_comments_map[comment_orm.id] = pydantic_comment
            top_level_comments.append(pydantic_comment)
        else:
            reply_to_user_id = None
            reply_to_user = None
            if comment_orm.reply_to_id:
                for existing_comment_orm in comments_orm:
                    if existing_comment_orm.id == comment_orm.reply_to_id:
                        reply_to_user_id = existing_comment_orm.author_id
                        reply_to_user = existing_comment_orm.author
                        break

            reply_dict = {
                "id": comment_orm.id,
                "content": comment_orm.content,
                "comment_type": comment_orm.comment_type,
                "article_id": comment_orm.article_id,
                "video_id": comment_orm.video_id,
                "scratch_id": comment_orm.scratch_id,
                "image_urls": list(comment_orm.image_urls or []),
                "parent_id": comment_orm.parent_id,
                "reply_to_id": comment_orm.reply_to_id,
                "is_visible": comment_orm.is_visible,
                "author_id": comment_orm.author_id,
                "author": comment_orm.author,
                "created_at": comment_orm.created_at,
                "updated_at": comment_orm.updated_at,
                "like_count": comment_like_info["like_count"],
                "is_liked": comment_like_info["is_liked"],
                "reply_to_user_id": reply_to_user_id,
                "reply_to_user": reply_to_user,
            }
            pydantic_reply = Reply.model_validate(reply_dict)
            all_comments_map[comment_orm.id] = pydantic_reply

    for comment_orm in comments_orm:
        if comment_orm.parent_id:
            parent_comment = all_comments_map.get(comment_orm.parent_id)
            current_reply = all_comments_map.get(comment_orm.id)
            if parent_comment and current_reply:
                if hasattr(parent_comment, "replies"):
                    parent_comment.replies.append(current_reply)
                else:
                    parent_comment.replies = [current_reply]

    def process_comment(comment):
        if hasattr(comment, "replies") and comment.replies:
            comment.replies.sort(key=lambda x: x.created_at)
            for reply in comment.replies:
                process_comment(reply)
            if hasattr(comment, "reply_count"):
                comment.reply_count = len(comment.replies)

    for comment in top_level_comments:
        process_comment(comment)

    if sort_by == "like_count":
        top_level_comments.sort(key=lambda x: (x.like_count, x.created_at), reverse=True)
    else:
        top_level_comments.sort(key=lambda x: x.created_at, reverse=True)

    return CommentCursorList(
        items=top_level_comments,
        has_next=pagination_result.has_next,
        has_previous=pagination_result.has_previous,
        next_cursor=pagination_result.next_cursor,
        previous_cursor=pagination_result.previous_cursor,
        total_count=pagination_result.total_count,
    )


@router.get("/user/me", response_model=list[Comment])
async def get_my_comments(
    *,
    db: AsyncSession = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_user),
    skip: int = 0,
    limit: int = 100,
) -> Any:
    """获取当前用户的评论列表"""
    comments = await crud.comment.get_by_user(db, author_id=current_user.id, skip=skip, limit=limit)
    return comments


@router.put("/{comment_id}", response_model=Comment)
async def update_comment(
    *,
    db: AsyncSession = Depends(deps.get_db),
    comment_id: int,
    comment_in: CommentUpdate,
    current_user: models.User = Depends(deps.get_current_user),
) -> Any:
    """更新评论"""
    # 获取评论
    comment = await crud.comment.get(db, id=comment_id)
    if not comment:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="评论不存在",
        )

    # 检查权限
    can_update_own = await PermissionChecker.check_permission(
        db,
        current_user,
        PermissionObject(resource=ResourceType.COMMENT, action=Action.UPDATE, scope=Scope.OWN),
    )
    can_manage = await PermissionChecker.check_permission(
        db,
        current_user,
        PermissionObject(resource=ResourceType.COMMENT, action=Action.MANAGE, scope=Scope.ALL),
    )
    if not ((comment.author_id == current_user.id and can_update_own) or can_manage):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限更新此评论",
        )

    update_data = comment_in.model_dump(exclude_unset=True)
    if "image_urls" in update_data and update_data["image_urls"] is None:
        update_data["image_urls"] = []

    new_content_value = update_data.get("content", comment.content)
    new_content_text = (new_content_value or "").strip()
    new_image_urls = update_data.get("image_urls", comment.image_urls or []) or []
    if not new_content_text and not new_image_urls:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="评论内容和图片不能同时为空",
        )

    # 更新评论
    if update_data:
        comment = await crud.comment.update(db, db_obj=comment, obj_in=update_data)
    return comment


@router.delete("/{comment_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_comment(
    *,
    db: AsyncSession = Depends(deps.get_db),
    comment_id: int,
    current_user: models.User = Depends(deps.get_current_user),
    content_stats_service: ContentStatsService = Depends(get_content_stats_service),
) -> None:
    """删除评论"""
    # 获取评论
    comment = await crud.comment.get(db, id=comment_id)
    if not comment:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="评论不存在",
        )

    # 检查权限
    can_delete_own = await PermissionChecker.check_permission(
        db,
        current_user,
        PermissionObject(resource=ResourceType.COMMENT, action=Action.DELETE, scope=Scope.OWN),
    )
    can_manage = await PermissionChecker.check_permission(
        db,
        current_user,
        PermissionObject(resource=ResourceType.COMMENT, action=Action.MANAGE, scope=Scope.ALL),
    )
    if not ((comment.author_id == current_user.id and can_delete_own) or can_manage):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限删除此评论",
        )

    # 记录要更新的内容
    content_type = comment.comment_type.value
    content_id = comment.article_id or comment.video_id or comment.scratch_id or comment.post_id

    # 删除评论
    await crud.comment.remove(db, id=comment_id)

    # 更新相关内容的评论数
    if content_id:
        await content_stats_service.update_comment_count(
            content_type=content_type, content_id=content_id, increment=-1
        )


@router.post("/{comment_id}/like", response_model=LikeStatus)
async def toggle_comment_like(
    *,
    db: AsyncSession = Depends(deps.get_db),
    comment_id: int,
    current_user: models.User = Depends(deps.get_current_user),
    content_stats_service: ContentStatsService = Depends(get_content_stats_service),
) -> Any:
    """切换评论点赞状态"""
    # 检查评论是否存在
    comment = await crud.comment.get(db, id=comment_id)
    if not comment:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="评论不存在",
        )

    # 检查评论是否可见
    if not comment.is_visible:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="评论不存在",
        )

    # 切换点赞状态
    _, is_liked = await crud.like.toggle_like(
        db, user_id=current_user.id, content_type="comment", content_id=comment_id
    )

    await db.commit()

    try:
        await content_stats_service.update_like_status(
            "comment", comment_id, current_user.id, is_liked
        )
    except Exception as exc:
        logger.warning("评论点赞缓存更新失败 comment_id={} err={}", comment_id, exc)

    # 获取最新的点赞数量
    like_count = await crud.like.get_content_like_count(
        db, content_type="comment", content_id=comment_id
    )

    return LikeStatus(
        content_type="comment",
        content_id=comment_id,
        is_liked=is_liked,
        like_count=like_count,
    )


@router.get("/{comment_id}/like", response_model=LikeStatus)
async def get_comment_like_status(
    *,
    db: AsyncSession = Depends(deps.get_db),
    comment_id: int,
    current_user: models.User = Depends(deps.get_current_user),
) -> Any:
    """获取评论点赞状态"""
    # 检查评论是否存在
    comment = await crud.comment.get(db, id=comment_id)
    if not comment:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="评论不存在",
        )

    # 检查评论是否可见
    if not comment.is_visible:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="评论不存在",
        )

    # 获取点赞状态
    is_liked = await crud.like.is_liked_by_user(
        db, user_id=current_user.id, content_type="comment", content_id=comment_id
    )

    # 获取点赞数量
    like_count = await crud.like.get_content_like_count(
        db, content_type="comment", content_id=comment_id
    )

    return LikeStatus(
        content_type="comment",
        content_id=comment_id,
        is_liked=is_liked,
        like_count=like_count,
    )


# ==================== 管理员评论管理接口 ====================


@router.get("/admin", response_model=list[FlatComment])
async def get_comments_admin(
    *,
    db: AsyncSession = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_user),
    content_type: CommentType | None = None,
    content_id: int | None = None,
    status: str | None = None,
    reported: bool | None = None,
    skip: int = 0,
    limit: int = 50,
) -> list[FlatComment]:
    """获取评论列表（管理员）- 支持多条件过滤"""
    from sqlalchemy import and_

    # 权限检查
    permission_checker = PermissionChecker()
    has_permission = await permission_checker.check_permission(
        db, current_user, PermissionObject(ResourceType.COMMENT, Action.MANAGE, Scope.ALL)
    )
    if not has_permission:
        raise HTTPException(status_code=403, detail="权限不足")

    # 构建查询条件
    conditions = []

    if content_type:
        conditions.append(models.Comment.content_type == content_type)
    if content_id:
        conditions.append(models.Comment.content_id == content_id)
    if status:
        conditions.append(models.Comment.status == status)
    if reported is not None:
        if reported:
            conditions.append(models.Comment.report_count > 0)
        else:
            conditions.append(models.Comment.report_count == 0)

    # 执行查询
    where_clause = and_(*conditions) if conditions else None

    comments = await crud.comment.get_multi_with_filters(
        db=db, where=where_clause, skip=skip, limit=limit
    )

    # 转换为FlatComment格式
    flat_comments = []
    for comment in comments:
        flat_comment = FlatComment(
            id=comment.id,
            content=comment.content,
            author_id=comment.author_id,
            author_name=comment.author.username if comment.author else "未知用户",
            content_type=comment.content_type,
            content_id=comment.content_id,
            image_urls=list(comment.image_urls or []),
            parent_id=comment.parent_id,
            status=comment.status,
            report_count=comment.report_count or 0,
            like_count=comment.like_count or 0,
            created_at=comment.created_at,
            updated_at=comment.updated_at,
        )
        flat_comments.append(flat_comment)

    return flat_comments


@router.put("/admin/{comment_id}/status")
async def update_comment_status_admin(
    *,
    db: AsyncSession = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_user),
    comment_id: int,
    status: str,
    reason: str | None = None,
    request: Request = None,
) -> dict:
    """更新评论状态（管理员）"""
    from app.models.audit_log import AuditAction, AuditResourceType
    from app.services.audit_log_service import AuditLogService

    # 权限检查
    permission_checker = PermissionChecker()
    has_permission = await permission_checker.check_permission(
        db, current_user, PermissionObject(ResourceType.COMMENT, Action.MANAGE, Scope.ALL)
    )
    if not has_permission:
        raise HTTPException(status_code=403, detail="权限不足")

    comment = await crud.comment.get(db=db, id=comment_id)
    if not comment:
        raise HTTPException(status_code=404, detail="评论不存在")

    # 记录操作前的值
    old_values = {"status": comment.status, "admin_note": getattr(comment, "admin_note", None)}

    # 更新状态
    update_data = {"status": status}
    if reason:
        update_data["admin_note"] = reason

    await crud.comment.update(db=db, db_obj=comment, obj_in=update_data)

    # 记录审计日志
    await AuditLogService.log_action(
        db=db,
        user=current_user,
        action=AuditAction.UPDATE,
        resource_type=AuditResourceType.COMMENT,
        resource_id=comment_id,
        resource_name=f"评论内容: {comment.content[:50]}...",
        description=f"更新评论状态为: {status}",
        reason=reason,
        old_values=old_values,
        new_values=update_data,
        request=request,
    )

    return {"message": "评论状态更新成功"}


@router.post("/admin/batch-review")
async def batch_review_comments_admin(
    *,
    db: AsyncSession = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_user),
    comment_ids: list[int],
    action: str,  # "approve", "reject", "delete"
    reason: str | None = None,
    request: Request = None,
) -> dict:
    """批量审核评论（管理员）"""
    from app.models.audit_log import AuditAction, AuditResourceType
    from app.services.audit_log_service import AuditLogService

    # 权限检查
    permission_checker = PermissionChecker()
    has_permission = await permission_checker.check_permission(
        db, current_user, PermissionObject(ResourceType.COMMENT, Action.MANAGE, Scope.ALL)
    )
    if not has_permission:
        raise HTTPException(status_code=403, detail="权限不足")

    processed_count = 0
    processed_comments = []

    for comment_id in comment_ids:
        comment = await crud.comment.get(db=db, id=comment_id)
        if comment:
            old_values = {
                "status": comment.status,
                "admin_note": getattr(comment, "admin_note", None),
            }

            if action == "approve":
                update_data = {"status": "approved"}
            elif action == "reject":
                update_data = {"status": "rejected"}
            elif action == "delete":
                await crud.comment.remove(db=db, id=comment_id)
                processed_count += 1
                processed_comments.append(
                    {"id": comment_id, "action": "delete", "content": comment.content[:50]}
                )
                continue
            else:
                continue

            if reason:
                update_data["admin_note"] = reason

            await crud.comment.update(db=db, db_obj=comment, obj_in=update_data)
            processed_count += 1
            processed_comments.append(
                {
                    "id": comment_id,
                    "action": action,
                    "content": comment.content[:50],
                    "old_values": old_values,
                }
            )

    # 记录批量操作审计日志
    if processed_comments:
        audit_action = AuditAction.BATCH_DELETE if action == "delete" else AuditAction.BATCH_UPDATE
        await AuditLogService.log_action(
            db=db,
            user=current_user,
            action=audit_action,
            resource_type=AuditResourceType.COMMENT,
            description=f"批量{action} {processed_count} 条评论",
            reason=reason,
            new_values={
                "action": action,
                "comment_ids": comment_ids,
                "processed_count": processed_count,
            },
            request=request,
        )

    return {"message": f"成功处理 {processed_count} 条评论", "processed_count": processed_count}


@router.get("/admin/reports", response_model=list[FlatComment])
async def get_reported_comments_admin(
    *,
    db: AsyncSession = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_user),
    skip: int = 0,
    limit: int = 50,
) -> list[FlatComment]:
    """获取被举报的评论列表（管理员）"""

    # 权限检查
    permission_checker = PermissionChecker()
    has_permission = await permission_checker.check_permission(
        db, current_user, PermissionObject(ResourceType.COMMENT, Action.MANAGE, Scope.ALL)
    )
    if not has_permission:
        raise HTTPException(status_code=403, detail="权限不足")

    # 查询被举报的评论
    comments = await crud.comment.get_multi_with_filters(
        db=db,
        where=models.Comment.report_count > 0,
        skip=skip,
        limit=limit,
        order_by=models.Comment.report_count.desc(),
    )

    # 转换为FlatComment格式
    flat_comments = []
    for comment in comments:
        flat_comment = FlatComment(
            id=comment.id,
            content=comment.content,
            author_id=comment.author_id,
            author_name=comment.author.username if comment.author else "未知用户",
            content_type=comment.content_type,
            content_id=comment.content_id,
            image_urls=list(comment.image_urls or []),
            parent_id=comment.parent_id,
            status=comment.status,
            report_count=comment.report_count or 0,
            like_count=comment.like_count or 0,
            created_at=comment.created_at,
            updated_at=comment.updated_at,
        )
        flat_comments.append(flat_comment)

    return flat_comments
