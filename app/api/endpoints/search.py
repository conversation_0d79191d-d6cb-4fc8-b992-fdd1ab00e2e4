"""
搜索 API 端点

提供：
- 统一搜索接口
- 分类搜索（文章/视频/Scratch）
- 搜索建议
- 热门搜索
- 搜索历史管理
- 搜索统计（管理员）
"""

import time
from typing import Any, Optional
from fastapi import APIRouter, Depends, HTTPException, Query, Request, status
from sqlalchemy.ext.asyncio import AsyncSession

from app import models, schemas
from app.api import deps
from app.api.permission_deps import require_permission
from app.core.logging import logger
from app.core.limiter import limiter
from app.core.pagination import CursorPaginationParams
from app.core.permissions import Permissions
from app.services.search_service import SearchService
from app.services.search_cache_service import SearchCacheService
from app.crud.crud_search import search_history, search_stats, search_suggestion
from app.schemas.search import (
    SearchParams,
    SearchResponse,
    SearchSuggestionParams,
    SearchSuggestionResponse,
    TrendingResponse,
    SearchHistoryParams,
    SearchHistoryResponse,
    MessageResponse,
)

router = APIRouter()

# 权限守卫
analytics_read_guard = require_permission(Permissions.ADMIN_ANALYTICS_READ_ALL)


# ==================== 统一搜索接口 ====================

@router.get("", response_model=SearchResponse)
@limiter.limit("60/minute")  # 登录用户 60次/分钟
async def search_content(
    request: Request,
    *,
    db: AsyncSession = Depends(deps.get_db),
    q: str = Query(..., min_length=2, max_length=100, description="搜索关键词"),
    type: str = Query("all", pattern="^(all|article|video|scratch)$", description="内容类型"),
    category_id: Optional[int] = Query(None, description="类别ID"),
    tag_ids: Optional[str] = Query(None, description="标签ID，逗号分隔"),
    author_id: Optional[int] = Query(None, description="作者ID"),
    sort_by: str = Query("relevance", pattern="^(relevance|hot|recent)$", description="排序方式"),
    difficulty: Optional[str] = Query(None, pattern="^(easy|medium|hard)$", description="难度（Scratch）"),
    cursor: Optional[str] = Query(None, description="游标"),
    size: int = Query(20, ge=1, le=50, description="每页大小"),
    highlight: bool = Query(True, description="是否高亮"),
    current_user: Optional[models.User] = Depends(deps.get_current_user_optional),
) -> Any:
    """
    统一搜索接口
    
    - 支持搜索文章、视频、Scratch 项目
    - 支持多维度过滤
    - 支持相关度、热度、时间排序
    - 返回高亮结果
    """
    start_time = time.time()
    
    try:
        # 解析标签 IDs
        tag_id_list = None
        if tag_ids:
            try:
                tag_id_list = [int(x.strip()) for x in tag_ids.split(",")]
            except ValueError:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="tag_ids 格式错误"
                )
        
        # 构建过滤条件
        filters = {
            "category_id": category_id,
            "tag_ids": tag_id_list,
            "author_id": author_id,
            "sort_by": sort_by,
            "difficulty": difficulty,
        }
        
        # 构建分页参数
        pagination = CursorPaginationParams(
            cursor=cursor,
            size=size,
            order_by="id",
            order_direction="desc"
        )
        
        # 根据类型搜索
        results = []
        if type == "all" or type == "article":
            articles = await SearchService.search_articles(
                db=db,
                query=q,
                category_id=category_id,
                tag_ids=tag_id_list,
                author_id=author_id,
                sort_by=sort_by,
                pagination=pagination,
            )
            results.extend(articles)
        
        if type == "all" or type == "video":
            videos = await SearchService.search_videos(
                db=db,
                query=q,
                category_id=category_id,
                tag_ids=tag_id_list,
                author_id=author_id,
                sort_by=sort_by,
                pagination=pagination,
            )
            results.extend(videos)
        
        if type == "all" or type == "scratch":
            scratch_items = await SearchService.search_scratch(
                db=db,
                query=q,
                difficulty=difficulty,
                sort_by=sort_by,
                pagination=pagination,
            )
            results.extend(scratch_items)
        
        # 检查是否有下一页
        has_next = len(results) > size
        if has_next:
            results = results[:size]
        
        # 记录搜索历史（异步，不阻塞响应）
        if current_user:
            try:
                await search_history.create(
                    db=db,
                    user_id=current_user.id,
                    query=q,
                    content_type=type,
                    filters=filters,
                    result_count=len(results),
                    ip_address=request.client.host if request.client else None,
                    user_agent=request.headers.get("user-agent"),
                )
            except Exception as e:
                logger.error(f"记录搜索历史失败: {e}")
        
        # 更新搜索统计（异步）
        try:
            await search_stats.increment_search_count(
                db=db,
                keyword=q,
                content_type=type,
                result_count=len(results),
            )
        except Exception as e:
            logger.error(f"更新搜索统计失败: {e}")
        
        # 计算响应时间
        search_time_ms = int((time.time() - start_time) * 1000)
        
        # 构建响应
        return SearchResponse(
            items=results,
            has_next=has_next,
            has_previous=bool(cursor),
            next_cursor=str(results[-1].id) if has_next and results else None,
            previous_cursor=None,  # 简化实现
            total_count=None,  # 不计算总数以提升性能
            search_time_ms=search_time_ms,
            suggestions=[],  # 可以添加相关搜索建议
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"搜索失败: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="搜索服务暂时不可用"
        )


# ==================== 分类搜索接口 ====================

@router.get("/articles", response_model=SearchResponse)
@limiter.limit("60/minute")
async def search_articles(
    request: Request,
    *,
    db: AsyncSession = Depends(deps.get_db),
    q: str = Query(..., min_length=2, max_length=100),
    category_id: Optional[int] = Query(None),
    tag_ids: Optional[str] = Query(None),
    author_id: Optional[int] = Query(None),
    sort_by: str = Query("relevance"),
    cursor: Optional[str] = Query(None),
    size: int = Query(20, ge=1, le=50),
    current_user: Optional[models.User] = Depends(deps.get_current_user_optional),
) -> Any:
    """搜索文章"""
    # 调用统一搜索，type 固定为 article
    return await search_content(
        request=request,
        db=db,
        q=q,
        type="article",
        category_id=category_id,
        tag_ids=tag_ids,
        author_id=author_id,
        sort_by=sort_by,
        cursor=cursor,
        size=size,
        current_user=current_user,
    )


@router.get("/videos", response_model=SearchResponse)
@limiter.limit("60/minute")
async def search_videos(
    request: Request,
    *,
    db: AsyncSession = Depends(deps.get_db),
    q: str = Query(..., min_length=2, max_length=100),
    category_id: Optional[int] = Query(None),
    tag_ids: Optional[str] = Query(None),
    author_id: Optional[int] = Query(None),
    sort_by: str = Query("relevance"),
    cursor: Optional[str] = Query(None),
    size: int = Query(20, ge=1, le=50),
    current_user: Optional[models.User] = Depends(deps.get_current_user_optional),
) -> Any:
    """搜索视频"""
    return await search_content(
        request=request,
        db=db,
        q=q,
        type="video",
        category_id=category_id,
        tag_ids=tag_ids,
        author_id=author_id,
        sort_by=sort_by,
        cursor=cursor,
        size=size,
        current_user=current_user,
    )


@router.get("/scratch", response_model=SearchResponse)
@limiter.limit("60/minute")
async def search_scratch_projects(
    request: Request,
    *,
    db: AsyncSession = Depends(deps.get_db),
    q: str = Query(..., min_length=2, max_length=100),
    difficulty: Optional[str] = Query(None),
    sort_by: str = Query("relevance"),
    cursor: Optional[str] = Query(None),
    size: int = Query(20, ge=1, le=50),
    current_user: Optional[models.User] = Depends(deps.get_current_user_optional),
) -> Any:
    """搜索 Scratch 项目"""
    return await search_content(
        request=request,
        db=db,
        q=q,
        type="scratch",
        difficulty=difficulty,
        sort_by=sort_by,
        cursor=cursor,
        size=size,
        current_user=current_user,
    )


# ==================== 搜索建议 ====================

@router.get("/suggestions", response_model=SearchSuggestionResponse)
@limiter.limit("60/minute")
async def get_search_suggestions(
    request: Request,
    *,
    db: AsyncSession = Depends(deps.get_db),
    q: str = Query(..., min_length=1, description="搜索前缀"),
    type: str = Query("all", description="内容类型"),
    limit: int = Query(10, ge=1, le=20, description="返回数量"),
) -> Any:
    """
    获取搜索建议（自动补全）
    
    - 基于用户输入前缀返回建议
    - 结合热门搜索和历史搜索
    """
    try:
        suggestions = await search_suggestion.get_suggestions(
            db=db,
            prefix=q,
            content_type=type,
            limit=limit,
        )
        
        # 转换为响应格式
        items = [
            {
                "keyword": s.keyword,
                "type": s.suggestion_type,
                "count": s.weight,
            }
            for s in suggestions
        ]
        
        return SearchSuggestionResponse(suggestions=items)
        
    except Exception as e:
        logger.error(f"获取搜索建议失败: {e}")
        # 返回空列表而不是错误
        return SearchSuggestionResponse(suggestions=[])


# ==================== 热门搜索 ====================

@router.get("/trending", response_model=TrendingResponse)
@limiter.limit("20/minute")
async def get_trending_keywords(
    request: Request,
    *,
    db: AsyncSession = Depends(deps.get_db),
    type: str = Query("all", description="内容类型"),
    period: str = Query("day", pattern="^(hour|day|week)$", description="时间段"),
    limit: int = Query(10, ge=1, le=50, description="返回数量"),
) -> Any:
    """
    获取热门搜索词
    
    - 返回指定时间段内的热门搜索关键词
    - 包含搜索次数和趋势信息
    """
    try:
        trending_stats = await search_stats.get_trending_keywords(
            db=db,
            content_type=type,
            period=period,
            limit=limit,
        )
        
        # 转换为响应格式
        keywords = [
            {
                "keyword": stat.keyword,
                "search_count": stat.search_count,
                "trend": "up",  # 简化实现，实际应该计算趋势
                "change_rate": 0.0,
            }
            for stat in trending_stats
        ]
        
        return TrendingResponse(
            trending_keywords=keywords,
            period=period,
            updated_at=trending_stats[0].last_searched_at if trending_stats else None,
        )
        
    except Exception as e:
        logger.error(f"获取热门搜索失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取热门搜索失败"
        )


# ==================== 搜索历史 ====================

@router.get("/history", response_model=SearchHistoryResponse)
async def get_search_history(
    *,
    db: AsyncSession = Depends(deps.get_db),
    type: str = Query("all", description="内容类型"),
    limit: int = Query(20, ge=1, le=100, description="返回数量"),
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    获取用户搜索历史
    
    - 需要登录
    - 返回用户的搜索历史记录
    """
    try:
        history = await search_history.get_user_history(
            db=db,
            user_id=current_user.id,
            content_type=type if type != "all" else None,
            limit=limit,
        )
        
        # 转换为响应格式
        items = [
            {
                "id": h.id,
                "query": h.query,
                "content_type": h.content_type,
                "result_count": h.result_count,
                "searched_at": h.created_at,
            }
            for h in history
        ]
        
        return SearchHistoryResponse(
            history=items,
            total=len(items),
        )
        
    except Exception as e:
        logger.error(f"获取搜索历史失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取搜索历史失败"
        )


@router.delete("/history/{history_id}", response_model=MessageResponse)
async def delete_search_history_item(
    *,
    db: AsyncSession = Depends(deps.get_db),
    history_id: int,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    删除指定的搜索历史记录
    
    - 需要登录
    - 只能删除自己的搜索历史
    """
    try:
        success = await search_history.delete_by_id(
            db=db,
            history_id=history_id,
            user_id=current_user.id,
        )
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="搜索历史记录不存在"
            )
        
        return MessageResponse(message="搜索历史已删除")
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除搜索历史失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="删除搜索历史失败"
        )


@router.delete("/history", response_model=MessageResponse)
async def clear_search_history(
    *,
    db: AsyncSession = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    清空用户的所有搜索历史
    
    - 需要登录
    - 删除当前用户的所有搜索记录
    """
    try:
        deleted_count = await search_history.delete_user_history(
            db=db,
            user_id=current_user.id,
        )
        
        return MessageResponse(
            message="所有搜索历史已清空",
            deleted_count=deleted_count,
        )
        
    except Exception as e:
        logger.error(f"清空搜索历史失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="清空搜索历史失败"
        )


# ==================== 搜索统计（管理员） ====================

@router.get("/stats")
async def get_search_stats(
    *,
    db: AsyncSession = Depends(deps.get_db),
    start_date: Optional[str] = Query(None, description="起始日期 YYYY-MM-DD"),
    end_date: Optional[str] = Query(None, description="结束日期 YYYY-MM-DD"),
    current_user=Depends(analytics_read_guard),
) -> Any:
    """
    获取搜索统计数据（管理员权限）
    
    - 需要管理员权限
    - 返回搜索相关的统计信息
    """
    try:
        # 解析日期
        from datetime import datetime, date
        
        start = None
        end = None
        if start_date:
            start = datetime.strptime(start_date, "%Y-%m-%d").date()
        if end_date:
            end = datetime.strptime(end_date, "%Y-%m-%d").date()
        
        stats = await search_stats.get_search_stats(
            db=db,
            start_date=start,
            end_date=end,
        )
        
        return stats
        
    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="日期格式错误，应为 YYYY-MM-DD"
        )
    except Exception as e:
        logger.error(f"获取搜索统计失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取搜索统计失败"
        )
