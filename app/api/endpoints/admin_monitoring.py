"""系统监控与告警 API"""

from __future__ import annotations

from datetime import datetime, timedelta
from typing import Any

from fastapi import APIRouter, Depends, HTTPException, Query, Request, status
from sqlalchemy.ext.asyncio import AsyncSession

from app.api import deps
from app.api.permission_deps import require_permission
from app.core.permissions import Permissions
from app.schemas.admin_monitoring import AlertRuleCreateRequest, AlertRuleUpdateRequest
from app.services.monitoring.alert_engine import MonitoringAlertEngine
from app.services.monitoring.collector import MonitoringCollector
from app.services.monitoring.service import MonitoringService

router = APIRouter()

monitoring_read_guard = require_permission(Permissions.ADMIN_MONITORING_READ_ALL)
monitoring_manage_guard = require_permission(Permissions.ADMIN_MONITORING_MANAGE_ALL)


async def get_monitoring_service() -> MonitoringService:
    collector = MonitoringCollector()
    alert_engine = MonitoringAlertEngine()
    return MonitoringService(collector=collector, alert_engine=alert_engine)


def _success(data: Any) -> dict[str, Any]:
    return {"status": "success", "data": data}


@router.get("/dashboard", summary="监控仪表盘")
async def get_dashboard(
    time_range: str = Query("5m"),
    db: AsyncSession = Depends(deps.get_db),
    current_user=Depends(monitoring_read_guard),
    service: MonitoringService = Depends(get_monitoring_service),
):
    data = await service.get_dashboard(db, time_range=time_range)
    return _success(data)


@router.get("/metrics", summary="指标明细")
async def get_metric_series(
    metric_name: str = Query(...),
    start: datetime | None = Query(None),
    end: datetime | None = Query(None),
    db: AsyncSession = Depends(deps.get_db),
    current_user=Depends(monitoring_read_guard),
    service: MonitoringService = Depends(get_monitoring_service),
):
    if not start or not end:
        end = datetime.utcnow()
        start = end - timedelta(minutes=30)
    series = await service.get_metric_series(db, metric_name=metric_name, start=start, end=end)
    return _success(series.model_dump(mode="json"))


@router.get("/alerts", summary="告警事件列表")
async def list_alerts(
    status_filter: str | None = Query(None, alias="status"),
    severity: str | None = Query(None),
    page: int = Query(1, ge=1),
    page_size: int = Query(20, ge=1, le=100),
    db: AsyncSession = Depends(deps.get_db),
    current_user=Depends(monitoring_read_guard),
    service: MonitoringService = Depends(get_monitoring_service),
):
    data = await service.list_alerts(
        db, status=status_filter, severity=severity, page=page, page_size=page_size
    )
    return _success(data)


@router.post("/alerts", summary="创建告警规则")
async def create_alert_rule(
    payload: AlertRuleCreateRequest,
    db: AsyncSession = Depends(deps.get_db),
    current_user=Depends(monitoring_manage_guard),
    service: MonitoringService = Depends(get_monitoring_service),
):
    data = await service.create_alert_rule(db, payload.model_dump(mode="json"))
    return _success(data)


@router.put("/alerts/{rule_id}", summary="更新告警规则")
async def update_alert_rule(
    rule_id: int,
    payload: AlertRuleUpdateRequest,
    db: AsyncSession = Depends(deps.get_db),
    current_user=Depends(monitoring_manage_guard),
    service: MonitoringService = Depends(get_monitoring_service),
):
    data = await service.update_alert_rule(db, rule_id, payload.model_dump(exclude_unset=True))
    return _success(data)


@router.delete("/alerts/{rule_id}", summary="删除告警规则")
async def delete_alert_rule(
    rule_id: int,
    db: AsyncSession = Depends(deps.get_db),
    current_user=Depends(monitoring_manage_guard),
    service: MonitoringService = Depends(get_monitoring_service),
):
    await service.delete_alert_rule(db, rule_id)
    return _success({"id": rule_id, "status": "deleted"})


@router.get("/channels", summary="告警通知渠道")
async def list_channels(
    db: AsyncSession = Depends(deps.get_db),
    current_user=Depends(monitoring_read_guard),
    service: MonitoringService = Depends(get_monitoring_service),
):
    data = await service.list_channels()
    return _success({"channels": data})


@router.post("/test-alert", summary="测试告警通知")
async def test_alert(
    request: Request,
    payload: dict[str, Any],
    db: AsyncSession = Depends(deps.get_db),
    current_user=Depends(monitoring_manage_guard),
    service: MonitoringService = Depends(get_monitoring_service),
):
    channel = payload.get("channel")
    if not channel:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="缺少channel参数")
    result = await service.test_channel(channel)
    return _success(result)
