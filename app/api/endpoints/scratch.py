import json
import logging
from typing import Any

from fastapi import (
    APIRouter,
    Depends,
    File,
    HTTPException,
    Path,
    Query,
    Request,
    UploadFile,
    status,
)
from fastapi.responses import J<PERSON><PERSON>esponse
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import joinedload

from app import crud, models, schemas
from app.api import deps
from app.api.endpoints.upload import (
    UploadResponse,
    check_file_size,
    handle_single_image_upload,
)
from app.api.permission_deps import require_permission
from app.core.limiter import limiter
from app.core.pagination import (
    CursorPaginationParams,
    CursorPaginationResponse,
)
from app.core.permission_system import ResourceType
from app.core.permissions import Permissions
from app.schemas.scratch import AdaptationType, ScratchProductAdapt, ScratchProjectStats
from app.services.recommendation_service import RecommendationService
from app.services.scratch_aggregation_service import ScratchAggregationService
from app.services.scratch_stats_service import ScratchStatsService
from app.services.service_factory import (
    get_recommendation_service,
    get_scratch_aggregation_service,
    get_tag_hot_service,
)
from app.services.tag_hot_service import TagHotService

# 配置日志
logger = logging.getLogger(__name__)

router = APIRouter()

# ==================== Scratch项目基础CRUD API ====================


@router.post("/create")
async def create_scratch_project(
    *,
    request: Request,
    db: AsyncSession = Depends(deps.get_db),
    original_id: int | None = Query(None, description="原项目ID"),
    is_copy: int | None = Query(None, description="是否复制"),
    is_remix: int | None = Query(None, description="是否翻新"),
    title: str = Query(..., description="项目标题"),
    highlights: str | None = Query(
        None,
        description="项目亮点，JSON数组或逗号分隔字符串",
    ),
    current_user: models.User = Depends(require_permission(Permissions.SCRATCH_PROJECT_CREATE_OWN)),
) -> Any:
    """
      创建Scratch项目
      - 概念：is_copy 和 is_remix 是在保存到项目服务端时附带的查询参数，用来告诉后端这次“创建”是“副本”还是“重混”。
    - 含义与用途
        - is_copy=1：另存为副本。后端可据此把新项目标记为从 original_id 复制而来，常用于同作者的“复制一份”，标题可用传入的 title，一般
    不建立“remix”关系。
        - is_remix=1：重混。后端可据此建立与 original_id 的“重混/父子”关系，用于来源归属、统计和在页面展示诸如“Remixed from …”之类信
    息，也可用传入的 title 设定默认标题。
    - 何时传递
        - 纯新建（空白项目）：不传这两个参数。
        - 另存为副本：传 { originalId, isCopy: 1, title }。
        - 重混：传 { originalId, isRemix: 1, title }。

      **权限要求**: 用户需要有创建内容的权限
      **功能特点**:
      - 自动设置作者为当前用户
      - 支持设置项目基本信息（标题、描述、难度等级等）
      - 创建成功后返回完整的项目信息
    """
    # 获取请求体中的代码
    body = await request.body()
    try:
        code = json.loads(body)
    except json.JSONDecodeError as e:
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY, detail="请求体必须是有效的JSON格式"
        ) from e

    def _parse_highlights(raw: str | None) -> list[str] | None:
        if raw is None:
            return None
        raw = raw.strip()
        if not raw:
            return []
        try:
            data = json.loads(raw)
            if isinstance(data, (list, tuple, set)):
                return [str(item).strip() for item in data if str(item).strip()]
        except json.JSONDecodeError:
            pass
        return [segment.strip() for segment in raw.split("|") if segment.strip()]

    parsed_highlights = schemas.normalize_highlights(_parse_highlights(highlights))

    create_data = schemas.ScratchProductCreate(
        title=title,
        code=code,
        can_adapt=True,
        author_id=current_user.id,
        highlights=parsed_highlights,
    )

    if is_copy == 1:
        if not original_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="复制操作需要提供原项目ID"
            )
        # 验证原项目是否存在
        original_project = await crud.scratch_product.get(db=db, id=original_id)
        if not original_project:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="原项目不存在")
        if original_project.author_id != current_user.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN, detail="不能复制其他作者的项目"
            )
        # 创建副本：设置改编关系为COPY，复制原项目代码
        create_data = schemas.ScratchProductCreate(
            title=title,
            code=original_project.code or {},  # 复制原代码
            can_adapt=True,
            author_id=current_user.id,
            highlights=parsed_highlights,
        )
        project = await crud.scratch_product.create(
            db=db, obj_in=create_data, author_id=current_user.id, commit=True
        )
        # 手动设置副本关系
        project.original_project_id = original_id
        project.root_project_id = original_id
        project.adapt_level = 1
        project.adaptation_type = AdaptationType.COPY.value
        await db.commit()
        await db.refresh(project)
        # 增加原项目的改编计数
        await crud.scratch_product.increment_adapt_count(db=db, project_id=original_id)

    elif is_remix == 1:
        if not original_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="重混操作需要提供原项目ID"
            )
        # 验证原项目是否存在
        original_project = await crud.scratch_product.get(db=db, id=original_id)
        if not original_project:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="原项目不存在")
        # 检查原项目是否允许改编
        if not original_project.can_adapt:
            raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="原项目不允许被改编")
        # 创建重混：使用改编服务
        adaptation_data = ScratchProductAdapt(
            title=title,
            adaptation_type=AdaptationType.REMIX,
            inherit_code=True,
            inherit_resources=True,
        )
        project = await crud.scratch_product.create_adaptation(
            db=db,
            original_project=original_project,
            obj_in=adaptation_data,
            author_id=current_user.id,
            commit=True,
        )

    else:
        # 原创作品
        project = await crud.scratch_product.create(
            db=db, obj_in=create_data, author_id=current_user.id, commit=True
        )

    logger.info(
        f"User {current_user.id} created scratch project {project.project_id}",
        extra={
            "user_id": current_user.id,
            "project_id": project.project_id,
            "action": "create_scratch_project",
            "is_copy": is_copy,
            "is_remix": is_remix,
        },
    )

    return {"project_id": project.project_id}


@router.get("/{project_id}", response_model=schemas.ScratchProductOut)
@limiter.limit("100/minute")
async def get_scratch_project(
    *,
    request: Request,
    db: AsyncSession = Depends(deps.get_db),
    project_id: int = Path(..., description="项目ID"),
    format: str = Query(None, description="返回格式，可选raw 或 None"),
    current_user: models.User | None = Depends(
        require_permission(
            Permissions.SCRATCH_PROJECT_READ_PUBLIC,
            resource_type=ResourceType.SCRATCH_PROJECT,
            resource_id_key="project_id",
            optional_user=True,
        )
    ),
) -> schemas.ScratchProductOut:
    """
    获取Scratch项目详情

    **权限要求**: 公开项目游客可访问，私有项目需要相应权限
    **功能特点**:
    - 返回项目的完整信息
    - 包含作者信息和改编关系
    - 支持权限控制
    """
    if format == "raw":
        found, code = await crud.scratch_product.get_code_only(db=db, project_id=project_id)
        if not found:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="项目不存在")
        return JSONResponse(content=code or {})

    # 预加载相关数据
    project = await crud.scratch_product.get(
        db=db,
        id=project_id,
        options=[
            joinedload(models.ScratchProduct.author).joinedload(models.User.stats),
            joinedload(models.ScratchProduct.original_project)
            .joinedload(models.ScratchProduct.author)
            .joinedload(models.User.stats),
            joinedload(models.ScratchProduct.root_project)
            .joinedload(models.ScratchProduct.author)
            .joinedload(models.User.stats),
            joinedload(models.ScratchProduct.tags),
        ],
    )

    if not project:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="项目不存在")

    stats_service = ScratchStatsService()
    user_id = current_user.id if current_user else None
    aggregated_stats = await stats_service.get_stats(
        db=db,
        content_id=project_id,
        user_id=user_id,
    )

    comment_count = await crud.comment.count_by_scratch(db=db, scratch_id=project_id)

    response = schemas.ScratchProductOut.model_validate(project)

    stats_payload = aggregated_stats or {}

    like_count = stats_payload.get("like_count", response.like_count)
    favorite_count = stats_payload.get("favorite_count", response.favorite_count)
    visit_count = stats_payload.get("visit_count", response.visit_count)
    adapt_count = stats_payload.get("adapt_count", response.adapt_count)

    response.like_count = like_count
    response.favorite_count = favorite_count
    response.visit_count = visit_count
    response.adapt_count = adapt_count
    response.comment_count = comment_count

    if response.stats is None:
        response.stats = ScratchProjectStats()

    response.stats.like_count = like_count
    response.stats.favorite_count = favorite_count
    response.stats.visit_count = visit_count
    response.stats.adapt_count = adapt_count
    response.stats.comment_count = comment_count
    response.stats.is_favorited_by_user = stats_payload.get(
        "is_favorited_by_user", response.stats.is_favorited_by_user
    )
    response.stats.is_liked_by_user = stats_payload.get(
        "is_liked_by_user", response.stats.is_liked_by_user
    )

    return response


@router.get(
    "/{project_id}/recommendations", response_model=list[schemas.ScratchProductOut]
)
async def get_scratch_recommendations(
    project_id: int,
    *,
    db: AsyncSession = Depends(deps.get_db),
    limit: int = Query(6, ge=1, le=20, description="推荐数量"),
    current_user: models.User | None = Depends(deps.get_current_user_optional),
    recommendation_service: RecommendationService = Depends(get_recommendation_service),
    scratch_aggregation_service: ScratchAggregationService = Depends(
        get_scratch_aggregation_service
    ),
) -> list[schemas.ScratchProductOut]:
    """获取与指定项目相似的推荐列表。"""

    project = await crud.scratch_product.get(db=db, id=project_id)
    if not project:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="项目不存在")

    recommendation_items = await recommendation_service.get_similar_content(
        content_type="scratch", content_id=project_id, limit=limit
    )

    recommended_ids = [item.content_id for item in recommendation_items]
    aggregated = await scratch_aggregation_service.get_projects_by_ids(
        db=db,
        project_ids=recommended_ids,
        current_user=current_user,
    )

    ordered: list[schemas.ScratchProductOut] = []
    if aggregated:
        aggregated_map = {item.project_id: item for item in aggregated}
        ordered.extend(
            aggregated_map[pid] for pid in recommended_ids if pid in aggregated_map
        )

    if len(ordered) < limit:
        fallback_projects = await crud.scratch_product.get_related_by_category(
            db=db,
            category=project.category,
            exclude_id=project_id,
            limit=limit,
        )
        fallback_ids = [item.project_id for item in fallback_projects]
        fallback_aggregated = await scratch_aggregation_service.get_projects_by_ids(
            db=db,
            project_ids=fallback_ids,
            current_user=current_user,
        )
        fallback_map = {item.project_id: item for item in fallback_aggregated}

        existing_ids = {item.project_id for item in ordered}
        for pid in fallback_ids:
            if pid == project_id or pid in existing_ids:
                continue
            item = fallback_map.get(pid)
            if item:
                ordered.append(item)
                existing_ids.add(pid)
            if len(ordered) >= limit:
                break

    return ordered[:limit]


@router.put("/{project_id}")
async def update_scratch_project(
    *,
    request: Request,
    db: AsyncSession = Depends(deps.get_db),
    project_id: int = Path(..., description="项目ID"),
    title: str = Query(..., description="项目标题"),
    current_user: models.User = Depends(
        require_permission(
            Permissions.SCRATCH_PROJECT_UPDATE_OWN,
            resource_type=ResourceType.SCRATCH_PROJECT,
            resource_id_key="project_id",
        )
    ),
) -> Any:
    """
    更新Scratch项目 只更新title 和 code

    **权限要求**: 只有项目作者或管理员可以更新
    **功能特点**:
    - 支持更新项目基本信息
    - 不允许更新改编关系字段（需要通过专门的改编API）
    - 自动记录更新日志
    """
    project = await crud.scratch_product.get(db=db, id=project_id)
    if not project:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="项目不存在")

    body = await request.body()
    try:
        code = json.loads(body)
    except json.JSONDecodeError as e:
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY, detail="请求体必须是有效的JSON格式"
        ) from e
    update_data = {
        "title": title,
        "code": code,
    }

    # 更新项目
    updated_project = await crud.scratch_product.update(db=db, db_obj=project, obj_in=update_data)

    # 记录更新日志
    logger.info(
        f"User {current_user.id} updated scratch project {project_id}",
        extra={
            "user_id": current_user.id,
            "project_id": project_id,
            "action": "update_scratch_project",
            "updated_fields": list(update_data.keys()),
        },
    )

    return {"project_id": updated_project.project_id}


@router.put("/{project_id}/detail")
async def update_scratch_project_detail(
    *,
    db: AsyncSession = Depends(deps.get_db),
    project_id: int = Path(..., description="项目ID"),
    update_detail: schemas.ScratchProductUpdate,
    current_user: models.User = Depends(
        require_permission(
            Permissions.SCRATCH_PROJECT_UPDATE_OWN,
            resource_type=ResourceType.SCRATCH_PROJECT,
            resource_id_key="project_id",
        )
    ),
    tag_hot_service: TagHotService = Depends(get_tag_hot_service),
) -> Any:
    """
    更新Scratch项目详情
    """
    project = await crud.scratch_product.get(db=db, id=project_id)
    if not project:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="项目不存在")

    update_payload = update_detail.model_dump(exclude_unset=True)
    raw_tags = update_payload.pop('tags', None)
    raw_highlights = update_payload.get('highlights')

    def _normalize_tags(values: Any) -> list[str] | None:
        if values is None:
            return None
        if not isinstance(values, (list, tuple, set)):
            values = [values]
        normalized: list[str] = []
        seen: set[str] = set()
        for item in values:
            if item is None:
                continue
            if isinstance(item, dict):
                candidate = item.get('name') if isinstance(item.get('name'), str) else None
            else:
                candidate = str(item) if not isinstance(item, str) else item
            if not candidate:
                continue
            name = candidate.strip()
            if not name:
                continue
            key = name.lower()
            if key in seen:
                continue
            seen.add(key)
            normalized.append(name[:50])
        return normalized

    tag_names = _normalize_tags(raw_tags)

    highlights_normalized = None
    if raw_highlights is not None:
        highlights_normalized = schemas.normalize_highlights(raw_highlights)
    if highlights_normalized is not None:
        update_payload['highlights'] = highlights_normalized

    # 更新项目基本字段
    updated_project = await crud.scratch_product.update(
        db=db,
        db_obj=project,
        obj_in=update_payload,
        commit=False,
    )

    if tag_names is not None:
        tag_objects = []
        if tag_names:
            tag_objects = await crud.tag.get_or_create_multi(
                db, names=tag_names, is_default=False, content_type='scratch'
            )
        updated_project.tags = tag_objects
        db.add(updated_project)

    await db.commit()
    await db.refresh(updated_project, attribute_names=['tags'])

    if tag_names:
        await tag_hot_service.record_bulk_events(
            tag_ids=[tag.id for tag in updated_project.tags],
            content_type="scratch",
            user_id=current_user.id,
        )

    # 记录更新日志
    updated_fields = list(update_payload.keys())
    if tag_names is not None:
        updated_fields.append('tags')
    if highlights_normalized is not None:
        updated_fields.append('highlights')
    logger.info(
        f"User {current_user.id} updated scratch project {project_id} detail",
        extra={
            "user_id": current_user.id,
            "project_id": project_id,
            "action": "update_scratch_project_detail",
            "updated_fields": updated_fields,
        },
    )

    return {"project_id": updated_project.project_id}


@router.post(
    "/{project_id}/share",
    response_model=schemas.ScratchPublishStatus,
)
async def share_scratch_project(
    *,
    db: AsyncSession = Depends(deps.get_db),
    project_id: int = Path(..., description="项目ID"),
    current_user: models.User = Depends(
        require_permission(
            Permissions.SCRATCH_PROJECT_UPDATE_OWN,
            resource_type=ResourceType.SCRATCH_PROJECT,
            resource_id_key="project_id",
        )
    ),
) -> schemas.ScratchPublishStatus:
    """将Scratch项目标记为已发布（分享）。"""
    project = await crud.scratch_product.get(db=db, id=project_id)
    if not project:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="项目不存在")

    if project.is_published:
        return schemas.ScratchPublishStatus(project_id=project.project_id, is_published=True)

    updated_project = await crud.scratch_product.update_publish_status(
        db=db, db_obj=project, is_published=True
    )

    logger.info(
        "User {} shared scratch project {}",
        current_user.id,
        project_id,
        extra={
            "user_id": current_user.id,
            "project_id": project_id,
            "action": "share_scratch_project",
        },
    )

    return schemas.ScratchPublishStatus(project_id=updated_project.project_id, is_published=True)


@router.post(
    "/{project_id}/unshare",
    response_model=schemas.ScratchPublishStatus,
)
async def unshare_scratch_project(
    *,
    db: AsyncSession = Depends(deps.get_db),
    project_id: int = Path(..., description="项目ID"),
    current_user: models.User = Depends(
        require_permission(
            Permissions.SCRATCH_PROJECT_UPDATE_OWN,
            resource_type=ResourceType.SCRATCH_PROJECT,
            resource_id_key="project_id",
        )
    ),
) -> schemas.ScratchPublishStatus:
    """取消分享，将Scratch项目标记为未发布。"""
    project = await crud.scratch_product.get(db=db, id=project_id)
    if not project:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="项目不存在")

    if not project.is_published:
        return schemas.ScratchPublishStatus(project_id=project.project_id, is_published=False)

    updated_project = await crud.scratch_product.update_publish_status(
        db=db, db_obj=project, is_published=False
    )

    logger.info(
        "User {} unshared scratch project {}",
        current_user.id,
        project_id,
        extra={
            "user_id": current_user.id,
            "project_id": project_id,
            "action": "unshare_scratch_project",
        },
    )

    return schemas.ScratchPublishStatus(project_id=updated_project.project_id, is_published=False)


@router.post(
    "/{project_id}/cover/upload",
    response_model=UploadResponse,
    dependencies=[Depends(check_file_size)],
)
async def upload_scratch_cover_thumbnail(
    *,
    db: AsyncSession = Depends(deps.get_db),
    project_id: int = Path(..., description="项目ID"),
    file: UploadFile = File(...),
    current_user: models.User = Depends(
        require_permission(
            Permissions.SCRATCH_PROJECT_UPDATE_OWN,
            resource_type=ResourceType.SCRATCH_PROJECT,
            resource_id_key="project_id",
        )
    ),
) -> UploadResponse:
    """上传并更新 Scratch 项目的封面图到 OSS。"""
    project = await crud.scratch_product.get(db=db, id=project_id)
    if not project:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="项目不存在")

    upload_result = await handle_single_image_upload(file=file, db=db)
    if not upload_result.file_url:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="封面图上传失败"
        )

    await crud.scratch_product.update(
        db=db,
        db_obj=project,
        obj_in={"cover_url": upload_result.file_url},
    )

    logger.info(
        "User {} uploaded scratch project {} cover",
        current_user.id,
        project_id,
        extra={
            "user_id": current_user.id,
            "project_id": project_id,
            "action": "upload_scratch_cover_thumbnail",
            "file_hash": upload_result.file_hash,
        },
    )

    return upload_result


@router.delete("/{project_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_scratch_project(
    *,
    db: AsyncSession = Depends(deps.get_db),
    project_id: int,
    current_user: models.User = Depends(
        require_permission(
            Permissions.SCRATCH_PROJECT_DELETE_OWN,
            resource_type=ResourceType.SCRATCH_PROJECT,
            resource_id_key="project_id",
        )
    ),
) -> None:
    """
    删除Scratch项目

    **权限要求**: 只有项目作者或管理员可以删除
    **注意事项**:
    - 删除项目会影响其改编链
    - 删除后无法恢复
    """
    project = await crud.scratch_product.get(db=db, id=project_id)
    if not project:
        return

    # 检查是否有其他项目改编自此项目
    adaptations = await crud.scratch_product.get_adaptations(db=db, original_project_id=project_id)
    if adaptations:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"无法删除此项目，因为有 {len(adaptations)} 个项目改编自此项目",
        )

    await crud.scratch_product.remove(db=db, id=project_id)

    logger.info(
        f"User {current_user.id} deleted scratch project {project_id}",
        extra={
            "user_id": current_user.id,
            "project_id": project_id,
            "action": "delete_scratch_project",
        },
    )


@router.get("/", response_model=CursorPaginationResponse[schemas.ScratchProductOut])
async def get_scratch_projects(
    *,
    db: AsyncSession = Depends(deps.get_db),
    pagination: CursorPaginationParams = Depends(),
    difficulty_level: int | None = Query(None, ge=1, le=5, description="难度等级筛选"),
    category: str | None = Query(None, description="分类筛选"),
    author_id: int | None = Query(None, description="作者ID筛选"),
    adaptation_type: schemas.AdaptationType | None = Query(None, description="改编类型筛选"),
    current_user: models.User | None = Depends(deps.get_current_user_optional),
) -> CursorPaginationResponse[schemas.ScratchProductOut]:
    """
    获取Scratch项目列表（分页）

    **功能特点**:
    - 支持多种筛选条件
    - 支持游标分页
    - 自动过滤用户无权限访问的项目
    """
    # 构建筛选条件
    filters = {}
    if difficulty_level is not None:
        filters["difficulty_level"] = difficulty_level
    if category is not None:
        filters["category"] = category
    if author_id is not None:
        filters["author_id"] = author_id
    if adaptation_type is not None:
        filters["adaptation_type"] = adaptation_type

    # 默认仅返回已分享（发布）项目，除非显式指定。
    filters.setdefault("is_public", True)

    # 获取分页数据
    paginated_result = await crud.scratch_product.get_paginated_projects(
        db=db,
        params=pagination,
        filters=filters,
        current_user=current_user,
    )

    return paginated_result


# ============== 批量操作和高级功能API ==============


@router.post("/multiple", response_model=list[schemas.ScratchProductOut])
async def get_multiple_projects(
    *,
    db: AsyncSession = Depends(deps.get_db),
    data: schemas.MultipleData,
    preserve_order: bool = Query(True, description="是否保持传入ID的顺序"),
    include_adaptations: bool = Query(False, description="是否包含改编关系信息"),
    current_user: models.User | None = Depends(deps.get_current_user_optional),
) -> Any:
    """
    批量获取Scratch项目详情

    **功能特点**:
    - 支持批量获取项目信息
    - 自动过滤用户无权限访问的项目
    - 支持保持传入ID的顺序
    - 可选择是否包含改编关系信息
    """
    # 参数验证
    if not data.projectIds:
        return []

    if len(data.projectIds) > 100:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="一次最多只能获取100个项目"
        )

    # 去重并保持顺序
    unique_ids = list(dict.fromkeys(data.projectIds))

    # 批量获取项目
    projects = await crud.scratch_product.get_multiple(
        db=db,
        ids=unique_ids,
        include_adaptations=include_adaptations,
    )

    # 过滤权限（这里简化处理，实际应该有更复杂的权限控制）
    accessible_projects = []
    for project in projects:
        # 简单的权限检查：公开项目或者是作者本人的项目
        if project.is_published or (current_user and project.author_id == current_user.id):
            accessible_projects.append(project)

    # 如果需要保持顺序，按照原始ID顺序重新排序
    if preserve_order and len(accessible_projects) > 1:
        projects_dict = {project.project_id: project for project in accessible_projects}
        ordered_projects = [projects_dict[id] for id in unique_ids if id in projects_dict]
        return ordered_projects

    return accessible_projects


@router.get(
    "/user/{user_id}/projects", response_model=CursorPaginationResponse[schemas.ScratchProductOut]
)
async def get_user_projects(
    user_id: int,
    *,
    db: AsyncSession = Depends(deps.get_db),
    pagination: CursorPaginationParams = Depends(),
    include_adaptations: bool = Query(False, description="是否包含改编作品"),
    current_user: models.User | None = Depends(deps.get_current_user_optional),
) -> CursorPaginationResponse[schemas.ScratchProductOut]:
    """
    获取指定用户的Scratch项目列表

    **功能特点**:
    - 返回指定用户创建的项目
    - 支持分页
    - 可选择是否包含改编作品
    - 自动处理权限控制（只显示公开项目或作者本人可见的项目）
    """
    # 检查目标用户是否存在
    target_user = await crud.user.get(db=db, id=user_id)
    if not target_user:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="用户不存在")

    # 权限检查
    is_owner = current_user and current_user.id == user_id
    is_admin = current_user and current_user.is_superuser
    can_view_private = is_owner or is_admin

    # 构建筛选条件
    filters = {"author_id": user_id}
    if not can_view_private:
        filters["is_public"] = True

    # 获取分页数据
    paginated_result = await crud.scratch_product.get_paginated_projects(
        db=db,
        params=pagination,
        filters=filters,
        current_user=current_user,
        include_adaptations=include_adaptations,
    )

    return paginated_result


@router.get("/search", response_model=CursorPaginationResponse[schemas.ScratchProductOut])
async def search_projects(
    *,
    db: AsyncSession = Depends(deps.get_db),
    q: str = Query(..., min_length=1, description="搜索关键词"),
    pagination: CursorPaginationParams = Depends(),
    difficulty_level: int | None = Query(None, ge=1, le=5, description="难度等级筛选"),
    category: str | None = Query(None, description="分类筛选"),
    adaptation_type: schemas.AdaptationType | None = Query(None, description="改编类型筛选"),
    current_user: models.User | None = Depends(deps.get_current_user_optional),
) -> CursorPaginationResponse[schemas.ScratchProductOut]:
    """
    搜索Scratch项目

    **功能特点**:
    - 支持关键词搜索（标题、描述）
    - 支持多种筛选条件组合
    - 支持分页
    - 自动过滤用户无权限访问的项目
    """
    # 构建搜索条件
    search_filters = {
        "search_query": q,
        "difficulty_level": difficulty_level,
        "category": category,
        "adaptation_type": adaptation_type,
    }

    # 执行搜索
    paginated_result = await crud.scratch_product.search_projects(
        db=db,
        params=pagination,
        filters=search_filters,
        current_user=current_user,
    )

    return paginated_result


# ============== 改编历史和追踪API ==============


@router.get("/{project_id}/adaptation-history", response_model=list[dict])
async def get_adaptation_history(
    *,
    db: AsyncSession = Depends(deps.get_db),
    project_id: int = Path(..., description="项目ID"),
    limit: int = Query(50, ge=1, le=200, description="返回数量限制"),
    current_user: models.User | None = Depends(deps.get_current_user_optional),
) -> Any:
    """
    获取项目的改编历史记录

    **功能特点**:
    - 返回项目的改编活动历史
    - 包含改编时间、改编者、改编类型等信息
    - 支持分页限制
    """
    # 验证项目是否存在
    project = await crud.scratch_product.get(db=db, id=project_id)
    if not project:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="项目不存在")

    # 获取改编历史（这里需要实现具体的历史记录查询逻辑）
    # 暂时返回模拟数据结构
    history = await crud.scratch_product.get_adaptation_history(
        db=db,
        project_id=project_id,
        limit=limit,
    )

    return history
