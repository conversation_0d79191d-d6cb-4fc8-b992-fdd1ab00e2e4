from typing import Literal

from fastapi import APIRouter, Depends, Form, Query
from fastapi_cache.decorator import cache
from sqlalchemy.ext.asyncio import AsyncSession

from app import crud
from app.api import deps
from app.api.permission_deps import require_permission
from app.core.permissions import Permissions
from app.schemas.tag import Tag, TagCreate, TagUpdate
from app.schemas.tag_hot import HotTag
from app.services.service_factory import get_tag_hot_service
from app.services.tag_hot_service import TagHotService

router = APIRouter()


@router.get("/default", response_model=list[Tag])
@cache(expire=3600)
async def read_default_tags(
    db: AsyncSession = Depends(deps.get_db),
    content_type: Literal["article", "video", "scratch", "post", "global"] | None = Query(
        None, description="标签所属内容类型"
    ),
):
    """获取默认标签，按内容类型过滤。"""

    tags = await crud.tag.get_default_tags(db, content_type=content_type)
    return tags


@router.get("/hot", response_model=list[HotTag])
async def read_hot_tags(
    *,
    db: AsyncSession = Depends(deps.get_db),
    content_type: str = Query("global", description="内容类型: article/video/scratch/post/global"),
    limit: int = Query(20, ge=1, le=100, description="返回数量"),
    tag_hot_service: TagHotService = Depends(get_tag_hot_service),
) -> list[HotTag]:
    """获取统一热门标签榜单。"""

    items = await tag_hot_service.get_hot_tags(db, content_type=content_type, limit=limit)
    if not items:
        snapshot = await tag_hot_service.restore_from_snapshot(content_type=content_type)
        if snapshot:
            return [HotTag(**payload) for payload in snapshot[:limit]]
    return [HotTag(**item.model_dump()) for item in items]


# ==================== 管理员标签管理接口 ====================


@router.post("/admin", response_model=Tag)
async def create_tag_admin(
    *,
    db: AsyncSession = Depends(deps.get_db),
    current_user: deps.User = Depends(require_permission(Permissions.TAG_MANAGE_ALL)),
    name: str = Form(...),
    content_type: Literal["article", "video", "scratch", "post", "global"] = Form("global"),
    is_default: bool = Form(False),
) -> Tag:
    """创建新标签（管理员）"""
    tag_in = TagCreate(name=name, content_type=content_type, is_default=is_default)
    return await crud.tag.create(db=db, obj_in=tag_in)


@router.put("/admin/{tag_id}", response_model=Tag)
async def update_tag_admin(
    *,
    db: AsyncSession = Depends(deps.get_db),
    current_user: deps.User = Depends(require_permission(Permissions.TAG_MANAGE_ALL)),
    tag_id: int,
    name: str | None = Form(None),
    content_type: Literal["article", "video", "scratch", "post", "global"] | None = Form(None),
    is_default: bool | None = Form(None),
) -> Tag:
    """更新标签信息（管理员）"""
    from fastapi import HTTPException

    tag = await crud.tag.get(db=db, id=tag_id)
    if not tag:
        raise HTTPException(status_code=404, detail="标签不存在")

    update_data = {}
    if name is not None:
        update_data["name"] = name
    if content_type is not None:
        update_data["content_type"] = content_type
    if is_default is not None:
        update_data["is_default"] = is_default

    tag_in = TagUpdate(**update_data)
    return await crud.tag.update(db=db, db_obj=tag, obj_in=tag_in)


@router.delete("/admin/{tag_id}")
async def delete_tag_admin(
    *,
    db: AsyncSession = Depends(deps.get_db),
    current_user: deps.User = Depends(require_permission(Permissions.TAG_MANAGE_ALL)),
    tag_id: int,
) -> dict:
    """删除标签（管理员）"""
    from fastapi import HTTPException

    tag = await crud.tag.get(db=db, id=tag_id)
    if not tag:
        raise HTTPException(status_code=404, detail="标签不存在")

    await crud.tag.remove(db=db, id=tag_id)
    return {"message": "标签删除成功"}





@router.get("/admin", response_model=list[Tag])
async def get_tags_admin(
    *,
    db: AsyncSession = Depends(deps.get_db),
    current_user: deps.User = Depends(require_permission(Permissions.TAG_MANAGE_ALL)),
    content_type: Literal["article", "video", "scratch", "global"] | None = None,
    skip: int = 0,
    limit: int = 100,
) -> list[Tag]:
    """获取标签列表（管理员）"""
    return await crud.tag.get_multi_with_filters(
        db=db, content_type=content_type, skip=skip, limit=limit
    )
