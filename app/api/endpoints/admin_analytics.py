"""后台分析 API"""

from __future__ import annotations

from datetime import datetime
from typing import Any

from fastapi import APIRouter, Depends, Request
from sqlalchemy.ext.asyncio import AsyncSession

from app.api import deps
from app.api.permission_deps import require_permission
from app.core.permissions import Permissions
from app.schemas.admin_analytics import AnalyticsExportRequest
from app.services.analytics.service import AnalyticsService

router = APIRouter()

analytics_read_guard = require_permission(Permissions.ADMIN_ANALYTICS_READ_ALL)
analytics_export_guard = require_permission(Permissions.ADMIN_ANALYTICS_EXPORT_ALL)


async def get_analytics_service() -> AnalyticsService:
    return AnalyticsService()


def _success(data: Any) -> dict[str, Any]:
    return {"status": "success", "data": data}


@router.get("/dashboard", summary="分析仪表盘")
async def get_dashboard(
    date_range: str | None = None,
    from_date: datetime | None = None,
    to_date: datetime | None = None,
    db: AsyncSession = Depends(deps.get_db),
    current_user=Depends(analytics_read_guard),
    service: AnalyticsService = Depends(get_analytics_service),
):
    time_range, filters = service.parse_params(date_range, from_date, to_date)
    data = await service.get_dashboard(db, time_range=time_range, filters=filters)
    return _success(data)


@router.get("/users", summary="用户分析")
async def get_users(
    date_range: str | None = None,
    from_date: datetime | None = None,
    to_date: datetime | None = None,
    db: AsyncSession = Depends(deps.get_db),
    current_user=Depends(analytics_read_guard),
    service: AnalyticsService = Depends(get_analytics_service),
):
    time_range, filters = service.parse_params(date_range, from_date, to_date)
    data = await service.get_users(db, time_range=time_range, filters=filters)
    return _success(data)


@router.get("/content", summary="内容分析")
async def get_content(
    date_range: str | None = None,
    from_date: datetime | None = None,
    to_date: datetime | None = None,
    db: AsyncSession = Depends(deps.get_db),
    current_user=Depends(analytics_read_guard),
    service: AnalyticsService = Depends(get_analytics_service),
):
    time_range, filters = service.parse_params(date_range, from_date, to_date)
    data = await service.get_content(db, time_range=time_range, filters=filters)
    return _success(data)


@router.get("/system", summary="系统分析")
async def get_system(
    date_range: str | None = None,
    from_date: datetime | None = None,
    to_date: datetime | None = None,
    db: AsyncSession = Depends(deps.get_db),
    current_user=Depends(analytics_read_guard),
    service: AnalyticsService = Depends(get_analytics_service),
):
    time_range, filters = service.parse_params(date_range, from_date, to_date)
    data = await service.get_system(db, time_range=time_range, filters=filters)
    return _success(data)


@router.post("/export", summary="导出分析数据")
async def export_data(
    request: AnalyticsExportRequest,
    db: AsyncSession = Depends(deps.get_db),
    current_user=Depends(analytics_export_guard),
    service: AnalyticsService = Depends(get_analytics_service),
):
    result = await service.trigger_export(
        db,
        dataset=request.dataset.value if hasattr(request.dataset, "value") else request.dataset,
        time_range=request.time_range.value if hasattr(request.time_range, "value") else request.time_range,
        filters=request.filters,
    )
    return _success(result)
