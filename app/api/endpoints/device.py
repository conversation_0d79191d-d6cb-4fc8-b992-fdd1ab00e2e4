"""设备管理相关接口"""

from datetime import datetime
from enum import Enum

from fastapi import APIRouter, Depends, HTTPException, Query, Request
from pydantic import BaseModel, Field
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from app.api import deps
from app.core.logging import logger
from app.models.user import User
from app.models.user_device import UserDevice
from app.services.device_service import DeviceTrustService
from app.services.service_factory import get_device_trust_service

router = APIRouter()


class RiskLevel(str, Enum):
    """设备风险等级枚举"""

    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"


class DeviceInfo(BaseModel):
    """设备信息响应模型"""

    id: int
    device_name: str | None = None
    device_type: str | None = None
    browser_name: str | None = None
    browser_version: str | None = None
    os_name: str | None = None
    os_version: str | None = None
    ip_address: str | None = None
    location: str | None = None
    is_trusted: bool
    trust_score: int
    login_count: int
    last_login_at: datetime
    first_login_at: datetime
    is_active: bool
    is_blocked: bool
    user_id: int | None = None

    class Config:
        from_attributes = True


class DeviceListResponse(BaseModel):
    """设备列表响应模型"""

    devices: list[DeviceInfo]
    total: int
    trusted_count: int
    active_count: int


class DeviceActionRequest(BaseModel):
    """设备操作请求模型"""

    device_id: int = Field(..., description="设备ID")
    reason: str | None = Field(None, description="操作原因")


@router.get("/devices", response_model=DeviceListResponse)
async def get_user_devices(
    current_user: User = Depends(deps.get_current_user),
    db: AsyncSession = Depends(deps.get_db),
    include_blocked: bool = False,
) -> DeviceListResponse:
    """获取用户设备列表

    Args:
        current_user: 当前用户
        db: 数据库会话
        include_blocked: 是否包含被阻止的设备

    Returns:
        DeviceListResponse: 设备列表响应
    """
    try:
        # 构建查询
        query = select(UserDevice).where(UserDevice.user_id == current_user.id)

        if not include_blocked:
            query = query.where(not UserDevice.is_blocked)

        # 按最后登录时间倒序排列
        query = query.order_by(UserDevice.last_login_at.desc())
        result = await db.execute(query)
        devices = result.scalars().all()

        # 统计信息
        total = len(devices)
        trusted_count = sum(1 for d in devices if d.is_trusted)
        active_count = sum(1 for d in devices if d.is_active and not d.is_blocked)

        return DeviceListResponse(
            devices=[DeviceInfo.model_validate(device) for device in devices],
            total=total,
            trusted_count=trusted_count,
            active_count=active_count,
        )

    except Exception as e:
        logger.error(f"获取用户设备列表失败：{str(e)}")
        raise HTTPException(status_code=500, detail="获取设备列表失败") from e


@router.get("/devices/{device_id}", response_model=DeviceInfo)
async def get_device_detail(
    device_id: int,
    current_user: User = Depends(deps.get_current_user),
    db: AsyncSession = Depends(deps.get_db),
) -> DeviceInfo:
    """获取设备详情

    Args:
        device_id: 设备ID
        current_user: 当前用户
        db: 数据库会话

    Returns:
        DeviceInfo: 设备信息
    """
    try:
        query = select(UserDevice).where(
            UserDevice.id == device_id, UserDevice.user_id == current_user.id
        )
        result = await db.execute(query)
        device = result.scalar_one_or_none()

        if not device:
            raise HTTPException(status_code=404, detail="设备不存在")

        return DeviceInfo.model_validate(device)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取设备详情失败：{str(e)}")
        raise HTTPException(status_code=500, detail="获取设备详情失败") from e


@router.post("/devices/trust")
async def trust_device(
    request: DeviceActionRequest,
    current_user: User = Depends(deps.get_current_user),
    db: AsyncSession = Depends(deps.get_db),
    device_service: DeviceTrustService = Depends(get_device_trust_service),
) -> dict:
    """信任设备

    Args:
        request: 设备操作请求
        current_user: 当前用户
        db: 数据库会话

    Returns:
        dict: 操作结果
    """
    try:
        query = select(UserDevice).where(
            UserDevice.id == request.device_id, UserDevice.user_id == current_user.id
        )
        result = await db.execute(query)
        device = result.scalar_one_or_none()

        if not device:
            raise HTTPException(status_code=404, detail="设备不存在")

        if device.is_blocked:
            raise HTTPException(status_code=400, detail="被阻止的设备无法设为信任")

        await device_service.trust_device(db, device)

        return {"message": "设备已设为信任"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"信任设备失败：{str(e)}")
        raise HTTPException(status_code=500, detail="信任设备失败") from e


@router.post("/devices/block")
async def block_device(
    request: DeviceActionRequest,
    current_user: User = Depends(deps.get_current_user),
    db: AsyncSession = Depends(deps.get_db),
    device_service: DeviceTrustService = Depends(get_device_trust_service),
) -> dict:
    """阻止设备

    Args:
        request: 设备操作请求
        current_user: 当前用户
        db: 数据库会话

    Returns:
        dict: 操作结果
    """
    try:
        query = select(UserDevice).where(
            UserDevice.id == request.device_id, UserDevice.user_id == current_user.id
        )
        result = await db.execute(query)
        device = result.scalar_one_or_none()

        if not device:
            raise HTTPException(status_code=404, detail="设备不存在")

        reason = request.reason or "用户手动阻止"
        await device_service.block_device(device.id, reason, db)

        return {"message": "设备已被阻止"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"阻止设备失败：{str(e)}")
        raise HTTPException(status_code=500, detail="阻止设备失败") from e


@router.post("/devices/unblock")
async def unblock_device(
    request: DeviceActionRequest,
    current_user: User = Depends(deps.get_current_user),
    db: AsyncSession = Depends(deps.get_db),
) -> dict:
    """解除阻止设备

    Args:
        request: 设备操作请求
        current_user: 当前用户
        db: 数据库会话

    Returns:
        dict: 操作结果
    """
    try:
        query = select(UserDevice).where(
            UserDevice.id == request.device_id, UserDevice.user_id == current_user.id
        )
        result = await db.execute(query)
        device = result.scalar_one_or_none()

        if not device:
            raise HTTPException(status_code=404, detail="设备不存在")

        if not device.is_blocked:
            raise HTTPException(status_code=400, detail="设备未被阻止")

        device.is_blocked = False
        device.is_active = True
        device.blocked_reason = None
        device.calculate_trust_score()

        await db.commit()

        return {"message": "设备阻止已解除"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"解除阻止设备失败：{str(e)}")
        raise HTTPException(status_code=500, detail="解除阻止设备失败") from e


@router.delete("/devices/{device_id}")
async def delete_device(
    device_id: int,
    current_user: User = Depends(deps.get_current_user),
    db: AsyncSession = Depends(deps.get_db),
) -> dict:
    """删除设备记录

    Args:
        device_id: 设备ID
        current_user: 当前用户
        db: 数据库会话

    Returns:
        dict: 操作结果
    """
    try:
        query = select(UserDevice).where(
            UserDevice.id == device_id, UserDevice.user_id == current_user.id
        )
        result = await db.execute(query)
        device = result.scalar_one_or_none()

        if not device:
            raise HTTPException(status_code=404, detail="设备不存在")

        await db.delete(device)
        await db.commit()

        return {"message": "设备记录已删除"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除设备记录失败：{str(e)}")
        raise HTTPException(status_code=500, detail="删除设备记录失败") from e


# ==================== 管理员设备管理接口 ====================


@router.get("/admin/devices", response_model=list[DeviceInfo])
async def get_all_devices_admin(
    current_user: User = Depends(deps.get_current_user),
    db: AsyncSession = Depends(deps.get_db),
    user_id: int | None = None,
    is_blocked: bool | None = None,
    risk_level: RiskLevel | None = Query(None, description="风险等级过滤: high/medium/low"),
    skip: int = 0,
    limit: int = 100,
) -> list[DeviceInfo]:
    """获取所有设备列表（管理员）
    
    Args:
        current_user: 当前用户
        db: 数据库会话
        user_id: 按用户ID过滤（可选）
        is_blocked: 按阻止状态过滤（可选）
        risk_level: 按风险等级过滤（可选），支持: high/medium/low
            - high: trust_score <= 30（高风险）
            - medium: 30 < trust_score <= 60（中风险）
            - low: trust_score > 60（低风险）
        skip: 跳过记录数
        limit: 返回记录数上限
    
    Returns:
        list[DeviceInfo]: 设备信息列表
    """
    from app.core.permission_system import (
        Action,
        Permission,
        PermissionChecker,
        ResourceType,
        Scope,
    )

    # 权限检查
    permission_checker = PermissionChecker()
    has_permission = await permission_checker.check_permission(
        db, current_user, Permission(ResourceType.DEVICE, Action.MANAGE, Scope.ALL)
    )
    if not has_permission:
        raise HTTPException(status_code=403, detail="权限不足")

    try:
        # 构建查询条件
        query = select(UserDevice)

        if user_id:
            query = query.where(UserDevice.user_id == user_id)
        if is_blocked is not None:
            query = query.where(UserDevice.is_blocked == is_blocked)
        
        # 根据风险等级过滤（基于 trust_score）
        if risk_level:
            if risk_level == RiskLevel.HIGH:
                # 高风险：trust_score <= 30
                query = query.where(UserDevice.trust_score <= 30)
            elif risk_level == RiskLevel.MEDIUM:
                # 中风险：30 < trust_score <= 60
                query = query.where(UserDevice.trust_score > 30, UserDevice.trust_score <= 60)
            elif risk_level == RiskLevel.LOW:
                # 低风险：trust_score > 60
                query = query.where(UserDevice.trust_score > 60)

        query = query.offset(skip).limit(limit).order_by(UserDevice.created_at.desc())

        result = await db.execute(query)
        devices = result.scalars().all()

        device_list = []
        for device in devices:
            device_info = DeviceInfo(
                id=device.id,
                device_name=device.device_name,
                device_type=device.device_type,
                browser_name=device.browser_name,
                browser_version=device.browser_version,
                os_name=device.os_name,
                os_version=device.os_version,
                ip_address=device.ip_address,
                is_trusted=device.is_trusted,
                is_blocked=device.is_blocked,
                trust_score=device.trust_score,
                login_count=device.login_count,
                last_login_at=device.last_login_at,
                first_login_at=device.first_login_at,
                is_active=device.is_active,
                user_id=device.user_id,
            )
            device_list.append(device_info)

        return device_list

    except Exception as e:
        logger.error(f"获取设备列表失败：{str(e)}")
        raise HTTPException(status_code=500, detail="获取设备列表失败") from e


@router.post("/admin/devices/block")
async def block_device_admin(
    request: DeviceActionRequest,
    current_user: User = Depends(deps.get_current_user),
    db: AsyncSession = Depends(deps.get_db),
    device_service: DeviceTrustService = Depends(get_device_trust_service),
    http_request: Request = None,
) -> dict:
    """封禁设备（管理员）"""
    from app.core.permission_system import (
        Action,
        Permission,
        PermissionChecker,
        ResourceType,
        Scope,
    )
    from app.models.audit_log import AuditAction, AuditResourceType
    from app.services.audit_log_service import AuditLogService

    # 权限检查
    permission_checker = PermissionChecker()
    has_permission = await permission_checker.check_permission(
        db, current_user, Permission(ResourceType.DEVICE, Action.MANAGE, Scope.ALL)
    )
    if not has_permission:
        raise HTTPException(status_code=403, detail="权限不足")

    try:
        # 查找设备
        query = select(UserDevice).where(UserDevice.id == request.device_id)
        result = await db.execute(query)
        device = result.scalar_one_or_none()

        if not device:
            raise HTTPException(status_code=404, detail="设备不存在")

        # 记录操作前的值
        old_values = {
            "is_blocked": device.is_blocked,
            "block_reason": getattr(device, "block_reason", None),
        }

        # 封禁设备
        await device_service.block_device(device.id, request.reason or "管理员封禁", db)

        # 记录审计日志
        await AuditLogService.log_action(
            db=db,
            user=current_user,
            action=AuditAction.BLOCK,
            resource_type=AuditResourceType.DEVICE,
            resource_id=device.id,
            resource_name=f"{device.device_name or 'Unknown'} ({device.ip_address})",
            description="封禁设备",
            reason=request.reason,
            old_values=old_values,
            new_values={"is_blocked": True, "block_reason": request.reason},
            request=http_request,
        )

        logger.info(f"管理员 {current_user.id} 封禁了设备 {device.id}")

        return {"message": "设备封禁成功", "device_id": device.id}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"封禁设备失败：{str(e)}")
        raise HTTPException(status_code=500, detail="封禁设备失败") from e


@router.post("/admin/devices/batch-block")
async def batch_block_devices_admin(
    device_ids: list[int],
    reason: str | None = None,
    current_user: User = Depends(deps.get_current_user),
    db: AsyncSession = Depends(deps.get_db),
    device_service: DeviceTrustService = Depends(get_device_trust_service),
    request: Request = None,
) -> dict:
    """批量封禁设备（管理员）"""
    from app.core.permission_system import (
        Action,
        Permission,
        PermissionChecker,
        ResourceType,
        Scope,
    )
    from app.models.audit_log import AuditAction, AuditResourceType
    from app.services.audit_log_service import AuditLogService

    # 权限检查
    permission_checker = PermissionChecker()
    has_permission = await permission_checker.check_permission(
        db, current_user, Permission(ResourceType.DEVICE, Action.MANAGE, Scope.ALL)
    )
    if not has_permission:
        raise HTTPException(status_code=403, detail="权限不足")

    blocked_count = 0
    blocked_devices = []

    for device_id in device_ids:
        try:
            # 查找设备
            query = select(UserDevice).where(UserDevice.id == device_id)
            result = await db.execute(query)
            device = result.scalar_one_or_none()

            if device and not device.is_blocked:
                await device_service.block_device(device_id, reason or "批量封禁", db)
                blocked_count += 1
                blocked_devices.append(
                    {
                        "id": device_id,
                        "name": device.device_name or "Unknown",
                        "ip": device.ip_address,
                    }
                )

        except Exception as e:
            logger.error(f"封禁设备 {device_id} 失败：{str(e)}")
            continue

    # 记录批量操作审计日志
    if blocked_devices:
        await AuditLogService.log_action(
            db=db,
            user=current_user,
            action=AuditAction.BATCH_UPDATE,
            resource_type=AuditResourceType.DEVICE,
            description=f"批量封禁 {blocked_count} 个设备",
            reason=reason,
            new_values={
                "action": "block",
                "device_ids": device_ids,
                "blocked_count": blocked_count,
                "blocked_devices": blocked_devices,
            },
            request=request,
        )

    logger.info(f"管理员 {current_user.id} 批量封禁了 {blocked_count} 个设备")

    return {"message": f"成功封禁 {blocked_count} 个设备", "blocked_count": blocked_count}


@router.post("/admin/devices/unblock")
async def unblock_device_admin(
    request: DeviceActionRequest,
    current_user: User = Depends(deps.get_current_user),
    db: AsyncSession = Depends(deps.get_db),
    device_service: DeviceTrustService = Depends(get_device_trust_service),
    http_request: Request = None,
) -> dict:
    """解除设备封禁（管理员）"""
    from app.core.permission_system import (
        Action,
        Permission,
        PermissionChecker,
        ResourceType,
        Scope,
    )
    from app.models.audit_log import AuditAction, AuditResourceType
    from app.services.audit_log_service import AuditLogService

    # 权限检查
    permission_checker = PermissionChecker()
    has_permission = await permission_checker.check_permission(
        db, current_user, Permission(ResourceType.DEVICE, Action.MANAGE, Scope.ALL)
    )
    if not has_permission:
        raise HTTPException(status_code=403, detail="权限不足")

    try:
        # 查找设备
        query = select(UserDevice).where(UserDevice.id == request.device_id)
        result = await db.execute(query)
        device = result.scalar_one_or_none()

        if not device:
            raise HTTPException(status_code=404, detail="设备不存在")

        if not device.is_blocked:
            raise HTTPException(status_code=400, detail="设备未被封禁")

        # 记录操作前的值
        old_values = {
            "is_blocked": device.is_blocked,
            "blocked_reason": device.blocked_reason,
        }

        # 解除封禁设备
        await device_service.unblock_device(device.id, db)

        # 记录审计日志
        await AuditLogService.log_action(
            db=db,
            user=current_user,
            action=AuditAction.UPDATE,
            resource_type=AuditResourceType.DEVICE,
            resource_id=device.id,
            resource_name=f"{device.device_name or 'Unknown'} ({device.ip_address})",
            description="解除设备封禁",
            reason=request.reason,
            old_values=old_values,
            new_values={"is_blocked": False, "blocked_reason": None},
            request=http_request,
        )

        logger.info(f"管理员 {current_user.id} 解除了设备 {device.id} 的封禁")

        return {"message": "设备解除封禁成功", "device_id": device.id}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"解除设备封禁失败：{str(e)}")
        raise HTTPException(status_code=500, detail="解除设备封禁失败") from e


@router.post("/admin/devices/batch-unblock")
async def batch_unblock_devices_admin(
    device_ids: list[int],
    reason: str | None = None,
    current_user: User = Depends(deps.get_current_user),
    db: AsyncSession = Depends(deps.get_db),
    device_service: DeviceTrustService = Depends(get_device_trust_service),
    request: Request = None,
) -> dict:
    """批量解除设备封禁（管理员）"""
    from app.core.permission_system import (
        Action,
        Permission,
        PermissionChecker,
        ResourceType,
        Scope,
    )
    from app.models.audit_log import AuditAction, AuditResourceType
    from app.services.audit_log_service import AuditLogService

    # 权限检查
    permission_checker = PermissionChecker()
    has_permission = await permission_checker.check_permission(
        db, current_user, Permission(ResourceType.DEVICE, Action.MANAGE, Scope.ALL)
    )
    if not has_permission:
        raise HTTPException(status_code=403, detail="权限不足")

    unblocked_count = 0
    unblocked_devices = []

    for device_id in device_ids:
        try:
            # 查找设备
            query = select(UserDevice).where(UserDevice.id == device_id)
            result = await db.execute(query)
            device = result.scalar_one_or_none()

            if device and device.is_blocked:
                await device_service.unblock_device(device_id, db)
                unblocked_count += 1
                unblocked_devices.append(
                    {
                        "id": device_id,
                        "name": device.device_name or "Unknown",
                        "ip": device.ip_address,
                    }
                )

        except Exception as e:
            logger.error(f"解除设备 {device_id} 封禁失败：{str(e)}")
            continue

    # 记录批量操作审计日志
    if unblocked_devices:
        await AuditLogService.log_action(
            db=db,
            user=current_user,
            action=AuditAction.BATCH_UPDATE,
            resource_type=AuditResourceType.DEVICE,
            description=f"批量解除 {unblocked_count} 个设备封禁",
            reason=reason,
            new_values={
                "action": "unblock",
                "device_ids": device_ids,
                "unblocked_count": unblocked_count,
                "unblocked_devices": unblocked_devices,
            },
            request=request,
        )

    logger.info(f"管理员 {current_user.id} 批量解除了 {unblocked_count} 个设备封禁")

    return {"message": f"成功解除 {unblocked_count} 个设备封禁", "unblocked_count": unblocked_count}


@router.get("/admin/devices/risk", response_model=list[DeviceInfo])
async def get_risk_devices_admin(
    current_user: User = Depends(deps.get_current_user),
    db: AsyncSession = Depends(deps.get_db),
    max_trust_score: int = 30,
    skip: int = 0,
    limit: int = 50,
) -> list[DeviceInfo]:
    """获取风险设备列表（管理员）- 基于信任分数筛选低信任设备"""
    from app.core.permission_system import (
        Action,
        Permission,
        PermissionChecker,
        ResourceType,
        Scope,
    )

    # 权限检查
    permission_checker = PermissionChecker()
    has_permission = await permission_checker.check_permission(
        db, current_user, Permission(ResourceType.DEVICE, Action.MANAGE, Scope.ALL)
    )
    if not has_permission:
        raise HTTPException(status_code=403, detail="权限不足")

    try:
        # 查询低信任设备（trust_score <= max_trust_score）
        query = (
            select(UserDevice)
            .where(UserDevice.trust_score <= max_trust_score)
            .offset(skip)
            .limit(limit)
            .order_by(UserDevice.trust_score.asc(), UserDevice.created_at.desc())
        )

        result = await db.execute(query)
        devices = result.scalars().all()

        device_list = []
        for device in devices:
            device_info = DeviceInfo(
                id=device.id,
                device_name=device.device_name,
                device_type=device.device_type,
                browser_name=device.browser_name,
                browser_version=device.browser_version,
                os_name=device.os_name,
                os_version=device.os_version,
                ip_address=device.ip_address,
                is_trusted=device.is_trusted,
                is_blocked=device.is_blocked,
                trust_score=device.trust_score,
                login_count=device.login_count,
                last_login_at=device.last_login_at,
                first_login_at=device.first_login_at,
                is_active=device.is_active,
                user_id=device.user_id,
            )
            device_list.append(device_info)

        return device_list

    except Exception as e:
        logger.error(f"获取风险设备列表失败：{str(e)}")
        raise HTTPException(status_code=500, detail="获取风险设备列表失败") from e
