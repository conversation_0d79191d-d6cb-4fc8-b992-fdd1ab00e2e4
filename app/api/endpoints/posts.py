"""沸点相关API端点"""

from collections.abc import Sequence
from typing import Any

from fastapi import APIRouter, Depends, File, HTTPException, Query, Response, UploadFile, status
from pydantic import BaseModel, Field
from sqlalchemy.ext.asyncio import AsyncSession

from app import crud, models
from app.api.deps import get_current_user, get_db
from app.core.pagination import CursorPaginationParams, CursorPaginationResponse
from app.models.comment import CommentType
from app.models.post import PostStatus
from app.models.user import User
from app.schemas.comment import (
    Comment,
    CommentCreate,
    CommentCursorList,
    CommentWithReplies,
    FlatComment,
    FlatCommentList,
    Reply,
)
from app.schemas.like import LikeStatus
from app.schemas.post import (
    PostCreate,
    PostDraftListResponse,
    PostDraftOut,
    PostDraftSave,
    PostListQuery,
    PostOut,
    PostPollSummary,
    PostPollVoteRequest,
    PostsPageResponse,
    PostUpdate,
)
from app.schemas.tag_hot import HotTag
from app.services.content_stats_service import ContentStatsService
from app.services.post_aggregation_service import PostAggregationService
from app.services.post_cache_service import PostCacheService
from app.services.service_factory import (
    get_content_stats_service,
    get_post_aggregation_service,
    get_post_cache_service,
    get_tag_hot_service,
    get_topic_stats_service,
)
from app.services.tag_hot_service import TagHotService
from app.services.topic_stats_service import TopicStatsService

router = APIRouter()


class PostCommentCreatePayload(BaseModel):
    """沸点评论创建请求模型"""

    content: str = Field(..., min_length=1, max_length=500, description="评论内容")
    parent_id: int | None = Field(None, description="父评论ID，用于回复")


def _build_post_comment_response(
    comments_orm: Sequence[models.Comment],
    pagination_result: CursorPaginationResponse,
    like_info: dict[int, dict[str, Any]],
    *,
    sort_by: str,
    flat: bool,
    max_level: int,
) -> CommentCursorList | FlatCommentList:
    """将评论 ORM 对象转换为 API 响应模型。"""

    if flat:
        flat_comments: list[FlatComment] = []
        comment_map = {comment.id: comment for comment in comments_orm}
        reply_counts: dict[int, int] = {}

        for comment in comments_orm:
            if comment.parent_id:
                reply_counts[comment.parent_id] = reply_counts.get(comment.parent_id, 0) + 1

        def get_comment_level_and_path(comment: models.Comment) -> tuple[int, str]:
            if not comment.parent_id:
                return 0, str(comment.id)

            parent = comment_map.get(comment.parent_id)
            if not parent:
                return 1, f"unknown.{comment.id}"

            parent_level, parent_path = get_comment_level_and_path(parent)
            return parent_level + 1, f"{parent_path}.{comment.id}"

        def count_total_replies(comment_id: int) -> int:
            total = reply_counts.get(comment_id, 0)
            for child_comment in comments_orm:
                if child_comment.parent_id == comment_id:
                    total += count_total_replies(child_comment.id)
            return total

        for comment in comments_orm:
            level, path = get_comment_level_and_path(comment)
            if level > max_level:
                continue

            comment_like_info = like_info.get(comment.id, {"like_count": 0, "is_liked": False})
            reply_to_user_id = None
            reply_to_user = None
            if comment.reply_to_id and comment.reply_to_id in comment_map:
                reply_to_comment = comment_map[comment.reply_to_id]
                reply_to_user_id = reply_to_comment.author_id
                reply_to_user = reply_to_comment.author

            flat_comment = FlatComment(
                id=comment.id,
                content=comment.content,
                comment_type=comment.comment_type,
                article_id=comment.article_id,
                video_id=comment.video_id,
                scratch_id=comment.scratch_id,
                post_id=comment.post_id,
                level=level,
                parent_id=comment.parent_id,
                reply_to_id=comment.reply_to_id,
                reply_to_user_id=reply_to_user_id,
                reply_to_user=reply_to_user,
                path=path,
                author_id=comment.author_id,
                author=comment.author,
                like_count=comment_like_info["like_count"],
                is_liked=comment_like_info["is_liked"],
                reply_count=reply_counts.get(comment.id, 0),
                total_reply_count=count_total_replies(comment.id),
                is_visible=comment.is_visible,
                created_at=comment.created_at,
                updated_at=comment.updated_at,
            )
            flat_comments.append(flat_comment)

        if sort_by == "like_count":
            flat_comments.sort(
                key=lambda item: (
                    item.path.count("."),
                    -item.like_count,
                    -item.created_at.timestamp(),
                )
            )
        else:
            flat_comments.sort(
                key=lambda item: (item.path.count("."), -item.created_at.timestamp())
            )

        return FlatCommentList(
            items=flat_comments,
            has_next=pagination_result.has_next,
            has_previous=pagination_result.has_previous,
            next_cursor=pagination_result.next_cursor,
            previous_cursor=pagination_result.previous_cursor,
            total_count=pagination_result.total_count,
        )

    all_comments_map: dict[int, CommentWithReplies | Reply] = {}
    top_level_comments: list[CommentWithReplies] = []

    for comment in comments_orm:
        comment_like_info = like_info.get(comment.id, {"like_count": 0, "is_liked": False})

        if not comment.parent_id:
            comment_dict = {
                "id": comment.id,
                "content": comment.content,
                "comment_type": comment.comment_type,
                "article_id": comment.article_id,
                "video_id": comment.video_id,
                "scratch_id": comment.scratch_id,
                "post_id": comment.post_id,
                "parent_id": comment.parent_id,
                "is_visible": comment.is_visible,
                "author_id": comment.author_id,
                "author": comment.author,
                "created_at": comment.created_at,
                "updated_at": comment.updated_at,
                "like_count": comment_like_info["like_count"],
                "is_liked": comment_like_info["is_liked"],
            }
            pydantic_comment = CommentWithReplies.model_validate(comment_dict)
            pydantic_comment.replies = []
            pydantic_comment.reply_count = 0
            all_comments_map[comment.id] = pydantic_comment
            top_level_comments.append(pydantic_comment)
        else:
            reply_to_user_id = None
            reply_to_user = None
            if comment.reply_to_id:
                for existing in comments_orm:
                    if existing.id == comment.reply_to_id:
                        reply_to_user_id = existing.author_id
                        reply_to_user = existing.author
                        break

            reply_dict = {
                "id": comment.id,
                "content": comment.content,
                "comment_type": comment.comment_type,
                "article_id": comment.article_id,
                "video_id": comment.video_id,
                "scratch_id": comment.scratch_id,
                "post_id": comment.post_id,
                "parent_id": comment.parent_id,
                "reply_to_id": comment.reply_to_id,
                "is_visible": comment.is_visible,
                "author_id": comment.author_id,
                "author": comment.author,
                "created_at": comment.created_at,
                "updated_at": comment.updated_at,
                "like_count": comment_like_info["like_count"],
                "is_liked": comment_like_info["is_liked"],
                "reply_to_user_id": reply_to_user_id,
                "reply_to_user": reply_to_user,
            }
            pydantic_reply = Reply.model_validate(reply_dict)
            all_comments_map[comment.id] = pydantic_reply

    for comment in comments_orm:
        if comment.parent_id:
            parent_comment = all_comments_map.get(comment.parent_id)
            current_reply = all_comments_map.get(comment.id)
            if parent_comment and current_reply:
                if hasattr(parent_comment, "replies"):
                    parent_comment.replies.append(current_reply)
                else:
                    parent_comment.replies = [current_reply]

    def process_comment(comment: CommentWithReplies | Reply) -> None:
        if hasattr(comment, "replies") and comment.replies:
            comment.replies.sort(key=lambda item: item.created_at)
            for reply in comment.replies:
                process_comment(reply)
            if hasattr(comment, "reply_count"):
                comment.reply_count = len(comment.replies)

    for comment in top_level_comments:
        process_comment(comment)

    if sort_by == "like_count":
        top_level_comments.sort(key=lambda item: (item.like_count, item.created_at), reverse=True)
    else:
        top_level_comments.sort(key=lambda item: item.created_at, reverse=True)

    return CommentCursorList(
        items=top_level_comments,
        has_next=pagination_result.has_next,
        has_previous=pagination_result.has_previous,
        next_cursor=pagination_result.next_cursor,
        previous_cursor=pagination_result.previous_cursor,
        total_count=pagination_result.total_count,
    )


@router.post("/{post_id}/comments", response_model=Comment, status_code=status.HTTP_201_CREATED)
async def create_post_comment(
    *,
    db: AsyncSession = Depends(get_db),
    post_id: int,
    payload: PostCommentCreatePayload,
    current_user: User = Depends(get_current_user),
    post_cache_service: PostCacheService = Depends(get_post_cache_service),
    content_stats_service: ContentStatsService = Depends(get_content_stats_service),
) -> None:
    """为指定沸点创建评论或回复。"""

    post = await post_cache_service.get_by_id(db, entity_id=post_id)
    if not post or post.status != PostStatus.PUBLISHED:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="沸点不存在或未发布")

    parent_id = payload.parent_id
    if parent_id:
        parent_comment = await crud.comment.get(db, id=parent_id)
        if not parent_comment:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="父评论不存在")
        if parent_comment.comment_type != CommentType.POST or parent_comment.post_id != post_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="父评论不属于该沸点"
            )

    comment_in = CommentCreate(
        content=payload.content,
        comment_type=CommentType.POST,
        post_id=post_id,
        parent_id=parent_id,
        reply_to_id=parent_id,
        is_visible=True,
    )

    comment = await crud.comment.create(db, obj_in=comment_in, author_id=current_user.id)

    await content_stats_service.update_comment_count(
        content_type="post", content_id=post_id, increment=1
    )

    return comment


@router.get("/{post_id}/comments", response_model=CommentCursorList | FlatCommentList)
async def get_post_comments(
    *,
    db: AsyncSession = Depends(get_db),
    post_id: int,
    cursor: str | None = None,
    size: int = 20,
    sort_by: str = "like_count",
    flat: bool = True,
    max_level: int = 10,
    current_user: User | None = Depends(get_current_user),
    post_cache_service: PostCacheService = Depends(get_post_cache_service),
    content_stats_service: ContentStatsService = Depends(get_content_stats_service),
) -> None:
    """获取指定沸点的评论列表。"""

    post = await post_cache_service.get_by_id(db, entity_id=post_id)
    if not post or post.status != PostStatus.PUBLISHED:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="沸点不存在或未发布")

    if sort_by not in ["like_count", "created_at"]:
        sort_by = "like_count"

    pagination_params = CursorPaginationParams(
        cursor=cursor,
        size=min(size, 100),
        order_by=sort_by,
        order_direction="desc",
    )

    pagination_result = await crud.comment.get_by_post_cursor(
        db, post_id=post_id, params=pagination_params
    )

    comments_orm = pagination_result.items
    comment_ids = [comment_orm.id for comment_orm in comments_orm]
    like_info: dict[int, dict[str, Any]] = {}

    if comment_ids:
        content_items = [("comment", comment_id) for comment_id in comment_ids]
        user_id = current_user.id if current_user else None
        stats_dict = await content_stats_service.batch_get_stats(
            db, content_items=content_items, user_id=user_id
        )
        for (_content_type, content_id), stats in stats_dict.items():
            like_info[content_id] = {
                "like_count": stats.get("like_count", 0),
                "is_liked": stats.get("is_liked_by_user", False),
            }

    return _build_post_comment_response(
        comments_orm,
        pagination_result,
        like_info,
        sort_by=sort_by,
        flat=flat,
        max_level=max_level,
    )


@router.post("/{post_id}/comments/{comment_id}/like", response_model=LikeStatus)
async def toggle_post_comment_like(
    *,
    db: AsyncSession = Depends(get_db),
    post_id: int,
    comment_id: int,
    current_user: User = Depends(get_current_user),
) -> LikeStatus:
    """切换指定帖子下评论的点赞状态。"""

    comment = await crud.comment.get(db, id=comment_id)
    if (
        not comment
        or comment.comment_type != CommentType.POST
        or comment.post_id != post_id
        or not comment.is_visible
    ):
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="评论不存在")

    try:
        _, is_liked = await crud.like.toggle_like(
            db, user_id=current_user.id, content_type="comment", content_id=comment_id
        )
        await db.flush()
        like_count = await crud.like.get_content_like_count(
            db, content_type="comment", content_id=comment_id
        )
        await db.commit()
    except Exception:
        await db.rollback()
        raise

    return LikeStatus(
        content_type="comment",
        content_id=comment_id,
        is_liked=is_liked,
        like_count=like_count,
    )


@router.get("/{post_id}/comments/{comment_id}/like", response_model=LikeStatus)
async def get_post_comment_like_status(
    *,
    db: AsyncSession = Depends(get_db),
    post_id: int,
    comment_id: int,
    current_user: User = Depends(get_current_user),
) -> LikeStatus:
    """获取指定帖子下评论的点赞状态。"""

    comment = await crud.comment.get(db, id=comment_id)
    if (
        not comment
        or comment.comment_type != CommentType.POST
        or comment.post_id != post_id
        or not comment.is_visible
    ):
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="评论不存在")

    is_liked = await crud.like.is_liked_by_user(
        db, user_id=current_user.id, content_type="comment", content_id=comment_id
    )

    like_count = await crud.like.get_content_like_count(
        db, content_type="comment", content_id=comment_id
    )

    return LikeStatus(
        content_type="comment",
        content_id=comment_id,
        is_liked=is_liked,
        like_count=like_count,
    )


@router.delete(
    "/{post_id}/comments/{comment_id}/like",
    status_code=status.HTTP_204_NO_CONTENT,
    response_class=Response,
)
async def unlike_post_comment(
    *,
    db: AsyncSession = Depends(get_db),
    post_id: int,
    comment_id: int,
    current_user: User = Depends(get_current_user),
) -> Response:
    """取消指定帖子下评论的点赞。"""

    comment = await crud.comment.get(db, id=comment_id)
    if (
        not comment
        or comment.comment_type != CommentType.POST
        or comment.post_id != post_id
        or not comment.is_visible
    ):
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="评论不存在")

    is_liked = await crud.like.is_liked_by_user(
        db, user_id=current_user.id, content_type="comment", content_id=comment_id
    )
    if not is_liked:
        return Response(status_code=status.HTTP_204_NO_CONTENT)

    try:
        await crud.like.toggle_like(
            db, user_id=current_user.id, content_type="comment", content_id=comment_id
        )
        await db.flush()
        await db.commit()
    except Exception:
        await db.rollback()
        raise

    return Response(status_code=status.HTTP_204_NO_CONTENT)


@router.post("/", response_model=PostOut, status_code=status.HTTP_201_CREATED)
async def create_post(
    *,
    db: AsyncSession = Depends(get_db),
    post_in: PostCreate,
    current_user: User = Depends(get_current_user),
    post_service: PostAggregationService = Depends(get_post_aggregation_service),
) -> PostOut:
    """
    创建新沸点

    支持的沸点类型：
    - text: 纯文本
    - image: 图片
    - video: 视频
    - link: 链接分享
    - poll: 投票
    - repost: 转发
    """
    # 验证转发的原始沸点是否存在
    if post_in.original_post_id:
        original_post = await post_service.get_post_by_id(
            db, post_id=post_in.original_post_id, current_user_id=current_user.id
        )
        if not original_post:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="原始沸点不存在")

    # 验证投票选项
    if post_in.post_type == "poll":
        if not post_in.poll_options or len(post_in.poll_options) < 2:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="投票至少需要2个选项"
            )
        if len(post_in.poll_options) > 10:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="投票选项不能超过10个"
            )

    post = await post_service.create_post(db, post_in=post_in, author_id=current_user.id)
    if not post:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="沸点创建成功但返回数据构建失败",
        )
    return post


@router.post("/drafts", response_model=PostDraftOut, status_code=status.HTTP_201_CREATED)
async def save_post_draft(
    *,
    db: AsyncSession = Depends(get_db),
    draft_in: PostDraftSave,
    current_user: User = Depends(get_current_user),
    post_service: PostAggregationService = Depends(get_post_aggregation_service),
) -> PostDraftOut:
    """保存或更新沸点草稿"""
    draft = await post_service.save_draft(db, draft_in=draft_in, author_id=current_user.id)
    return draft


@router.get("/drafts", response_model=PostDraftListResponse)
async def list_post_drafts(
    *,
    db: AsyncSession = Depends(get_db),
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页数量"),
    current_user: User = Depends(get_current_user),
    post_service: PostAggregationService = Depends(get_post_aggregation_service),
) -> PostDraftListResponse:
    """获取当前用户的草稿列表"""
    offset = (page - 1) * size
    drafts = await post_service.list_drafts(
        db, author_id=current_user.id, limit=size, offset=offset
    )
    return PostDraftListResponse(items=drafts)


@router.delete(
    "/drafts/{draft_id}", status_code=status.HTTP_204_NO_CONTENT, response_class=Response
)
async def delete_post_draft(
    *,
    db: AsyncSession = Depends(get_db),
    draft_id: int,
    current_user: User = Depends(get_current_user),
    post_service: PostAggregationService = Depends(get_post_aggregation_service),
) -> None:
    """删除草稿"""
    await post_service.delete_draft(db, draft_id=draft_id, author_id=current_user.id)


@router.get("/", response_model=PostsPageResponse)
async def get_posts(
    *,
    db: AsyncSession = Depends(get_db),
    current_user: User | None = Depends(get_current_user),
    post_service: PostAggregationService = Depends(get_post_aggregation_service),
    author_id: int | None = Query(None, description="作者ID"),
    post_type: str | None = Query(None, description="沸点类型"),
    topic: str | None = Query(None, description="话题标签"),
    is_hot: bool | None = Query(None, description="是否热门"),
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页数量"),
    sort_by: str = Query("created_at", description="排序字段"),
    sort_order: str = Query("desc", pattern="^(asc|desc)$", description="排序方向"),
) -> PostsPageResponse:
    """
    获取沸点列表

    支持的排序字段：
    - created_at: 创建时间
    - hot_score: 热度分数
    - like_count: 点赞数
    - comment_count: 评论数
    """
    query_params = PostListQuery(
        author_id=author_id,
        post_type=post_type,
        topic=topic,
        is_hot=is_hot,
        page=page,
        size=size,
        sort_by=sort_by,
        sort_order=sort_order,
    )

    user_id = current_user.id if current_user else None
    posts = await post_service.get_posts(db, query_params=query_params, current_user_id=user_id) or []
    total_count = await crud.post.count_posts_with_filters(
        db,
        author_id=author_id,
        post_type=post_type,
        topic=topic,
        visibility=None,
        is_hot=is_hot,
    ) or 0

    has_next = page * size < total_count
    has_previous = page > 1

    return PostsPageResponse(
        posts=posts,
        total_count=total_count,
        has_next=has_next,
        has_previous=has_previous,
        page=page,
        size=size,
    )


@router.get("/hot", response_model=PostsPageResponse)
async def get_hot_posts(
    *,
    db: AsyncSession = Depends(get_db),
    current_user: User | None = Depends(get_current_user),
    post_service: PostAggregationService = Depends(get_post_aggregation_service),
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页数量"),
) -> PostsPageResponse:
    """获取热门沸点列表"""
    query_params = PostListQuery(
        is_hot=True,
        page=page,
        size=size,
        sort_by="hot_score",
        sort_order="desc",
    )

    user_id = current_user.id if current_user else None
    posts = await post_service.get_posts(db, query_params=query_params, current_user_id=user_id) or []
    total_count = await crud.post.count_posts_with_filters(
        db,
        is_hot=True,
    ) or 0
    has_next = page * size < total_count
    has_previous = page > 1

    return PostsPageResponse(
        posts=posts,
        total_count=total_count,
        has_next=has_next,
        has_previous=has_previous,
        page=page,
        size=size,
    )


@router.get("/following", response_model=PostsPageResponse)
async def get_following_posts(
    *,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
    post_service: PostAggregationService = Depends(get_post_aggregation_service),
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页数量"),
) -> PostsPageResponse:
    """获取关注用户的沸点列表"""
    posts = await post_service.get_following_posts(
        db, user_id=current_user.id, page=page, size=size
    ) or []
    total_count = await crud.post.count_following_posts(db, user_id=current_user.id) or 0
    has_next = page * size < total_count
    has_previous = page > 1

    return PostsPageResponse(
        posts=posts,
        total_count=total_count,
        has_next=has_next,
        has_previous=has_previous,
        page=page,
        size=size,
    )


@router.get("/{post_id}", response_model=PostOut)
async def get_post(
    *,
    db: AsyncSession = Depends(get_db),
    post_id: int,
    current_user: User | None = Depends(get_current_user),
    post_service: PostAggregationService = Depends(get_post_aggregation_service),
) -> PostOut:
    """获取沸点详情"""
    user_id = current_user.id if current_user else None
    post = await post_service.get_post_by_id(db, post_id=post_id, current_user_id=user_id)

    if not post:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="沸点不存在")

    return post


@router.put("/{post_id}", response_model=PostOut)
async def update_post(
    *,
    db: AsyncSession = Depends(get_db),
    post_id: int,
    post_in: PostUpdate,
    current_user: User = Depends(get_current_user),
    post_service: PostAggregationService = Depends(get_post_aggregation_service),
) -> PostOut:
    """更新沸点"""
    # 检查沸点是否存在且属于当前用户
    existing_post = await post_service.get_post_by_id(
        db, post_id=post_id, current_user_id=current_user.id
    )
    if not existing_post:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="沸点不存在")

    if existing_post.author_id != current_user.id:
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="无权限修改此沸点")

    post = await post_service.update_post(
        db, post_id=post_id, post_in=post_in, current_user_id=current_user.id
    )
    return post


@router.delete("/{post_id}", status_code=status.HTTP_204_NO_CONTENT, response_class=Response)
async def delete_post(
    *,
    db: AsyncSession = Depends(get_db),
    post_id: int,
    current_user: User = Depends(get_current_user),
    post_service: PostAggregationService = Depends(get_post_aggregation_service),
) -> None:
    """删除沸点"""
    # 检查沸点是否存在且属于当前用户
    existing_post = await post_service.get_post_by_id(
        db, post_id=post_id, current_user_id=current_user.id
    )
    if not existing_post:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="沸点不存在")

    if existing_post.author_id != current_user.id:
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="无权限删除此沸点")

    await post_service.delete_post(db, post_id=post_id, current_user_id=current_user.id)


@router.post("/{post_id}/like", status_code=status.HTTP_204_NO_CONTENT, response_class=Response)
async def like_post(
    *,
    db: AsyncSession = Depends(get_db),
    post_id: int,
    current_user: User = Depends(get_current_user),
    post_service: PostAggregationService = Depends(get_post_aggregation_service),
) -> None:
    """点赞沸点"""
    await post_service.like_post(db, post_id=post_id, user_id=current_user.id)


@router.delete("/{post_id}/like", status_code=status.HTTP_204_NO_CONTENT, response_class=Response)
async def unlike_post(
    *,
    db: AsyncSession = Depends(get_db),
    post_id: int,
    current_user: User = Depends(get_current_user),
    post_service: PostAggregationService = Depends(get_post_aggregation_service),
) -> None:
    """取消点赞沸点"""
    await post_service.unlike_post(db, post_id=post_id, user_id=current_user.id)


@router.post("/{post_id}/poll/vote", response_model=PostPollSummary)
async def vote_poll(
    *,
    db: AsyncSession = Depends(get_db),
    post_id: int,
    vote_data: PostPollVoteRequest,
    current_user: User = Depends(get_current_user),
    post_service: PostAggregationService = Depends(get_post_aggregation_service),
) -> PostPollSummary:
    """投票"""
    summary = await post_service.vote_poll(
        db, post_id=post_id, user_id=current_user.id, option_indexes=vote_data.option_indexes
    )
    return summary


@router.post("/upload/media", response_model=dict)
async def upload_post_media(
    *,
    db: AsyncSession = Depends(get_db),
    file: UploadFile = File(...),
    current_user: User = Depends(get_current_user),
) -> Any:
    """
    上传沸点媒体文件（图片/视频）

    复用现有的上传服务，返回文件URL供创建沸点时使用
    """
    from app.api.endpoints.upload import handle_single_image_upload
    from app.services.service_factory import get_post_video_upload_service

    # 检查文件类型
    if not file.content_type:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="无法确定文件类型")

    # 支持的媒体类型
    allowed_image_types = ["image/jpeg", "image/png", "image/gif", "image/webp"]
    allowed_video_types = ["video/mp4", "video/webm", "video/ogg"]

    if file.content_type not in allowed_image_types + allowed_video_types:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="不支持的文件类型，仅支持图片（jpg, png, gif, webp）和视频（mp4, webm, avi, mov）",
        )

    # 检查文件大小
    if file.content_type in allowed_image_types:
        max_size = 10 * 1024 * 1024  # 10MB for images
    else:
        max_size = 500 * 1024 * 1024  # 500MB for videos
    file_size = getattr(file, "size", None)
    if file_size and file_size > max_size:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="文件大小超出限制")

    # 复用现有的图片上传处理（对于图片）
    if file.content_type in allowed_image_types:
        upload_result = await handle_single_image_upload(file=file, db=db)
        return {
            "file_url": upload_result.file_url,
            "file_hash": upload_result.file_hash,
            "media_type": "image",
            "width": getattr(upload_result, "width", None),
            "height": getattr(upload_result, "height", None),
        }

    post_video_upload_service = get_post_video_upload_service()
    upload_result = await post_video_upload_service.upload(file=file, db=db)

    return {
        "file_url": upload_result.file_url,
        "file_hash": upload_result.file_hash,
        "media_type": "video",
        "cover_url": upload_result.cover_url,
        "duration": upload_result.duration,
        "width": upload_result.width,
        "height": upload_result.height,
        "size": upload_result.size,
    }


# ==================== 热门话题和标签相关接口 ====================


@router.get("/topics/hot", response_model=dict)
async def get_hot_topics(
    *,
    db: AsyncSession = Depends(get_db),
    topic_stats_service: TopicStatsService = Depends(get_topic_stats_service),
    sort_by: str = Query("hot_score", description="排序字段"),
    sort_order: str = Query("desc", description="排序方向", pattern="^(asc|desc)$"),
    min_post_count: int | None = Query(None, description="最小帖子数量", ge=1),
    days: int | None = Query(None, description="时间范围（天数）", ge=1, le=365),
    page: int = Query(1, description="页码", ge=1),
    size: int = Query(20, description="每页数量", ge=1, le=100),
) -> dict:
    """获取热门话题列表"""
    from app.schemas.topic_stats import TopicListQuery

    query_params = TopicListQuery(
        sort_by=sort_by,
        sort_order=sort_order,
        min_post_count=min_post_count,
        days=days,
        page=page,
        size=size,
    )

    result = await topic_stats_service.get_hot_topics(db, query_params=query_params)
    return result.model_dump()


@router.get("/topics/trending", response_model=dict)
async def get_trending_topics(
    *,
    db: AsyncSession = Depends(get_db),
    topic_stats_service: TopicStatsService = Depends(get_topic_stats_service),
    min_post_count: int | None = Query(None, description="最小帖子数量", ge=1),
    page: int = Query(1, description="页码", ge=1),
    size: int = Query(20, description="每页数量", ge=1, le=100),
) -> dict:
    """获取趋势话题列表"""
    from app.schemas.topic_stats import TopicListQuery

    query_params = TopicListQuery(
        sort_by="trend_score",
        sort_order="desc",
        min_post_count=min_post_count,
        page=page,
        size=size,
    )

    result = await topic_stats_service.get_trending_topics(db, query_params=query_params)
    return result.model_dump()


@router.get("/topics/{topic}/detail", response_model=dict)
async def get_topic_detail(
    *,
    db: AsyncSession = Depends(get_db),
    topic: str,
    topic_stats_service: TopicStatsService = Depends(get_topic_stats_service),
) -> dict:
    """获取话题详情"""
    result = await topic_stats_service.get_topic_detail(db, topic=topic)
    if not result:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="话题不存在")

    return result.model_dump()


@router.get("/topics/{topic}", response_model=PostsPageResponse)
async def get_posts_by_topic(
    *,
    db: AsyncSession = Depends(get_db),
    topic: str,
    current_user: User | None = Depends(get_current_user),
    post_service: PostAggregationService = Depends(get_post_aggregation_service),
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页数量"),
) -> PostsPageResponse:
    """根据话题获取沸点列表

    注意：此路由必须放在所有明确的 /topics/** 静态路由之后，
    以避免 FastAPI 优先匹配参数化路由导致 /topics/hot 等接口无法访问。
    """
    query_params = PostListQuery(
        topic=topic,
        page=page,
        size=size,
        sort_by="created_at",
        sort_order="desc",
    )

    user_id = current_user.id if current_user else None
    posts = await post_service.get_posts(db, query_params=query_params, current_user_id=user_id) or []
    total_count = await crud.post.count_posts_with_filters(
        db,
        topic=topic,
        visibility=None,
    ) or 0
    has_next = page * size < total_count
    has_previous = page > 1

    return PostsPageResponse(
        posts=posts,
        total_count=total_count,
        has_next=has_next,
        has_previous=has_previous,
        page=page,
        size=size,
    )


@router.get("/tags/hot", response_model=list[HotTag])
async def get_hot_tags(
    *,
    db: AsyncSession = Depends(get_db),
    content_type: str = Query("post", description="内容类型"),
    limit: int = Query(20, description="返回数量", ge=1, le=100),
    tag_hot_service: TagHotService = Depends(get_tag_hot_service),
) -> list[HotTag]:
    """获取热门标签列表"""
    items = await tag_hot_service.get_hot_tags(db, content_type=content_type, limit=limit)
    return [HotTag(**item.model_dump()) for item in items]
