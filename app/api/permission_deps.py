from collections.abc import Callable
from typing import Any

from fastapi import Depends, HTTPException, Request, status
from sqlalchemy.ext.asyncio import AsyncSession

from app.api.deps import get_current_user, get_current_user_optional
from app.core.permission_system import Permission, PermissionChecker, ResourceType
from app.db.session import get_db
from app.models.user import User


async def get_resource_by_id(
    db: AsyncSession, resource_type: ResourceType, resource_id: int
) -> Any:
    """根据资源类型和ID获取资源模型实例"""
    from app.models import article, comment, scratch, user, video

    model_map = {
        ResourceType.USER: user.User,
        ResourceType.ARTICLE: article.Article,
        ResourceType.VIDEO: video.Video,
        ResourceType.COMMENT: comment.Comment,
        ResourceType.SCRATCH_PROJECT: scratch.ScratchProduct,
    }
    model = model_map.get(resource_type)
    if not model:
        return None

    return await db.get(model, resource_id)


def require_permission(
    permission: Permission,
    resource_type: ResourceType | None = None,
    resource_id_key: str | None = None,
    optional_user: bool = False,
) -> Callable:
    """
    统一的权限检查依赖工厂。

    Args:
        permission (Permission): 需要的权限对象 (从 app.core.permissions.Permissions 常量中获取)。
        resource_type (ResourceType, optional): 如果需要检查资源所有权或特定于资源的条件，则提供资源类型。
        resource_id_key (str, optional): 从请求路径参数中提取资源ID的键名。
        optional_user (bool, optional): 如果为True，则允许游客访问（如果权限允许）。

    Returns:
        Callable: FastAPI 依赖函数。
    """

    async def dependency(
        request: Request,
        db: AsyncSession = Depends(get_db),
        current_user: User | None = Depends(
            get_current_user_optional if optional_user else get_current_user
        ),
    ) -> User | None:
        """
        实际执行权限检查的内部函数。
        """
        resource = None
        # 如果需要基于资源的检查，则加载资源
        if resource_type and resource_id_key:
            resource_id_str = request.path_params.get(resource_id_key)
            if not resource_id_str or not resource_id_str.isdigit():
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"路径中缺少或无效的资源ID: {resource_id_key}",
                )

            resource = await get_resource_by_id(db, resource_type, int(resource_id_str))
            if not resource:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND, detail=f"{resource_type.value} 未找到"
                )

        # 调用核心检查器
        await PermissionChecker.require_permission(db, current_user, permission, resource)

        return current_user

    return dependency
