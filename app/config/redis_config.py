from pydantic import Field
from pydantic_settings import BaseSettings


class RedisConfig(BaseSettings):
    """Redis和缓存配置"""

    REDIS_MODE: str = Field(
        default="standalone", description="Redis 运行模式 (standalone, sentinel, cluster)"
    )
    REDIS_URL: str = Field(
        default="redis://localhost:6379/0", description="Redis连接URL (standalone模式)"
    )
    # Sentinel 模式配置
    REDIS_SENTINELS: str = Field(
        default="redis-sentinel:26379", description="Redis Sentinel 地址 (逗号分隔)"
    )
    REDIS_SENTINEL_MASTER_NAME: str = Field(
        default="mymaster", description="Redis Sentinel 主节点名称"
    )
    REDIS_MAX_CONNECTIONS: int = Field(default=20, description="Redis连接池最大连接数")
    REDIS_SOCKET_CONNECT_TIMEOUT: int = Field(default=5, description="Redis连接超时(秒)")
    REDIS_SOCKET_TIMEOUT: int = Field(default=5, description="Redis操作超时(秒)")
    REDIS_HEALTH_CHECK_INTERVAL: int = Field(default=30, description="Redis健康检查间隔(秒)")
    RECOMMENDATION_CACHE_ENABLED: bool = Field(default=True, description="是否启用推荐缓存")
    RECOMMENDATION_USER_QUEUE_EXPIRE_SECONDS: int = Field(
        default=1800, description="用户推荐队列缓存过期时间（秒），默认30分钟"
    )
    RECOMMENDATION_CACHE_EXPIRE_SECONDS: int = Field(
        default=3600, description="推荐结果缓存过期时间（秒），默认1小时"
    )
    RECOMMENDATION_NULL_CACHE_EXPIRE_SECONDS: int = Field(
        default=300, description="推荐空结果缓存过期时间（秒），默认5分钟"
    )
    RECOMMENDATION_CACHE_JITTER_SECONDS: int = Field(
        default=600, description="推荐缓存过期时间的随机抖动范围（秒），默认10分钟"
    )
    RECOMMENDATION_CONCURRENCY_LIMIT: int = Field(
        default=10, description="推荐系统并发查询限制，默认10个并发"
    )
    # 视频缓存设置
    VIDEO_CACHE_ENABLED: bool = Field(default=True, description="是否启用视频详情缓存")
    VIDEO_CACHE_EXPIRE_SECONDS: int = Field(
        default=3600, description="视频详情缓存过期时间（秒），默认1小时"
    )
    CONTENT_STATS_CACHE_EXPIRE_SECONDS: int = Field(
        default=60 * 60 * 24 * 7, description="内容统计信息缓存过期时间（秒），默认7天"
    )

    # 文章缓存设置
    ARTICLE_CACHE_ENABLED: bool = Field(default=True, description="是否启用文章详情缓存")
    ARTICLE_CACHE_EXPIRE_SECONDS: int = Field(
        default=3600, description="文章详情缓存过期时间（秒），默认1小时"
    )

    # 用户缓存设置
    USER_CACHE_ENABLED: bool = Field(default=True, description="是否启用用户详情缓存")
    USER_CACHE_EXPIRE_SECONDS: int = Field(
        default=3600, description="用户详情缓存过期时间（秒），默认1小时"
    )
    USER_CACHE_WARMUP_DAYS: int = Field(
        default=7, description="缓存预热天数，预热最近N天有高价值互动的用户"
    )
    USER_CACHE_WARMUP_BATCH_SIZE: int = Field(
        default=500, description="缓存预热时，从数据库批量读取用户的批处理大小"
    )

    # 布隆过滤器设置 (用于防止缓存穿透)
    USER_BLOOM_FILTER_KEY: str = Field(
        default="bf:user:ids", description="用户ID布隆过滤器的Redis Key"
    )
    USER_BLOOM_FILTER_ERROR_RATE: float = Field(
        default=0.001, description="用户ID布隆过滤器的错误率"
    )
    USER_BLOOM_FILTER_INITIAL_CAPACITY: int = Field(
        default=2000000, description="用户ID布隆过滤器的初始容量"
    )
    VIDEO_BLOOM_FILTER_KEY: str = Field(
        default="bf:video:ids", description="视频ID布隆过滤器的Redis Key"
    )
    VIDEO_BLOOM_FILTER_ERROR_RATE: float = Field(
        default=0.001, description="视频ID布隆过滤器的错误率"
    )
    VIDEO_BLOOM_FILTER_INITIAL_CAPACITY: int = Field(
        default=1000000, description="视频ID布隆过滤器的初始容量"
    )
    ARTICLE_BLOOM_FILTER_KEY: str = Field(
        default="bf:article:ids", description="文章ID布隆过滤器的Redis Key"
    )
    ARTICLE_BLOOM_FILTER_ERROR_RATE: float = Field(
        default=0.001, description="文章ID布隆过滤器的错误率"
    )
    ARTICLE_BLOOM_FILTER_INITIAL_CAPACITY: int = Field(
        default=500000, description="文章ID布隆过滤器的初始容量"
    )
    POST_BLOOM_FILTER_KEY: str = Field(
        default="bf:post:ids", description="沸点ID布隆过滤器的Redis Key"
    )
    POST_BLOOM_FILTER_ERROR_RATE: float = Field(
        default=0.001, description="沸点ID布隆过滤器的错误率"
    )
    POST_BLOOM_FILTER_INITIAL_CAPACITY: int = Field(
        default=500000, description="沸点ID布隆过滤器的初始容量"
    )
    NULL_CACHE_EXPIRE_SECONDS: int = Field(
        default=60, description="空值缓存的过期时间（秒），用于防止缓存穿透"
    )
    CACHE_EXPIRE_JITTER_SECONDS: int = Field(
        default=300, description="缓存过期时间的随机抖动范围（秒），用于防止缓存雪崩"
    )
