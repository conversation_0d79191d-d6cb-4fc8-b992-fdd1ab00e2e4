from pydantic import Field
from pydantic_settings import BaseSettings


class StorageConfig(BaseSettings):
    """对象存储和文件配置"""

    # 阿里云OSS配置
    OSS_ENDPOINT: str = Field(default="your-oss-endpoint", description="OSS Endpoint")
    OSS_REGION: str = Field(default="your-oss-region", description="OSS Region")
    OSS_BUCKET_NAME: str = Field(default="your-oss-bucket-name", description="OSS Bucket名称")

    # OSS认证配置 - 通过环境变量设置
    # ALIBABA_CLOUD_ACCESS_KEY_ID: 阿里云AccessKey ID
    # ALIBABA_CLOUD_ACCESS_KEY_SECRET: 阿里云AccessKey Secret
    OSS_MULTIPART_THRESHOLD: int = Field(
        default=5 * 1024 * 1024, description="OSS分片上传阈值（字节）"
    )
    OSS_PART_SIZE: int = Field(default=1 * 1024 * 1024, description="OSS分片大小（字节）")
    OSS_CDN_DOMAIN: str = Field(default="your-oss-cdn-domain", description="OSS CDN域名")
    OSS_PRIVATE_BUCKET: bool = Field(default=True, description="OSS Bucket是否为私有访问")
    OSS_SIGN_URL_EXPIRE: int = Field(default=300, description="签名URL有效期（秒）")
    OSS_SIGN_URL_USE_CDN: bool = Field(
        default=True, description="签名URL是否替换为CDN域名"
    )
    OSS_SIGN_URL_CACHE_ENABLED: bool = Field(
        default=False, description="是否缓存已生成的签名URL"
    )
    OSS_SIGN_URL_CACHE_PADDING: int = Field(
        default=5, description="缓存过期安全差值（秒），应小于签名有效期"
    )
    OSS_SIGN_URL_MAX_BATCH: int = Field(
        default=50, description="单次签名生成的最大资源数量"
    )
    FILE_HASH_EXPIRE: int = Field(
        default=7 * 24 * 3600, description="文件哈希缓存过期时间（秒），默认7天"
    )

    # 本地文件存储配置
    UPLOAD_DIR: str = Field(default="./static", description="本地文件上传目录")
    MAX_FILE_SIZE: int = Field(
        default=10 * 1024 * 1024, description="最大文件大小（字节），默认10MB"
    )
