from pydantic import Field
from pydantic_settings import BaseSettings


class AppConfig(BaseSettings):
    """应用配置设置"""

    MODE: str = Field(default="dev", description="应用运行模式")
    API_V1_STR: str = "/api/v1"
    PROJECT_NAME: str = "Steam游戏数据聚合API"
    CACHE_SINGLE_GET_FALLBACK: bool = Field(
        default=True,
        description="当 Bloom 判定不存在时，单条读取允许兜底一次 DB 查询并回填（默认关闭）",
    )
    WS_AUTH_DEBUG_ENABLED: bool = Field(
        default=False,
        description="是否启用 WebSocket 鉴权调试日志（仅排障时开启）",
    )
