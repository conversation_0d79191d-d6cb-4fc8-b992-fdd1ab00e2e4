"""应用配置中心"""

from functools import lru_cache

from app.config.api_keys_config import ApiKeysConfig
from app.config.app_config import AppConfig
from app.config.auth_config import AuthConfig
from app.config.celery_config import CeleryConfig
from app.config.db_config import DbConfig
from app.config.init_config import InitConfig
from app.config.post_config import PostConfig
from app.config.redis_config import RedisConfig
from app.config.storage_config import StorageConfig


class Settings(
    AppConfig,
    DbConfig,
    RedisConfig,
    CeleryConfig,
    StorageConfig,
    ApiKeysConfig,
    InitConfig,
    AuthConfig,
    PostConfig,
):
    """
    应用配置设置
    通过多重继承将所有配置模块组合在一起
    """

    def model_post_init(self, __context) -> None:
        """在模型初始化后动态配置Celery的URL"""
        if self.REDIS_MODE == "sentinel":
            sentinels = [f"sentinel://{s.strip()}" for s in self.REDIS_SENTINELS.split(",")]
            sentinel_url = ";".join(sentinels)

            # 为Broker使用Sentinel，但Backend使用直连Redis避免事件循环冲突
            self.CELERY_BROKER_URL = f"{sentinel_url}/1"
            # 使用直连Redis作为结果后端，避免与FastAPI事件循环冲突
            self.CELERY_RESULT_BACKEND = "redis://***********:6379/2"

    class Config:
        env_file = [".env", ".env.dev"]
        env_file_encoding = "utf-8"
        case_sensitive = True
        extra = "ignore"  # 忽略额外的字段


# 创建设置实例
settings = Settings()


@lru_cache
def get_settings() -> Settings:
    return Settings()
