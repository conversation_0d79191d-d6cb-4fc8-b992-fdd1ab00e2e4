from pydantic import Field
from pydantic_settings import BaseSettings


class PostConfig(BaseSettings):
    """沸点功能配置"""

    # 缓存配置
    POST_CACHE_ENABLED: bool = Field(default=True, description="是否启用沸点缓存")
    POST_CACHE_EXPIRE_SECONDS: int = Field(default=3600, description="沸点缓存过期时间（秒）")
    POST_HOT_CACHE_EXPIRE_SECONDS: int = Field(default=300, description="热门沸点缓存过期时间（秒）")
    POST_TOPIC_CACHE_EXPIRE_SECONDS: int = Field(default=600, description="话题沸点缓存过期时间（秒）")
    
    # 内容限制
    POST_CONTENT_MAX_LENGTH: int = Field(default=500, description="沸点内容最大长度")
    POST_TOPIC_MAX_LENGTH: int = Field(default=100, description="话题标签最大长度")
    POST_LOCATION_MAX_LENGTH: int = Field(default=200, description="位置信息最大长度")
    POST_REPOST_COMMENT_MAX_LENGTH: int = Field(default=200, description="转发评论最大长度")
    
    # 媒体文件限制
    POST_MAX_MEDIA_FILES: int = Field(default=9, description="单个沸点最大媒体文件数")
    POST_MAX_IMAGE_SIZE: int = Field(default=10 * 1024 * 1024, description="图片最大大小（字节）")
    POST_MAX_VIDEO_SIZE: int = Field(default=100 * 1024 * 1024, description="视频最大大小（字节）")
    POST_MAX_VIDEO_DURATION: int = Field(default=300, description="视频最大时长（秒）")
    
    # 投票功能限制
    POST_POLL_MIN_OPTIONS: int = Field(default=2, description="投票最少选项数")
    POST_POLL_MAX_OPTIONS: int = Field(default=10, description="投票最多选项数")
    POST_POLL_OPTION_MAX_LENGTH: int = Field(default=100, description="投票选项最大长度")
    POST_POLL_MAX_DURATION_HOURS: int = Field(default=168, description="投票最大持续时间（小时）")
    
    # @提及限制
    POST_MAX_MENTIONS: int = Field(default=10, description="单个沸点最大@提及数")
    POST_MENTION_TEXT_MAX_LENGTH: int = Field(default=50, description="@提及文本最大长度")
    
    # 链接分享限制
    POST_LINK_TITLE_MAX_LENGTH: int = Field(default=200, description="链接标题最大长度")
    POST_LINK_DESCRIPTION_MAX_LENGTH: int = Field(default=500, description="链接描述最大长度")
    POST_LINK_URL_MAX_LENGTH: int = Field(default=500, description="链接URL最大长度")
    
    # 热度计算配置
    POST_HOT_SCORE_LIKE_WEIGHT: float = Field(default=1.0, description="点赞权重")
    POST_HOT_SCORE_COMMENT_WEIGHT: float = Field(default=2.0, description="评论权重")
    POST_HOT_SCORE_REPOST_WEIGHT: float = Field(default=3.0, description="转发权重")
    POST_HOT_SCORE_VIEW_WEIGHT: float = Field(default=0.1, description="浏览权重")
    POST_HOT_SCORE_TIME_DECAY: float = Field(default=0.8, description="时间衰减因子")
    POST_HOT_THRESHOLD: int = Field(default=100, description="热门沸点阈值")
    
    # 分页配置
    POST_DEFAULT_PAGE_SIZE: int = Field(default=20, description="默认分页大小")
    POST_MAX_PAGE_SIZE: int = Field(default=100, description="最大分页大小")
    
    # 审核配置
    POST_AUTO_REVIEW_ENABLED: bool = Field(default=True, description="是否启用自动审核")
    POST_SENSITIVE_WORDS_CHECK: bool = Field(default=True, description="是否检查敏感词")
    POST_SPAM_DETECTION_ENABLED: bool = Field(default=True, description="是否启用垃圾内容检测")
    
    # 通知配置
    POST_MENTION_NOTIFICATION_ENABLED: bool = Field(default=True, description="是否启用@提及通知")
    POST_LIKE_NOTIFICATION_ENABLED: bool = Field(default=True, description="是否启用点赞通知")
    POST_COMMENT_NOTIFICATION_ENABLED: bool = Field(default=True, description="是否启用评论通知")
    POST_REPOST_NOTIFICATION_ENABLED: bool = Field(default=True, description="是否启用转发通知")
    
    # 推荐配置
    POST_RECOMMENDATION_ENABLED: bool = Field(default=True, description="是否启用沸点推荐")
    POST_TRENDING_TOPICS_COUNT: int = Field(default=10, description="热门话题数量")
    POST_TRENDING_TOPICS_CACHE_SECONDS: int = Field(default=1800, description="热门话题缓存时间（秒）")
    
    # 搜索配置
    POST_SEARCH_ENABLED: bool = Field(default=True, description="是否启用沸点搜索")
    POST_SEARCH_MIN_QUERY_LENGTH: int = Field(default=2, description="搜索查询最小长度")
    POST_SEARCH_MAX_RESULTS: int = Field(default=100, description="搜索结果最大数量")
    
    # 统计配置
    POST_STATS_UPDATE_INTERVAL: int = Field(default=300, description="统计数据更新间隔（秒）")
    POST_VIEW_COUNT_ENABLED: bool = Field(default=True, description="是否启用浏览计数")
    POST_VIEW_COUNT_CACHE_SECONDS: int = Field(default=60, description="浏览计数缓存时间（秒）")
    
    # 导出配置
    POST_EXPORT_ENABLED: bool = Field(default=True, description="是否启用沸点导出")
    POST_EXPORT_MAX_COUNT: int = Field(default=1000, description="单次导出最大数量")
    POST_EXPORT_FORMATS: list[str] = Field(default=["json", "csv"], description="支持的导出格式")
    
    # 备份配置
    POST_BACKUP_ENABLED: bool = Field(default=True, description="是否启用沸点备份")
    POST_BACKUP_INTERVAL_HOURS: int = Field(default=24, description="备份间隔（小时）")
    POST_BACKUP_RETENTION_DAYS: int = Field(default=30, description="备份保留天数")
    
    # 性能配置
    POST_BULK_INSERT_BATCH_SIZE: int = Field(default=100, description="批量插入批次大小")
    POST_BULK_UPDATE_BATCH_SIZE: int = Field(default=50, description="批量更新批次大小")
    POST_CONCURRENT_REQUESTS_LIMIT: int = Field(default=10, description="并发请求限制")
    
    # 安全配置
    POST_RATE_LIMIT_PER_MINUTE: int = Field(default=30, description="每分钟发布限制")
    POST_RATE_LIMIT_PER_HOUR: int = Field(default=100, description="每小时发布限制")
    POST_DUPLICATE_CHECK_ENABLED: bool = Field(default=True, description="是否启用重复内容检查")
    POST_DUPLICATE_CHECK_WINDOW_HOURS: int = Field(default=24, description="重复检查时间窗口（小时）")
    
    # 调试配置
    POST_DEBUG_MODE: bool = Field(default=False, description="是否启用调试模式")
    POST_LOG_LEVEL: str = Field(default="INFO", description="日志级别")
    POST_METRICS_ENABLED: bool = Field(default=True, description="是否启用指标收集")
