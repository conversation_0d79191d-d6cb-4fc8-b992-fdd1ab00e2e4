"""布隆过滤器服务模块"""

import logging

from app.config import settings
from app.db.redis import (
    bloom_filter_add,
    bloom_filter_exists,
    bloom_filter_madd,
    bloom_filter_mexists,
    bloom_filter_reserve,
    key_exists,
)

logger = logging.getLogger(__name__)


class BloomFilterService:
    """
    基于 RedisBloom 的通用布隆过滤器服务
    """

    def __init__(
        self,
        filter_key: str,
        error_rate: float = 0.001,
        initial_capacity: int = 1000000,
    ):
        """
        初始化布隆过滤器服务
        :param filter_key: 在 Redis 中存储布隆过滤器的键
        :param error_rate: 期望的错误率
        :param initial_capacity: 初始容量
        """
        self.filter_key = filter_key
        self.error_rate = error_rate
        self.initial_capacity = initial_capacity

    async def _reserve_filter_if_not_exists(self) -> None:
        """如果过滤器不存在，则创建并保留它"""
        try:
            exists = await key_exists(self.filter_key)
            if not exists:
                logger.info(f"布隆过滤器 '{self.filter_key}' 不存在。正在创建...")
                await bloom_filter_reserve(self.filter_key, self.error_rate, self.initial_capacity)
                logger.info(
                    f"布隆过滤器 '{self.filter_key}' 已创建，容量: {self.initial_capacity}, 错误率: {self.error_rate}"
                )
        except Exception as e:
            logger.error(f"创建或检查布隆过滤器 '{self.filter_key}' 失败: {e}")

    async def add(self, item: str) -> bool:
        """向布隆过滤器添加一个元素"""
        try:
            result = await bloom_filter_add(self.filter_key, item)
            return bool(result)
        except Exception as e:
            logger.error(f"向布隆过滤器 '{self.filter_key}' 添加元素 '{item}' 失败: {e}")
            return False

    async def exists(self, item: str) -> bool:
        """检查一个元素是否存在于布隆过滤器中"""
        try:
            result = await bloom_filter_exists(self.filter_key, item)
            return bool(result)
        except Exception as e:
            logger.error(f"从布隆过滤器 '{self.filter_key}' 检查元素 '{item}' 失败: {e}")
            return True

    async def add_multi(self, items: list[str]) -> list[bool]:
        """批量向布隆过滤器添加元素"""
        if not items:
            return []
        try:
            results = await bloom_filter_madd(self.filter_key, *items)
            return [bool(r) for r in results]
        except Exception as e:
            logger.error(f"向布隆过滤器 '{self.filter_key}' 批量添加元素失败: {e}")
            return [False] * len(items)

    async def exists_multi(self, items: list[str]) -> list[bool]:
        """批量检查元素是否存在于布隆过滤器中"""
        if not items:
            return []
        try:
            results = await bloom_filter_mexists(self.filter_key, *items)
            return [bool(r) for r in results]
        except Exception as e:
            logger.error(f"从布隆过滤器 '{self.filter_key}' 批量检查元素失败: {e}")
            # 在失败的情况下，我们假设它们都存在，以避免冲击数据库（优雅降级）
            return [True] * len(items)


# --- 实例化的服务 ---
# 视频ID布隆过滤器
video_bloom_filter = BloomFilterService(
    filter_key=settings.VIDEO_BLOOM_FILTER_KEY,
    error_rate=settings.VIDEO_BLOOM_FILTER_ERROR_RATE,
    initial_capacity=settings.VIDEO_BLOOM_FILTER_INITIAL_CAPACITY,
)

# 文章ID布隆过滤器
article_bloom_filter = BloomFilterService(
    filter_key=settings.ARTICLE_BLOOM_FILTER_KEY,
    error_rate=settings.ARTICLE_BLOOM_FILTER_ERROR_RATE,
    initial_capacity=settings.ARTICLE_BLOOM_FILTER_INITIAL_CAPACITY,
)

# 沸点ID布隆过滤器
post_bloom_filter = BloomFilterService(
    filter_key=settings.POST_BLOOM_FILTER_KEY,
    error_rate=settings.POST_BLOOM_FILTER_ERROR_RATE,
    initial_capacity=settings.POST_BLOOM_FILTER_INITIAL_CAPACITY,
)

# 用户ID布隆过滤器
user_bloom_filter = BloomFilterService(
    filter_key=settings.USER_BLOOM_FILTER_KEY,
    error_rate=settings.USER_BLOOM_FILTER_ERROR_RATE,
    initial_capacity=settings.USER_BLOOM_FILTER_INITIAL_CAPACITY,
)
