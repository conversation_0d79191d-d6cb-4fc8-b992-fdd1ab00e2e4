"""媒体文件探测工具"""

from __future__ import annotations

import asyncio
from dataclasses import dataclass

import ffmpeg


@dataclass(slots=True)
class MediaMetadata:
    """视频/音频基础元数据"""

    duration: int | None
    width: int | None
    height: int | None
    codec_name: str | None
    codec_long_name: str | None


async def probe_media_metadata(file_path: str) -> MediaMetadata | None:
    """使用 ffprobe 提取媒体元数据"""

    try:
        probe = await asyncio.to_thread(ffmpeg.probe, file_path)
    except ffmpeg.Error:
        return None

    format_info = probe.get("format", {})
    duration = format_info.get("duration")
    duration_int = int(float(duration)) if duration else None

    video_stream = None
    for stream in probe.get("streams", []):
        if stream.get("codec_type") == "video":
            video_stream = stream
            break

    width = int(video_stream["width"]) if video_stream and video_stream.get("width") else None
    height = int(video_stream["height"]) if video_stream and video_stream.get("height") else None
    codec_name = video_stream.get("codec_name") if video_stream else None
    codec_long_name = video_stream.get("codec_long_name") if video_stream else None

    return MediaMetadata(
        duration=duration_int,
        width=width,
        height=height,
        codec_name=codec_name,
        codec_long_name=codec_long_name,
    )
