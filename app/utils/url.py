"""URL 相关辅助函数。"""

from __future__ import annotations

from typing import Iterable

from app.config import get_settings


_SETTINGS = get_settings()


def _is_absolute_url(url: str) -> bool:
    return url.startswith("http://") or url.startswith("https://")


def _needs_leading_slash(url: str, cdn_domain: str) -> str:
    if not url:
        return url
    if url.startswith("/"):
        return url
    if url.startswith(cdn_domain):
        return url
    return f"/{url}"


def prepend_cdn(
    url: str | None,
    *,
    cdn_domain: str | None = None,
    prefixes: Iterable[str] | None = None,
) -> str | None:
    if url is None:
        return None

    text = url.strip()
    if not text:
        return None

    if _is_absolute_url(text):
        return text

    domain = cdn_domain or _SETTINGS.OSS_CDN_DOMAIN or ""
    if not domain:
        return text

    normalized = text

    candidate_prefixes = list(prefixes or [])
    candidate_prefixes.extend(["/", "steam/"])
    if any(normalized.startswith(prefix) for prefix in candidate_prefixes):
        normalized = normalized.lstrip("/")

    return domain + _needs_leading_slash(normalized, domain)


def prepend_cdn_list(urls: list[str] | None, *, cdn_domain: str | None = None, prefixes: Iterable[str] | None = None) -> list[str] | None:
    if urls is None:
        return None
    return [prepend_cdn(item, cdn_domain=cdn_domain, prefixes=prefixes) for item in urls]
