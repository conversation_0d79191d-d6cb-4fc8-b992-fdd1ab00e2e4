import time
from datetime import UTC, datetime, timedelta

from sqlalchemy import delete, select

from app import crud
from app.core.async_runner import run_async
from app.core.celery import OUTBOX_QUEUE_NAME, app
from app.core.logging import logger
from app.db.session import SessionLocal, SyncSessionLocal
from app.models.outbox import OutboxMessage
from app.services.service_factory import (
    get_article_cache_service,
    get_post_cache_service,
    get_user_cache_service,
    get_video_cache_service,
)
from app.utils.bloom_filters import article_bloom_filter


@app.task(
    autoretry_for=(Exception,),
    max_retries=3,
    retry_backoff=True,
    retry_backoff_max=60,
    retry_jitter=True,
)
async def task_update_user_cache(user_id: int):
    """
    异步任务：从数据库获取最新的用户信息并更新到缓存中。
    """
    logger.info(f"开始执行用户缓存更新任务，user_id: {user_id}")
    db = SessionLocal()
    user_cache_service = get_user_cache_service()
    try:
        user = await crud.user.get(db, id=user_id)
        if user:
            # 调用带有版本检查的set方法
            await user_cache_service.set_user(user, check_version=True)
            logger.info(f"成功更新用户缓存，user_id: {user_id}, version: {user.cache_version}")
        else:
            # 如果用户已不存在，则写入空缓存
            await user_cache_service.set_null_cache(user_id)
            logger.warning(f"用户不存在，已写入空缓存，user_id: {user_id}")
    finally:
        await db.close()


@app.task(
    autoretry_for=(Exception,),
    max_retries=3,
    retry_backoff=True,
    retry_backoff_max=60,
    retry_jitter=True,
)
async def task_invalidate_user_cache(user_id: int):
    """
    异步任务：从缓存中删除指定用户的信息。
    """
    logger.info(f"开始执行用户缓存失效任务，user_id: {user_id}")
    user_cache_service = get_user_cache_service()
    await user_cache_service.invalidate_user(user_id)
    logger.info(f"成功使缓存失效，user_id: {user_id}")


@app.task(
    autoretry_for=(Exception,),
    max_retries=3,
    retry_backoff=True,
    retry_backoff_max=60,
    retry_jitter=True,
)
async def task_update_article_cache(article_id: int):
    """
    异步任务：从数据库获取最新的文章信息并更新到缓存中。
    """
    logger.info(f"开始执行文章缓存更新任务，article_id: {article_id}")
    db = SessionLocal()
    article_cache_service = get_article_cache_service()
    try:
        article = await crud.article.get(db, id=article_id)
        if article and not article.is_deleted:
            await article_cache_service.set_article(article, check_version=True)
            # 将文章ID写入 Bloom（不可删除，增量维护）
            try:
                await article_bloom_filter._reserve_filter_if_not_exists()
                await article_bloom_filter.add(str(article_id))
            except Exception as e:
                logger.warning(f"写入文章Bloom失败 article_id={article_id}: {e}")
            logger.info(
                f"成功更新文章缓存，article_id: {article_id}, version: {article.cache_version}"
            )
        else:
            # 如果文章不存在或已删除，则写入空缓存
            await article_cache_service.set_null_cache(article_id)
            logger.warning(f"文章不存在或已删除，已写入空缓存，article_id: {article_id}")
    finally:
        await db.close()


@app.task(
    autoretry_for=(Exception,),
    max_retries=3,
    retry_backoff=True,
    retry_backoff_max=60,
    retry_jitter=True,
)
async def task_invalidate_article_cache(article_id: int):
    """
    异步任务：从缓存中删除指定文章的信息。
    """
    logger.info(f"开始执行文章缓存失效任务，article_id: {article_id}")
    article_cache_service = get_article_cache_service()
    await article_cache_service.invalidate_article(article_id)
    logger.info(f"成功使文章缓存失效，article_id: {article_id}")


@app.task(
    autoretry_for=(Exception,),
    max_retries=3,
    retry_backoff=True,
    retry_backoff_max=60,
    retry_jitter=True,
)
async def task_update_video_cache(video_id: int):
    """
    异步任务：从数据库获取最新的视频信息并更新到缓存中。
    """
    logger.info(f"开始执行视频缓存更新任务，video_id: {video_id}")
    db = SessionLocal()
    video_cache_service = get_video_cache_service()
    try:
        video = await crud.video.get(db, id=video_id)
        if video and not video.is_deleted:
            await video_cache_service.set_entity(video, check_version=True)
            logger.info(f"成功更新视频缓存，video_id: {video_id}, version: {video.cache_version}")
        else:
            # 如果视频不存在或已删除，则写入空缓存
            await video_cache_service.set_null_cache(video_id)
            logger.warning(f"视频不存在或已删除，已写入空缓存，video_id: {video_id}")
    finally:
        await db.close()


@app.task(
    autoretry_for=(Exception,),
    max_retries=3,
    retry_backoff=True,
    retry_backoff_max=60,
    retry_jitter=True,
)
async def task_invalidate_video_cache(video_id: int):
    """
    异步任务：从缓存中删除指定视频的信息。
    """
    logger.info(f"开始执行视频缓存失效任务，video_id: {video_id}")
    video_cache_service = get_video_cache_service()
    await video_cache_service.invalidate_entity(video_id)
    logger.info(f"成功使视频缓存失效，video_id: {video_id}")


@app.task(
    autoretry_for=(Exception,),
    max_retries=3,
    retry_backoff=True,
    retry_backoff_max=60,
    retry_jitter=True,
)
def task_handle_post_cache_event(payload: dict):
    """
    处理沸点相关的缓存失效事件。
    """

    async def _handle():
        event = payload.get("event")
        post_id = payload.get("post_id")
        author_id = payload.get("author_id")
        topic = payload.get("topic")
        previous_topic = payload.get("previous_topic")

        logger.info(
            "开始处理沸点缓存事件 event={} post_id={} author_id={} topic={} previous_topic={}",
            event,
            post_id,
            author_id,
            topic,
            previous_topic,
        )

        post_cache_service = get_post_cache_service()

        if author_id:
            await post_cache_service.invalidate_author_cache(author_id)

        topics_to_invalidate = {topic, previous_topic}
        for topic_value in {t for t in topics_to_invalidate if t}:
            await post_cache_service.invalidate_topic_cache(topic_value)

        if event == "deleted":
            await post_cache_service.invalidate_hot_cache()

        logger.info("沸点缓存事件处理完成 event={} post_id={}", event, post_id)

    run_async(_handle())


# 映射主题到任务
TASK_MAP = {
    "user.created": "app.tasks.cache_tasks.task_update_user_cache",
    "user.updated": "app.tasks.cache_tasks.task_update_user_cache",
    "user.deleted": "app.tasks.cache_tasks.task_invalidate_user_cache",
    "stats.update": "app.tasks.stats_tasks.task_update_stats",
    "article.created": "app.tasks.cache_tasks.task_update_article_cache",
    "article.updated": "app.tasks.cache_tasks.task_update_article_cache",
    "article.deleted": "app.tasks.cache_tasks.task_invalidate_article_cache",
    "video.created": "app.tasks.cache_tasks.task_update_video_cache",
    "video.updated": "app.tasks.cache_tasks.task_update_video_cache",
    "video.deleted": "app.tasks.cache_tasks.task_invalidate_video_cache",
    "post.created": "app.tasks.cache_tasks.task_handle_post_cache_event",
    "post.updated": "app.tasks.cache_tasks.task_handle_post_cache_event",
    "post.deleted": "app.tasks.cache_tasks.task_handle_post_cache_event",
    "content.created": "app.tasks.notification_tasks.create_content_notification",
    # 通知相关任务
    "notification.created": "app.tasks.notification_tasks.send_notification_websocket",
    "notification.read": "app.tasks.notification_tasks.update_unread_count",
    "notification.all_read": "app.tasks.notification_tasks.update_unread_count",
    # 点赞和评论通知任务
    "like.created": "app.tasks.notification_tasks.create_like_notification",
    "comment.created": "app.tasks.notification_tasks.create_comment_notification",
    "comment.reply_created": "app.tasks.notification_tasks.create_reply_notification",
    "scratch.stats.update": "app.tasks.stats_tasks.task_update_stats",
    "recommendation.user_followed": "app.tasks.recommendation_tasks.task_invalidate_user_recommendations_cache",
}


@app.task(
    name="app.tasks.cache_tasks.task_relay_outbox_messages",
    bind=True,
    autoretry_for=(Exception,),
    max_retries=3,
    retry_backoff=True,
    track_started=True,
    queue=OUTBOX_QUEUE_NAME,
)
def task_relay_outbox_messages(self):
    """
    轮询发件箱表，将待处理的消息转发到相应的Celery任务。
    添加分布式锁防止多个实例同时运行。
    """

    task_id = getattr(self.request, "id", "unknown")
    started_at = time.perf_counter()
    logger.debug(f"Outbox relay task start task_id={task_id}")

    # 直接调用同步版本，不再使用 run_async 辅助函数
    # 导入同步Redis客户端
    import redis
    from app.config import settings

    # 创建同步Redis连接
    # 根据Redis模式创建合适的客户端
    if settings.REDIS_MODE.lower() == "sentinel":
        # Sentinel模式：使用直连Redis避免事件循环问题
        redis_client = redis.from_url(
            "redis://172.20.0.10:6379/0",  # 直连主节点
            decode_responses=True,
            socket_connect_timeout=settings.REDIS_SOCKET_CONNECT_TIMEOUT,
            socket_timeout=settings.REDIS_SOCKET_TIMEOUT,
        )
    else:
        # Standalone模式：使用配置的URL
        redis_client = redis.from_url(
            settings.REDIS_URL,
            decode_responses=True,
            socket_connect_timeout=settings.REDIS_SOCKET_CONNECT_TIMEOUT,
            socket_timeout=settings.REDIS_SOCKET_TIMEOUT,
        )

    lock_key = f"outbox_relay_lock:{task_id}"
    lock_timeout = 300  # 5分钟锁超时

    try:
        # 获取分布式锁（同步版本）
        if not redis_client.set(lock_key, "1", nx=True, ex=lock_timeout):
            logger.debug(f"无法获取分布式锁，任务可能正在执行 task_id={task_id}")
            return None

        logger.debug(f"Outbox relay acquired redis task_id={task_id}")
        logger.info(f"获取分布式锁成功，开始执行发件箱消息中继任务 task_id={task_id}")

        # 使用原生 psycopg2 连接避免 SQLAlchemy 的 greenlet 问题
        import psycopg2
        import psycopg2.extras

        from app.config import settings

        # 从 DATABASE_URL 解析连接参数，移除 SQLAlchemy 特定的驱动标识
        db_url = settings.DATABASE_URL
        # 移除 SQLAlchemy 驱动标识，只保留标准的 postgresql:// 格式
        if "+asyncpg://" in db_url:
            db_url = db_url.replace("+asyncpg://", "://")
        elif "+psycopg2://" in db_url:
            db_url = db_url.replace("+psycopg2://", "://")
        # 确保使用标准的 postgresql:// 格式
        if not db_url.startswith("postgresql://"):
            db_url = db_url.replace("postgres://", "postgresql://", 1)

        # 使用 psycopg2 直接连接
        conn = psycopg2.connect(db_url)
        try:
            logger.debug(f"Outbox relay querying pending messages task_id={task_id}")

            with conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor) as cur:
                # 使用原始 SQL 查询避免 greenlet 问题
                cur.execute("""
                    SELECT id, topic, payload, status, created_at, updated_at
                    FROM outbox_messages
                    WHERE status = 'pending'
                    ORDER BY created_at
                    LIMIT 100
                """)
                rows = cur.fetchall()

                # 手动构建 OutboxMessage 对象
                messages = []
                for row in rows:
                    msg = OutboxMessage()
                    msg.id = row["id"]
                    msg.topic = row["topic"]
                    msg.payload = row["payload"]
                    msg.status = row["status"]
                    msg.created_at = row["created_at"]
                    msg.updated_at = row["updated_at"]
                    messages.append(msg)
            logger.debug(
                f"Outbox relay fetched pending messages count={len(messages)} task_id={task_id}"
            )

            if not messages:
                logger.info(f"没有待处理的发件箱消息 task_id={task_id}")
                return

            success_count = 0
            failed_count = 0

            for msg in messages:
                task_name = TASK_MAP.get(msg.topic)
                if not task_name:
                    logger.warning(f"未找到主题 '{msg.topic}' 对应的任务")
                    msg.status = "failed"
                    failed_count += 1
                    continue

                try:
                    # 根据不同的主题类型，构建不同的任务参数
                    if msg.topic in ["like.created"]:
                        # 点赞通知任务参数
                        args = [
                            msg.payload.get("liker_user_id"),
                            msg.payload.get("content_type"),
                            msg.payload.get("content_id"),
                        ]
                    elif msg.topic in ["comment.created"]:
                        # 评论通知任务参数
                        args = [
                            msg.payload.get("comment_id"),
                            msg.payload.get("author_id"),
                            msg.payload.get("content_type"),
                            msg.payload.get("content_id"),
                        ]
                    elif msg.topic in ["comment.reply_created"]:
                        # 回复通知任务参数
                        args = [
                            msg.payload.get("comment_id"),
                            msg.payload.get("author_id"),
                            msg.payload.get("reply_to_id"),
                            msg.payload.get("content_type"),
                            msg.payload.get("content_id"),
                        ]
                    elif msg.topic == "content.created":
                        args = [
                            msg.payload.get("content_type"),
                            msg.payload.get("content_id"),
                        ]
                    elif msg.topic in ["post.created", "post.updated", "post.deleted"]:
                        args = [msg.payload]
                    elif msg.topic in ["stats.update", "scratch.stats.update"]:
                        # 统计更新任务需要完整的 payload 作为参数
                        args = [msg.payload]
                    elif msg.topic in ["article.created", "article.updated"]:
                        # 文章相关任务需要 article_id
                        task_arg = msg.payload.get("article_id")
                        if not task_arg:
                            logger.error(
                                f"消息 {msg.id} (topic: {msg.topic}) 的 payload 中缺少 article_id: {msg.payload}"
                            )
                            msg.status = "failed"
                            failed_count += 1
                            continue
                        args = [task_arg]
                    elif msg.topic == "article.deleted":
                        # 文章删除任务需要 article_id
                        task_arg = msg.payload.get("article_id")
                        if not task_arg:
                            logger.error(
                                f"消息 {msg.id} (topic: {msg.topic}) 的 payload 中缺少 article_id: {msg.payload}"
                            )
                            msg.status = "failed"
                            failed_count += 1
                            continue
                        args = [task_arg]
                    elif msg.topic in ["video.created", "video.updated", "video.deleted"]:
                        # 视频相关任务需要 video_id
                        task_arg = msg.payload.get("video_id")
                        if not task_arg:
                            logger.error(
                                f"消息 {msg.id} (topic: {msg.topic}) 的 payload 中缺少 video_id: {msg.payload}"
                            )
                            msg.status = "failed"
                            failed_count += 1
                            continue
                        args = [task_arg]
                    elif msg.topic in ["user.created", "user.updated"]:
                        # 用户相关任务需要 user_id
                        task_arg = msg.payload.get("user_id")
                        if not task_arg:
                            logger.error(
                                f"消息 {msg.id} (topic: {msg.topic}) 的 payload 中缺少 user_id: {msg.payload}"
                            )
                            msg.status = "failed"
                            failed_count += 1
                            continue
                        args = [task_arg]
                    elif msg.topic == "user.deleted":
                        # 用户删除任务需要 user_id
                        task_arg = msg.payload.get("user_id")
                        if not task_arg:
                            logger.error(
                                f"消息 {msg.id} (topic: {msg.topic}) 的 payload 中缺少 user_id: {msg.payload}"
                            )
                            msg.status = "failed"
                            failed_count += 1
                            continue
                        args = [task_arg]
                    elif msg.topic in [
                        "notification.created",
                        "notification.read",
                        "notification.all_read",
                    ]:
                        # 通知相关任务需要 notification_id 或完整 payload
                        if msg.topic == "notification.all_read":
                            args = [msg.payload]  # all_read 需要完整 payload
                        else:
                            task_arg = msg.payload.get("notification_id")
                            if not task_arg:
                                logger.error(
                                    f"消息 {msg.id} (topic: {msg.topic}) 的 payload 中缺少 notification_id: {msg.payload}"
                                )
                                msg.status = "failed"
                                failed_count += 1
                                continue
                            args = [task_arg]
                    else:
                        # 其他未知主题
                        logger.error(f"消息 {msg.id} (topic: {msg.topic}) 的主题类型未定义")
                        msg.status = "failed"
                        failed_count += 1
                        continue

                    # 检查参数完整性
                    if any(arg is None for arg in args):
                        logger.error(f"消息 {msg.id} (topic: {msg.topic}) 的参数不完整: {args}")
                        msg.status = "failed"
                        failed_count += 1
                        continue

                    celery_task = app.signature(task_name, args=args)
                    celery_task.delay()
                    msg.status = "sent"
                    success_count += 1
                    logger.info(f"成功转发消息 {msg.id} (topic: {msg.topic}) task_id={task_id}")
                except Exception as e:
                    logger.error(f"转发消息 {msg.id} 失败: {e}", exc_info=True)
                    msg.status = "failed"
                    failed_count += 1

            # 批量更新消息状态，使用 psycopg2 避免异步操作
            if messages:
                # 收集需要更新的消息ID和状态
                sent_ids = [msg.id for msg in messages if msg.status == "sent"]
                failed_ids = [msg.id for msg in messages if msg.status == "failed"]

                with conn.cursor() as cur:
                    # 批量更新成功的消息
                    if sent_ids:
                        cur.execute(
                            """
                            UPDATE outbox_messages
                            SET status = 'sent', updated_at = NOW()
                            WHERE id = ANY(%s)
                        """,
                            (sent_ids,),
                        )

                    # 批量更新失败的消息
                    if failed_ids:
                        cur.execute(
                            """
                            UPDATE outbox_messages
                            SET status = 'failed', updated_at = NOW()
                            WHERE id = ANY(%s)
                        """,
                            (failed_ids,),
                        )

                conn.commit()

            # 记录处理结果
            logger.info(
                f"任务执行完成: 成功 {success_count} 条，失败 {failed_count} 条 task_id={task_id}"
            )

            # 如果失败率过高，发送告警
            if failed_count > 0 and success_count / (success_count + failed_count) < 0.8:
                logger.warning(f"消息处理失败率过高: {failed_count}/{success_count + failed_count}")
        finally:
            conn.close()

    except Exception as e:
        logger.error(f"获取分布式锁或执行任务失败: {e} task_id={task_id}", exc_info=True)
    finally:
        # 释放分布式锁
        try:
            redis_client.delete(lock_key)
            logger.debug(f"分布式锁已释放 task_id={task_id}")
        except Exception as e:
            logger.error(f"释放分布式锁失败: {e} task_id={task_id}")
        finally:
            # 确保关闭Redis连接
            try:
                redis_client.close()
            except Exception:
                pass

    elapsed = time.perf_counter() - started_at
    logger.debug(f"Outbox relay task end task_id={task_id} elapsed={elapsed:.3f}s")
    return None


@app.task(
    name="app.tasks.cache_tasks.cleanup_outbox_messages",
    autoretry_for=(Exception,),
    max_retries=3,
    retry_backoff=True,
    retry_backoff_max=300,
    retry_jitter=True,
)
def cleanup_outbox_messages(days: int = 30, batch_size: int = 500) -> int:
    """
    周期性清理历史 Outbox 消息，仅删除 sent/failed 且过期的数据。

    Args:
        days: 需要保留的天数，默认保留最近 30 天的数据。
        batch_size: 每批删除的记录数，避免长事务影响数据库。

    Returns:
        实际删除的消息条数。
    """

    cutoff = datetime.now(UTC) - timedelta(days=days)
    logger.info(
        "开始清理过期的 outbox 消息 cutoff=%s days=%s batch_size=%s",
        cutoff.isoformat(),
        days,
        batch_size,
    )

    session = SyncSessionLocal()
    deleted_total = 0

    try:
        while True:
            ids = (
                session.execute(
                    select(OutboxMessage.id)
                    .where(
                        OutboxMessage.status.in_(("sent", "failed")),
                        OutboxMessage.updated_at < cutoff,
                    )
                    .order_by(OutboxMessage.updated_at)
                    .limit(batch_size)
                )
                .scalars()
                .all()
            )

            if not ids:
                break

            session.execute(delete(OutboxMessage).where(OutboxMessage.id.in_(ids)))
            session.commit()
            deleted_total += len(ids)
            logger.debug(
                "已清理 outbox 记录 batch=%s total=%s cutoff=%s",
                len(ids),
                deleted_total,
                cutoff.isoformat(),
            )

    except Exception:
        session.rollback()
        logger.exception("清理 outbox 消息失败，将在下次重试")
        raise
    finally:
        session.close()

    logger.info(
        "完成 outbox 历史清理 deleted_total=%s cutoff=%s",
        deleted_total,
        cutoff.isoformat(),
    )
    return deleted_total
