"""
话题统计相关的Celery任务
"""

import asyncio
import logging
from datetime import datetime, timedelta

from sqlalchemy.ext.asyncio import AsyncSession

from app.config.post_config import PostConfig
from app.core.celery import app
from app.crud import topic_stats as crud_topic_stats
from app.db.redis import delete_key, sorted_set_add
from app.db.session import SessionLocal
from app.services.topic_stats_service import TopicStatsService

logger = logging.getLogger(__name__)


@app.task
def update_hot_topics():
    """
    定时任务：更新热门话题排行榜
    每15分钟执行一次
    """
    
    async def update():
        try:
            async with SessionLocal() as db:
                # 创建话题统计服务
                config = PostConfig()
                topic_stats_service = TopicStatsService(config=config)
                
                # 更新热门话题排行榜
                updated_count = await topic_stats_service.update_hot_rankings(db)
                
                logger.info(f"热门话题排行榜更新完成，更新了 {updated_count} 个话题")
                
        except Exception as e:
            logger.error(f"更新热门话题排行榜失败: {e}")
            raise
    
    asyncio.run(update())


@app.task
def calculate_topic_trends():
    """
    定时任务：计算话题趋势分数
    每小时执行一次
    """
    
    async def calculate():
        try:
            async with SessionLocal() as db:
                # 获取所有有帖子的话题
                topics_stats = await crud_topic_stats.get_multi(
                    db, skip=0, limit=1000  # 限制处理数量
                )
                
                updated_count = 0
                for stats in topics_stats:
                    if stats.post_count > 0:
                        # 计算趋势分数（基于最近24小时的活动）
                        trend_score = await calculate_topic_trend_score(db, stats.topic)
                        
                        if abs(stats.trend_score - trend_score) > 0.01:
                            stats.trend_score = trend_score
                            updated_count += 1
                
                await db.commit()
                logger.info(f"话题趋势分数计算完成，更新了 {updated_count} 个话题")
                
        except Exception as e:
            logger.error(f"计算话题趋势分数失败: {e}")
            raise
    
    asyncio.run(calculate())


@app.task
def cleanup_old_topic_trends():
    """
    定时任务：清理过期的话题趋势数据
    每天执行一次
    """
    
    async def cleanup():
        try:
            async with SessionLocal() as db:
                # 删除30天前的趋势数据
                cutoff_date = datetime.utcnow() - timedelta(days=30)
                
                # 这里需要实现删除逻辑
                # 由于我们的TopicTrend模型没有delete方法，暂时跳过
                # TODO: 实现清理逻辑
                
                logger.info("话题趋势数据清理完成")
                
        except Exception as e:
            logger.error(f"清理话题趋势数据失败: {e}")
            raise
    
    asyncio.run(cleanup())


@app.task
def update_topic_peak_times():
    """
    定时任务：更新话题的峰值时间
    每天执行一次
    """
    
    async def update():
        try:
            async with SessionLocal() as db:
                # 获取所有话题统计
                topics_stats = await crud_topic_stats.get_multi(
                    db, skip=0, limit=1000
                )
                
                updated_count = 0
                for stats in topics_stats:
                    # 检查当前热度是否为历史峰值
                    if not stats.peak_time or stats.hot_score > 0:
                        # 简化逻辑：如果当前有热度且没有峰值时间，或者热度很高，就更新峰值时间
                        if (not stats.peak_time and stats.hot_score > 10) or \
                           (stats.peak_time and stats.hot_score > 50):
                            stats.peak_time = datetime.utcnow()
                            updated_count += 1
                
                await db.commit()
                logger.info(f"话题峰值时间更新完成，更新了 {updated_count} 个话题")
                
        except Exception as e:
            logger.error(f"更新话题峰值时间失败: {e}")
            raise
    
    asyncio.run(update())


async def calculate_topic_trend_score(db: AsyncSession, topic: str) -> float:
    """
    计算话题的趋势分数
    基于最近24小时的活动增长率
    """
    try:
        # 获取话题统计
        stats = await crud_topic_stats.get_by_topic(db, topic=topic)
        if not stats:
            return 0.0
        
        # 简化的趋势计算：基于最后发帖时间和当前热度
        if not stats.last_post_at:
            return 0.0
        
        # 计算距离最后发帖的小时数
        hours_since_last_post = (datetime.utcnow() - stats.last_post_at).total_seconds() / 3600
        
        # 如果最近24小时内有活动，给予更高的趋势分数
        if hours_since_last_post <= 24:
            # 越近期的活动，趋势分数越高
            time_factor = max(0, 1 - (hours_since_last_post / 24))
            trend_score = stats.hot_score * time_factor * 2  # 乘以2增强趋势效果
        else:
            # 超过24小时没有活动，趋势分数衰减
            decay_factor = max(0.1, 1 / (hours_since_last_post / 24))
            trend_score = stats.hot_score * decay_factor
        
        return round(trend_score, 2)
        
    except Exception as e:
        logger.error(f"计算话题趋势分数失败: {topic}, error: {e}")
        return 0.0


@app.task
def generate_topic_insights():
    """
    定时任务：生成话题洞察报告
    每周执行一次
    """
    
    async def generate():
        try:
            async with SessionLocal() as db:
                # 获取本周最热门的话题
                hot_topics = await crud_topic_stats.get_hot_topics(
                    db, limit=10, days=7
                )
                
                # 获取本周增长最快的话题
                trending_topics = await crud_topic_stats.get_trending_topics(
                    db, limit=10
                )
                
                # 生成洞察数据（这里可以扩展为更复杂的分析）
                insights = {
                    "week_start": (datetime.utcnow() - timedelta(days=7)).isoformat(),
                    "week_end": datetime.utcnow().isoformat(),
                    "hot_topics": [
                        {
                            "topic": stats.topic,
                            "post_count": stats.post_count,
                            "hot_score": stats.hot_score,
                        }
                        for stats in hot_topics
                    ],
                    "trending_topics": [
                        {
                            "topic": stats.topic,
                            "post_count": stats.post_count,
                            "trend_score": stats.trend_score,
                        }
                        for stats in trending_topics
                    ],
                    "total_topics": len(hot_topics) + len(trending_topics),
                }
                
                # 这里可以将洞察数据存储到缓存或发送通知
                # TODO: 实现洞察数据的存储和通知逻辑
                
                logger.info(f"话题洞察报告生成完成，包含 {insights['total_topics']} 个话题")
                
        except Exception as e:
            logger.error(f"生成话题洞察报告失败: {e}")
            raise
    
    asyncio.run(generate())


# 任务调度配置（需要在Celery Beat中配置）
TOPIC_STATS_SCHEDULE = {
    'update-hot-topics': {
        'task': 'app.tasks.topic_stats_tasks.update_hot_topics',
        'schedule': 900.0,  # 每15分钟
    },
    'calculate-topic-trends': {
        'task': 'app.tasks.topic_stats_tasks.calculate_topic_trends',
        'schedule': 3600.0,  # 每小时
    },
    'cleanup-old-topic-trends': {
        'task': 'app.tasks.topic_stats_tasks.cleanup_old_topic_trends',
        'schedule': 86400.0,  # 每天
    },
    'update-topic-peak-times': {
        'task': 'app.tasks.topic_stats_tasks.update_topic_peak_times',
        'schedule': 86400.0,  # 每天
    },
    'generate-topic-insights': {
        'task': 'app.tasks.topic_stats_tasks.generate_topic_insights',
        'schedule': 604800.0,  # 每周
    },
}
