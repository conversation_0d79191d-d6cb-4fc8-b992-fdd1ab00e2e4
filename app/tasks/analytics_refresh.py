"""分析快照刷新任务"""

from app.core.celery import app
from app.core.logging import logger
from app.db.session import SessionLocal
from app.services.analytics.pipeline import AnalyticsPipeline


def get_pipeline() -> AnalyticsPipeline:
    return AnalyticsPipeline()


@app.task(name="app.tasks.analytics_refresh.refresh_snapshots")
async def task_refresh_snapshots() -> None:
    pipeline = get_pipeline()
    db = SessionLocal()
    try:
        for snapshot_type in ("dashboard", "users", "content", "system"):
            await pipeline.refresh_snapshot(
                db,
                snapshot_type=snapshot_type,
                time_range="last_7_days",
            )
        logger.info("Analytics snapshots refreshed")
    finally:
        await db.close()
