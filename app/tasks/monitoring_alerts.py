"""监控告警调度 Celery 任务"""

from app.core.celery import app
from app.core.logging import logger
from app.db.session import SessionLocal
from app.services.monitoring.alert_engine import MonitoringAlertEngine


def get_monitoring_alert_engine() -> MonitoringAlertEngine:
    return MonitoringAlertEngine()


@app.task(name="app.tasks.monitoring_alerts.evaluate")
async def task_evaluate_alerts() -> None:
    engine = get_monitoring_alert_engine()
    db = SessionLocal()
    try:
        await engine.evaluate_rules(db)
        logger.info("Monitoring alert evaluation completed")
    finally:
        await db.close()
