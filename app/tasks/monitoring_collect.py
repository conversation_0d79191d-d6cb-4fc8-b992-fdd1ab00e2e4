"""监控采集 Celery 任务"""

from app.core.celery import app
from app.core.logging import logger
from app.db.session import SessionLocal
from app.services.monitoring.collector import MonitoringCollector


def get_monitoring_collector() -> MonitoringCollector:
    return MonitoringCollector()


@app.task(name="app.tasks.monitoring_collect.collect_metrics")
async def task_collect_metrics() -> None:
    collector = get_monitoring_collector()
    db = SessionLocal()
    try:
        await collector.collect_and_record(db, source="celery")
        logger.info("Monitoring metrics collected")
    finally:
        await db.close()
