"""热门标签相关的 Celery 任务"""

import logging
from collections.abc import Iterable
from datetime import UTC, datetime, timedelta

from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.celery import app
from app.core.async_runner import run_async
from app.db.redis import get_redis_master, increment_key
from app.db.session import SessionLocal
from app.models.tag_stats import TagStats
from app.services.tag_hot_service import TagHotItem, TagHotService

logger = logging.getLogger(__name__)

SUPPORTED_CONTENT_TYPES = ["article", "video", "scratch", "post", "global"]


async def _upsert_tag_stats(
    db: AsyncSession,
    *,
    content_type: str,
    items: Iterable[TagHotItem],
) -> None:
    tag_list = list(items)
    if not tag_list:
        return

    tag_ids = [item.tag_id for item in tag_list]
    rows = await db.execute(
        select(TagStats).where(
            TagStats.content_type == content_type,
            TagStats.tag_id.in_(tag_ids),
        )
    )
    existing = {stats.tag_id: stats for stats in rows.scalars().all()}

    for item in tag_list:
        stats = existing.get(item.tag_id)
        if stats is None:
            stats = TagStats(
                tag_id=item.tag_id,
                content_type=content_type,
            )
            db.add(stats)

        stats.total_score = item.score
        stats.unique_users_est = item.unique_users
        stats.window_delta = item.window_score
        stats.last_seen_at = item.last_event_at

    await db.commit()
    await increment_key(f"metrics:tag_hot:rollup_updates:{content_type}", expire=3600)


@app.task(name="app.tasks.tag_hot_tasks.handle_tag_hot_event")
def handle_tag_hot_event(payload: dict) -> None:
    """处理单个热门标签事件"""

    async def _run() -> None:
        service = TagHotService()
        try:
            await service.record_event(
                tag_id=int(payload["tag_id"]),
                content_type=str(payload["content_type"]),
                score_delta=float(payload.get("score_delta", 1.0)),
                user_id=payload.get("user_id"),
            )
        except Exception as exc:  # pragma: no cover - 日志路径
            logger.error("处理热门标签事件失败 payload=%s error=%s", payload, exc)

    run_async(_run())
@app.task(name="app.tasks.tag_hot_tasks.rollup_tag_hot_rankings")
def rollup_tag_hot_rankings() -> None:
    """定期将 Redis 排行回填数据库"""

    async def _run() -> None:
        service = TagHotService()
        async with SessionLocal() as db:
            for content_type in SUPPORTED_CONTENT_TYPES:
                try:
                    items = await service.get_hot_tags(
                        db, content_type=content_type, limit=service.fallback_limit
                    )
                    await _upsert_tag_stats(db, content_type=content_type, items=items)
                    await service.cache_hot_tags_snapshot(
                        content_type=content_type,
                        payload=[item.model_dump() for item in items],
                        expire_seconds=600,
                    )
                except Exception as exc:  # pragma: no cover - 日志路径
                    logger.error("热门标签回填失败 content_type=%s error=%s", content_type, exc)

    run_async(_run())


@app.task(name="app.tasks.tag_hot_tasks.cleanup_tag_hot_windows")
def cleanup_tag_hot_windows() -> None:
    """清理过期的窗口排行键"""

    async def _run() -> None:
        service = TagHotService()
        cutoff = datetime.now(UTC) - timedelta(hours=service.window_retention_hours)
        cutoff_str = cutoff.strftime("%Y%m%d%H")
        redis_master = await get_redis_master()

        async for key in redis_master.scan_iter(match="tag:hot:*:win:*"):
            try:
                key_str = key if isinstance(key, str) else key.decode("utf-8")
                window_part = key_str.rsplit(":", maxsplit=1)[-1]
                if window_part < cutoff_str:
                    await redis_master.delete(key)
                    await increment_key("metrics:tag_hot:window_cleanup", expire=3600)
            except Exception as exc:  # pragma: no cover - 日志路径
                logger.debug("清理窗口键失败 key=%s error=%s", key, exc)

    run_async(_run())
