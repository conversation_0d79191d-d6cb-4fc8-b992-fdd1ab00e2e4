"""推荐系统相关的Celery任务"""

import ast
import json
import logging
from collections import defaultdict
from datetime import UTC, datetime

from sklearn.feature_extraction import DictVectorizer
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.async_runner import run_async
from app.core.celery import app
from app.crud import article as crud_article
from app.crud import user as crud_user
from app.crud import user_behavior as crud_user_behavior
from app.crud import video as crud_video
from app.db.redis import (
    delete_key,
    hash_get,
    hash_get_all,
    hash_set,
    sorted_set_add,
)
from app.db.session import SessionLocal
from app.services.recommendation_cache_service import RecommendationCacheService
from scripts.fix_recommendation_system import RecommendationSystemFixer

logger = logging.getLogger(__name__)


def _resolve_content_timestamp(item) -> float:
    """回退处理内容的时间戳，避免 created_at 为空导致异常。"""

    timestamp_source = getattr(item, "created_at", None) or getattr(item, "updated_at", None)
    if timestamp_source is None:
        logger.debug(
            "fallback timestamp for item without created_at/updated_at id=%s", getattr(item, "id", None)
        )
        timestamp_source = datetime.now(UTC)
    elif isinstance(timestamp_source, datetime) and timestamp_source.tzinfo is None:
        timestamp_source = timestamp_source.replace(tzinfo=UTC)
    elif not isinstance(timestamp_source, datetime):
        try:
            timestamp_source = datetime.fromisoformat(str(timestamp_source))
            if timestamp_source.tzinfo is None:
                timestamp_source = timestamp_source.replace(tzinfo=UTC)
        except (TypeError, ValueError):
            logger.debug(
                "unable to parse timestamp for item id=%s, using current time", getattr(item, "id", None)
            )
            timestamp_source = datetime.now(UTC)

    return timestamp_source.timestamp()


@app.task
def update_user_profiles():
    """
    定时任务：更新用户画像
    """

    async def update():
        async with SessionLocal() as db:
            users = await crud_user.get_multi(db)
            for user in users:
                await update_user_profile(db, user.id)

    run_async(update())


async def update_user_profile(db: AsyncSession, user_id: int):
    """
    更新单个用户画像
    """
    interactions = await crud_user_behavior.user_interaction.get_user_interactions(
        db=db, user_id=user_id, limit=500, days=90
    )
    profile = defaultdict(float)
    for behavior in interactions:
        # 根据行为类型给予不同权重
        weight = {
            "click": 1.0,
            "like": 2.0,
            "favorite": 3.0,
            "comment": 4.0,
            "share": 5.0,
        }.get(behavior.behavior_type, 0.5)
        # 这里简化处理，实际应用中会更复杂
        profile[f"category:{behavior.content_type}:{behavior.content_id}"] += weight

    # 将用户画像存入Redis
    await hash_set("user_profiles", str(user_id), json.dumps(profile, ensure_ascii=False))


@app.task
def update_content_features():
    """
    定时任务：更新内容特征
    """

    async def update():
        async with SessionLocal() as db:
            articles = await crud_article.get_multi(db)
            for article in articles:
                await update_content_feature(db, "article", article.id)
            videos = await crud_video.get_multi(db)
            for video in videos:
                await update_content_feature(db, "video", video.id)

    run_async(update())


async def update_content_feature(db: AsyncSession, content_type: str, content_id: int):
    """
    更新单个内容特征
    """
    # 简化处理，实际应用中会从内容中提取关键词、标签等
    feature = {"category": content_type}
    await hash_set(
        "content_features",
        f"{content_type}:{content_id}",
        json.dumps(feature, ensure_ascii=False),
    )


@app.task
def generate_recommendations():
    """
    定时任务：为所有用户生成推荐
    """

    async def generate():
        async with SessionLocal() as db:
            users = await crud_user.get_multi(db)
            for user in users:
                await generate_recommendation_for_user(db, user.id)

    run_async(generate())


async def generate_recommendation_for_user(db: AsyncSession, user_id: int):
    """
    为单个用户生成推荐
    """
    user_profile_raw = await hash_get("user_profiles", str(user_id))
    if not user_profile_raw:
        return

    try:
        user_profile = json.loads(user_profile_raw)
    except json.JSONDecodeError:
        try:
            user_profile = ast.literal_eval(user_profile_raw)
        except (ValueError, SyntaxError):
            logger.warning("无法解析用户画像 user_id={}", user_id)
            return

    content_features_raw = await hash_get_all("content_features")
    if not content_features_raw:
        return

    content_features = {}
    for raw_key, raw_value in content_features_raw.items():
        key = raw_key.decode("utf-8") if isinstance(raw_key, bytes) else str(raw_key)
        value_str = raw_value.decode("utf-8") if isinstance(raw_value, bytes) else str(raw_value)
        try:
            content_features[key] = json.loads(value_str)
        except json.JSONDecodeError:
            try:
                content_features[key] = ast.literal_eval(value_str)
            except (ValueError, SyntaxError):
                logger.debug("跳过无法解析的内容特征 key={}", key)

    if not content_features:
        logger.warning("未找到可用内容特征 user_id={}", user_id)
        return

    # 使用DictVectorizer转换特征
    vectorizer = DictVectorizer(sparse=True)
    user_vector = vectorizer.fit_transform([user_profile])
    content_vectors = vectorizer.transform(content_features.values())

    # 计算相似度（这里用点积简化）
    scores = user_vector.dot(content_vectors.T).toarray()[0]

    # 生成推荐列表
    recommendations = sorted(
        zip(content_features.keys(), scores, strict=False), key=lambda x: x[1], reverse=True
    )

    # 存储推荐结果到Redis
    rec_key = f"recommendations:{user_id}"
    await delete_key(rec_key)
    rec_to_store = dict(recommendations[:100])  # 只存前100个
    if rec_to_store:
        await sorted_set_add(rec_key, rec_to_store)


@app.task
def update_hot_recommendations():
    """
    定时任务：更新热门推荐
    """

    async def update():
        async with SessionLocal() as db:
            # 聚合用户行为数据，计算热门内容
            hot_articles = await crud_user_behavior.get_hot_content(db, content_type="article")
            hot_videos = await crud_user_behavior.get_hot_content(db, content_type="video")

            hot_rec_key_article = "rec_pool:hot:article"
            hot_rec_key_video = "rec_pool:hot:video"

            await delete_key(hot_rec_key_article)
            if hot_articles:
                await sorted_set_add(
                    hot_rec_key_article,
                    {str(item.content_id): item.hot_score for item in hot_articles},
                )

            await delete_key(hot_rec_key_video)
            if hot_videos:
                await sorted_set_add(
                    hot_rec_key_video,
                    {str(item.content_id): item.hot_score for item in hot_videos},
                )

    run_async(update())


@app.task
def update_latest_recommendations():
    """
    定时任务：更新最新推荐
    """

    async def update():
        async with SessionLocal() as db:
            latest_articles = await crud_article.get_multi(db, limit=100)
            latest_videos = await crud_video.get_multi(db, limit=100)

            latest_rec_key_article = "rec_pool:latest:article"
            latest_rec_key_video = "rec_pool:latest:video"

            await delete_key(latest_rec_key_article)
            if latest_articles:
                article_mapping = {}
                for item in latest_articles:
                    item_id = getattr(item, "id", None)
                    if item_id is None:
                        continue
                    article_mapping[str(item_id)] = _resolve_content_timestamp(item)

                if article_mapping:
                    await sorted_set_add(latest_rec_key_article, article_mapping)

            await delete_key(latest_rec_key_video)
            if latest_videos:
                video_mapping = {}
                for item in latest_videos:
                    item_id = getattr(item, "id", None)
                    if item_id is None:
                        continue
                    video_mapping[str(item_id)] = _resolve_content_timestamp(item)

                if video_mapping:
                    await sorted_set_add(latest_rec_key_video, video_mapping)

    run_async(update())


@app.task(
    autoretry_for=(Exception,),
    max_retries=3,
    retry_backoff=True,
    retry_backoff_max=60,
    retry_jitter=True,
)
def task_invalidate_user_recommendations_cache(user_id: int):
    """使指定用户的推荐缓存失效"""

    async def invalidate():
        cache_service = RecommendationCacheService()
        await cache_service.invalidate_user_recommendations_cache(user_id)

    run_async(invalidate())


def _run_fixer_method(method_name: str):
    """辅助函数：复用 RecommendationSystemFixer 的实现片段。"""

    async def run():
        async with RecommendationSystemFixer() as fixer:
            method = getattr(fixer, method_name, None)
            if method is None:
                raise AttributeError(f"RecommendationSystemFixer 缺少方法 {method_name}")
            return await method()

    return run_async(run())


@app.task(
    name="app.tasks.recommendation_tasks.task_update_item_profiles_incremental",
    autoretry_for=(Exception,),
    max_retries=3,
    retry_backoff=True,
    retry_backoff_max=60,
    retry_jitter=True,
)
def task_update_item_profiles_incremental():
    """
    增量刷新内容画像及相关特征。

    当前实现回退为全量刷新，确保内容画像、特征与 Redis 相似度池保持一致。
    后续可在此基础上扩展真正的增量逻辑（根据更新时间过滤内容）。
    """
    logger.info("开始执行增量内容画像刷新任务（当前为全量刷新）")
    update_content_features()
    logger.info("内容画像刷新任务执行完成")


@app.task(
    name="app.tasks.recommendation_tasks.task_update_user_profiles_incremental",
    autoretry_for=(Exception,),
    max_retries=3,
    retry_backoff=True,
    retry_backoff_max=60,
    retry_jitter=True,
)
def task_update_user_profiles_incremental():
    """
    增量刷新用户画像。

    当前实现回退为全量刷新，优先保证用户画像可用。
    """
    logger.info("开始执行增量用户画像刷新任务（当前为全量刷新）")
    update_user_profiles()
    logger.info("用户画像刷新任务执行完成")


@app.task(
    name="app.tasks.recommendation_tasks.task_recluster_users_full",
    autoretry_for=(Exception,),
    max_retries=3,
    retry_backoff=True,
    retry_backoff_max=120,
    retry_jitter=True,
)
def task_recluster_users_full():
    """全量重建用户群组映射 user_to_group。"""
    logger.info("开始执行用户群组重建任务")
    stats = _run_fixer_method("_initialize_user_groups")
    logger.info("用户群组重建完成: {}", stats)
    return stats


@app.task(
    name="app.tasks.recommendation_tasks.task_regenerate_group_recommendations_full",
    autoretry_for=(Exception,),
    max_retries=3,
    retry_backoff=True,
    retry_backoff_max=120,
    retry_jitter=True,
)
def task_regenerate_group_recommendations_full():
    """全量刷新群组推荐池（热门/最新/群组池等）。"""
    logger.info("开始执行群组推荐池重建任务")
    stats = _run_fixer_method("_initialize_recommendation_pools")
    logger.info("群组推荐池重建完成: {}", stats)
    return stats


@app.task(
    name="app.tasks.recommendation_tasks.task_calculate_hot_content",
    autoretry_for=(Exception,),
    max_retries=3,
    retry_backoff=True,
    retry_backoff_max=60,
    retry_jitter=True,
)
def task_calculate_hot_content():
    """刷新热门内容推荐池。"""
    logger.info("开始执行热门内容计算任务")
    update_hot_recommendations()
    logger.info("热门内容计算任务完成")


@app.task(
    name="app.tasks.recommendation_tasks.task_calculate_item_similarity",
    autoretry_for=(Exception,),
    max_retries=3,
    retry_backoff=True,
    retry_backoff_max=120,
    retry_jitter=True,
)
def task_calculate_item_similarity():
    """重建内容相似度池。"""
    logger.info("开始执行内容相似度重建任务")
    stats = _run_fixer_method("_initialize_similarity_data")
    logger.info("内容相似度重建完成: {}", stats)
    return stats


@app.task(
    name="app.tasks.recommendation_tasks.task_cache_latest_content",
    autoretry_for=(Exception,),
    max_retries=3,
    retry_backoff=True,
    retry_backoff_max=60,
    retry_jitter=True,
)
def task_cache_latest_content():
    """刷新最新内容推荐池。"""
    logger.info("开始执行最新内容缓存任务")
    update_latest_recommendations()
    logger.info("最新内容缓存任务完成")
