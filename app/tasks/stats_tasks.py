from collections.abc import Coroutine
from typing import Any

from app import crud
from app.core.celery import app
from app.core.logging import logger
from app.db.session import SessionLocal
from app.services.user_stats_service import UserStatsCacheService

# 导入其他未来可能需要的统计服务
# from app.services.article_stats_service import article_stats_service
# from app.services.video_stats_service import video_stats_service


# 创建用户统计缓存服务实例
user_stats_service = UserStatsCacheService()

# 映射实体类型到对应的CRUD操作和缓存服务
STATS_HANDLER_MAP: dict[str, dict[str, Any]] = {
    "user": {"crud": crud.user_stats, "cache_service": user_stats_service},
    "scratch": {"crud": crud.scratch_product, "cache_service": None},  # Scratch项目使用专门的服务
    # "article": {"crud": crud.article_stats, "cache_service": article_stats_service},
    # "video": {"crud": crud.video_stats, "cache_service": video_stats_service},
}


@app.task(
    name="app.tasks.stats_tasks.task_update_stats",
    autoretry_for=(Exception,),
    max_retries=3,
    retry_backoff=True,
    retry_backoff_max=60,
    retry_jitter=True,
)
async def task_update_stats(payload: dict):
    """
    一个统一的、通用的任务，用于更新任何实体的统计数据。
    """
    entity_type = payload.get("entity_type")
    entity_id = payload.get("entity_id")
    field_name = payload.get("field_name")
    value_change = payload.get("value_change")

    if not all([entity_type, entity_id, field_name, value_change]):
        logger.error(f"任务 'task_update_stats' 收到不完整的 payload: {payload}")
        return

    try:
        value_change = int(value_change)
    except (TypeError, ValueError):
        logger.error(
            "任务 'task_update_stats' 收到非法的 value_change: payload=%s", payload
        )
        return

    logger.info(
        f"开始执行统计更新任务: entity_type={entity_type}, entity_id={entity_id}, "
        f"field={field_name}, change={value_change}"
    )

    handler = STATS_HANDLER_MAP.get(entity_type)
    if not handler:
        logger.error(f"未找到实体类型 '{entity_type}' 的统计处理器。")
        return

    crud_handler = handler["crud"]
    cache_service_handler = handler["cache_service"]

    db = SessionLocal()
    try:
        # 特殊处理Scratch项目统计更新
        if entity_type == "scratch":
            # 对于Scratch项目，根据 value_change 判断增减
            if field_name == "like_count":
                if value_change > 0:
                    await crud_handler.increment_like_count(db, project_id=entity_id, commit=True)
                elif value_change < 0:
                    await crud_handler.decrement_like_count(db, project_id=entity_id, commit=True)
                else:
                    logger.warning(
                        "忽略 Scratch 点赞计数的零值变更: entity_id=%s", entity_id
                    )
                    return
            elif field_name == "favorite_count":
                if value_change > 0:
                    await crud_handler.increment_favorite_count(db, project_id=entity_id, commit=True)
                elif value_change < 0:
                    await crud_handler.decrement_favorite_count(db, project_id=entity_id, commit=True)
                else:
                    logger.warning(
                        "忽略 Scratch 收藏计数的零值变更: entity_id=%s", entity_id
                    )
                    return
            else:
                logger.error(f"Unsupported field for Scratch stats update: {field_name}")
                return
        else:
            # 原有的统计更新逻辑
            await crud_handler.increment(
                db, user_id=entity_id, field=field_name, value=value_change, commit=True
            )

        logger.info(
            f"成功更新数据库统计: entity_type={entity_type}, entity_id={entity_id}, "
            f"field={field_name}"
        )

        # 使缓存失效
        # 注意：这里的invalidate方法可能需要统一的接口，或者根据类型调用不同的方法
        # 假设所有缓存服务都有一个 invalidate_{entity_type}_stats(entity_id) 的方法
        invalidate_method_name = f"invalidate_{entity_type}_stats"
        if hasattr(cache_service_handler, invalidate_method_name):
            invalidate_method: Coroutine = getattr(cache_service_handler, invalidate_method_name)
            await invalidate_method(entity_id)
            logger.info(f"成功使统计缓存失效: entity_type={entity_type}, entity_id={entity_id}")
        else:
            # 兼容 user_stats_service 的 invalidate_user_stats
            await cache_service_handler.invalidate_user_stats(entity_id)
            logger.info(f"成功使统计缓存失效: entity_type={entity_type}, entity_id={entity_id}")

    except Exception as e:
        logger.error(f"执行统计更新任务失败: payload={payload}, error={e}", exc_info=True)
        # 任务会自动重试
        raise
    finally:
        await db.close()
