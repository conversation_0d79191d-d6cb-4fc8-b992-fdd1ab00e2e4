#!/usr/bin/env python3
"""
填充搜索建议数据脚本

从现有的文章标题中提取关键词，生成搜索建议数据
"""

import asyncio
import logging
import os
import re
import sys

from sqlalchemy import text
from sqlalchemy.ext.asyncio import AsyncSession

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.db.session import SessionLocal

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def extract_keywords_from_title(title: str) -> list[str]:
    """从标题中提取关键词"""
    # 移除标点符号，分割成词
    words = re.findall(r"[\w\u4e00-\u9fff]+", title)

    keywords = []

    # 添加完整标题（如果长度合适）
    if 2 <= len(title) <= 50:
        keywords.append(title)

    # 添加单个词（长度大于1的）
    for word in words:
        if len(word) > 1:
            keywords.append(word)

    # 添加前缀（2-10个字符）
    for i in range(2, min(len(title) + 1, 11)):
        prefix = title[:i]
        if len(prefix.strip()) >= 2:
            keywords.append(prefix)

    return list(set(keywords))  # 去重


async def populate_search_suggestions(db: AsyncSession):
    """填充搜索建议数据"""
    logger.info("开始填充搜索建议数据...")

    # 1. 获取所有已发布且已审核的文章标题
    result = await db.execute(
        text("""
            SELECT DISTINCT title 
            FROM articles 
            WHERE is_published = true 
            AND is_approved = true 
            AND is_deleted = false
            AND title IS NOT NULL
            AND LENGTH(title) > 1
        """)
    )

    titles = [row[0] for row in result.fetchall()]
    logger.info(f"找到 {len(titles)} 个文章标题")

    # 2. 提取所有关键词
    all_keywords: set[str] = set()

    for title in titles:
        keywords = extract_keywords_from_title(title)
        all_keywords.update(keywords)

    logger.info(f"提取到 {len(all_keywords)} 个唯一关键词")

    # 3. 清空现有的搜索建议
    await db.execute(text("DELETE FROM search_suggestions"))
    await db.commit()
    logger.info("已清空现有搜索建议")

    # 4. 批量插入新的搜索建议
    suggestions_data = []

    for keyword in all_keywords:
        # 计算权重（基于关键词长度和出现频率）
        weight = min(100, len(keyword) * 2)

        # 为不同内容类型创建建议
        content_types = ["all", "article"]

        for content_type in content_types:
            suggestions_data.append(
                {
                    "keyword": keyword,
                    "content_type": content_type,
                    "suggestion_type": "popular",
                    "weight": weight,
                    "is_active": True,
                }
            )

    # 批量插入
    if suggestions_data:
        insert_sql = text("""
            INSERT INTO search_suggestions (keyword, content_type, suggestion_type, weight, is_active, created_at, updated_at)
            VALUES (:keyword, :content_type, :suggestion_type, :weight, :is_active, NOW(), NOW())
            ON CONFLICT (keyword, content_type, suggestion_type)
            DO UPDATE SET
                weight = EXCLUDED.weight,
                updated_at = NOW()
        """)

        await db.execute(insert_sql, suggestions_data)
        await db.commit()

        logger.info(f"成功插入 {len(suggestions_data)} 条搜索建议")

    # 5. 验证结果
    result = await db.execute(text("SELECT COUNT(*) FROM search_suggestions"))
    count = result.scalar()
    logger.info(f"搜索建议表中现有 {count} 条记录")

    # 6. 显示一些示例
    result = await db.execute(
        text("""
            SELECT keyword, content_type, weight 
            FROM search_suggestions 
            WHERE keyword LIKE '关于%' 
            ORDER BY weight DESC 
            LIMIT 10
        """)
    )

    examples = result.fetchall()
    if examples:
        logger.info("示例搜索建议（以'关于'开头）:")
        for keyword, content_type, weight in examples:
            logger.info(f"  - {keyword} ({content_type}, 权重: {weight})")


async def main():
    """主函数"""
    logger.info("开始执行搜索建议填充脚本...")

    async with SessionLocal() as db:
        try:
            await populate_search_suggestions(db)
            logger.info("搜索建议填充完成！")
        except Exception as e:
            logger.error(f"填充搜索建议失败: {e}", exc_info=True)
            await db.rollback()
            raise
        finally:
            await db.close()


if __name__ == "__main__":
    asyncio.run(main())
