"""
用于管理和追踪异步任务状态的服务
使用Redis作为后端存储，以实现高效读写
"""

import base64
import binascii
import json
import zlib

from app.db.redis import hash_get, hash_get_many, hash_set_with_expire
from app.schemas.upload import TaskStatus, UploadStatus

# 使用Redis哈希表来存储所有任务的状态，键为 "task_statuses"
REDIS_HASH_KEY = "task_statuses"
# 默认过期时间：7天
DEFAULT_TTL_SECONDS = 7 * 24 * 60 * 60
# 终态（完成或失败）的过期时间：1天
TERMINAL_STATE_TTL_SECONDS = 1 * 24 * 60 * 60


class TaskStatusService:
    @staticmethod
    def _deserialize(raw_value: bytes | str | memoryview | None) -> dict | None:
        if raw_value is None:
            return None

        if isinstance(raw_value, memoryview):
            raw_value = raw_value.tobytes()

        if isinstance(raw_value, str):
            try:
                raw_value = base64.b64decode(raw_value)
            except (binascii.Error, ValueError):
                try:
                    raw_value = raw_value.encode("utf-8")
                except Exception:  # noqa: BLE001
                    return None

        try:
            decompressed = zlib.decompress(raw_value)
        except (zlib.error, TypeError):
            return None

        try:
            return json.loads(decompressed)
        except json.JSONDecodeError:
            return None

    @staticmethod
    async def update_status(
        task_id: str,
        status: UploadStatus,
        progress: float | None = None,
        file_metadata: dict | None = None,
        message: str | None = None,
    ):
        """
        更新或创建一个任务的状态（增加压缩和过期策略）
        """
        task_data = {
            "taskId": task_id,
            "status": status.value,
            "progress": progress,
            "file_metadata": file_metadata,
            "message": message,
        }
        task_data_cleaned = {k: v for k, v in task_data.items() if v is not None}

        # 压缩数据
        compressed_data = zlib.compress(json.dumps(task_data_cleaned).encode("utf-8"))
        encoded_data = base64.b64encode(compressed_data).decode("ascii")

        # 设置过期时间
        ttl = (
            TERMINAL_STATE_TTL_SECONDS
            if status
            in [UploadStatus.COMPLETE, UploadStatus.UPLOAD_ERROR, UploadStatus.CREATE_ERROR]
            else DEFAULT_TTL_SECONDS
        )
        await hash_set_with_expire(REDIS_HASH_KEY, task_id, encoded_data, expire=ttl)

    @staticmethod
    async def get_status(task_id: str) -> TaskStatus | None:
        """
        获取单个任务的状态
        """
        task_data_raw = await hash_get(REDIS_HASH_KEY, task_id)
        payload = TaskStatusService._deserialize(task_data_raw)
        if payload:
            return TaskStatus(**payload)
        return None

    @staticmethod
    async def get_statuses(task_ids: list[str]) -> list[TaskStatus]:
        """
        批量获取多个任务的状态 (增加解压逻辑)
        """
        if not task_ids:
            return []

        tasks_data_compressed = await hash_get_many(REDIS_HASH_KEY, task_ids)

        statuses = []
        for i, raw_value in enumerate(tasks_data_compressed):
            payload = TaskStatusService._deserialize(raw_value)
            if payload:
                statuses.append(TaskStatus(**payload))
            else:
                statuses.append(
                    TaskStatus(
                        taskId=task_ids[i], status=UploadStatus.IDLE, message="Task not found."
                    )
                )
        return statuses
