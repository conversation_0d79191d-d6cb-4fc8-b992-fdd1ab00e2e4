"""Token 管理服务"""

import json
from datetime import timed<PERSON>ta

from jose import jwt

from app.config.auth_config import AuthConfig
from app.core.logging import logger
from app.db.redis import (
    delete_key,
    get_key,
    set_add,
    set_get_all_members,
    set_key,
    set_key_expire,
    set_remove,
)
from app.services.interfaces.token_service_interface import ITokenService
from app.utils.time_utils import TimeUtils, JWTTimeHelper


class TokenService(ITokenService):
    """Token 管理服务"""

    def __init__(self, auth_config: AuthConfig):
        """初始化Token服务"""
        self.auth_config = auth_config

    def _get_token_key(self, token: str) -> str:
        """获取 token 在 Redis 中的键"""
        return f"token:{token}"

    def _get_user_tokens_key(self, username: str) -> str:
        """获取用户所有 token 在 Redis 中的键"""
        return f"user_tokens:{username}"

    def _get_device_token_key(self, username: str, device_id: int, token_type: str | None = None) -> str:
        """获取设备 token 在 Redis 中的键"""
        base_key = f"device_token:{username}:{device_id}"
        if token_type:
            safe_type = token_type.replace(":", "_")
            return f"{base_key}:{safe_type}"
        return base_key

    @staticmethod
    def _mask_token(token: str | None) -> str:
        """对 token 进行脱敏以便在日志中安全输出"""
        if not token:
            return ""
        if len(token) <= 20:
            return token
        return f"{token[:10]}...{token[-6:]}"

    async def create_token(
        self,
        username: str,
        device_id: int | None = None,
        expires_delta: timedelta | None = None,
        token_type: str = "access",
    ) -> str:
        """创建并存储 token"""
        try:
            to_encode = {"sub": username, "type": token_type}
            if device_id:
                to_encode["device_id"] = device_id

            if expires_delta:
                expire = TimeUtils.calculate_expiry_seconds(int(expires_delta.total_seconds()))
            else:
                expire = TimeUtils.calculate_expiry(self.auth_config.ACCESS_TOKEN_EXPIRE_MINUTES)

            to_encode["exp"] = expire
            token = jwt.encode(to_encode, self.auth_config.SECRET_KEY, algorithm="HS256")

            token_info = {
                "username": username,
                "device_id": device_id,
                "token_type": token_type,
                "created_at": TimeUtils.format_for_jwt(TimeUtils.get_utc_now()),
                "expires_at": TimeUtils.format_for_jwt(expire),
            }

            expire_seconds = (
                int(expires_delta.total_seconds())
                if expires_delta
                else self.auth_config.ACCESS_TOKEN_EXPIRE_MINUTES * 60
            )

            await set_key(
                self._get_token_key(token),
                json.dumps(token_info),
                expire=expire_seconds,
            )

            user_tokens_key = self._get_user_tokens_key(username)
            await set_add(user_tokens_key, token)
            await set_key_expire(user_tokens_key, expire=expire_seconds)

            if device_id:
                normalized_type = token_type or "access"
                device_token_key = self._get_device_token_key(
                    username, device_id, normalized_type
                )
                old_token = await get_key(device_token_key, fallback_to_master=True)

                if not old_token and normalized_type == "access":
                    legacy_key = self._get_device_token_key(username, device_id)
                    legacy_token = await get_key(legacy_key, fallback_to_master=True)
                    if legacy_token:
                        logger.debug(
                            f"检测到旧格式设备 token，将迁移并撤销: user={username} device={device_id} token_type={normalized_type} old_token={self._mask_token(legacy_token)}"
                        )
                        old_token = legacy_token
                        await delete_key(legacy_key)

                if old_token:
                    logger.debug(
                        f"检测到设备现有 token，将撤销旧令牌: user={username} device={device_id} token_type={normalized_type} old_token={self._mask_token(old_token)}"
                    )
                    await self.revoke_token(old_token)

                await set_key(device_token_key, token, expire=expire_seconds)
                logger.debug(
                    f"设备 token 更新完成: user={username} device={device_id} token_type={normalized_type} token={self._mask_token(token)} expire={expire_seconds}"
                )

            logger.info(f"Token 创建成功，用户：{username}，设备：{device_id}")
            return token

        except Exception as e:
            logger.error(f"创建 token 失败：{str(e)}")
            raise

    async def verify_token(self, token: str) -> dict | None:
        """验证 token 是否有效"""
        try:
            token_info_str = await get_key(
                self._get_token_key(token), fallback_to_master=True
            )
            if not token_info_str:
                logger.debug(
                    f"未在缓存中找到 token 信息，token={self._mask_token(token)}"
                )
                return None

            import asyncio

            loop = asyncio.get_running_loop()
            try:
                payload = await loop.run_in_executor(
                    None, jwt.decode, token, self.auth_config.SECRET_KEY, ["HS256"]
                )
            except jwt.ExpiredSignatureError:
                logger.debug(
                    f"Token 已过期，执行撤销: token={self._mask_token(token)}"
                )
                await self.revoke_token(token)
                return None
            except jwt.JWTError:
                logger.debug(
                    f"Token 校验失败，执行撤销: token={self._mask_token(token)}"
                )
                await self.revoke_token(token)
                return None

            token_info = json.loads(token_info_str)
            token_info.update(payload)
            user_for_log = token_info.get("username") or token_info.get("sub")
            device_for_log = token_info.get("device_id")
            type_for_log = token_info.get("token_type") or token_info.get("type")
            logger.debug(
                f"Token 验证通过: user={user_for_log} device={device_for_log} token_type={type_for_log} token={self._mask_token(token)}"
            )
            return token_info

        except Exception as e:
            logger.error(f"验证 token 失败：{str(e)}")
            return None

    async def revoke_token(self, token: str) -> bool:
        """撤销 token"""
        try:
            token_info_str = await get_key(
                self._get_token_key(token), fallback_to_master=True
            )
            if not token_info_str:
                logger.debug(
                    f"撤销请求未命中缓存，token={self._mask_token(token)}"
                )
                return False

            token_info = json.loads(token_info_str)
            username = token_info.get("username")
            device_id = token_info.get("device_id")
            token_type = token_info.get("token_type") or token_info.get("type") or "access"
            logger.debug(
                f"开始撤销 token: user={username} device={device_id} token_type={token_type} token={self._mask_token(token)}"
            )

            await delete_key(self._get_token_key(token))

            if username:
                await set_remove(self._get_user_tokens_key(username), token)
                logger.debug(
                    f"已从用户 token 集合移除: user={username} token={self._mask_token(token)}"
                )

            if username and device_id:
                normalized_type = token_type or "access"
                device_token_key = self._get_device_token_key(
                    username, device_id, normalized_type
                )
                current_token = await get_key(
                    device_token_key, fallback_to_master=True
                )
                if current_token == token:
                    await delete_key(device_token_key)
                    logger.debug(
                        f"已删除设备 token 键: key={device_token_key} token={self._mask_token(token)}"
                    )

                legacy_key = self._get_device_token_key(username, device_id)
                legacy_token = await get_key(legacy_key, fallback_to_master=True)
                if legacy_token == token:
                    await delete_key(legacy_key)
                    logger.debug(
                        f"已删除旧格式设备 token 键: key={legacy_key} token={self._mask_token(token)}"
                    )

            logger.info(f"Token 撤销成功，用户：{username}，设备：{device_id}")
            return True

        except Exception as e:
            logger.error(f"撤销 token 失败：{str(e)}")
            return False

    async def revoke_user_tokens(self, username: str) -> int:
        """撤销用户的所有 token"""
        try:
            user_tokens_key = self._get_user_tokens_key(username)
            tokens = await set_get_all_members(user_tokens_key)

            revoked_count = 0
            for token in tokens:
                if await self.revoke_token(token):
                    revoked_count += 1

            await delete_key(user_tokens_key)

            logger.info(f"用户 {username} 的所有 token 已撤销，共 {revoked_count} 个")
            return revoked_count

        except Exception as e:
            logger.error(f"撤销用户 token 失败：{str(e)}")
            return 0

    async def revoke_device_token(self, username: str, device_id: int) -> bool:
        """撤销设备的 token"""
        try:
            user_tokens_key = self._get_user_tokens_key(username)
            tokens = await set_get_all_members(user_tokens_key)

            revoked = False
            for token in tokens:
                token_info_str = await get_key(
                    self._get_token_key(token), fallback_to_master=True
                )
                if not token_info_str:
                    continue

                try:
                    token_info = json.loads(token_info_str)
                except (TypeError, ValueError, json.JSONDecodeError):
                    continue

                if token_info.get("device_id") != device_id:
                    continue

                if await self.revoke_token(token):
                    revoked = True

            if revoked:
                return True

            # 兼容旧格式的设备 token 键
            legacy_key = self._get_device_token_key(username, device_id)
            legacy_token = await get_key(legacy_key, fallback_to_master=True)
            if legacy_token:
                return await self.revoke_token(legacy_token)

            return False

        except Exception as e:
            logger.error(f"撤销设备 token 失败：{str(e)}")
            return False

    async def get_user_active_tokens(self, username: str) -> list[dict]:
        """获取用户的活跃 token 列表"""
        try:
            user_tokens_key = self._get_user_tokens_key(username)
            tokens = await set_get_all_members(user_tokens_key)

            active_tokens = []
            for token in tokens:
                token_info = await self.verify_token(token)
                if token_info:
                    active_tokens.append(token_info)

            return active_tokens

        except Exception as e:
            logger.error(f"获取用户活跃 token 失败：{str(e)}")
            return []

    async def create_access_token(
        self, data: dict, expires_delta: timedelta | None = None, device_id: int | None = None
    ) -> str:
        """创建访问令牌"""
        username = data.get("sub")
        token_type = data.get("type", "access")

        if not username:
            raise ValueError("Token data must contain 'sub' field")

        try:
            token = await self.create_token(
                username=username,
                device_id=device_id,
                expires_delta=expires_delta,
                token_type=token_type,
            )

            logger.info(f"访问令牌创建成功: 用户={username}, 设备={device_id}")
            return token

        except Exception as e:
            logger.error(f"创建访问令牌失败: 用户={username}, 错误: {str(e)}")
            raise

    async def refresh_token(self, refresh_token: str) -> str | None:
        """刷新令牌"""
        try:
            token_info = await self.verify_token(refresh_token)
            if not token_info or token_info.get("type") != "refresh":
                logger.warning("无效的刷新令牌")
                return None

            username = token_info.get("username")
            device_id = token_info.get("device_id")

            await self.revoke_token(refresh_token)

            new_token = await self.create_access_token(data={"sub": username}, device_id=device_id)

            logger.info(f"令牌刷新成功: 用户={username}")
            return new_token

        except Exception as e:
            logger.error(f"刷新令牌失败: {str(e)}")
            return None

    async def validate_token(self, token: str) -> dict | None:
        """验证令牌"""
        try:
            token_info = await self.verify_token(token)
            if token_info:
                logger.debug(f"令牌验证成功: 用户={token_info.get('username')}")
            return token_info

        except Exception as e:
            logger.error(f"令牌验证失败: {str(e)}")
            return None
