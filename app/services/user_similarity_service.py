"""
用户相似度计算服务

实现多种用户相似度计算算法：
1. 基于行为的相似度 - 用户交互行为模式
2. 基于兴趣的相似度 - 兴趣标签和偏好分类
3. 基于内容的相似度 - 用户创建和消费的内容类型
4. 综合相似度 - 多种算法的加权组合
"""

import json
import logging
import math
from datetime import datetime, timedelta

from sqlalchemy import and_, func, select
from sqlalchemy.ext.asyncio import AsyncSession

from app import crud, models
from app.services.recommendation_cache_service import RecommendationCacheService

logger = logging.getLogger(__name__)


class UserSimilarityService:
    """用户相似度计算服务"""

    def __init__(self):
        # 不同相似度类型的权重
        self.similarity_weights = {
            "behavior": 0.4,  # 行为相似度
            "interest": 0.3,  # 兴趣相似度
            "content": 0.2,  # 内容相似度
            "social": 0.1,  # 社交相似度
        }

        # 缓存服务
        self.cache_service = RecommendationCacheService()

    async def calculate_user_similarity(
        self,
        db: AsyncSession,
        user_id1: int,
        user_id2: int,
        similarity_types: list[str] | None = None,
    ) -> dict[str, float]:
        """
        计算两个用户之间的相似度

        Args:
            db: 数据库会话
            user_id1: 用户1的ID
            user_id2: 用户2的ID
            similarity_types: 要计算的相似度类型列表，None表示计算所有类型

        Returns:
            包含各种相似度分数的字典
        """
        if similarity_types is None:
            similarity_types = ["behavior", "interest", "content", "social"]

        # 检查缓存
        cached_result = await self.cache_service.get_user_similarity_cache(user_id1, user_id2)
        if cached_result:
            return {k: v for k, v in cached_result.items() if k in similarity_types}

        similarities = {}

        try:
            # 计算各种类型的相似度
            if "behavior" in similarity_types:
                similarities["behavior"] = await self._calculate_behavior_similarity(
                    db, user_id1, user_id2
                )

            if "interest" in similarity_types:
                similarities["interest"] = await self._calculate_interest_similarity(
                    db, user_id1, user_id2
                )

            if "content" in similarity_types:
                similarities["content"] = await self._calculate_content_similarity(
                    db, user_id1, user_id2
                )

            if "social" in similarity_types:
                similarities["social"] = await self._calculate_social_similarity(
                    db, user_id1, user_id2
                )

            # 计算综合相似度
            similarities["overall"] = self._calculate_overall_similarity(similarities)

            # 缓存结果
            await self.cache_service.set_user_similarity_cache(user_id1, user_id2, similarities)

            return similarities

        except Exception as e:
            logger.error(f"计算用户相似度失败: {e}")
            return dict.fromkeys(similarity_types, 0.0)

    async def _calculate_behavior_similarity(
        self, db: AsyncSession, user_id1: int, user_id2: int
    ) -> float:
        """计算基于行为的相似度"""
        try:
            # 获取两个用户的行为特征
            user1_behaviors = await self._get_user_behavior_features(db, user_id1)
            user2_behaviors = await self._get_user_behavior_features(db, user_id2)

            if not user1_behaviors or not user2_behaviors:
                return 0.0

            # 计算余弦相似度
            return self._cosine_similarity(user1_behaviors, user2_behaviors)

        except Exception as e:
            logger.error(f"计算行为相似度失败: {e}")
            return 0.0

    async def _calculate_interest_similarity(
        self, db: AsyncSession, user_id1: int, user_id2: int
    ) -> float:
        """计算基于兴趣的相似度"""
        try:
            # 获取用户画像
            profile1 = await crud.user_profile.get_by_user_id(db, user_id=user_id1)
            profile2 = await crud.user_profile.get_by_user_id(db, user_id=user_id2)

            if not profile1 or not profile2:
                return 0.0

            # 解析兴趣标签
            interests1 = {}
            interests2 = {}
            categories1 = {}
            categories2 = {}

            if profile1.interest_tags:
                try:
                    interests1 = json.loads(profile1.interest_tags)
                except json.JSONDecodeError:
                    pass

            if profile2.interest_tags:
                try:
                    interests2 = json.loads(profile2.interest_tags)
                except json.JSONDecodeError:
                    pass

            if profile1.preferred_categories:
                try:
                    categories1 = json.loads(profile1.preferred_categories)
                except json.JSONDecodeError:
                    pass

            if profile2.preferred_categories:
                try:
                    categories2 = json.loads(profile2.preferred_categories)
                except json.JSONDecodeError:
                    pass

            # 计算兴趣标签相似度
            interest_sim = self._dict_cosine_similarity(interests1, interests2)

            # 计算分类偏好相似度
            category_sim = self._dict_cosine_similarity(categories1, categories2)

            # 加权平均
            return 0.7 * interest_sim + 0.3 * category_sim

        except Exception as e:
            logger.error(f"计算兴趣相似度失败: {e}")
            return 0.0

    async def _calculate_content_similarity(
        self, db: AsyncSession, user_id1: int, user_id2: int
    ) -> float:
        """计算基于内容的相似度"""
        try:
            # 获取用户创建的内容特征
            content1_features = await self._get_user_content_features(db, user_id1)
            content2_features = await self._get_user_content_features(db, user_id2)

            if not content1_features or not content2_features:
                return 0.0

            return self._cosine_similarity(content1_features, content2_features)

        except Exception as e:
            logger.error(f"计算内容相似度失败: {e}")
            return 0.0

    async def _calculate_social_similarity(
        self, db: AsyncSession, user_id1: int, user_id2: int
    ) -> float:
        """计算基于社交网络的相似度"""
        try:
            # 获取共同关注的用户数量
            mutual_following = await self._get_mutual_following_count(db, user_id1, user_id2)

            # 获取两个用户的关注总数
            user1_following_count = await self._get_user_following_count(db, user_id1)
            user2_following_count = await self._get_user_following_count(db, user_id2)

            if user1_following_count == 0 or user2_following_count == 0:
                return 0.0

            # 使用Jaccard相似度
            union_count = user1_following_count + user2_following_count - mutual_following
            if union_count == 0:
                return 0.0

            return mutual_following / union_count

        except Exception as e:
            logger.error(f"计算社交相似度失败: {e}")
            return 0.0

    async def _get_user_behavior_features(self, db: AsyncSession, user_id: int) -> dict[str, float]:
        """获取用户行为特征向量"""
        try:
            # 获取最近30天的用户交互数据
            thirty_days_ago = datetime.utcnow() - timedelta(days=30)

            stmt = (
                select(
                    models.UserInteraction.content_type,
                    models.UserInteraction.interaction_type,
                    func.count().label("count"),
                    func.sum(models.UserInteraction.weight).label("total_weight"),
                )
                .where(
                    and_(
                        models.UserInteraction.user_id == user_id,
                        models.UserInteraction.created_at >= thirty_days_ago,
                    )
                )
                .group_by(
                    models.UserInteraction.content_type, models.UserInteraction.interaction_type
                )
            )

            result = await db.execute(stmt)
            interactions = result.fetchall()

            features = {}
            for content_type, interaction_type, count, total_weight in interactions:
                key = f"{content_type}_{interaction_type}"
                features[key] = float(total_weight or 0)

            return features

        except Exception as e:
            logger.error(f"获取用户行为特征失败: {e}")
            return {}

    async def _get_user_content_features(self, db: AsyncSession, user_id: int) -> dict[str, float]:
        """获取用户内容特征向量"""
        try:
            features = {}

            # 获取用户创建的文章数量和分类
            article_stmt = (
                select(
                    models.Category.name,
                    func.count().label("count"),
                )
                .select_from(models.Article)
                .join(models.Category, models.Article.category_id == models.Category.id)
                .where(models.Article.author_id == user_id)
                .group_by(models.Category.name)
            )

            result = await db.execute(article_stmt)
            article_categories = result.fetchall()

            for category_name, count in article_categories:
                features[f"article_{category_name}"] = float(count)

            # 获取用户创建的视频数量和分类
            video_stmt = (
                select(
                    models.Category.name,
                    func.count().label("count"),
                )
                .select_from(models.Video)
                .join(models.Category, models.Video.category_id == models.Category.id)
                .where(models.Video.author_id == user_id)
                .group_by(models.Category.name)
            )

            result = await db.execute(video_stmt)
            video_categories = result.fetchall()

            for category_name, count in video_categories:
                features[f"video_{category_name}"] = float(count)

            return features

        except Exception as e:
            logger.error(f"获取用户内容特征失败: {e}")
            return {}

    async def _get_mutual_following_count(
        self, db: AsyncSession, user_id1: int, user_id2: int
    ) -> int:
        """获取两个用户的共同关注数量"""
        try:
            # 使用子查询计算共同关注
            stmt = select(func.count()).select_from(
                select(models.user_follow.c.followed_id)
                .where(models.user_follow.c.follower_id == user_id1)
                .intersect(
                    select(models.user_follow.c.followed_id).where(
                        models.user_follow.c.follower_id == user_id2
                    )
                )
                .subquery()
            )

            result = await db.execute(stmt)
            return result.scalar() or 0

        except Exception as e:
            logger.error(f"获取共同关注数量失败: {e}")
            return 0

    async def _get_user_following_count(self, db: AsyncSession, user_id: int) -> int:
        """获取用户关注的总数"""
        try:
            stmt = select(func.count()).select_from(
                select(models.user_follow.c.followed_id)
                .where(models.user_follow.c.follower_id == user_id)
                .subquery()
            )

            result = await db.execute(stmt)
            return result.scalar() or 0

        except Exception as e:
            logger.error(f"获取用户关注数量失败: {e}")
            return 0

    def _cosine_similarity(self, features1: dict[str, float], features2: dict[str, float]) -> float:
        """计算余弦相似度"""
        try:
            all_keys = set(features1.keys()) | set(features2.keys())
            if not all_keys:
                return 0.0

            vector1 = [features1.get(key, 0) for key in all_keys]
            vector2 = [features2.get(key, 0) for key in all_keys]

            dot_product = sum(a * b for a, b in zip(vector1, vector2, strict=False))
            norm1 = math.sqrt(sum(a * a for a in vector1))
            norm2 = math.sqrt(sum(b * b for b in vector2))

            if norm1 == 0 or norm2 == 0:
                return 0.0

            return dot_product / (norm1 * norm2)

        except Exception:
            return 0.0

    def _dict_cosine_similarity(self, dict1: dict[str, float], dict2: dict[str, float]) -> float:
        """计算字典的余弦相似度"""
        return self._cosine_similarity(dict1, dict2)

    def _calculate_overall_similarity(self, similarities: dict[str, float]) -> float:
        """计算综合相似度"""
        try:
            weighted_sum = 0.0
            total_weight = 0.0

            for sim_type, score in similarities.items():
                if sim_type in self.similarity_weights:
                    weight = self.similarity_weights[sim_type]
                    weighted_sum += score * weight
                    total_weight += weight

            if total_weight == 0:
                return 0.0

            return weighted_sum / total_weight

        except Exception:
            return 0.0

    async def get_similar_users(
        self,
        db: AsyncSession,
        user_id: int,
        similarity_type: str = "overall",
        min_similarity: float = 0.1,
        limit: int = 50,
    ) -> list[tuple[int, float]]:
        """
        获取与指定用户相似的用户列表

        Args:
            db: 数据库会话
            user_id: 目标用户ID
            similarity_type: 相似度类型
            min_similarity: 最小相似度阈值
            limit: 返回数量限制

        Returns:
            相似用户列表，每个元素为(user_id, similarity_score)
        """
        try:
            # 获取所有活跃用户
            stmt = (
                select(models.User.id)
                .where(
                    and_(
                        models.User.is_active == True,
                        models.User.id != user_id,
                    )
                )
                .limit(1000)
            )  # 限制计算范围

            result = await db.execute(stmt)
            candidate_users = [row[0] for row in result.fetchall()]

            similar_users = []

            # 批量计算相似度
            for candidate_user_id in candidate_users:
                similarities = await self.calculate_user_similarity(
                    db, user_id, candidate_user_id, [similarity_type]
                )

                score = similarities.get(similarity_type, 0.0)
                if score >= min_similarity:
                    similar_users.append((candidate_user_id, score))

            # 按相似度排序
            similar_users.sort(key=lambda x: x[1], reverse=True)

            return similar_users[:limit]

        except Exception as e:
            logger.error(f"获取相似用户失败: {e}")
            return []
