"""
用户推荐服务

实现多种用户推荐算法：
1. 协同过滤推荐 - 基于用户行为相似度
2. 基于内容的推荐 - 基于用户兴趣标签和偏好
3. 社交网络推荐 - 朋友的朋友、共同关注
4. 热门用户推荐 - 活跃度和内容质量
"""

import json
import logging
from collections import defaultdict
from typing import Any

from sqlalchemy import and_, desc, func, select, text
from sqlalchemy.ext.asyncio import AsyncSession

from app import crud, models, schemas
from app.services.recommendation_cache_service import RecommendationCacheService

logger = logging.getLogger(__name__)


class UserRecommendationService:
    """用户推荐服务"""

    def __init__(self):
        self.cache_service = RecommendationCacheService()
        # 不同推荐算法的权重
        self.algorithm_weights = {
            "collaborative": 0.4,  # 协同过滤
            "content_based": 0.3,  # 基于内容
            "social_network": 0.2,  # 社交网络
            "popular": 0.1,  # 热门用户
        }

    async def get_user_recommendations(
        self,
        db: AsyncSession,
        user_id: int,
        page: int = 1,
        page_size: int = 20,
        algorithm_type: str | None = None,
    ) -> schemas.PaginatedRecommendationResponse:
        """
        获取用户推荐列表

        Args:
            db: 数据库会话
            user_id: 当前用户ID
            page: 页码
            page_size: 每页大小
            algorithm_type: 指定算法类型，None表示混合推荐

        Returns:
            分页的用户推荐响应
        """
        try:
            # 检查缓存
            cached_recs = await self._get_cached_recommendations(
                user_id, algorithm_type or "hybrid", page, page_size
            )
            if cached_recs:
                return cached_recs

            # 获取当前用户已关注的用户ID列表
            following_ids = await self._get_user_following_ids(db, user_id)

            # 根据算法类型生成推荐
            if algorithm_type == "collaborative":
                recommendations = await self._collaborative_filtering(db, user_id, following_ids)
            elif algorithm_type == "content_based":
                recommendations = await self._content_based_recommendation(
                    db, user_id, following_ids
                )
            elif algorithm_type == "social_network":
                recommendations = await self._social_network_recommendation(
                    db, user_id, following_ids
                )
            elif algorithm_type == "popular":
                recommendations = await self._popular_user_recommendation(
                    db, user_id, following_ids
                )
            elif algorithm_type is None or algorithm_type == "hybrid":
                # 混合推荐
                recommendations = await self._hybrid_recommendation(db, user_id, following_ids)
            else:
                raise ValueError("不支持的推荐算法类型")

            # 分页处理
            total_count = len(recommendations)
            start_idx = (page - 1) * page_size
            end_idx = start_idx + page_size
            page_recommendations = recommendations[start_idx:end_idx]

            # 构建响应
            response = schemas.PaginatedRecommendationResponse(
                items=page_recommendations,
                total_count=total_count,
                page=page,
                page_size=page_size,
                has_next=end_idx < total_count,
                algorithm_type=algorithm_type or "hybrid",
            )

            # 缓存结果
            await self.cache_service.set_user_recommendations_cache(
                user_id, algorithm_type or "hybrid", recommendations
            )

            return response

        except ValueError:
            raise
        except Exception as e:
            logger.error(f"获取用户推荐失败: {e}")
            # 返回空结果
            return schemas.PaginatedRecommendationResponse(
                items=[],
                total_count=0,
                page=page,
                page_size=page_size,
                has_next=False,
                algorithm_type=algorithm_type or "hybrid",
            )

    async def _get_user_following_ids(self, db: AsyncSession, user_id: int) -> set[int]:
        """获取用户已关注的用户ID列表"""
        try:
            user = await crud.user.get(db, id=user_id)
            if not user:
                return set()

            # 获取关注列表
            stmt = select(models.User.id).where(
                models.User.id.in_(
                    select(models.user_follow.c.followed_id).where(
                        models.user_follow.c.follower_id == user_id
                    )
                )
            )
            result = await db.execute(stmt)
            following_ids = {row[0] for row in result.fetchall()}
            following_ids.add(user_id)  # 排除自己
            return following_ids

        except Exception as e:
            logger.error(f"获取用户关注列表失败: {e}")
            return {user_id}

    async def _collaborative_filtering(
        self, db: AsyncSession, user_id: int, following_ids: set[int]
    ) -> list[schemas.RecommendationItem]:
        """协同过滤推荐 - 基于用户行为相似度"""
        try:
            # 1. 获取当前用户的行为特征
            user_interactions = await self._get_user_interaction_features(db, user_id)
            if not user_interactions:
                return []

            # 2. 计算与其他用户的相似度
            similar_users = await self._calculate_user_similarity(db, user_id, user_interactions)

            # 3. 基于相似用户的关注推荐
            recommendations = []
            for similar_user_id, similarity_score in similar_users[:50]:  # 取前50个相似用户
                if similar_user_id in following_ids:
                    continue

                # 获取相似用户的信息
                user = await crud.user.get(db, id=similar_user_id)
                if user and user.is_active:
                    recommendations.append(
                        schemas.RecommendationItem(
                            content_type="user",
                            content_id=similar_user_id,
                            score=similarity_score * self.algorithm_weights["collaborative"],
                            reason="与你行为相似的用户",
                            algorithm_type="collaborative",
                        )
                    )

            return sorted(recommendations, key=lambda x: x.score, reverse=True)[:100]

        except Exception as e:
            logger.error(f"协同过滤推荐失败: {e}")
            return []

    async def _content_based_recommendation(
        self, db: AsyncSession, user_id: int, following_ids: set[int]
    ) -> list[schemas.RecommendationItem]:
        """基于内容的推荐 - 基于用户兴趣标签和偏好"""
        try:
            # 1. 获取用户画像
            user_profile = await crud.user_profile.get_by_user_id(db, user_id=user_id)
            if not user_profile:
                return []

            # 2. 解析用户兴趣标签
            interest_tags = {}
            if user_profile.interest_tags:
                try:
                    interest_tags = json.loads(user_profile.interest_tags)
                except json.JSONDecodeError:
                    pass

            preferred_categories = {}
            if user_profile.preferred_categories:
                try:
                    preferred_categories = json.loads(user_profile.preferred_categories)
                except json.JSONDecodeError:
                    pass

            if not interest_tags and not preferred_categories:
                return []

            # 3. 查找具有相似兴趣的用户
            recommendations = []

            # 查询其他用户的画像
            stmt = select(models.UserProfile).where(
                and_(
                    models.UserProfile.user_id != user_id,
                    models.UserProfile.user_id.notin_(following_ids),
                    models.UserProfile.interest_tags.isnot(None),
                )
            )
            result = await db.execute(stmt)
            other_profiles = result.scalars().all()

            for profile in other_profiles:
                try:
                    other_interests = json.loads(profile.interest_tags or "{}")
                    other_categories = json.loads(profile.preferred_categories or "{}")

                    # 计算兴趣相似度
                    similarity_score = self._calculate_interest_similarity(
                        interest_tags, preferred_categories, other_interests, other_categories
                    )

                    if similarity_score > 0.1:  # 相似度阈值
                        recommendations.append(
                            schemas.RecommendationItem(
                                content_type="user",
                                content_id=profile.user_id,
                                score=similarity_score * self.algorithm_weights["content_based"],
                                reason="兴趣相似的用户",
                                algorithm_type="content_based",
                            )
                        )
                except (json.JSONDecodeError, AttributeError):
                    continue

            return sorted(recommendations, key=lambda x: x.score, reverse=True)[:100]

        except Exception as e:
            logger.error(f"基于内容的推荐失败: {e}")
            return []

    async def _social_network_recommendation(
        self, db: AsyncSession, user_id: int, following_ids: set[int]
    ) -> list[schemas.RecommendationItem]:
        """社交网络推荐 - 朋友的朋友、共同关注"""
        try:
            recommendations = []

            # 1. 朋友的朋友推荐（二度关系）
            friends_of_friends = await self._get_friends_of_friends(db, user_id, following_ids)
            for friend_id, mutual_count in friends_of_friends.items():
                if friend_id not in following_ids:
                    score = min(mutual_count * 0.1, 1.0)  # 共同关注数量影响分数
                    recommendations.append(
                        schemas.RecommendationItem(
                            content_type="user",
                            content_id=friend_id,
                            score=score * self.algorithm_weights["social_network"],
                            reason=f"你们有{mutual_count}个共同关注",
                            algorithm_type="social_network",
                        )
                    )

            return sorted(recommendations, key=lambda x: x.score, reverse=True)[:100]

        except Exception as e:
            logger.error(f"社交网络推荐失败: {e}")
            return []

    async def _popular_user_recommendation(
        self,
        db: AsyncSession,
        user_id: int,  # noqa: ARG002
        following_ids: set[int],
    ) -> list[schemas.RecommendationItem]:
        """热门用户推荐 - 基于活跃度和内容质量"""
        try:
            # 查询热门用户（基于粉丝数、内容数量等）
            stmt = (
                select(models.User, models.UserStats)
                .join(models.UserStats, models.User.id == models.UserStats.user_id)
                .where(
                    and_(
                        models.User.id.notin_(following_ids),
                        models.User.is_active == True,
                        models.UserStats.follower_count > 10,  # 至少有10个粉丝
                    )
                )
                .order_by(desc(models.UserStats.follower_count))
                .limit(100)
            )

            result = await db.execute(stmt)
            popular_users = result.fetchall()

            recommendations = []
            for user, stats in popular_users:
                # 计算热门度分数
                popularity_score = min(stats.follower_count / 1000.0, 1.0)  # 标准化分数

                recommendations.append(
                    schemas.RecommendationItem(
                        content_type="user",
                        content_id=user.id,
                        score=popularity_score * self.algorithm_weights["popular"],
                        reason=f"热门用户 ({stats.follower_count}关注者)",
                        algorithm_type="popular",
                    )
                )

            return recommendations

        except Exception as e:
            logger.error(f"热门用户推荐失败: {e}")
            return []

    async def _hybrid_recommendation(
        self, db: AsyncSession, user_id: int, following_ids: set[int]
    ) -> list[schemas.RecommendationItem]:
        """混合推荐 - 结合多种算法"""
        try:
            all_recommendations = []

            # 获取各种算法的推荐结果
            collaborative_recs = await self._collaborative_filtering(db, user_id, following_ids)
            content_recs = await self._content_based_recommendation(db, user_id, following_ids)
            social_recs = await self._social_network_recommendation(db, user_id, following_ids)
            popular_recs = await self._popular_user_recommendation(db, user_id, following_ids)

            # 合并推荐结果
            all_recommendations.extend(collaborative_recs)
            all_recommendations.extend(content_recs)
            all_recommendations.extend(social_recs)
            all_recommendations.extend(popular_recs)

            # 按用户ID去重，保留最高分数
            user_scores = {}
            for rec in all_recommendations:
                user_id_key = rec.content_id
                if user_id_key not in user_scores or rec.score > user_scores[user_id_key].score:
                    user_scores[user_id_key] = rec

            # 排序并返回
            final_recommendations = list(user_scores.values())
            return sorted(final_recommendations, key=lambda x: x.score, reverse=True)[:200]

        except Exception as e:
            logger.error(f"混合推荐失败: {e}")
            return []

    async def _get_user_interaction_features(
        self, db: AsyncSession, user_id: int
    ) -> dict[str, Any]:
        """获取用户交互特征"""
        try:
            # 获取用户最近的交互行为
            stmt = (
                select(
                    models.UserInteraction.content_type,
                    models.UserInteraction.interaction_type,
                    func.count().label("count"),
                    func.sum(models.UserInteraction.weight).label("total_weight"),
                )
                .where(models.UserInteraction.user_id == user_id)
                .group_by(
                    models.UserInteraction.content_type, models.UserInteraction.interaction_type
                )
            )

            result = await db.execute(stmt)
            interactions = result.fetchall()

            features = {}
            for content_type, interaction_type, count, total_weight in interactions:
                key = f"{content_type}_{interaction_type}"
                features[key] = {
                    "count": count,
                    "weight": float(total_weight or 0),
                }

            return features

        except Exception as e:
            logger.error(f"获取用户交互特征失败: {e}")
            return {}

    async def _calculate_user_similarity(
        self, db: AsyncSession, user_id: int, user_features: dict[str, Any]
    ) -> list[tuple[int, float]]:
        """计算用户相似度"""
        try:
            # 获取其他用户的交互特征
            stmt = (
                select(
                    models.UserInteraction.user_id,
                    models.UserInteraction.content_type,
                    models.UserInteraction.interaction_type,
                    func.count().label("count"),
                    func.sum(models.UserInteraction.weight).label("total_weight"),
                )
                .where(models.UserInteraction.user_id != user_id)
                .group_by(
                    models.UserInteraction.user_id,
                    models.UserInteraction.content_type,
                    models.UserInteraction.interaction_type,
                )
            )

            result = await db.execute(stmt)
            other_interactions = result.fetchall()

            # 构建其他用户的特征向量
            other_user_features = defaultdict(dict)
            for (
                other_user_id,
                content_type,
                interaction_type,
                count,
                total_weight,
            ) in other_interactions:
                key = f"{content_type}_{interaction_type}"
                other_user_features[other_user_id][key] = {
                    "count": count,
                    "weight": float(total_weight or 0),
                }

            # 计算余弦相似度
            similarities = []
            for other_user_id, other_features in other_user_features.items():
                similarity = self._cosine_similarity(user_features, other_features)
                if similarity > 0.1:  # 相似度阈值
                    similarities.append((other_user_id, similarity))

            return sorted(similarities, key=lambda x: x[1], reverse=True)

        except Exception as e:
            logger.error(f"计算用户相似度失败: {e}")
            return []

    def _cosine_similarity(self, features1: dict[str, Any], features2: dict[str, Any]) -> float:
        """计算余弦相似度"""
        try:
            # 获取所有特征键
            all_keys = set(features1.keys()) | set(features2.keys())
            if not all_keys:
                return 0.0

            # 构建向量
            vector1 = []
            vector2 = []
            for key in all_keys:
                weight1 = features1.get(key, {}).get("weight", 0)
                weight2 = features2.get(key, {}).get("weight", 0)
                vector1.append(weight1)
                vector2.append(weight2)

            # 计算余弦相似度
            dot_product = sum(a * b for a, b in zip(vector1, vector2, strict=False))
            norm1 = sum(a * a for a in vector1) ** 0.5
            norm2 = sum(b * b for b in vector2) ** 0.5

            if norm1 == 0 or norm2 == 0:
                return 0.0

            return dot_product / (norm1 * norm2)

        except Exception:
            return 0.0

    def _calculate_interest_similarity(
        self,
        interests1: dict[str, float],
        categories1: dict[str, float],
        interests2: dict[str, float],
        categories2: dict[str, float],
    ) -> float:
        """计算兴趣相似度"""
        try:
            # 兴趣标签相似度
            interest_sim = self._dict_cosine_similarity(interests1, interests2)

            # 分类偏好相似度
            category_sim = self._dict_cosine_similarity(categories1, categories2)

            # 加权平均
            return 0.7 * interest_sim + 0.3 * category_sim

        except Exception:
            return 0.0

    def _dict_cosine_similarity(self, dict1: dict[str, float], dict2: dict[str, float]) -> float:
        """计算字典的余弦相似度"""
        try:
            all_keys = set(dict1.keys()) | set(dict2.keys())
            if not all_keys:
                return 0.0

            vector1 = [dict1.get(key, 0) for key in all_keys]
            vector2 = [dict2.get(key, 0) for key in all_keys]

            dot_product = sum(a * b for a, b in zip(vector1, vector2, strict=False))
            norm1 = sum(a * a for a in vector1) ** 0.5
            norm2 = sum(b * b for b in vector2) ** 0.5

            if norm1 == 0 or norm2 == 0:
                return 0.0

            return dot_product / (norm1 * norm2)

        except Exception:
            return 0.0

    async def _get_friends_of_friends(
        self,
        db: AsyncSession,
        user_id: int,
        following_ids: set[int],  # noqa: ARG002
    ) -> dict[int, int]:
        """获取朋友的朋友（二度关系）"""
        try:
            # 查询朋友的朋友
            stmt = text("""
                SELECT
                    f2.followed_id as friend_of_friend_id,
                    COUNT(*) as mutual_count
                FROM user_follow f1
                JOIN user_follow f2 ON f1.followed_id = f2.follower_id
                WHERE f1.follower_id = :user_id
                    AND f2.followed_id != :user_id
                    AND f2.followed_id NOT IN (
                        SELECT followed_id FROM user_follow WHERE follower_id = :user_id
                    )
                GROUP BY f2.followed_id
                HAVING COUNT(*) >= 2
                ORDER BY mutual_count DESC
                LIMIT 100
            """)

            result = await db.execute(stmt, {"user_id": user_id})
            friends_of_friends = {row[0]: row[1] for row in result.fetchall()}

            return friends_of_friends

        except Exception as e:
            logger.error(f"获取朋友的朋友失败: {e}")
            return {}

    async def _get_cached_recommendations(
        self, user_id: int, algorithm_type: str, page: int, page_size: int
    ) -> schemas.PaginatedRecommendationResponse | None:
        """从缓存获取推荐结果"""
        try:
            cached_items = await self.cache_service.get_user_recommendations_cache(
                user_id, algorithm_type, page, page_size
            )

            if not cached_items:
                return None

            items = []
            for item_data, score in cached_items:
                try:
                    # 解析缓存的推荐项
                    parts = item_data.split(":")
                    if len(parts) >= 2:
                        content_id = int(parts[1])
                        items.append(
                            schemas.RecommendationItem(
                                content_type="user",
                                content_id=content_id,
                                score=float(score),
                                reason="缓存推荐",
                                algorithm_type=algorithm_type,
                            )
                        )
                except (ValueError, IndexError):
                    continue

            # 估算总数（这里简化处理）
            total_count = len(items) + (page - 1) * page_size
            if len(items) == page_size:
                total_count += 1  # 可能还有更多

            return schemas.PaginatedRecommendationResponse(
                items=items,
                total_count=total_count,
                page=page,
                page_size=page_size,
                has_next=len(items) == page_size,
                algorithm_type=algorithm_type,
            )

        except Exception as e:
            logger.error(f"获取缓存推荐失败: {e}")
            return None
