"""统一热门标签服务"""

from __future__ import annotations

import json
import logging
from datetime import UTC, datetime
from typing import Iterable, Sequence

from sqlalchemy import Select, select
from sqlalchemy.exc import ProgrammingError
from sqlalchemy.ext.asyncio import AsyncSession

from app.db.redis import (
    delete_key,
    get_key,
    get_redis_master,
    hash_get,
    hash_set,
    hyperloglog_add,
    hyperloglog_count,
    increment_key,
    set_key,
    sorted_set_get_range,
    sorted_set_get_score,
    sorted_set_increment_by,
)
from app.models.tag import Tag
from app.models.tag_stats import TagStats

logger = logging.getLogger(__name__)


class TagHotItem:
    """热门标签条目"""

    __slots__ = ("tag_id", "name", "content_type", "score", "window_score", "unique_users", "last_event_at")

    def __init__(
        self,
        *,
        tag_id: int,
        name: str,
        content_type: str,
        score: float,
        window_score: float,
        unique_users: int,
        last_event_at: datetime | None,
    ) -> None:
        self.tag_id = tag_id
        self.name = name
        self.content_type = content_type
        self.score = score
        self.window_score = window_score
        self.unique_users = unique_users
        self.last_event_at = last_event_at

    def model_dump(self) -> dict[str, object]:
        return {
            "tag_id": self.tag_id,
            "name": self.name,
            "content_type": self.content_type,
            "score": round(self.score, 4),
            "window_score": round(self.window_score, 4),
            "unique_users": self.unique_users,
            "last_event_at": self.last_event_at.isoformat() if self.last_event_at else None,
        }


class TagHotService:
    """统一热门标签服务"""

    def __init__(
        self,
        *,
        window_hours: int = 24,
        window_retention_hours: int = 72,
        fallback_limit: int = 200,
        meta_ttl_seconds: int = 7 * 24 * 3600,
    ) -> None:
        self.window_hours = window_hours
        self.window_retention_hours = window_retention_hours
        self.window_ttl_seconds = window_retention_hours * 3600
        self.fallback_limit = fallback_limit
        self.meta_ttl_seconds = meta_ttl_seconds

    # ----------------------- redis key helpers -----------------------
    def _hot_key(self, content_type: str) -> str:
        return f"tag:hot:{content_type}"

    def _window_key(self, content_type: str, window: str) -> str:
        return f"tag:hot:{content_type}:win:{window}"

    def _meta_key(self, tag_id: int) -> str:
        return f"tag:meta:{tag_id}"

    def _hll_key(self, content_type: str, tag_id: int) -> str:
        return f"tag:hll:{content_type}:{tag_id}"

    def _current_window(self, event_time: datetime | None = None) -> str:
        dt = (event_time or datetime.now(UTC)).replace(minute=0, second=0, microsecond=0)
        return dt.strftime("%Y%m%d%H")

    async def _increment_metric(self, metric: str, expire_seconds: int = 600) -> None:
        try:
            await increment_key(metric, expire=expire_seconds)
        except Exception as exc:  # pragma: no cover - 指标异常时写日志
            logger.debug("热门标签指标累计失败 metric=%s error=%s", metric, exc)

    # ----------------------- event ingestion -----------------------
    async def record_event(
        self,
        *,
        tag_id: int,
        content_type: str,
        score_delta: float = 1.0,
        user_id: int | None = None,
        event_time: datetime | None = None,
    ) -> None:
        """记录标签热度事件"""

        if score_delta == 0:
            return

        event_dt = event_time or datetime.now(UTC)
        str_tag_id = str(tag_id)

        # 热度排行榜
        try:
            await sorted_set_increment_by(self._hot_key(content_type), str_tag_id, score_delta)
            await sorted_set_increment_by(self._hot_key("global"), str_tag_id, score_delta)
        except Exception as exc:
            logger.warning("更新热门标签排行失败 tag_id=%s error=%s", tag_id, exc)
        else:
            await self._increment_metric(f"metrics:tag_hot:event_count:{content_type}")
            await self._increment_metric("metrics:tag_hot:event_count:global")

        # 窗口排行（用于趋势判断）
        window = self._current_window(event_dt)
        try:
            await sorted_set_increment_by(self._window_key(content_type, window), str_tag_id, score_delta)
            await sorted_set_increment_by(self._window_key("global", window), str_tag_id, score_delta)
            redis_master = await get_redis_master()
            await redis_master.expire(self._window_key(content_type, window), self.window_ttl_seconds)
            await redis_master.expire(self._window_key("global", window), self.window_ttl_seconds)
        except Exception as exc:
            logger.warning("更新热门标签窗口排行失败 tag_id=%s error=%s", tag_id, exc)

        # HyperLogLog 统计独立用户
        if user_id is not None:
            try:
                user_token = str(user_id)
                await hyperloglog_add(self._hll_key(content_type, tag_id), user_token)
                await hyperloglog_add(self._hll_key("global", tag_id), user_token)
            except Exception as exc:
                logger.debug("HyperLogLog 更新失败 tag_id=%s error=%s", tag_id, exc)
            else:
                await self._increment_metric(f"metrics:tag_hot:event_users:{content_type}")

        # 元信息记录
        try:
            meta_key = self._meta_key(tag_id)
            meta_payload = {
                f"{content_type}:last_event_ts": event_dt.isoformat(),
                "last_event_ts": event_dt.isoformat(),
            }
            await hash_set(meta_key, "last_event_ts", meta_payload["last_event_ts"])
            await hash_set(meta_key, f"{content_type}:last_event_ts", meta_payload[f"{content_type}:last_event_ts"])
            await hash_set(meta_key, "content_types", json.dumps(sorted({content_type, "global"})))
            redis_master = await get_redis_master()
            await redis_master.expire(meta_key, self.meta_ttl_seconds)
        except Exception as exc:
            logger.debug("更新热门标签元信息失败 tag_id=%s error=%s", tag_id, exc)

    # ----------------------- query interface -----------------------
    async def record_bulk_events(
        self,
        *,
        tag_ids: Iterable[int],
        content_type: str,
        score_delta: float = 1.0,
        user_id: int | None = None,
        event_time: datetime | None = None,
    ) -> None:
        for tag_id in tag_ids:
            await self.record_event(
                tag_id=tag_id,
                content_type=content_type,
                score_delta=score_delta,
                user_id=user_id,
                event_time=event_time,
            )

    async def get_hot_tags(
        self,
        db: AsyncSession,
        *,
        content_type: str,
        limit: int = 20,
    ) -> list[TagHotItem]:
        """获取热门标签列表"""

        if limit <= 0:
            return []

        hot_key = self._hot_key(content_type)
        redis_items = await sorted_set_get_range(hot_key, 0, limit - 1, with_scores=True, desc=True)

        if redis_items:
            tag_ids = [int(member) for member, _ in redis_items]
            tag_map = await self._load_tags(db, tag_ids)
            results: list[TagHotItem] = []
            for member, score in redis_items:
                tag_id = int(member)
                tag = tag_map.get(tag_id)
                if not tag:
                    continue
                window_score = await self._get_window_score(content_type, tag_id)
                unique_users = await self._get_unique_users(content_type, tag_id)
                last_event_at = await self._get_last_event_at(tag_id, content_type)
                results.append(
                    TagHotItem(
                        tag_id=tag_id,
                        name=tag.name,
                        content_type=content_type,
                        score=float(score),
                        window_score=window_score,
                        unique_users=unique_users,
                        last_event_at=last_event_at,
                    )
                )
            if results:
                await self._increment_metric(f"metrics:tag_hot:cache_hit:{content_type}")
                return results[:limit]

        # Redis 未命中，回退数据库
        logger.info("热门标签 Redis 未命中，使用数据库回退 content_type=%s", content_type)
        stmt: Select = (
            select(TagStats, Tag)
            .join(Tag, TagStats.tag_id == Tag.id)
            .where(TagStats.content_type == content_type)
            .order_by(TagStats.total_score.desc())
            .limit(min(limit, self.fallback_limit))
        )
        try:
            rows = await db.execute(stmt)
        except ProgrammingError as exc:
            if self._is_missing_tag_stats_error(exc):
                logger.warning(
                    "热门标签数据库回退失败，tag_stats 表缺失 content_type=%s error=%s",
                    content_type,
                    exc,
                )
                snapshot_payload = await self.restore_from_snapshot(content_type=content_type)
                items = self._items_from_snapshot(snapshot_payload, fallback_content_type=content_type)
                await self._increment_metric(f"metrics:tag_hot:cache_miss:{content_type}")
                return items[:limit]
            raise
        items: list[TagHotItem] = []
        for stats, tag in rows.all():
            items.append(
                TagHotItem(
                    tag_id=tag.id,
                    name=tag.name,
                    content_type=content_type,
                    score=float(stats.total_score or 0.0),
                    window_score=float(stats.window_delta or 0.0),
                    unique_users=int(stats.unique_users_est or 0),
                    last_event_at=stats.last_seen_at,
                )
                )

        await self._increment_metric(f"metrics:tag_hot:cache_miss:{content_type}")
        return items[:limit]

    @staticmethod
    def _is_missing_tag_stats_error(exc: ProgrammingError) -> bool:
        origin = getattr(exc, "orig", None)
        parts: list[str] = []
        if origin is not None:
            if hasattr(origin, "args") and origin.args:
                parts.extend(str(arg) for arg in origin.args)
            else:
                parts.append(str(origin))
        parts.append(str(exc))
        message = " ".join(parts).lower()
        return "tag_stats" in message and (
            "does not exist" in message
            or "undefined table" in message
            or "no such table" in message
        )

    def _items_from_snapshot(
        self,
        snapshot: Sequence[dict[str, object]],
        *,
        fallback_content_type: str,
    ) -> list[TagHotItem]:
        items: list[TagHotItem] = []
        for entry in snapshot:
            try:
                tag_id = int(entry.get("tag_id"))
                name_value = entry.get("name")
                if not name_value:
                    continue
                name = str(name_value)
                content_type = str(entry.get("content_type") or fallback_content_type)
                score = float(entry.get("score", 0.0))
                window_score = float(entry.get("window_score", 0.0))
                unique_users = int(entry.get("unique_users", 0))
                last_event_raw = entry.get("last_event_at")
            except (TypeError, ValueError):
                continue

            last_event_at: datetime | None = None
            if isinstance(last_event_raw, str):
                try:
                    last_event_at = datetime.fromisoformat(last_event_raw)
                except ValueError:
                    last_event_at = None
            elif isinstance(last_event_raw, datetime):
                last_event_at = last_event_raw

            items.append(
                TagHotItem(
                    tag_id=tag_id,
                    name=name,
                    content_type=content_type,
                    score=score,
                    window_score=window_score,
                    unique_users=unique_users,
                    last_event_at=last_event_at,
                )
            )
        return items

    async def cache_hot_tags_snapshot(
        self,
        *,
        content_type: str,
        payload: Sequence[dict[str, object]],
        expire_seconds: int = 300,
    ) -> None:
        """缓存热门标签快照（用于回退）"""
        if not payload:
            return
        try:
            cache_key = f"tag:hot:snapshot:{content_type}"
            await set_key(cache_key, json.dumps(list(payload)), expire=expire_seconds)
        except Exception as exc:
            logger.debug("写入热门标签快照失败 content_type=%s error=%s", content_type, exc)

    async def restore_from_snapshot(self, *, content_type: str) -> list[dict[str, object]]:
        """从快照缓存恢复热门标签"""
        cache_key = f"tag:hot:snapshot:{content_type}"
        raw = await get_key(cache_key)
        if not raw:
            return []
        try:
            payload = json.loads(raw)
            return payload if isinstance(payload, list) else []
        except json.JSONDecodeError:
            logger.warning("热门标签快照解析失败 content_type=%s", content_type)
            return []

    async def clear_caches(self, *, tag_ids: Iterable[int] | None = None) -> None:
        """清理标签相关缓存"""
        try:
            if tag_ids:
                for tag_id in tag_ids:
                    await delete_key(self._meta_key(tag_id))
                    await delete_key(self._hll_key("global", tag_id))
            await delete_key(self._hot_key("global"))
        except Exception as exc:
            logger.debug("清理热门标签缓存失败 error=%s", exc)

    # ----------------------- internal helpers -----------------------
    async def _load_tags(self, db: AsyncSession, tag_ids: Sequence[int]) -> dict[int, Tag]:
        if not tag_ids:
            return {}
        stmt = select(Tag).where(Tag.id.in_(tag_ids))
        rows = await db.execute(stmt)
        tags = rows.scalars().all()
        return {tag.id: tag for tag in tags}

    async def _get_window_score(self, content_type: str, tag_id: int) -> float:
        try:
            window_key = self._window_key(content_type, self._current_window())
            score = await sorted_set_get_score(window_key, str(tag_id))
            return float(score or 0.0)
        except Exception:
            return 0.0

    async def _get_unique_users(self, content_type: str, tag_id: int) -> int:
        try:
            return int(await hyperloglog_count(self._hll_key(content_type, tag_id)))
        except Exception:
            return 0

    async def _get_last_event_at(self, tag_id: int, content_type: str) -> datetime | None:
        try:
            value = await hash_get(self._meta_key(tag_id), f"{content_type}:last_event_ts")
            if value is None:
                value = await hash_get(self._meta_key(tag_id), "last_event_ts")
            if value:
                return datetime.fromisoformat(value)
        except Exception:
            pass
        return None
