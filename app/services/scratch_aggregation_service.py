"""Scratch 项目数据聚合服务"""

from sqlalchemy.ext.asyncio import AsyncSession

from app import crud, models, schemas
from app.services.scratch_stats_service import ScratchStatsService


class ScratchAggregationService:
    """聚合 Scratch 项目所需的展示数据（作者、统计信息等）。"""

    def __init__(self, scratch_stats_service: ScratchStatsService) -> None:
        self.scratch_stats_service = scratch_stats_service

    async def get_projects_by_ids(
        self,
        db: AsyncSession,
        *,
        project_ids: list[int],
        current_user: models.User | None = None,
    ) -> list[schemas.ScratchProductOut]:
        if not project_ids:
            return []

        projects = await crud.scratch_product.get_multi_by_ids(db=db, ids=project_ids)
        if not projects:
            return []

        try:
            stats_map = await self.scratch_stats_service.batch_get_stats(
                db=db,
                content_ids=project_ids,
                user_id=current_user.id if current_user else None,
            )
        except Exception:
            stats_map = {}

        aggregated: list[schemas.ScratchProductOut] = []
        for project in projects:
            dto = schemas.ScratchProductOut.model_validate(project)

            project_stats = stats_map.get(project.project_id, {})

            like_count = project_stats.get("like_count", dto.like_count)
            visit_count = project_stats.get("visit_count", dto.visit_count)
            favorite_count = project_stats.get("favorite_count", dto.favorite_count)
            adapt_count = project_stats.get("adapt_count", dto.adapt_count)
            is_favorited = project_stats.get(
                "is_favorited_by_user",
                dto.stats.is_favorited_by_user if dto.stats else False,
            )
            is_liked = project_stats.get(
                "is_liked_by_user",
                dto.stats.is_liked_by_user if dto.stats else False,
            )

            dto.like_count = like_count
            dto.visit_count = visit_count
            dto.favorite_count = favorite_count
            dto.adapt_count = adapt_count

            if dto.stats is None:
                dto.stats = schemas.ScratchProjectStats()

            dto.stats.like_count = like_count
            dto.stats.visit_count = visit_count
            dto.stats.favorite_count = favorite_count
            dto.stats.adapt_count = adapt_count
            dto.stats.is_favorited_by_user = bool(is_favorited)
            dto.stats.is_liked_by_user = bool(is_liked)

            aggregated.append(dto)

        return aggregated
