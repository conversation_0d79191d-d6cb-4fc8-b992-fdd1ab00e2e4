"""沸点通知服务"""

import logging
from typing import Any

from sqlalchemy.ext.asyncio import AsyncSession

from app.notifications.models import NotificationPriority, NotificationType
from app.services.notification_service import NotificationService

logger = logging.getLogger(__name__)


class PostNotificationService:
    """沸点相关通知服务"""

    def __init__(self):
        self.notification_service = NotificationService()

    async def send_like_notification(
        self,
        db: AsyncSession,
        *,
        liker_user_id: int,
        liker_username: str,
        post_id: int,
        post_author_id: int,
        post_content: str | None = None,
    ) -> None:
        """发送点赞通知"""
        # 不给自己发通知
        if liker_user_id == post_author_id:
            return

        # 截取沸点内容用于显示
        content_preview = self._get_content_preview(post_content)

        try:
            await self.notification_service.create_notification(
                db=db,
                user_id=post_author_id,
                notification_type=NotificationType.POST_LIKE,
                title="收到新点赞",
                message=f"{liker_username} 点赞了您的沸点{content_preview}",
                priority=NotificationPriority.LOW,
                data={
                    "liker_user_id": liker_user_id,
                    "liker_username": liker_username,
                    "post_id": post_id,
                    "action_type": "like",
                },
                action_url=f"/posts/{post_id}",
                expires_in_hours=72,  # 3天后过期
            )
            logger.info(f"点赞通知发送成功 - 点赞者: {liker_username}, 沸点: {post_id}")
        except Exception as e:
            logger.error(f"点赞通知发送失败: {str(e)}")

    async def send_mention_notification(
        self,
        db: AsyncSession,
        *,
        mentioner_user_id: int,
        mentioner_username: str,
        mentioned_user_id: int,
        post_id: int,
        post_content: str | None = None,
        mention_text: str,
    ) -> None:
        """发送@提及通知"""
        # 不给自己发通知
        if mentioner_user_id == mentioned_user_id:
            return

        content_preview = self._get_content_preview(post_content)

        try:
            await self.notification_service.create_notification(
                db=db,
                user_id=mentioned_user_id,
                notification_type=NotificationType.POST_MENTION,
                title="您被提及了",
                message=f"{mentioner_username} 在沸点中提及了您{content_preview}",
                priority=NotificationPriority.NORMAL,
                data={
                    "mentioner_user_id": mentioner_user_id,
                    "mentioner_username": mentioner_username,
                    "post_id": post_id,
                    "mention_text": mention_text,
                    "action_type": "mention",
                },
                action_url=f"/posts/{post_id}",
                expires_in_hours=168,  # 7天后过期
            )
            logger.info(f"@提及通知发送成功 - 提及者: {mentioner_username}, 被提及者: {mentioned_user_id}")
        except Exception as e:
            logger.error(f"@提及通知发送失败: {str(e)}")

    async def send_comment_notification(
        self,
        db: AsyncSession,
        *,
        commenter_user_id: int,
        commenter_username: str,
        post_id: int,
        post_author_id: int,
        post_content: str | None = None,
        comment_content: str | None = None,
    ) -> None:
        """发送评论通知"""
        # 不给自己发通知
        if commenter_user_id == post_author_id:
            return

        content_preview = self._get_content_preview(post_content)
        comment_preview = self._get_content_preview(comment_content, max_length=30)

        try:
            await self.notification_service.create_notification(
                db=db,
                user_id=post_author_id,
                notification_type=NotificationType.POST_COMMENT,
                title="收到新评论",
                message=f"{commenter_username} 评论了您的沸点{content_preview}：{comment_preview}",
                priority=NotificationPriority.NORMAL,
                data={
                    "commenter_user_id": commenter_user_id,
                    "commenter_username": commenter_username,
                    "post_id": post_id,
                    "action_type": "comment",
                },
                action_url=f"/posts/{post_id}",
                expires_in_hours=168,  # 7天后过期
            )
            logger.info(f"评论通知发送成功 - 评论者: {commenter_username}, 沸点: {post_id}")
        except Exception as e:
            logger.error(f"评论通知发送失败: {str(e)}")

    async def send_repost_notification(
        self,
        db: AsyncSession,
        *,
        reposter_user_id: int,
        reposter_username: str,
        original_post_id: int,
        original_post_author_id: int,
        original_post_content: str | None = None,
        repost_comment: str | None = None,
    ) -> None:
        """发送转发通知"""
        # 不给自己发通知
        if reposter_user_id == original_post_author_id:
            return

        content_preview = self._get_content_preview(original_post_content)
        comment_preview = self._get_content_preview(repost_comment, max_length=30) if repost_comment else ""

        message = f"{reposter_username} 转发了您的沸点{content_preview}"
        if comment_preview:
            message += f"，并评论：{comment_preview}"

        try:
            await self.notification_service.create_notification(
                db=db,
                user_id=original_post_author_id,
                notification_type=NotificationType.POST_REPOST,
                title="沸点被转发",
                message=message,
                priority=NotificationPriority.NORMAL,
                data={
                    "reposter_user_id": reposter_user_id,
                    "reposter_username": reposter_username,
                    "original_post_id": original_post_id,
                    "repost_comment": repost_comment,
                    "action_type": "repost",
                },
                action_url=f"/posts/{original_post_id}",
                expires_in_hours=168,  # 7天后过期
            )
            logger.info(f"转发通知发送成功 - 转发者: {reposter_username}, 原沸点: {original_post_id}")
        except Exception as e:
            logger.error(f"转发通知发送失败: {str(e)}")

    async def send_poll_vote_notification(
        self,
        db: AsyncSession,
        *,
        voter_user_id: int,
        voter_username: str,
        post_id: int,
        post_author_id: int,
        post_content: str | None = None,
        option_indexes: list[int],
    ) -> None:
        """发送投票通知"""
        # 不给自己发通知
        if voter_user_id == post_author_id:
            return

        content_preview = self._get_content_preview(post_content)

        try:
            await self.notification_service.create_notification(
                db=db,
                user_id=post_author_id,
                notification_type=NotificationType.POST_POLL_VOTE,
                title="收到新投票",
                message=f"{voter_username} 参与了您的投票{content_preview}",
                priority=NotificationPriority.LOW,
                data={
                    "voter_user_id": voter_user_id,
                    "voter_username": voter_username,
                    "post_id": post_id,
                    "option_indexes": option_indexes,
                    "action_type": "poll_vote",
                },
                action_url=f"/posts/{post_id}",
                expires_in_hours=168,  # 7天后过期
            )
            logger.info(f"投票通知发送成功 - 投票者: {voter_username}, 沸点: {post_id}")
        except Exception as e:
            logger.error(f"投票通知发送失败: {str(e)}")

    def _get_content_preview(self, content: str | None, max_length: int = 20) -> str:
        """获取内容预览"""
        if not content:
            return ""
        
        # 移除换行符和多余空格
        content = " ".join(content.split())
        
        if len(content) <= max_length:
            return f"「{content}」"
        
        return f"「{content[:max_length]}...」"

    async def send_batch_notifications(
        self,
        db: AsyncSession,
        notifications: list[dict[str, Any]],
    ) -> None:
        """批量发送通知"""
        for notification_data in notifications:
            try:
                notification_type = notification_data.get("type")
                if notification_type == "like":
                    await self.send_like_notification(db, **notification_data["data"])
                elif notification_type == "mention":
                    await self.send_mention_notification(db, **notification_data["data"])
                elif notification_type == "comment":
                    await self.send_comment_notification(db, **notification_data["data"])
                elif notification_type == "repost":
                    await self.send_repost_notification(db, **notification_data["data"])
                elif notification_type == "poll_vote":
                    await self.send_poll_vote_notification(db, **notification_data["data"])
                else:
                    logger.warning(f"未知的通知类型: {notification_type}")
            except Exception as e:
                logger.error(f"批量发送通知失败: {str(e)}")


# 全局实例
post_notification_service = PostNotificationService()
