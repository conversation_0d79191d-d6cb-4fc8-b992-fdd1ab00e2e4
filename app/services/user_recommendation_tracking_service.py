"""
用户推荐效果追踪服务

追踪和分析用户推荐的效果指标：
1. 点击率 (CTR) - 推荐展示vs点击
2. 关注转化率 - 推荐点击vs实际关注
3. 推荐质量评分 - 基于用户反馈
4. 算法效果对比 - 不同算法的表现
"""

import json
import logging
from datetime import datetime, timedelta
from typing import Any

from sqlalchemy import and_, desc, func, select
from sqlalchemy.ext.asyncio import AsyncSession

from app import crud, models, schemas
from app.db.redis import hash_get, hash_set, redis_master_client

logger = logging.getLogger(__name__)


class UserRecommendationTrackingService:
    """用户推荐效果追踪服务"""

    def __init__(self):
        self.tracking_enabled = True
        self.metrics_cache_expire = 3600  # 1小时

    async def track_recommendation_display(
        self,
        db: AsyncSession,
        user_id: int,
        recommended_user_ids: list[int],
        algorithm_type: str,
        position: str | None = None,
        extra_data: dict[str, Any] | None = None,
    ) -> bool:
        """
        追踪推荐展示事件

        Args:
            db: 数据库会话
            user_id: 当前用户ID
            recommended_user_ids: 被推荐的用户ID列表
            algorithm_type: 推荐算法类型
            position: 推荐位置
            extra_data: 额外数据

        Returns:
            是否成功记录
        """
        try:
            # 创建推荐日志
            log_data = schemas.RecommendationLogCreate(
                algorithm_type=algorithm_type,
                recommended_items=json.dumps({"user_ids": recommended_user_ids}),
                recommendation_reason="user_recommendation_display",
                position=position,
            )

            await crud.recommendation_log.create_with_user(
                db=db, obj_in=log_data, user_id=user_id
            )

            # 更新展示计数缓存
            await self._update_display_metrics(algorithm_type, len(recommended_user_ids))

            return True

        except Exception as e:
            logger.error(f"追踪推荐展示失败: {e}")
            return False

    async def track_recommendation_click(
        self,
        db: AsyncSession,
        user_id: int,
        clicked_user_id: int,
        algorithm_type: str,
        position: int | None = None,
        extra_data: dict[str, Any] | None = None,
    ) -> bool:
        """
        追踪推荐点击事件

        Args:
            db: 数据库会话
            user_id: 当前用户ID
            clicked_user_id: 被点击的用户ID
            algorithm_type: 推荐算法类型
            position: 点击位置
            extra_data: 额外数据

        Returns:
            是否成功记录
        """
        try:
            # 创建推荐日志
            log_data = schemas.RecommendationLogCreate(
                algorithm_type=algorithm_type,
                recommended_items=json.dumps({"clicked_user_id": clicked_user_id}),
                recommendation_reason="user_recommendation_click",
                position=str(position) if position else None,
            )

            await crud.recommendation_log.create_with_user(
                db=db, obj_in=log_data, user_id=user_id
            )

            # 更新点击计数缓存
            await self._update_click_metrics(algorithm_type)

            return True

        except Exception as e:
            logger.error(f"追踪推荐点击失败: {e}")
            return False

    async def track_follow_conversion(
        self,
        db: AsyncSession,
        user_id: int,
        followed_user_id: int,
        algorithm_type: str,
        source: str = "recommendation",
        extra_data: dict[str, Any] | None = None,
    ) -> bool:
        """
        追踪关注转化事件

        Args:
            db: 数据库会话
            user_id: 当前用户ID
            followed_user_id: 被关注的用户ID
            algorithm_type: 推荐算法类型
            source: 关注来源
            extra_data: 额外数据

        Returns:
            是否成功记录
        """
        try:
            # 创建推荐日志
            log_data = schemas.RecommendationLogCreate(
                algorithm_type=algorithm_type,
                recommended_items=json.dumps({"followed_user_id": followed_user_id}),
                recommendation_reason="user_recommendation_follow",
                position=source,
            )

            await crud.recommendation_log.create_with_user(
                db=db, obj_in=log_data, user_id=user_id
            )

            # 更新关注转化计数缓存
            await self._update_conversion_metrics(algorithm_type)

            return True

        except Exception as e:
            logger.error(f"追踪关注转化失败: {e}")
            return False

    async def get_recommendation_metrics(
        self,
        db: AsyncSession,
        algorithm_type: str | None = None,
        days: int = 7,
    ) -> dict[str, Any]:
        """
        获取推荐效果指标

        Args:
            db: 数据库会话
            algorithm_type: 算法类型，None表示所有算法
            days: 统计天数

        Returns:
            推荐效果指标字典
        """
        try:
            # 检查缓存
            cache_key = f"rec_metrics:{algorithm_type or 'all'}:{days}"
            cached_metrics = await self._get_cached_metrics(cache_key)
            if cached_metrics:
                return cached_metrics

            # 计算时间范围
            start_date = datetime.utcnow() - timedelta(days=days)

            # 构建查询条件
            base_conditions = [models.RecommendationLog.created_at >= start_date]
            if algorithm_type:
                base_conditions.append(models.RecommendationLog.algorithm_type == algorithm_type)

            # 计算展示次数
            display_stmt = select(func.count()).where(
                and_(
                    *base_conditions,
                    models.RecommendationLog.recommendation_reason == "user_recommendation_display",
                )
            )
            display_result = await db.execute(display_stmt)
            total_displays = display_result.scalar() or 0

            # 计算点击次数
            click_stmt = select(func.count()).where(
                and_(
                    *base_conditions,
                    models.RecommendationLog.recommendation_reason == "user_recommendation_click",
                )
            )
            click_result = await db.execute(click_stmt)
            total_clicks = click_result.scalar() or 0

            # 计算关注转化次数
            follow_stmt = select(func.count()).where(
                and_(
                    *base_conditions,
                    models.RecommendationLog.recommendation_reason == "user_recommendation_follow",
                )
            )
            follow_result = await db.execute(follow_stmt)
            total_follows = follow_result.scalar() or 0

            # 计算指标
            ctr = (total_clicks / total_displays * 100) if total_displays > 0 else 0.0
            conversion_rate = (total_follows / total_clicks * 100) if total_clicks > 0 else 0.0
            overall_conversion = (total_follows / total_displays * 100) if total_displays > 0 else 0.0

            # 获取算法分布
            algorithm_distribution = await self._get_algorithm_distribution(db, start_date)

            # 获取用户参与度
            user_engagement = await self._get_user_engagement_metrics(db, start_date)

            metrics = {
                "period": f"{days} days",
                "algorithm_type": algorithm_type or "all",
                "total_displays": total_displays,
                "total_clicks": total_clicks,
                "total_follows": total_follows,
                "click_through_rate": round(ctr, 2),
                "follow_conversion_rate": round(conversion_rate, 2),
                "overall_conversion_rate": round(overall_conversion, 2),
                "algorithm_distribution": algorithm_distribution,
                "user_engagement": user_engagement,
                "generated_at": datetime.utcnow().isoformat(),
            }

            # 缓存结果
            await self._cache_metrics(cache_key, metrics)

            return metrics

        except Exception as e:
            logger.error(f"获取推荐指标失败: {e}")
            return {
                "error": str(e),
                "period": f"{days} days",
                "algorithm_type": algorithm_type or "all",
            }

    async def get_algorithm_comparison(
        self, db: AsyncSession, days: int = 7
    ) -> dict[str, Any]:
        """
        获取不同算法的效果对比

        Args:
            db: 数据库会话
            days: 统计天数

        Returns:
            算法对比结果
        """
        try:
            algorithms = ["collaborative", "content_based", "social_network", "popular", "hybrid"]
            comparison_data = {}

            for algorithm in algorithms:
                metrics = await self.get_recommendation_metrics(db, algorithm, days)
                comparison_data[algorithm] = {
                    "displays": metrics.get("total_displays", 0),
                    "clicks": metrics.get("total_clicks", 0),
                    "follows": metrics.get("total_follows", 0),
                    "ctr": metrics.get("click_through_rate", 0.0),
                    "conversion_rate": metrics.get("follow_conversion_rate", 0.0),
                    "overall_conversion": metrics.get("overall_conversion_rate", 0.0),
                }

            # 计算最佳算法
            best_ctr_algorithm = max(
                comparison_data.items(),
                key=lambda x: x[1]["ctr"],
                default=("none", {"ctr": 0})
            )

            best_conversion_algorithm = max(
                comparison_data.items(),
                key=lambda x: x[1]["conversion_rate"],
                default=("none", {"conversion_rate": 0})
            )

            return {
                "period": f"{days} days",
                "algorithms": comparison_data,
                "best_ctr_algorithm": {
                    "name": best_ctr_algorithm[0],
                    "ctr": best_ctr_algorithm[1]["ctr"],
                },
                "best_conversion_algorithm": {
                    "name": best_conversion_algorithm[0],
                    "conversion_rate": best_conversion_algorithm[1]["conversion_rate"],
                },
                "generated_at": datetime.utcnow().isoformat(),
            }

        except Exception as e:
            logger.error(f"获取算法对比失败: {e}")
            return {"error": str(e), "period": f"{days} days"}

    async def _update_display_metrics(self, algorithm_type: str, count: int) -> None:
        """更新展示指标缓存"""
        try:
            key = f"rec_display_count:{algorithm_type}"
            await redis_master_client.incrby(key, count)
            await redis_master_client.expire(key, 86400)  # 24小时过期
        except Exception as e:
            logger.error(f"更新展示指标失败: {e}")

    async def _update_click_metrics(self, algorithm_type: str) -> None:
        """更新点击指标缓存"""
        try:
            key = f"rec_click_count:{algorithm_type}"
            await redis_master_client.incr(key)
            await redis_master_client.expire(key, 86400)  # 24小时过期
        except Exception as e:
            logger.error(f"更新点击指标失败: {e}")

    async def _update_conversion_metrics(self, algorithm_type: str) -> None:
        """更新转化指标缓存"""
        try:
            key = f"rec_conversion_count:{algorithm_type}"
            await redis_master_client.incr(key)
            await redis_master_client.expire(key, 86400)  # 24小时过期
        except Exception as e:
            logger.error(f"更新转化指标失败: {e}")

    async def _get_cached_metrics(self, cache_key: str) -> dict[str, Any] | None:
        """从缓存获取指标"""
        try:
            cached_data = await hash_get(cache_key, "metrics")
            if cached_data:
                return json.loads(cached_data)
            return None
        except Exception as e:
            logger.error(f"获取缓存指标失败: {e}")
            return None

    async def _cache_metrics(self, cache_key: str, metrics: dict[str, Any]) -> None:
        """缓存指标数据"""
        try:
            await hash_set(cache_key, "metrics", json.dumps(metrics))
            await redis_master_client.expire(cache_key, self.metrics_cache_expire)
        except Exception as e:
            logger.error(f"缓存指标失败: {e}")

    async def _get_algorithm_distribution(
        self, db: AsyncSession, start_date: datetime
    ) -> dict[str, int]:
        """获取算法分布"""
        try:
            stmt = (
                select(
                    models.RecommendationLog.algorithm_type,
                    func.count().label("count"),
                )
                .where(models.RecommendationLog.created_at >= start_date)
                .group_by(models.RecommendationLog.algorithm_type)
            )
            result = await db.execute(stmt)
            return {row[0]: row[1] for row in result.fetchall()}
        except Exception as e:
            logger.error(f"获取算法分布失败: {e}")
            return {}

    async def _get_user_engagement_metrics(
        self, db: AsyncSession, start_date: datetime
    ) -> dict[str, Any]:
        """获取用户参与度指标"""
        try:
            # 活跃用户数
            active_users_stmt = select(func.count(func.distinct(models.RecommendationLog.user_id))).where(
                models.RecommendationLog.created_at >= start_date
            )
            active_users_result = await db.execute(active_users_stmt)
            active_users = active_users_result.scalar() or 0

            # 平均每用户推荐数
            avg_recs_stmt = select(func.avg(func.count())).select_from(
                select(models.RecommendationLog.user_id)
                .where(models.RecommendationLog.created_at >= start_date)
                .group_by(models.RecommendationLog.user_id)
                .subquery()
            )
            avg_recs_result = await db.execute(avg_recs_stmt)
            avg_recommendations = float(avg_recs_result.scalar() or 0)

            return {
                "active_users": active_users,
                "average_recommendations_per_user": round(avg_recommendations, 2),
            }

        except Exception as e:
            logger.error(f"获取用户参与度指标失败: {e}")
            return {"active_users": 0, "average_recommendations_per_user": 0.0}
