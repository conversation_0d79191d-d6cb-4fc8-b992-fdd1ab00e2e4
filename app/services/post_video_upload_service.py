"""帖子视频上传服务：负责接收单个视频文件并直接上传到 OSS。

该服务明确只服务于帖子（Post）场景，不会写入 `videos` 表，也不会
触发批量上传中的复杂任务状态流。整体流程：

1. 将上传的文件保存到临时目录中，便于后续处理。
2. 计算文件哈希，实现秒传能力；命中后返回已有 URL 与元数据。
3. 对新文件调用 FFprobe 获取基础元数据（时长、分辨率、编码），
   并抽取一帧作为封面。
4. 将原视频原封不动上传至 OSS，目录统一为 `steam/post-videos/`。
5. 将封面统一转换为 webp 并上传。若封面生成失败，仅记录日志。
6. 将视频元数据写入 FileHash 记录，便于下次命中秒传。

返回值允许调用方决定如何落库或响应 API。
"""

from __future__ import annotations

import asyncio
import os
import shutil
import uuid
from dataclasses import dataclass
from typing import Any

import ffmpeg
from fastapi import HTTPException, UploadFile, status
from sqlalchemy.ext.asyncio import AsyncSession

from app.config import get_settings
from app.core.logging import logger
from app.crud.file_hash import file_hash as crud_file_hash
from app.schemas.file_hash import FileHashCreate
from app.tasks.image_processing import process_and_upload_image
from app.utils.file_probe import probe_media_metadata

settings = get_settings()


SUPPORTED_VIDEO_TYPES = {
    "video/mp4": ".mp4",
    "video/webm": ".webm",
    "video/ogg": ".ogv",
}

TEMP_UPLOAD_DIR = "/tmp/uploads"
os.makedirs(TEMP_UPLOAD_DIR, exist_ok=True)


@dataclass(slots=True)
class PostVideoUploadResult:
    """帖子视频上传结果"""

    file_hash: str
    file_url: str
    cover_url: str | None
    duration: int | None
    width: int | None
    height: int | None
    size: int

    def as_media_payload(self) -> dict[str, Any]:
        return {
            "file_hash": self.file_hash,
            "media_type": "video",
            "file_url": self.file_url,
            "cover_url": self.cover_url,
            "duration": self.duration,
            "width": self.width,
            "height": self.height,
            "size": self.size,
        }


class PostVideoUploadService:
    """帖子视频上传服务"""

    async def upload(self, *, file: UploadFile, db: AsyncSession) -> PostVideoUploadResult:
        if not file.content_type or file.content_type not in SUPPORTED_VIDEO_TYPES:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="暂仅支持 mp4/webm/ogg 格式",
            )

        file_extension = SUPPORTED_VIDEO_TYPES[file.content_type]

        temp_path = self._save_temp_file(file)
        try:
            file_hash = await self._calculate_hash(temp_path)
            existing = await crud_file_hash.get_by_hash(db, file_hash=file_hash)
            if existing and existing.file_path:
                metadata = existing.file_metadata or {}
                return PostVideoUploadResult(
                    file_hash=file_hash,
                    file_url=existing.file_path,
                    cover_url=metadata.get("cover_url"),
                    duration=metadata.get("duration"),
                    width=metadata.get("width"),
                    height=metadata.get("height"),
                    size=metadata.get("size"),
                )

            metadata = await probe_media_metadata(temp_path)
            if metadata is None:
                raise HTTPException(status_code=500, detail="无法解析视频文件")

            cover_url = await self._extract_and_upload_cover(temp_path, file_hash)

            file_url = await self._upload_video(temp_path, file_hash, file_extension)
            if not file_url:
                raise HTTPException(status_code=500, detail="上传视频失败")

            file_size = os.path.getsize(temp_path)

            metadata_payload = {
                "duration": metadata.duration,
                "width": metadata.width,
                "height": metadata.height,
                "size": file_size,
                "cover_url": cover_url,
            }
            await crud_file_hash.create(
                db,
                obj_in=FileHashCreate(
                    file_path=file_url,
                    file_hash=file_hash,
                    file_metadata=metadata_payload,
                ),
            )

            return PostVideoUploadResult(
                file_hash=file_hash,
                file_url=file_url,
                cover_url=cover_url,
                duration=metadata.duration,
                width=metadata.width,
                height=metadata.height,
                size=file_size,
            )
        finally:
            if os.path.exists(temp_path):
                os.remove(temp_path)

    def _save_temp_file(self, file: UploadFile) -> str:
        temp_path = os.path.join(TEMP_UPLOAD_DIR, f"post_video_{uuid.uuid4()}_{file.filename}")
        with open(temp_path, "wb") as buffer:
            shutil.copyfileobj(file.file, buffer)
        file.file.close()
        return temp_path

    async def _calculate_hash(self, file_path: str) -> str:
        import hashlib

        hash_algorithm = hashlib.sha256()
        with open(file_path, "rb") as f:
            while chunk := f.read(8 * 1024 * 1024):
                hash_algorithm.update(chunk)
                await asyncio.sleep(0)
        return hash_algorithm.hexdigest()

    async def _extract_and_upload_cover(self, file_path: str, file_hash: str) -> str | None:
        capture_time = 0.1
        cover_path = os.path.join(TEMP_UPLOAD_DIR, f"{file_hash}_cover.jpg")
        try:
            (
                ffmpeg.input(file_path, ss=capture_time)
                .output(cover_path, vframes=1, format="image2", vcodec="mjpeg")
                .run(capture_stdout=True, capture_stderr=True, overwrite_output=True)
            )
            with open(cover_path, "rb") as cover_file:
                cover_data = cover_file.read()
            cover_hash = f"{file_hash}_cover"
            return await process_and_upload_image(cover_data, cover_hash)
        except Exception as exc:  # noqa: BLE001
            logger.error("生成视频封面失败: {}", exc)
            return None
        finally:
            if os.path.exists(cover_path):
                os.remove(cover_path)

    async def _upload_video(
        self, file_path: str, file_hash: str, file_extension: str
    ) -> str | None:
        from app.services.partial_upload import PartialUpload

        with open(file_path, "rb") as f:
            file_data = f.read()

        partial = PartialUpload()
        return await asyncio.to_thread(
            partial.upload,
            file_data,
            file_hash,
            "post_video",
            3,
            file_extension,
        )
