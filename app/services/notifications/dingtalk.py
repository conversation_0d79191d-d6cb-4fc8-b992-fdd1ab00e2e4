"""钉钉告警通知占位实现"""

from __future__ import annotations

from dataclasses import dataclass
from typing import Any


@dataclass
class DingtalkChannelConfig:
    webhook_url: str
    secret: str | None = None


class DingtalkNotifier:
    def __init__(self, config: DingtalkChannelConfig) -> None:
        self.config = config

    async def send_alert(self, message: str, context: dict[str, Any] | None = None) -> bool:
        # 集成留作后续实现，这里返回成功占位
        return True
