from __future__ import annotations

import json
import secrets
from datetime import datetime, timezone
from typing import Any, Awaitable, Callable, Iterable, Sequence

from fastapi import HTTPException, status
from sqlalchemy import Select, func, select
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.cache import ConfigCache
from app.models import AdminConfigChangeLog, AdminConfigSetting, AdminFeatureToggle
from app.models.audit_log import AuditAction, AuditResourceType
from app.models.user import User
from app.schemas.admin_config import (
    ConfigBatchUpdateResponse,
    ConfigHistoryEntry,
    ConfigHistoryResponse,
    ConfigListResponse,
    ConfigMutation,
    ConfigSetting,
    ConfigUpdateRequest,
    ConfigUpdateResultEntry,
    FeatureToggle,
    FeatureToggleUpdateRequest,
)
from app.schemas.admin_config import ConfigRollbackResponse as RollbackSchema
from app.services.audit_log_service import AuditLogService


class ConfigService:
    """动态配置中心服务"""

    MASK_TOKEN = "***"

    class ConfigConflictError(Exception):
        """配置版本冲突异常"""

    class ConfigNotFoundError(Exception):
        """配置不存在异常"""

    def __init__(
        self,
        cache: ConfigCache | None = None,
        broadcaster: Callable[[str, dict[str, Any]], Awaitable[None]] | None = None,
        audit_logger: Callable[..., Awaitable[Any]] | None = None,
    ) -> None:
        self.cache = cache
        self._broadcaster = broadcaster or self._default_broadcaster
        self._audit_logger = audit_logger or AuditLogService.log_action

    async def list_configs(
        self, db: AsyncSession, *, category: str | None = None
    ) -> ConfigListResponse:
        """列出配置项与功能开关"""
        stmt: Select[tuple[AdminConfigSetting]] = select(AdminConfigSetting)
        if category:
            stmt = stmt.where(AdminConfigSetting.category == category)
        stmt = stmt.order_by(AdminConfigSetting.category, AdminConfigSetting.key)
        settings = (await db.execute(stmt)).scalars().all()

        features_stmt: Select[tuple[AdminFeatureToggle]] = select(AdminFeatureToggle).order_by(
            AdminFeatureToggle.name
        )
        features = (await db.execute(features_stmt)).scalars().all()

        config_items = [self._to_schema(setting) for setting in settings]
        feature_items = [self._to_feature_schema(toggle) for toggle in features]
        return ConfigListResponse(configs=config_items, features=feature_items)

    async def batch_update_configs(
        self,
        db: AsyncSession,
        *,
        current_user: User,
        update_request: ConfigUpdateRequest,
        request: Any | None = None,
    ) -> ConfigBatchUpdateResponse:
        """批量更新配置"""
        if not update_request.configs:
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="缺少配置变更项")

        change_id = self._generate_change_id("chg")
        response_entries: list[ConfigUpdateResultEntry] = []
        audit_old: dict[str, Any] = {}
        audit_new: dict[str, Any] = {}
        updated_settings: list[AdminConfigSetting] = []
        now = datetime.now(timezone.utc)

        try:
            for mutation in update_request.configs:
                setting = await self._get_setting_by_key(db, mutation.key, for_update=True)
                if not setting:
                    raise self.ConfigNotFoundError(f"配置不存在: {mutation.key}")

                expected_version = mutation.version
                if expected_version is not None and expected_version != setting.version:
                    raise self.ConfigConflictError(
                        f"配置 {mutation.key} 版本冲突，当前版本 {setting.version}"
                    )

                value_type = mutation.value_type.value if mutation.value_type else setting.value_type
                old_value_raw = setting.value
                old_value_type = setting.value_type
                old_sensitive = setting.is_sensitive
                new_value_raw = self._serialize_value(mutation.value, value_type)

                setting.value = new_value_raw
                setting.value_type = value_type
                setting.version = (setting.version or 1) + 1
                setting.updated_at = now
                setting.updated_by = current_user.id
                if mutation.is_sensitive is not None:
                    setting.is_sensitive = mutation.is_sensitive

                change_log = AdminConfigChangeLog(
                    change_id=change_id,
                    config_key=setting.key,
                    category=setting.category,
                    old_value=old_value_raw,
                    new_value=new_value_raw,
                    change_type="update",
                    change_reason=mutation.reason,
                    changed_by=current_user.id,
                    changed_at=now,
                    is_rollback=False,
                )
                db.add(change_log)
                updated_settings.append(setting)

                response_entries.append(
                    ConfigUpdateResultEntry(
                        key=setting.key,
                        old_value=self._display_value(
                            old_value_raw, old_value_type, is_sensitive=old_sensitive
                        ),
                        new_value=self._display_value(
                            new_value_raw, setting.value_type, is_sensitive=setting.is_sensitive
                        ),
                        status="updated",
                    )
                )

                audit_old[setting.key] = self._display_value(
                    old_value_raw, old_value_type, is_sensitive=old_sensitive
                )
                audit_new[setting.key] = self._display_value(
                    new_value_raw, setting.value_type, is_sensitive=setting.is_sensitive
                )

            await db.commit()
        except self.ConfigConflictError:
            await db.rollback()
            raise
        except self.ConfigNotFoundError:
            await db.rollback()
            raise
        except Exception:
            await db.rollback()
            raise

        await self._refresh_cache(updated_settings)
        await self._broadcast(
            "config_update",
            {"change_id": change_id, "keys": [setting.key for setting in updated_settings]},
        )
        await self._audit(
            db,
            current_user,
            action=AuditAction.BATCH_UPDATE,
            description=f"批量更新配置: {', '.join(setting.key for setting in updated_settings)}",
            old_values=audit_old,
            new_values=audit_new,
            request=request,
        )

        return ConfigBatchUpdateResponse(
            change_id=change_id, updated_configs=response_entries, broadcast_status="scheduled"
        )

    async def list_history(
        self,
        db: AsyncSession,
        *,
        page: int,
        page_size: int,
        config_key: str | None = None,
        from_date: datetime | None = None,
        to_date: datetime | None = None,
    ) -> ConfigHistoryResponse:
        """查询配置变更历史"""
        stmt: Select[tuple[AdminConfigChangeLog]] = select(AdminConfigChangeLog)
        if config_key:
            stmt = stmt.where(AdminConfigChangeLog.config_key == config_key)
        if from_date:
            stmt = stmt.where(AdminConfigChangeLog.changed_at >= from_date)
        if to_date:
            stmt = stmt.where(AdminConfigChangeLog.changed_at <= to_date)

        total = await db.execute(select(func.count()).select_from(stmt.subquery()))
        total_count = total.scalar_one()

        stmt = stmt.order_by(AdminConfigChangeLog.changed_at.desc()).offset(
            (page - 1) * page_size
        ).limit(page_size)
        logs = (await db.execute(stmt)).scalars().all()

        key_sensitivity = await self._load_sensitivity_map(db, {log.config_key for log in logs})

        entries = [
            ConfigHistoryEntry(
                change_id=log.change_id,
                config_key=log.config_key,
                category=log.category,
                old_value=self._mask_if_sensitive(log.old_value, key_sensitivity.get(log.config_key)),
                new_value=self._mask_if_sensitive(log.new_value, key_sensitivity.get(log.config_key)),
                change_type=log.change_type,
                change_reason=log.change_reason,
                changed_by=log.changed_by,
                changed_at=log.changed_at,
                rollback_id=log.rollback_id,
                is_rollback=log.is_rollback,
            )
            for log in logs
        ]

        return ConfigHistoryResponse(total=total_count, page=page, page_size=page_size, changes=entries)

    async def rollback_change(
        self,
        db: AsyncSession,
        *,
        change_id: str,
        current_user: User,
        reason: str,
        request: Any | None = None,
    ) -> RollbackSchema:
        """回滚配置变更"""
        stmt = select(AdminConfigChangeLog).where(AdminConfigChangeLog.change_id == change_id)
        logs = (await db.execute(stmt)).scalars().all()
        if not logs:
            raise self.ConfigNotFoundError(f"未找到变更记录 {change_id}")

        rollback_change_id = self._generate_change_id("rbk")
        now = datetime.now(timezone.utc)
        affected_settings: list[AdminConfigSetting] = []

        try:
            for log in logs:
                setting = await self._get_setting_by_key(db, log.config_key, for_update=True)
                if not setting:
                    continue

                current_value = setting.value
                setting.value = log.old_value
                setting.version = (setting.version or 1) + 1
                setting.updated_at = now
                setting.updated_by = current_user.id

                rollback_log = AdminConfigChangeLog(
                    change_id=rollback_change_id,
                    config_key=setting.key,
                    category=setting.category,
                    old_value=current_value,
                    new_value=log.old_value,
                    change_type="rollback",
                    change_reason=reason,
                    changed_by=current_user.id,
                    changed_at=now,
                    rollback_id=change_id,
                    is_rollback=True,
                )
                db.add(rollback_log)
                affected_settings.append(setting)

            await db.commit()
        except Exception:
            await db.rollback()
            raise

        await self._refresh_cache(affected_settings)
        await self._broadcast(
            "config_rollback",
            {"rollback_id": rollback_change_id, "original_change_id": change_id},
        )
        await self._audit(
            db,
            current_user,
            action=AuditAction.UPDATE,
            description=f"回滚配置变更 {change_id}",
            old_values=None,
            new_values=None,
            reason=reason,
            request=request,
        )

        return RollbackSchema(
            rollback_id=rollback_change_id,
            original_change_id=change_id,
            status="completed",
            rollback_at=now,
            rollback_by=current_user.id,
        )

    async def list_feature_toggles(self, db: AsyncSession) -> list[FeatureToggle]:
        stmt = select(AdminFeatureToggle).order_by(AdminFeatureToggle.name)
        toggles = (await db.execute(stmt)).scalars().all()
        return [self._to_feature_schema(toggle) for toggle in toggles]

    async def update_feature_toggle(
        self,
        db: AsyncSession,
        *,
        name: str,
        current_user: User,
        update_request: FeatureToggleUpdateRequest,
        request: Any | None = None,
    ) -> FeatureToggle:
        stmt = select(AdminFeatureToggle).where(AdminFeatureToggle.name == name)
        toggle = (await db.execute(stmt)).scalar_one_or_none()
        if not toggle:
            raise self.ConfigNotFoundError(f"功能开关不存在: {name}")

        old_state = toggle.is_enabled
        toggle.is_enabled = update_request.is_enabled
        if update_request.config is not None:
            toggle.toggle_config = update_request.config
        toggle.updated_at = datetime.now(timezone.utc)
        toggle.updated_by = current_user.id
        db.add(toggle)

        change_log = AdminConfigChangeLog(
            change_id=self._generate_change_id("ftg"),
            config_key=toggle.name,
            category="feature_toggle",
            old_value=json.dumps({"enabled": old_state}),
            new_value=json.dumps({"enabled": toggle.is_enabled}),
            change_type="feature_toggle",
            change_reason=update_request.reason,
            changed_by=current_user.id,
            changed_at=toggle.updated_at,
            is_rollback=False,
        )
        db.add(change_log)
        await db.commit()
        await db.refresh(toggle)

        await self._set_feature_cache(toggle)
        await self._broadcast(
            "feature_toggle",
            {"name": toggle.name, "is_enabled": toggle.is_enabled, "updated_by": current_user.id},
        )
        await self._audit(
            db,
            current_user,
            action=AuditAction.UPDATE,
            description=f"更新功能开关 {toggle.name}",
            old_values={"is_enabled": old_state},
            new_values={"is_enabled": toggle.is_enabled},
            reason=update_request.reason,
            request=request,
        )

        return self._to_feature_schema(toggle)

    async def _refresh_cache(self, settings: Sequence[AdminConfigSetting]) -> None:
        if not self.cache:
            return
        for setting in settings:
            await self.cache.set_config(setting.key, setting.value)

    async def _set_feature_cache(self, toggle: AdminFeatureToggle) -> None:
        if not self.cache:
            return
        await self.cache.set_feature(toggle.name, toggle.is_enabled)

    async def _get_setting_by_key(
        self, db: AsyncSession, key: str, *, for_update: bool = False
    ) -> AdminConfigSetting | None:
        stmt = select(AdminConfigSetting).where(AdminConfigSetting.key == key)
        if for_update:
            stmt = stmt.with_for_update()
        return (await db.execute(stmt)).scalar_one_or_none()

    async def _load_sensitivity_map(
        self, db: AsyncSession, keys: Iterable[str]
    ) -> dict[str, bool]:
        keys = {key for key in keys if key}
        if not keys:
            return {}
        stmt = select(AdminConfigSetting.key, AdminConfigSetting.is_sensitive).where(
            AdminConfigSetting.key.in_(keys)
        )
        rows = (await db.execute(stmt)).all()
        return {key: sensitive for key, sensitive in rows}

    def _to_schema(self, setting: AdminConfigSetting) -> ConfigSetting:
        value = self._display_value(
            setting.value, setting.value_type, is_sensitive=setting.is_sensitive
        )
        return ConfigSetting(
            id=setting.id,
            category=setting.category,
            key=setting.key,
            value=value,
            value_type=setting.value_type,
            description=setting.description,
            metadata=setting.settings_metadata or {},
            version=setting.version,
            is_sensitive=setting.is_sensitive,
            updated_at=setting.updated_at,
            updated_by=setting.updated_by,
        )

    def _to_feature_schema(self, toggle: AdminFeatureToggle) -> FeatureToggle:
        return FeatureToggle(
            id=toggle.id,
            name=toggle.name,
            is_enabled=toggle.is_enabled,
            description=toggle.description,
            scope=toggle.scope,
            config=toggle.toggle_config or {},
            updated_at=toggle.updated_at,
            updated_by=toggle.updated_by,
        )

    def _display_value(
        self, raw_value: str | None, value_type: str | None, *, is_sensitive: bool
    ) -> Any:
        if is_sensitive:
            return self.MASK_TOKEN
        return self._deserialize_value(raw_value, value_type)

    def _mask_if_sensitive(self, value: Any, is_sensitive: bool | None) -> Any:
        if not is_sensitive:
            return self._deserialize_value(value, None) if isinstance(value, str) else value
        return self.MASK_TOKEN

    def _serialize_value(self, value: Any, value_type: str | None) -> str | None:
        if value is None:
            return None

        if not value_type:
            return str(value)

        match value_type:
            case "int":
                return str(int(value))
            case "float":
                return str(float(value))
            case "boolean":
                return "true" if bool(value) else "false"
            case "json":
                return json.dumps(value, ensure_ascii=False)
            case _:
                return str(value)

    def _deserialize_value(self, value: str | None, value_type: str | None) -> Any:
        if value is None:
            return None
        if not value_type:
            return value

        try:
            match value_type:
                case "int":
                    return int(value)
                case "float":
                    return float(value)
                case "boolean":
                    return value.lower() in {"true", "1", "yes", "on"}
                case "json":
                    return json.loads(value)
                case _:
                    return value
        except Exception:
            return value

    def _generate_change_id(self, prefix: str) -> str:
        timestamp = datetime.now(timezone.utc).strftime("%Y%m%d%H%M%S")
        suffix = secrets.token_hex(3)
        return f"{prefix}_{timestamp}_{suffix}"

    async def _broadcast(self, topic: str, payload: dict[str, Any]) -> None:
        try:
            await self._broadcaster(topic, payload)
        except Exception:
            pass

    async def _default_broadcaster(self, topic: str, payload: dict[str, Any]) -> None:
        try:
            from app.tasks.config_broadcast import broadcast_config_event

            broadcast_config_event.delay(topic, payload)
        except Exception:
            pass

    async def _audit(
        self,
        db: AsyncSession,
        user: User,
        *,
        action: AuditAction,
        description: str,
        old_values: dict[str, Any] | None,
        new_values: dict[str, Any] | None,
        reason: str | None = None,
        request: Any | None = None,
    ) -> None:
        try:
            await self._audit_logger(
                db=db,
                user=user,
                action=action,
                resource_type=AuditResourceType.SYSTEM,
                resource_id=None,
                resource_name="admin_config",
                description=description,
                reason=reason,
                old_values=old_values,
                new_values=new_values,
                request=request,
            )
        except Exception:
            pass
