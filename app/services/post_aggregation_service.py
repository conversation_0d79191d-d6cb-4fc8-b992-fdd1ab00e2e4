"""
沸点数据聚合服务
- 负责将沸点的基础信息、作者、统计数据、审核信息等高效地聚合在一起。
"""

import logging
from datetime import UTC, datetime, timedelta

from fastapi import HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession

from app import models, schemas
from app.crud.post import post as crud_post
from app.models.post import PostMedia, PostStatus, PostType
from app.services.content_stats_service import ContentStatsService
from app.services.post_cache_service import PostCacheService
from app.services.topic_stats_service import TopicStatsService
from app.services.user_cache_service import UserCacheService

logger = logging.getLogger(__name__)


class PostAggregationService:
    def __init__(
        self,
        post_cache_service: PostCacheService,
        content_stats_service: ContentStatsService,
        user_cache_service: UserCacheService,
        topic_stats_service: TopicStatsService,
        post_notification_service: "PostNotificationService",
    ):
        self.post_cache_service = post_cache_service
        self.content_stats_service = content_stats_service
        self.user_cache_service = user_cache_service
        self.topic_stats_service = topic_stats_service
        self.post_notification_service = post_notification_service

    async def _aggregate_posts_by_ids(
        self,
        db: AsyncSession,
        *,
        post_ids: list[int],
        current_user_id: int | None = None,
        include_stats: bool = True,
        include_media: bool = True,
    ) -> list[schemas.PostOut]:
        """
        根据沸点ID列表，聚合完整的沸点信息
        这是一个核心的私有方法，用于代码复用
        """
        if not post_ids:
            return []

        # 1. 获取沸点基础信息
        posts_basic = await self.post_cache_service.get_posts_by_ids(db, post_ids=post_ids)
        if not posts_basic:
            logger.warning(
                "_aggregate_posts_by_ids 未获取到沸点基础信息",
                extra={
                    "post_ids": post_ids,
                    "current_user_id": current_user_id,
                    "include_stats": include_stats,
                },
            )
            return []

        # 2. 获取作者信息（需要先获取沸点信息才能知道作者ID）
        author_ids = list(dict.fromkeys(post.author_id for post in posts_basic))
        if author_ids:
            authors = await self.user_cache_service.get_users_by_ids(db, user_ids=author_ids)
            if not authors:
                logger.warning(
                    "_aggregate_posts_by_ids 未获取到任何作者信息",
                    extra={"author_ids": author_ids, "post_ids": post_ids},
                )
        else:
            authors = []

        # 3. 获取统计信息（按照顺序执行，避免在同一会话上并发查询导致冲突）
        stats_list = []
        if include_stats and current_user_id:
            stats_list = await self.content_stats_service.get_posts_stats(
                db, post_ids=post_ids, user_id=current_user_id
            )

        # 4. 获取媒体信息
        media_map: dict[int, list[schemas.PostMediaOut] | None] = {}
        if include_media:
            media_map = await self.post_cache_service.get_media_map(db, post_ids=post_ids)

        # 5. 获取投票实时统计
        poll_summary_map: dict[int, schemas.PostPollSummary] = {}
        poll_post_ids = [post.id for post in posts_basic if post.post_type == PostType.POLL]
        user_poll_choices: dict[int, list[int]] = {}

        if poll_post_ids:
            vote_counts_map = await crud_post.get_poll_vote_counts(db, post_ids=poll_post_ids)
            if current_user_id:
                user_poll_choices = await crud_post.get_user_poll_choices(
                    db, post_ids=poll_post_ids, user_id=current_user_id
                )

            for poll_post in posts_basic:
                if poll_post.post_type != PostType.POLL:
                    continue
                poll_summary = self._build_poll_summary(
                    post=poll_post,
                    vote_counts=vote_counts_map.get(poll_post.id, {}),
                    user_choices=user_poll_choices.get(poll_post.id, []),
                )
                poll_summary_map[poll_post.id] = poll_summary

        # 构建映射字典
        author_map = {author.id: author for author in authors}
        stats_map = {stat.content_id: stat for stat in stats_list} if stats_list else {}

        # 聚合数据
        result = []
        missing_author_ids: set[int] = set()

        for post in posts_basic:
            author = author_map.get(post.author_id)
            if not author:
                missing_author_ids.add(post.author_id)
                continue  # 跳过没有作者信息的沸点

            stats = stats_map.get(post.id) if include_stats else None
            poll_summary = poll_summary_map.get(post.id)
            user_poll_votes = (
                poll_summary.user_choices if poll_summary and poll_summary.user_choices else None
            )

            # 构建PostOut对象
            post_out = schemas.PostOut(
                **post.model_dump(),
                author=author,
                stats=stats,
                media=media_map.get(post.id) or None,
                poll_summary=poll_summary,
                user_poll_votes=user_poll_votes,
            )
            result.append(post_out)

        if missing_author_ids:
            logger.warning(
                "_aggregate_posts_by_ids 存在缺失作者信息的沸点",
                extra={
                    "missing_author_ids": list(missing_author_ids),
                    "post_ids": post_ids,
                    "fetched_post_count": len(posts_basic),
                    "resolved_count": len(result),
                },
            )

        if not result:
            logger.warning(
                "_aggregate_posts_by_ids 聚合结果为空",
                extra={
                    "post_ids": post_ids,
                    "missing_author_ids": list(missing_author_ids),
                    "stats_enabled": include_stats,
                    "stats_count": len(stats_list),
                },
            )

        return result

    def _build_poll_summary(
        self,
        *,
        post: schemas.PostBase,
        vote_counts: dict[int, int] | None,
        user_choices: list[int] | None,
    ) -> schemas.PostPollSummary:
        """根据投票统计数据构建摘要，并同步更新选项票数"""

        vote_counts = vote_counts or {}
        user_choices = user_choices or []
        total_votes = sum(vote_counts.values())
        option_stats: list[schemas.PostPollOptionStat] = []

        if post.poll_options:
            for index, option in enumerate(post.poll_options):
                count = vote_counts.get(index, 0)
                ratio = count / total_votes if total_votes else 0.0
                option.vote_count = count
                option.vote_ratio = ratio
                option_stats.append(
                    schemas.PostPollOptionStat(
                        index=index,
                        text=option.text,
                        vote_count=count,
                        vote_ratio=ratio,
                    )
                )

        return schemas.PostPollSummary(
            post_id=post.id,
            total_votes=total_votes,
            option_stats=option_stats,
            user_choices=user_choices,
            multiple_choice=bool(post.poll_multiple_choice),
            expires_at=post.poll_expires_at,
        )

    async def create_post(
        self,
        db: AsyncSession,
        *,
        post_in: schemas.PostCreate,
        author_id: int,
    ) -> schemas.PostOut:
        """创建新沸点"""

        # 如果关联草稿，先获取草稿并验证归属
        draft: models.Post | None = None
        if post_in.draft_id:
            draft = await crud_post.get_by_id_for_author(
                db,
                post_id=post_in.draft_id,
                author_id=author_id,
            )
            if not draft or draft.status != PostStatus.DRAFT:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="草稿不存在或已发布",
                )

        # 构建创建数据
        create_data = {
            "author_id": author_id,
            "content": post_in.content,
            "post_type": post_in.post_type,
            "visibility": post_in.visibility,
            "topic": post_in.topic,
            "location": post_in.location,
            "status": PostStatus.PUBLISHED,
        }

        # 处理转发
        if post_in.original_post_id:
            create_data.update(
                {
                    "original_post_id": post_in.original_post_id,
                    "repost_comment": post_in.repost_comment,
                    "post_type": PostType.REPOST,
                }
            )

        # 处理链接分享
        if post_in.post_type == PostType.LINK:
            create_data.update(
                {
                    "link_url": post_in.link_url,
                    "link_title": post_in.link_title,
                    "link_description": post_in.link_description,
                    "link_image": post_in.link_image,
                }
            )

        # 处理投票
        if post_in.post_type == PostType.POLL and post_in.poll_options:
            poll_options_data = [
                {"text": option, "vote_count": 0} for option in post_in.poll_options
            ]
            create_data.update(
                {
                    "poll_options": poll_options_data,
                    "poll_multiple_choice": post_in.poll_multiple_choice,
                }
            )

            if post_in.poll_expires_hours:
                expires_at = datetime.now(UTC) + timedelta(hours=post_in.poll_expires_hours)
                create_data["poll_expires_at"] = expires_at

        try:
            if draft:
                # 将草稿转为正式发布
                for field, value in create_data.items():
                    setattr(draft, field, value)
                draft.status = PostStatus.PUBLISHED
                draft.cache_version += 1
                post = draft
                await db.flush()
            else:
                post = await crud_post.create(db, obj_in=create_data, commit=False)

            # 处理媒体文件
            if post_in.media_files:
                await self._create_post_media(db, post_id=post.id, media_urls=post_in.media_files)

            # 处理@提及
            if post_in.mentions:
                await self._create_post_mentions(
                    db,
                    post_id=post.id,
                    mentions=post_in.mentions,
                    author_id=author_id,
                    post_content=post_in.content,
                )

            # 创建发件箱消息延迟处理缓存
            crud_post._create_event(
                db,
                topic="post.created",
                payload={
                    "event": "created",
                    "post_id": post.id,
                    "author_id": post.author_id,
                    "topic": post.topic,
                },
            )

            if draft:
                await db.flush()
            await db.commit()

            # 更新话题统计（在事务提交后异步执行）
            if post.topic:
                try:
                    await self.topic_stats_service.increment_topic_usage(db, topic=post.topic)
                except Exception as e:
                    logger.warning(f"更新话题统计失败: {post.topic}, error: {e}")

        except Exception:
            await db.rollback()
            raise

        # 返回聚合后的沸点信息
        posts = await self._aggregate_posts_by_ids(
            db, post_ids=[post.id], current_user_id=author_id
        )
        if not posts:
            logger.error(
                "create_post 聚合返回为空",
                extra={
                    "post_id": post.id,
                    "author_id": author_id,
                    "draft_id": post_in.draft_id,
                    "has_media": bool(post_in.media_files),
                    "has_mentions": bool(post_in.mentions),
                },
            )
            return None
        return posts[0]

    async def save_draft(
        self,
        db: AsyncSession,
        *,
        draft_in: schemas.PostDraftSave,
        author_id: int,
    ) -> schemas.PostDraftOut:
        """保存或更新草稿"""
        try:
            if draft_in.id:
                draft = await crud_post.get_by_id_for_author(
                    db,
                    post_id=draft_in.id,
                    author_id=author_id,
                )
                if not draft or draft.status not in {PostStatus.DRAFT, PostStatus.DELETED}:
                    raise HTTPException(
                        status_code=status.HTTP_404_NOT_FOUND,
                        detail="草稿不存在",
                    )
                updated = await crud_post.update_draft(db, draft=draft, draft_in=draft_in)
                draft_id = draft.id
            else:
                updated = await crud_post.create_draft(db, author_id=author_id, draft_in=draft_in)
                draft_id = updated.id

            await self._replace_draft_media(
                db,
                draft_id=draft_id,
                media_urls=draft_in.media_files or [],
            )

            await db.commit()
            await db.refresh(updated)
            await db.refresh(updated, attribute_names=["media"])
        except Exception:
            await db.rollback()
            raise

        return schemas.PostDraftOut.model_validate(updated)

    async def _replace_draft_media(
        self,
        db: AsyncSession,
        *,
        draft_id: int,
        media_urls: list[str],
    ) -> None:
        """替换草稿媒体记录"""
        from sqlalchemy import delete

        if draft_id <= 0:
            return

        # 清理旧媒体
        await db.execute(delete(PostMedia).where(PostMedia.post_id == draft_id))

        if not media_urls:
            return

        await self._create_post_media(db, post_id=draft_id, media_urls=media_urls)

    async def list_drafts(
        self,
        db: AsyncSession,
        *,
        author_id: int,
        limit: int = 20,
        offset: int = 0,
    ) -> list[schemas.PostDraftOut]:
        drafts = await crud_post.get_drafts_by_author(
            db, author_id=author_id, limit=limit, offset=offset
        )
        return [schemas.PostDraftOut.model_validate(draft) for draft in drafts]

    async def delete_draft(
        self,
        db: AsyncSession,
        *,
        draft_id: int,
        author_id: int,
    ) -> None:
        draft = await crud_post.get_by_id_for_author(
            db,
            post_id=draft_id,
            author_id=author_id,
        )
        if not draft or draft.status not in {PostStatus.DRAFT, PostStatus.DELETED}:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="草稿不存在")

        try:
            await crud_post.delete_draft(db, draft=draft, hard_delete=True)
            await db.commit()
        except Exception:
            await db.rollback()
            raise

    async def get_post_by_id(
        self,
        db: AsyncSession,
        *,
        post_id: int,
        current_user_id: int | None = None,
    ) -> schemas.PostOut | None:
        """获取单个沸点详情"""
        posts = await self._aggregate_posts_by_ids(
            db, post_ids=[post_id], current_user_id=current_user_id
        )
        if not posts:
            logger.warning(
                "get_post_by_id 聚合返回为空",
                extra={"post_id": post_id, "current_user_id": current_user_id},
            )
            return None
        return posts[0]

    async def get_posts(
        self,
        db: AsyncSession,
        *,
        query_params: schemas.PostListQuery,
        current_user_id: int | None = None,
    ) -> list[schemas.PostOut]:
        """获取沸点列表"""
        offset = (query_params.page - 1) * query_params.size

        # 从数据库获取沸点
        posts = await crud_post.get_posts_with_filters(
            db,
            author_id=query_params.author_id,
            post_type=query_params.post_type,
            topic=query_params.topic,
            visibility=query_params.visibility,
            is_hot=query_params.is_hot,
            limit=query_params.size,
            offset=offset,
            sort_by=query_params.sort_by,
            sort_order=query_params.sort_order,
        )

        post_ids = [post.id for post in posts]
        aggregated_posts = await self._aggregate_posts_by_ids(
            db, post_ids=post_ids, current_user_id=current_user_id
        )
        if len(post_ids) and not aggregated_posts:
            logger.warning(
                "get_posts 聚合结果为空",
                extra={
                    "requested_post_ids": post_ids,
                    "current_user_id": current_user_id,
                    "filters": query_params.model_dump(),
                },
            )
        return aggregated_posts or []

    async def get_following_posts(
        self,
        db: AsyncSession,
        *,
        user_id: int,
        page: int = 1,
        size: int = 20,
    ) -> list[schemas.PostOut]:
        """获取关注用户的沸点列表"""
        offset = (page - 1) * size

        posts = await crud_post.get_following_posts(db, user_id=user_id, limit=size, offset=offset)

        post_ids = [post.id for post in posts]
        aggregated_posts = await self._aggregate_posts_by_ids(
            db, post_ids=post_ids, current_user_id=user_id
        )
        if post_ids and not aggregated_posts:
            logger.warning(
                "get_following_posts 聚合结果为空",
                extra={
                    "requested_post_ids": post_ids,
                    "user_id": user_id,
                    "page": page,
                    "size": size,
                },
            )
        return aggregated_posts or []

    async def get_post_reposts(
        self,
        db: AsyncSession,
        *,
        original_post_id: int,
        current_user_id: int | None = None,
        page: int = 1,
        size: int = 20,
    ) -> list[schemas.PostOut]:
        """获取沸点的转发列表"""
        offset = (page - 1) * size

        reposts = await crud_post.get_post_reposts(
            db, original_post_id=original_post_id, limit=size, offset=offset
        )

        post_ids = [post.id for post in reposts]
        aggregated_posts = await self._aggregate_posts_by_ids(
            db, post_ids=post_ids, current_user_id=current_user_id
        )
        if post_ids and not aggregated_posts:
            logger.warning(
                "get_post_reposts 聚合结果为空",
                extra={
                    "requested_post_ids": post_ids,
                    "original_post_id": original_post_id,
                    "current_user_id": current_user_id,
                },
            )
        return aggregated_posts or []

    async def update_post(
        self,
        db: AsyncSession,
        *,
        post_id: int,
        post_in: schemas.PostUpdate,
        current_user_id: int,
    ) -> schemas.PostOut:
        """更新沸点"""
        # 获取现有沸点
        existing_post = await crud_post.get(db, id=post_id)
        if not existing_post:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="沸点不存在")

        previous_topic = existing_post.topic

        try:
            # 更新沸点但暂不提交
            updated_post = await crud_post.update(
                db, db_obj=existing_post, obj_in=post_in, commit=False
            )

            crud_post._create_event(
                db,
                topic="post.updated",
                payload={
                    "event": "updated",
                    "post_id": updated_post.id,
                    "author_id": updated_post.author_id,
                    "topic": updated_post.topic,
                    "previous_topic": previous_topic,
                },
            )

            await db.commit()
        except Exception:
            await db.rollback()
            raise

        # 返回更新后的沸点
        posts = await self._aggregate_posts_by_ids(
            db, post_ids=[post_id], current_user_id=current_user_id
        )
        return posts[0]

    async def delete_post(
        self,
        db: AsyncSession,
        *,
        post_id: int,
        current_user_id: int,
    ) -> None:
        """删除沸点（物理删除）"""
        try:
            post = await crud_post.delete(db, post_id=post_id, commit=False)
            if not post:
                await db.rollback()
                return

            crud_post._create_event(
                db,
                topic="post.deleted",
                payload={
                    "event": "deleted",
                    "post_id": post.id,
                    "author_id": post.author_id,
                    "topic": post.topic,
                },
            )

            await db.commit()

            # 减少话题统计（在事务提交后异步执行）
            if post.topic:
                try:
                    await self.topic_stats_service.decrement_topic_usage(db, topic=post.topic)
                except Exception as e:
                    logger.warning(f"减少话题统计失败: {post.topic}, error: {e}")

            # 清理缓存，避免返回已删除数据
            try:
                await self.post_cache_service.invalidate_entity(post_id)
                await self.post_cache_service.invalidate_author_cache(post.author_id)
                if post.topic:
                    await self.post_cache_service.invalidate_topic_cache(post.topic)
                await self.post_cache_service.invalidate_hot_cache()
            except Exception as e:
                logger.warning(f"删除沸点后清理缓存失败: {e}")

        except Exception:
            await db.rollback()
            raise

    async def like_post(
        self,
        db: AsyncSession,
        *,
        post_id: int,
        user_id: int,
    ) -> None:
        """点赞沸点"""
        from app.crud.like import like as crud_like

        # 检查沸点是否存在
        post = await crud_post.get(db, id=post_id)
        if not post:
            raise ValueError("沸点不存在")

        # 使用现有的点赞服务
        _, is_liked = await crud_like.toggle_like(
            db,
            user_id=user_id,
            content_type="post",
            content_id=post_id,
            commit=False,
        )

        # 提交事务
        await db.commit()

        # 更新缓存中的点赞状态
        if hasattr(self, "content_stats_service"):
            await self.content_stats_service.update_like_status("post", post_id, user_id, is_liked)

        # 更新话题互动统计
        if post.topic:
            try:
                likes_delta = 1 if is_liked else -1
                await self.topic_stats_service.increment_topic_usage(
                    db, topic=post.topic, likes_delta=likes_delta
                )
            except Exception as e:
                logger.warning(f"更新话题点赞统计失败: {post.topic}, error: {e}")

        # 发送点赞通知（仅在点赞时发送，取消点赞不发送）
        if is_liked:
            try:
                # 获取沸点和用户信息
                from app.crud.user import user as crud_user

                post = await crud_post.get(db, id=post_id)
                liker = await crud_user.get(db, id=user_id)

                if post and liker and post.author_id != user_id:  # 不给自己发通知
                    await self.post_notification_service.send_like_notification(
                        db,
                        liker_user_id=user_id,
                        liker_username=liker.username,
                        post_id=post_id,
                        post_author_id=post.author_id,
                        post_content=post.content,
                    )
            except Exception as e:
                # 通知发送失败不应该影响主流程
                logger.error(f"发送点赞通知失败: {str(e)}")

    async def unlike_post(
        self,
        db: AsyncSession,
        *,
        post_id: int,
        user_id: int,
    ) -> None:
        """取消点赞沸点"""
        # 实际上点赞和取消点赞都是toggle操作，所以直接调用like_post
        await self.like_post(db, post_id=post_id, user_id=user_id)

    async def vote_poll(
        self,
        db: AsyncSession,
        *,
        post_id: int,
        user_id: int,
        option_indexes: list[int],
    ) -> schemas.PostPollSummary:
        """投票"""
        # 检查沸点是否存在且为投票类型
        post = await crud_post.get(db, id=post_id)
        if not post:
            raise ValueError("沸点不存在")

        if post.post_type != "poll":
            raise ValueError("该沸点不是投票类型")

        # 检查投票是否已过期
        if post.poll_expires_at and post.poll_expires_at < datetime.now(UTC):
            raise ValueError("投票已过期")

        # 检查选项索引是否有效
        if not post.poll_options:
            raise ValueError("投票选项不存在")

        max_index = len(post.poll_options) - 1
        for index in option_indexes:
            if index < 0 or index > max_index:
                raise ValueError(f"无效的投票选项索引: {index}")

        # 检查是否允许多选
        if not post.poll_multiple_choice and len(option_indexes) > 1:
            raise ValueError("该投票不允许多选")

        # 删除用户之前的投票记录
        from sqlalchemy import delete

        from app.models.post import PostPollVote
        from app.schemas.post import PostPollVoteCreate

        await db.execute(
            delete(PostPollVote).where(
                PostPollVote.post_id == post_id, PostPollVote.user_id == user_id
            )
        )

        # 创建新的投票记录
        for option_index in option_indexes:
            vote_record = PostPollVoteCreate(
                post_id=post_id, user_id=user_id, option_index=option_index
            )
            await crud_post.create_post_poll_vote(db, obj_in=vote_record, commit=False)

        await db.commit()

        # 构建实时投票摘要
        post_schema = schemas.PostBase.model_validate(post)
        vote_counts_map = await crud_post.get_poll_vote_counts(db, post_ids=[post_id])
        user_choice_map = await crud_post.get_user_poll_choices(
            db, post_ids=[post_id], user_id=user_id
        )

        poll_summary = self._build_poll_summary(
            post=post_schema,
            vote_counts=vote_counts_map.get(post_id, {}),
            user_choices=user_choice_map.get(post_id, []),
        )

        # 发送投票通知
        try:
            from app.crud.user import user as crud_user

            voter = await crud_user.get(db, id=user_id)
            if voter and post.author_id != user_id:  # 不给自己发通知
                await self.post_notification_service.send_poll_vote_notification(
                    db,
                    voter_user_id=user_id,
                    voter_username=voter.username,
                    post_id=post_id,
                    post_author_id=post.author_id,
                    post_content=post.content,
                    option_indexes=option_indexes,
                )
        except Exception as e:
            # 通知发送失败不应该影响主流程
            logger.error(f"发送投票通知失败: {str(e)}")

        return poll_summary

    async def _create_post_media(
        self,
        db: AsyncSession,
        *,
        post_id: int,
        media_urls: list[str],
    ) -> None:
        """创建沸点媒体文件"""
        if not media_urls:
            return

        from app.crud.file_hash import file_hash as crud_file_hash
        from app.schemas.post import PostMediaCreate

        for index, media_url in enumerate(media_urls):
            # 从file_hash表获取媒体文件信息
            file_hash_record = await crud_file_hash.get_by_path(db, file_path=media_url)

            # 确定媒体类型
            media_type = "image"  # 默认
            if media_url.endswith((".mp4", ".webm", ".avi", ".mov")):
                media_type = "video"
            elif media_url.endswith((".mp3", ".wav", ".aac")):
                media_type = "audio"

            # 从file_hash记录中获取媒体属性
            media_width = None
            media_height = None
            media_duration = None
            media_size = None

            if file_hash_record and file_hash_record.file_metadata:
                metadata = file_hash_record.file_metadata
                media_width = metadata.get("width")
                media_height = metadata.get("height")
                media_duration = metadata.get("duration")
                media_size = metadata.get("size")

            # 生成缩略图URL（对于视频）
            thumbnail_url = None
            if media_type == "video":
                # 假设缩略图URL规则：原视频URL + _thumb.jpg
                thumbnail_url = media_url.replace(".mp4", "_thumb.jpg").replace(
                    ".webm", "_thumb.jpg"
                )

            media_record = PostMediaCreate(
                post_id=post_id,
                media_type=media_type,
                media_url=media_url,
                thumbnail_url=thumbnail_url,
                media_size=media_size,
                media_width=media_width,
                media_height=media_height,
                media_duration=media_duration,
                sort_order=index,
            )

            # 创建媒体记录
            await crud_post.create_post_media(db, obj_in=media_record, commit=False)

    async def _create_post_mentions(
        self,
        db: AsyncSession,
        *,
        post_id: int,
        mentions: list[str],
        author_id: int | None = None,
        post_content: str | None = None,
    ) -> None:
        """创建沸点@提及"""
        if not mentions:
            return

        from app.crud.user import user as crud_user
        from app.schemas.post import PostMentionCreate

        # 获取作者信息用于通知
        author = None
        if author_id:
            author = await crud_user.get(db, id=author_id)

        for mention_text in mentions:
            # 去掉@符号，获取用户名
            username = mention_text.lstrip("@").strip()
            if not username:
                continue

            # 查找用户是否存在
            mentioned_user = await crud_user.get_by_username(db, username=username)
            if not mentioned_user:
                continue  # 用户不存在，跳过

            # 不能@自己
            if author_id and mentioned_user.id == author_id:
                continue

            # 创建@提及记录
            mention_record = PostMentionCreate(
                post_id=post_id, mentioned_user_id=mentioned_user.id, mention_text=mention_text
            )

            await crud_post.create_post_mention(db, obj_in=mention_record, commit=False)

            # 发送@提及通知
            if author:
                try:
                    await self.post_notification_service.send_mention_notification(
                        db,
                        mentioner_user_id=author_id,
                        mentioner_username=author.username,
                        mentioned_user_id=mentioned_user.id,
                        post_id=post_id,
                        post_content=post_content,
                        mention_text=mention_text,
                    )
                except Exception as e:
                    # 通知发送失败不应该影响主流程
                    logger.error(f"发送@提及通知失败: {str(e)}")

            # TODO: 发送@提及通知
            # await self.notification_service.send_mention_notification(
            #     mentioned_user_id=mentioned_user.id,
            #     post_id=post_id,
            #     mention_text=mention_text
            # )
