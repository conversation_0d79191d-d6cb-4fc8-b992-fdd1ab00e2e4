import asyncio
from collections.abc import Sequence
from typing import Any

from sqlalchemy.ext.asyncio import AsyncSession

from app import schemas
from app.core.pagination import CursorPaginationParams, CursorPaginationResponse
from app.services.user_cache_service import UserCacheService
from app.services.user_stats_service import UserStatsCacheService


class UserAggregationService:
    def __init__(
        self,
        user_cache_service: UserCacheService,
        user_stats_service: UserStatsCacheService,
    ):
        self.user_cache_service = user_cache_service
        self.user_stats_service = user_stats_service

    async def get_user_profile(
        self, db: AsyncSession, user_id: int
    ) -> schemas.UserAggregated | None:
        """
        获取完整的用户个人资料，聚合了核心信息和统计数据。
        """
        # 1. 获取用户核心信息
        user_base = await self.user_cache_service.get_entity(db, entity_id=user_id)
        if not user_base:
            return None

        # 2. 获取用户统计信息
        user_stats = await self.user_stats_service.get_user_stats(db, user_id=user_id)

        # 如果找不到统计信息，则创建一个空的默认实例
        if user_stats is None:
            user_stats = schemas.UserStats(user_id=user_id)

        # 3. 聚合数据
        user_aggregated_data = user_base.model_dump()
        user_aggregated_data["stats"] = user_stats

        return schemas.UserAggregated(**user_aggregated_data)

    async def get_users_by_ids(
        self, db: AsyncSession, user_ids: Sequence[int]
    ) -> dict[int, schemas.UserAggregated]:
        """批量获取用户信息并聚合统计数据。"""

        if not user_ids:
            return {}

        # 去重但保持原有顺序
        unique_ids: list[int] = []
        seen: set[int] = set()
        for user_id in user_ids:
            if user_id not in seen:
                seen.add(user_id)
                unique_ids.append(user_id)

        cache_map = await self.user_cache_service.get_entities_batch(db, unique_ids)
        results: dict[int, schemas.UserAggregated] = {}

        for user_id in unique_ids:
            user_base = cache_map.get(user_id)
            if user_base is None:
                continue

            user_stats = await self.user_stats_service.get_user_stats(db, user_id=user_id)
            if user_stats is None:
                user_stats = schemas.UserStats(user_id=user_id)

            aggregated_data = user_base.model_dump()
            aggregated_data["stats"] = user_stats
            results[user_id] = schemas.UserAggregated(**aggregated_data)

        return results

    async def get_paginated_users(
        self,
        *,
        db: AsyncSession,
        params: CursorPaginationParams,
        filters: dict[str, Any] | None = None,
        include_total: bool = False,
    ) -> CursorPaginationResponse[schemas.UserAggregated]:
        """按游标分页获取用户并聚合统计数据。"""
        paginated_users = await self.user_cache_service.crud.get_paginated_users(
            db=db,
            params=params,
            filters=filters,
            include_total=include_total,
        )

        base_users: list[schemas.UserBase] = []
        user_ids: list[int] = []
        for user in paginated_users.items:
            try:
                user_schema = schemas.UserBase.model_validate(user)
            except Exception:
                continue
            user_ids.append(user_schema.id)
            base_users.append(user_schema)

        if not user_ids or not base_users:
            return CursorPaginationResponse[schemas.UserAggregated](
                items=[],
                has_next=paginated_users.has_next,
                has_previous=paginated_users.has_previous,
                next_cursor=paginated_users.next_cursor,
                previous_cursor=paginated_users.previous_cursor,
                total_count=paginated_users.total_count,
            )

        stats_tasks = [
            self.user_stats_service.get_user_stats(db, user_id=user_id) for user_id in user_ids
        ]
        stats_results = await asyncio.gather(*stats_tasks) if stats_tasks else []
        stats_map = dict(zip(user_ids, stats_results))

        aggregated_items: list[schemas.UserAggregated] = []
        for user_base in base_users:
            user_stats = stats_map.get(user_base.id) or schemas.UserStats(user_id=user_base.id)
            aggregated_data = user_base.model_dump()
            aggregated_data["stats"] = user_stats
            aggregated_items.append(schemas.UserAggregated(**aggregated_data))

        return CursorPaginationResponse[schemas.UserAggregated](
            items=aggregated_items,
            has_next=paginated_users.has_next,
            has_previous=paginated_users.has_previous,
            next_cursor=paginated_users.next_cursor,
            previous_cursor=paginated_users.previous_cursor,
            total_count=paginated_users.total_count,
        )


# 全局实例将在 service_factory 中创建和管理
