"""
搜索缓存服务

提供：
- 搜索结果缓存
- 搜索建议缓存
- 热门搜索缓存
"""

from typing import Optional, List, Dict, Any
import json
import hashlib
from redis.asyncio import Redis

from app.core.logging import logger
from app.config import settings


class SearchCacheService:
    """搜索缓存服务"""
    
    # 缓存键前缀
    SEARCH_RESULT_PREFIX = "search:result"
    SEARCH_SUGGESTION_PREFIX = "search:suggest"
    TRENDING_PREFIX = "search:trending"
    
    # 缓存过期时间（秒）
    RESULT_TTL = 300  # 5分钟
    SUGGESTION_TTL = 3600  # 1小时
    TRENDING_TTL = 600  # 10分钟
    
    def __init__(self, redis: Optional[Redis] = None):
        """
        初始化缓存服务
        
        Args:
            redis: Redis 客户端实例
        """
        self.redis = redis
    
    @staticmethod
    def generate_cache_key(
        prefix: str,
        content_type: str,
        query: str,
        filters: Optional[Dict[str, Any]] = None,
    ) -> str:
        """
        生成缓存键
        
        Args:
            prefix: 键前缀
            content_type: 内容类型
            query: 搜索查询
            filters: 过滤条件
            
        Returns:
            缓存键字符串
        """
        # 将过滤条件转为稳定的字符串
        if filters:
            filter_str = json.dumps(filters, sort_keys=True)
        else:
            filter_str = ""
        
        # 生成哈希
        combined = f"{content_type}:{query}:{filter_str}"
        hash_str = hashlib.md5(combined.encode()).hexdigest()
        
        return f"{prefix}:{content_type}:{hash_str}"
    
    async def get_search_results(
        self,
        content_type: str,
        query: str,
        filters: Optional[Dict[str, Any]] = None,
    ) -> Optional[List[Dict[str, Any]]]:
        """
        获取缓存的搜索结果
        
        Args:
            content_type: 内容类型
            query: 搜索查询
            filters: 过滤条件
            
        Returns:
            缓存的结果列表，如果不存在返回 None
        """
        if not self.redis:
            return None
        
        try:
            key = self.generate_cache_key(
                self.SEARCH_RESULT_PREFIX,
                content_type,
                query,
                filters,
            )
            
            cached = await self.redis.get(key)
            if cached:
                logger.debug(f"搜索缓存命中: key={key}")
                return json.loads(cached)
            
            logger.debug(f"搜索缓存未命中: key={key}")
            return None
            
        except Exception as e:
            logger.error(f"获取搜索缓存失败: {e}")
            return None
    
    async def cache_search_results(
        self,
        content_type: str,
        query: str,
        filters: Optional[Dict[str, Any]],
        results: List[Dict[str, Any]],
        ttl: Optional[int] = None,
    ) -> bool:
        """
        缓存搜索结果
        
        Args:
            content_type: 内容类型
            query: 搜索查询
            filters: 过滤条件
            results: 搜索结果列表
            ttl: 过期时间（秒），None 使用默认值
            
        Returns:
            是否成功
        """
        if not self.redis:
            return False
        
        try:
            key = self.generate_cache_key(
                self.SEARCH_RESULT_PREFIX,
                content_type,
                query,
                filters,
            )
            
            value = json.dumps(results, ensure_ascii=False)
            ttl = ttl or self.RESULT_TTL
            
            await self.redis.setex(key, ttl, value)
            logger.debug(f"缓存搜索结果: key={key}, ttl={ttl}")
            return True
            
        except Exception as e:
            logger.error(f"缓存搜索结果失败: {e}")
            return False
    
    async def get_suggestions(
        self,
        prefix: str,
        content_type: str = "all",
    ) -> Optional[List[Dict[str, Any]]]:
        """
        获取缓存的搜索建议
        
        Args:
            prefix: 搜索前缀
            content_type: 内容类型
            
        Returns:
            建议列表
        """
        if not self.redis:
            return None
        
        try:
            key = f"{self.SEARCH_SUGGESTION_PREFIX}:{content_type}:{prefix}"
            cached = await self.redis.get(key)
            
            if cached:
                logger.debug(f"建议缓存命中: key={key}")
                return json.loads(cached)
            
            return None
            
        except Exception as e:
            logger.error(f"获取建议缓存失败: {e}")
            return None
    
    async def cache_suggestions(
        self,
        prefix: str,
        content_type: str,
        suggestions: List[Dict[str, Any]],
        ttl: Optional[int] = None,
    ) -> bool:
        """
        缓存搜索建议
        
        Args:
            prefix: 搜索前缀
            content_type: 内容类型
            suggestions: 建议列表
            ttl: 过期时间
            
        Returns:
            是否成功
        """
        if not self.redis:
            return False
        
        try:
            key = f"{self.SEARCH_SUGGESTION_PREFIX}:{content_type}:{prefix}"
            value = json.dumps(suggestions, ensure_ascii=False)
            ttl = ttl or self.SUGGESTION_TTL
            
            await self.redis.setex(key, ttl, value)
            logger.debug(f"缓存搜索建议: key={key}")
            return True
            
        except Exception as e:
            logger.error(f"缓存建议失败: {e}")
            return False
    
    async def get_trending(
        self,
        content_type: str = "all",
        period: str = "day",
    ) -> Optional[List[Dict[str, Any]]]:
        """
        获取缓存的热门搜索
        
        Args:
            content_type: 内容类型
            period: 时间段
            
        Returns:
            热门搜索列表
        """
        if not self.redis:
            return None
        
        try:
            key = f"{self.TRENDING_PREFIX}:{content_type}:{period}"
            cached = await self.redis.get(key)
            
            if cached:
                logger.debug(f"热门搜索缓存命中: key={key}")
                return json.loads(cached)
            
            return None
            
        except Exception as e:
            logger.error(f"获取热门搜索缓存失败: {e}")
            return None
    
    async def cache_trending(
        self,
        content_type: str,
        period: str,
        trending: List[Dict[str, Any]],
        ttl: Optional[int] = None,
    ) -> bool:
        """
        缓存热门搜索
        
        Args:
            content_type: 内容类型
            period: 时间段
            trending: 热门搜索列表
            ttl: 过期时间
            
        Returns:
            是否成功
        """
        if not self.redis:
            return False
        
        try:
            key = f"{self.TRENDING_PREFIX}:{content_type}:{period}"
            value = json.dumps(trending, ensure_ascii=False)
            ttl = ttl or self.TRENDING_TTL
            
            await self.redis.setex(key, ttl, value)
            logger.debug(f"缓存热门搜索: key={key}")
            return True
            
        except Exception as e:
            logger.error(f"缓存热门搜索失败: {e}")
            return False
    
    async def invalidate_search_cache(
        self,
        content_type: Optional[str] = None,
    ) -> int:
        """
        使搜索缓存失效
        
        Args:
            content_type: 内容类型，None 表示全部
            
        Returns:
            删除的键数量
        """
        if not self.redis:
            return 0
        
        try:
            if content_type:
                pattern = f"{self.SEARCH_RESULT_PREFIX}:{content_type}:*"
            else:
                pattern = f"{self.SEARCH_RESULT_PREFIX}:*"
            
            # 查找匹配的键
            keys = []
            async for key in self.redis.scan_iter(match=pattern):
                keys.append(key)
            
            # 删除键
            if keys:
                count = await self.redis.delete(*keys)
                logger.info(f"失效搜索缓存: 删除键数={count}")
                return count
            
            return 0
            
        except Exception as e:
            logger.error(f"失效搜索缓存失败: {e}")
            return 0
    
    async def clear_all_cache(self) -> bool:
        """
        清空所有搜索相关缓存
        
        Returns:
            是否成功
        """
        if not self.redis:
            return False
        
        try:
            patterns = [
                f"{self.SEARCH_RESULT_PREFIX}:*",
                f"{self.SEARCH_SUGGESTION_PREFIX}:*",
                f"{self.TRENDING_PREFIX}:*",
            ]
            
            total_deleted = 0
            for pattern in patterns:
                keys = []
                async for key in self.redis.scan_iter(match=pattern):
                    keys.append(key)
                
                if keys:
                    count = await self.redis.delete(*keys)
                    total_deleted += count
            
            logger.info(f"清空搜索缓存: 删除键数={total_deleted}")
            return True
            
        except Exception as e:
            logger.error(f"清空搜索缓存失败: {e}")
            return False
