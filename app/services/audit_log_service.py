"""审计日志服务"""

import json
from datetime import datetime
from typing import Any, Dict, List, Optional

from fastapi import Request
from sqlalchemy import and_, desc, func, or_, select
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.logging import logger
from app.models.audit_log import AuditAction, AuditLog, AuditResourceType
from app.models.user import User


class AuditLogService:
    """审计日志服务"""
    
    @staticmethod
    async def log_action(
        db: AsyncSession,
        user: User,
        action: AuditAction,
        resource_type: AuditResourceType,
        resource_id: Optional[int] = None,
        resource_name: Optional[str] = None,
        description: Optional[str] = None,
        reason: Optional[str] = None,
        old_values: Optional[Dict[str, Any]] = None,
        new_values: Optional[Dict[str, Any]] = None,
        request: Optional[Request] = None,
        success: bool = True,
        error_message: Optional[str] = None,
        endpoint: Optional[str] = None,
        method: Optional[str] = None,
        duration_ms: Optional[int] = None
    ) -> AuditLog:
        """记录审计日志"""
        
        try:
            # 处理敏感信息
            safe_old_values = AuditLogService._sanitize_values(old_values)
            safe_new_values = AuditLogService._sanitize_values(new_values)
            
            # 从请求中提取信息
            ip_address = None
            user_agent = None
            if request:
                ip_address = AuditLogService._get_client_ip(request)
                user_agent = request.headers.get("user-agent")
                if not endpoint:
                    endpoint = str(request.url.path)
                if not method:
                    method = request.method
            
            # 创建审计日志记录
            audit_log = AuditLog(
                user_id=user.id,
                user_name=user.username,
                user_email=user.email,
                ip_address=ip_address,
                user_agent=user_agent,
                action=action,
                resource_type=resource_type,
                resource_id=resource_id,
                resource_name=resource_name,
                description=description,
                reason=reason,
                old_values=safe_old_values,
                new_values=safe_new_values,
                success=success,
                error_message=error_message,
                endpoint=endpoint,
                method=method,
                duration_ms=duration_ms
            )
            
            db.add(audit_log)
            await db.commit()
            await db.refresh(audit_log)
            
            logger.info(f"审计日志记录成功: {user.username} {action} {resource_type} {resource_id}")
            return audit_log
            
        except Exception as e:
            logger.error(f"记录审计日志失败: {str(e)}")
            await db.rollback()
            raise
    
    @staticmethod
    def _sanitize_values(values: Optional[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
        """清理敏感信息"""
        if not values:
            return values
        
        sensitive_fields = {
            'password', 'hashed_password', 'token', 'secret', 'key',
            'private_key', 'access_token', 'refresh_token'
        }
        
        sanitized = {}
        for key, value in values.items():
            if any(field in key.lower() for field in sensitive_fields):
                sanitized[key] = "***REDACTED***"
            else:
                sanitized[key] = value
        
        return sanitized
    
    @staticmethod
    def _get_client_ip(request: Request) -> Optional[str]:
        """获取客户端IP地址"""
        # 检查代理头
        forwarded_for = request.headers.get("x-forwarded-for")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("x-real-ip")
        if real_ip:
            return real_ip
        
        # 回退到直接连接IP
        if hasattr(request, "client") and request.client:
            return request.client.host
        
        return None
    
    @staticmethod
    async def get_logs(
        db: AsyncSession,
        user_id: Optional[int] = None,
        action: Optional[AuditAction] = None,
        resource_type: Optional[AuditResourceType] = None,
        resource_id: Optional[int] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        success: Optional[bool] = None,
        keyword: Optional[str] = None,
        skip: int = 0,
        limit: int = 100
    ) -> List[AuditLog]:
        """查询审计日志"""
        
        query = select(AuditLog)
        conditions = []
        
        if user_id:
            conditions.append(AuditLog.user_id == user_id)
        if action:
            conditions.append(AuditLog.action == action)
        if resource_type:
            conditions.append(AuditLog.resource_type == resource_type)
        if resource_id:
            conditions.append(AuditLog.resource_id == resource_id)
        if start_date:
            conditions.append(AuditLog.timestamp >= start_date)
        if end_date:
            conditions.append(AuditLog.timestamp <= end_date)
        if success is not None:
            conditions.append(AuditLog.success == success)
        if keyword:
            conditions.append(
                or_(
                    AuditLog.description.ilike(f"%{keyword}%"),
                    AuditLog.reason.ilike(f"%{keyword}%"),
                    AuditLog.resource_name.ilike(f"%{keyword}%")
                )
            )
        
        if conditions:
            query = query.where(and_(*conditions))
        
        query = query.order_by(desc(AuditLog.timestamp)).offset(skip).limit(limit)
        
        result = await db.execute(query)
        return result.scalars().all()
    
    @staticmethod
    async def get_log_count(
        db: AsyncSession,
        user_id: Optional[int] = None,
        action: Optional[AuditAction] = None,
        resource_type: Optional[AuditResourceType] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        success: Optional[bool] = None
    ) -> int:
        """获取日志总数"""
        
        query = select(func.count(AuditLog.id))
        conditions = []
        
        if user_id:
            conditions.append(AuditLog.user_id == user_id)
        if action:
            conditions.append(AuditLog.action == action)
        if resource_type:
            conditions.append(AuditLog.resource_type == resource_type)
        if start_date:
            conditions.append(AuditLog.timestamp >= start_date)
        if end_date:
            conditions.append(AuditLog.timestamp <= end_date)
        if success is not None:
            conditions.append(AuditLog.success == success)
        
        if conditions:
            query = query.where(and_(*conditions))
        
        result = await db.execute(query)
        return result.scalar() or 0
