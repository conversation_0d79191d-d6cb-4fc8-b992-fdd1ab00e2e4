"""OSS 访问辅助服务"""

from __future__ import annotations

import asyncio
import json
from collections import Counter
from collections.abc import Iterable
from dataclasses import dataclass
from urllib.parse import urlsplit, urlunsplit

import oss2

from app.config import get_settings
from app.core.logging import logger
from app.db.redis import get_redis_master, get_redis_slave
from app.services.partial_upload import PartialUpload

settings = get_settings()


@dataclass
class SignedUrlResult:
    """封装签名结果"""

    url: str
    expires_in: int
    use_cdn: bool


class OSSAccessService:
    """为私有 OSS 提供签名访问能力"""

    def __init__(self, partial_upload: PartialUpload):
        self.partial_upload = partial_upload

    @staticmethod
    def _normalize_object_name(file_path: str) -> str:
        if not file_path:
            raise ValueError("file_path不能为空")

        if file_path.startswith(("http://", "https://")):
            parsed = urlsplit(file_path)
            object_name = parsed.path.lstrip("/")
        else:
            object_name = file_path.lstrip("/")

        if not object_name:
            raise ValueError("无法解析有效的对象路径")

        return object_name

    @staticmethod
    def _validate_object_name(object_name: str) -> None:
        normalized = object_name.strip()
        if not normalized:
            raise ValueError("无法解析有效的对象路径")

        if normalized.startswith("."):
            raise ValueError("非法的对象路径")

        parts = normalized.split("/")
        if any(part in {"..", ""} for part in parts):
            raise ValueError("非法的对象路径")

    async def generate_signed_url(
        self,
        file_path: str,
        expires: int | None = None,
        use_cdn: bool | None = None,
        cache: bool | None = None,
    ) -> SignedUrlResult:
        object_name = self._normalize_object_name(file_path)

        expire_seconds = expires or settings.OSS_SIGN_URL_EXPIRE
        effective_use_cdn = settings.OSS_SIGN_URL_USE_CDN if use_cdn is None else use_cdn
        cache_enabled = settings.OSS_SIGN_URL_CACHE_ENABLED if cache is None else cache

        cache_key = self._build_cache_key(object_name, expire_seconds, effective_use_cdn)
        if cache_enabled:
            cached = await self._get_cached_result(cache_key)
            if cached:
                return cached

        signed = self.partial_upload.bucket.sign_url("GET", object_name, expire_seconds)

        final_url, applied_cdn = self._apply_cdn_if_needed(signed, object_name, effective_use_cdn)

        logger.debug(
            "Generated signed url",
            extra={
                "object_name": object_name,
                "expire_seconds": expire_seconds,
                "use_cdn": applied_cdn,
            },
        )

        result = SignedUrlResult(url=final_url, expires_in=expire_seconds, use_cdn=applied_cdn)

        if cache_enabled:
            await self._set_cached_result(cache_key, result, expire_seconds)

        return result

    async def fetch_object(self, file_path: str) -> tuple[bytes, str | None]:
        """直接读取 OSS 对象内容并返回数据与内容类型"""

        object_name = self._normalize_object_name(file_path)
        self._validate_object_name(object_name)

        try:
            result = await asyncio.to_thread(self.partial_upload.bucket.get_object, object_name)
        except oss2.exceptions.NoSuchKey as exc:
            raise FileNotFoundError(object_name) from exc
        except oss2.exceptions.OssError as exc:  # noqa: PERF203 - 需要保持异常信息
            logger.error(
                "Fetch oss object failed",
                extra={"object_name": object_name, "error": str(exc)},
            )
            raise RuntimeError("OSS 对象读取失败") from exc

        try:
            body: bytes = await asyncio.to_thread(result.read)
        finally:
            try:
                result.close()
            except Exception:  # pragma: no cover - 关闭时失败无需中断流程
                logger.debug("Closing OSS object stream failed", exc_info=True)

        content_type = None
        if hasattr(result, "headers"):
            content_type = result.headers.get("Content-Type") or result.headers.get("content-type")
        if not content_type and hasattr(result, "resp"):
            content_type = result.resp.headers.get("Content-Type")

        logger.debug(
            "Fetched oss object",
            extra={
                "object_name": object_name,
                "size": len(body),
                "content_type": content_type,
            },
        )

        return body, content_type

    async def generate_signed_urls(
        self,
        file_paths: Iterable[str],
        expires: int | None = None,
        use_cdn: bool | None = None,
        cache: bool | None = None,
    ) -> dict[str, SignedUrlResult]:
        unique_paths = list(dict.fromkeys(file_paths))
        logger.debug(
            "Signing oss assets batch",
            extra={
                "path_count": len(unique_paths),
                "paths": unique_paths,
                "expires_override": expires,
                "use_cdn_override": use_cdn,
                "cache_override": cache,
            },
        )
        tasks = [
            self.generate_signed_url(path, expires=expires, use_cdn=use_cdn, cache=cache)
            for path in unique_paths
        ]
        results = await asyncio.gather(*tasks, return_exceptions=True)

        mapping: dict[str, SignedUrlResult] = {}
        failure_types: Counter[str] = Counter()
        failures: list[dict[str, str]] = []
        for original, result in zip(unique_paths, results, strict=False):
            if isinstance(result, Exception):
                error_extra = {
                    "path": original,
                    "error": str(result),
                    "error_type": type(result).__name__,
                }
                logger.warning(
                    "Failed to sign asset",
                    extra=error_extra,
                    exc_info=result,
                )
                failure_types[error_extra["error_type"]] += 1
                failures.append(error_extra)
                continue
            mapping[original] = result

        if failures:
            logger.debug(
                "Signed oss assets batch completed with failures",
                extra={
                    "requested": len(unique_paths),
                    "succeeded": len(mapping),
                    "failed": len(failures),
                    "failure_types": dict(failure_types),
                    "failed_assets": failures,
                },
            )
        else:
            logger.debug(
                "Signed oss assets batch completed successfully",
                extra={
                    "requested": len(unique_paths),
                    "succeeded": len(mapping),
                    "failed": 0,
                },
            )

        return mapping

    def _apply_cdn_if_needed(
        self, signed_url: str, object_name: str, use_cdn: bool
    ) -> tuple[str, bool]:
        if not use_cdn:
            return signed_url, False

        cdn_domain = settings.OSS_CDN_DOMAIN.strip()
        if not cdn_domain:
            return signed_url, False

        original = urlsplit(signed_url)

        if cdn_domain.startswith(("http://", "https://")):
            cdn_parts = urlsplit(cdn_domain.rstrip("/"))
            scheme = cdn_parts.scheme or original.scheme
            netloc = cdn_parts.netloc
            base_path = cdn_parts.path.rstrip("/")
        else:
            scheme = original.scheme or "https"
            netloc = cdn_domain
            base_path = ""

        path = f"{base_path}/{object_name}".replace("//", "/")
        rebuilt = urlunsplit((scheme, netloc, path, original.query, ""))

        return rebuilt, True

    @staticmethod
    def _build_cache_key(object_name: str, expires: int, use_cdn: bool) -> str:
        cdn_flag = "1" if use_cdn else "0"
        return f"oss:signed:{object_name}:{expires}:{cdn_flag}"

    @staticmethod
    def _cache_ttl(expires: int) -> int:
        padding = settings.OSS_SIGN_URL_CACHE_PADDING
        ttl = max(expires - padding, 1)
        return ttl

    async def _get_cached_result(self, cache_key: str) -> SignedUrlResult | None:
        try:
            redis_conn = await get_redis_slave()
            if redis_conn is None:
                return None
            cached = await redis_conn.get(cache_key)
            if not cached:
                return None
            payload = json.loads(cached)
            return SignedUrlResult(**payload)
        except Exception as exc:
            logger.debug(
                "Read signed url cache failed",
                extra={"error": str(exc), "cache_key": cache_key},
            )
            return None

    async def _set_cached_result(
        self, cache_key: str, result: SignedUrlResult, expires: int
    ) -> None:
        try:
            redis_conn = await get_redis_master()
            if redis_conn is None:
                return
            ttl = self._cache_ttl(expires)
            payload = json.dumps(
                {
                    "url": result.url,
                    "expires_in": result.expires_in,
                    "use_cdn": result.use_cdn,
                }
            )
            await redis_conn.set(cache_key, payload, ex=ttl)
        except Exception as exc:
            logger.debug(
                "Write signed url cache failed",
                extra={"error": str(exc), "cache_key": cache_key},
            )
