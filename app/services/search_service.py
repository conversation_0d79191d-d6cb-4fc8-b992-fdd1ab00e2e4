"""
核心搜索服务

提供统一的内容搜索功能，支持：
- 文章、视频、Scratch 项目搜索
- 全文搜索和模糊搜索
- 多维度过滤和排序
- 结果高亮
"""

import hashlib
from typing import Any

import jieba
from sqlalchemy import func, select
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.logging import logger
from app.core.pagination import CursorPaginationParams
from app.models.article import Article
from app.models.scratch import ScratchProduct
from app.models.video import Video


class SearchService:
    """核心搜索服务"""

    @staticmethod
    def tokenize_query(query: str, use_jieba: bool = True) -> str:
        """
        对查询进行分词处理

        Args:
            query: 原始查询字符串
            use_jieba: 是否使用 jieba 分词

        Returns:
            分词后的字符串，空格分隔
        """
        if not use_jieba:
            return query

        # 使用 jieba 进行搜索引擎模式分词
        words = jieba.cut_for_search(query)
        return " ".join(words)

    @staticmethod
    def generate_query_hash(query: str, filters: dict[str, Any]) -> str:
        """
        生成查询的哈希值，用于缓存键

        Args:
            query: 搜索查询
            filters: 过滤条件

        Returns:
            MD5 哈希字符串
        """
        # 将查询和过滤条件组合成字符串
        filter_str = str(sorted(filters.items()))
        combined = f"{query}:{filter_str}"
        return hashlib.md5(combined.encode()).hexdigest()

    @staticmethod
    async def search_articles(
        db: AsyncSession,
        query: str,
        *,
        category_id: int | None = None,
        tag_ids: list[int] | None = None,
        author_id: int | None = None,
        sort_by: str = "relevance",
        pagination: CursorPaginationParams,
    ) -> list[Article]:
        """
        搜索文章

        Args:
            db: 数据库会话
            query: 搜索关键词
            category_id: 类别ID过滤
            tag_ids: 标签ID列表过滤
            author_id: 作者ID过滤
            sort_by: 排序方式 (relevance/hot/recent)
            pagination: 分页参数

        Returns:
            文章列表
        """
        # 1. 分词处理
        tokenized_query = SearchService.tokenize_query(query)
        ts_query = func.plainto_tsquery("simple", tokenized_query)

        # 2. 构建基础查询
        stmt = select(Article).where(
            Article.is_published == True, Article.is_approved == True, Article.is_deleted == False
        )

        # 3. 添加全文搜索条件
        stmt = stmt.where(Article.search_vector.op("@@")(ts_query))

        # 4. 应用过滤条件
        if category_id:
            stmt = stmt.where(Article.category_id == category_id)

        if author_id:
            stmt = stmt.where(Article.author_id == author_id)

        if tag_ids:
            # 标签过滤需要 join article_tags 表
            from app.models.tag import article_tags

            stmt = stmt.join(article_tags, Article.id == article_tags.c.article_id).where(
                article_tags.c.tag_id.in_(tag_ids)
            )

        # 5. 排序
        if sort_by == "relevance":
            # 相关度排序：结合文本匹配度和热度
            stmt = stmt.order_by(
                (
                    func.ts_rank_cd(Article.search_vector, ts_query) * 0.7
                    + Article.visit_count / 1000.0 * 0.3
                ).desc()
            )
        elif sort_by == "hot":
            # 热度排序
            stmt = stmt.order_by(Article.visit_count.desc())
        elif sort_by == "recent":
            # 时间排序
            stmt = stmt.order_by(Article.created_at.desc())
        else:
            stmt = stmt.order_by(Article.id.desc())

        # 6. 分页
        stmt = stmt.limit(pagination.size + 1)
        if pagination.cursor:
            # 实现游标分页逻辑
            # 这里简化处理，实际应该解析游标
            pass

        # 7. 执行查询
        result = await db.execute(stmt)
        articles = result.scalars().all()

        logger.info(f"搜索文章: query='{query}', 结果数={len(articles)}")
        return articles

    @staticmethod
    async def search_videos(
        db: AsyncSession,
        query: str,
        *,
        category_id: int | None = None,
        tag_ids: list[int] | None = None,
        author_id: int | None = None,
        sort_by: str = "relevance",
        pagination: CursorPaginationParams,
    ) -> list[Video]:
        """搜索视频（实现逻辑类似 search_articles）"""
        tokenized_query = SearchService.tokenize_query(query)
        ts_query = func.plainto_tsquery("simple", tokenized_query)

        stmt = select(Video).where(
            Video.is_published == True,
            Video.is_approved == True,
            Video.is_deleted == False,
            Video.search_vector.op("@@")(ts_query),
        )

        if category_id:
            stmt = stmt.where(Video.category_id == category_id)
        if author_id:
            stmt = stmt.where(Video.author_id == author_id)

        # 排序
        if sort_by == "relevance":
            stmt = stmt.order_by(
                (
                    func.ts_rank_cd(Video.search_vector, ts_query) * 0.7
                    + Video.visit_count / 1000.0 * 0.3
                ).desc()
            )
        elif sort_by == "hot":
            stmt = stmt.order_by(Video.visit_count.desc())
        elif sort_by == "recent":
            stmt = stmt.order_by(Video.created_at.desc())

        stmt = stmt.limit(pagination.size + 1)
        result = await db.execute(stmt)
        return result.scalars().all()

    @staticmethod
    async def search_scratch(
        db: AsyncSession,
        query: str,
        *,
        difficulty: str | None = None,
        sort_by: str = "relevance",
        pagination: CursorPaginationParams,
    ) -> list[ScratchProduct]:
        """搜索 Scratch 项目"""
        tokenized_query = SearchService.tokenize_query(query)
        ts_query = func.plainto_tsquery("simple", tokenized_query)

        stmt = select(ScratchProduct).where(
            ScratchProduct.is_published == True, ScratchProduct.search_vector.op("@@")(ts_query)
        )

        if difficulty:
            stmt = stmt.where(ScratchProduct.difficulty == difficulty)

        # 排序
        if sort_by == "relevance":
            stmt = stmt.order_by(
                (
                    func.ts_rank_cd(ScratchProduct.search_vector, ts_query) * 0.7
                    + (ScratchProduct.visit_count + ScratchProduct.like_count) / 1000.0 * 0.3
                ).desc()
            )
        elif sort_by == "hot":
            stmt = stmt.order_by((ScratchProduct.visit_count + ScratchProduct.like_count).desc())
        elif sort_by == "recent":
            stmt = stmt.order_by(ScratchProduct.created_at.desc())

        stmt = stmt.limit(pagination.size + 1)
        result = await db.execute(stmt)
        return result.scalars().all()

    @staticmethod
    def highlight_text(text: str, query: str, max_length: int = 200) -> str:
        """
        高亮搜索关键词

        Args:
            text: 原始文本
            query: 搜索关键词
            max_length: 高亮片段最大长度

        Returns:
            带 <em> 标签高亮的文本片段
        """
        if not text or not query:
            return text[:max_length] if text else ""

        # 简化实现：查找第一个关键词出现位置
        text_lower = text.lower()
        query_lower = query.lower()

        pos = text_lower.find(query_lower)
        if pos == -1:
            # 未找到关键词，返回开头片段
            return text[:max_length]

        # 计算片段范围
        start = max(0, pos - 50)
        end = min(len(text), pos + len(query) + 150)
        snippet = text[start:end]

        # 简单替换关键词为高亮版本（实际应该用更智能的方法）
        highlighted = snippet.replace(
            text[pos : pos + len(query)], f"<em>{text[pos : pos + len(query)]}</em>"
        )

        # 添加省略号
        if start > 0:
            highlighted = "..." + highlighted
        if end < len(text):
            highlighted = highlighted + "..."

        return highlighted
