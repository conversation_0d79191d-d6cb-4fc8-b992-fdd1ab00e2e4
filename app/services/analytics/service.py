from __future__ import annotations

from datetime import datetime
from typing import Any

from sqlalchemy.ext.asyncio import AsyncSession

from app.services.analytics.pipeline import AnalyticsPipeline


class AnalyticsService:
    def __init__(self, pipeline: AnalyticsPipeline | None = None) -> None:
        self.pipeline = pipeline or AnalyticsPipeline()

    async def get_dashboard(
        self,
        db: AsyncSession,
        *,
        time_range: str = "last_7_days",
        filters: dict[str, Any] | None = None,
    ) -> dict[str, Any]:
        data = await self.pipeline.get_snapshot_data(
            db,
            snapshot_type="dashboard",
            time_range=time_range,
            filters=filters,
        )
        return data

    async def get_users(self, db: AsyncSession, *, time_range: str, filters: dict[str, Any] | None = None) -> dict[str, Any]:
        return await self.pipeline.get_snapshot_data(
            db,
            snapshot_type="users",
            time_range=time_range,
            filters=filters,
        )

    async def get_content(
        self, db: AsyncSession, *, time_range: str, filters: dict[str, Any] | None = None
    ) -> dict[str, Any]:
        return await self.pipeline.get_snapshot_data(
            db,
            snapshot_type="content",
            time_range=time_range,
            filters=filters,
        )

    async def get_system(
        self, db: AsyncSession, *, time_range: str, filters: dict[str, Any] | None = None
    ) -> dict[str, Any]:
        return await self.pipeline.get_snapshot_data(
            db,
            snapshot_type="system",
            time_range=time_range,
            filters=filters,
        )

    async def trigger_refresh(
        self,
        db: AsyncSession,
        *,
        snapshot_type: str,
        time_range: str,
        filters: dict[str, Any] | None = None,
    ) -> None:
        await self.pipeline.refresh_snapshot(
            db,
            snapshot_type=snapshot_type,
            time_range=time_range,
            filters=filters,
        )

    async def trigger_export(
        self,
        db: AsyncSession,
        *,
        dataset: str,
        time_range: str,
        filters: dict[str, Any] | None = None,
    ) -> dict[str, Any]:
        result = await self.pipeline.export_snapshot(
            db,
            snapshot_type=dataset,
            time_range=time_range,
            filters=filters,
        )
        return {
            "export_id": result.filename,
            "status": "completed",
            "path": result.path,
            "rows": result.rows,
        }

    def parse_params(
        self,
        date_range: str | None,
        from_date: datetime | None,
        to_date: datetime | None,
        extra_filters: dict[str, Any] | None = None,
    ) -> tuple[str, dict[str, Any]]:
        filters = extra_filters.copy() if extra_filters else {}
        if from_date:
            filters["from"] = from_date.isoformat()
        if to_date:
            filters["to"] = to_date.isoformat()
        time_range = date_range or "last_7_days"
        return time_range, filters
