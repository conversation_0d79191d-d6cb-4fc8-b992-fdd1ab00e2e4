from __future__ import annotations

import csv
import io
from datetime import datetime, timezone
from pathlib import Path
from typing import Any, Callable

from sqlalchemy import delete, func, select
from sqlalchemy.ext.asyncio import AsyncSession

from app.models.admin_analytics import AdminAnalyticsSnapshot, AdminAnalyticsSnapshotData
from app.models.article import Article
from app.models.user import User
from app.models.video import Video
from app.services.storage.export_store import ExportStorage, ExportResult

SnapshotData = dict[str, Any]
AnalyticsDataProvider = Callable[
    [AsyncSession, str, str, dict[str, Any]], SnapshotData
]


class AnalyticsPipeline:
    """负责生成分析快照与导出文件"""

    def __init__(
        self,
        storage: ExportStorage | None = None,
        data_provider: AnalyticsDataProvider | None = None,
    ) -> None:
        self.storage = storage or ExportStorage()
        self.data_provider = data_provider or self._default_collect

    async def refresh_snapshot(
        self,
        db: AsyncSession,
        *,
        snapshot_type: str,
        time_range: str,
        filters: dict[str, Any] | None = None,
    ) -> AdminAnalyticsSnapshot:
        filters = filters or {}
        started_at = datetime.now(timezone.utc)

        payload = await self.data_provider(db, snapshot_type, time_range, filters)

        snapshot = await self._get_or_create_snapshot(db, snapshot_type, time_range, filters)

        # 清理旧数据
        await db.execute(
            delete(AdminAnalyticsSnapshotData).where(
                AdminAnalyticsSnapshotData.snapshot_id == snapshot.id
            )
        )

        total_records = 0
        for section, body in payload.items():
            if isinstance(body, list):
                total_records += len(body)
            entry = AdminAnalyticsSnapshotData(
                snapshot_id=snapshot.id,
                data_type=section,
                payload=body,
            )
            db.add(entry)

        snapshot.status = "completed"
        snapshot.generated_at = datetime.now(timezone.utc)
        snapshot.generation_duration = int(
            (snapshot.generated_at - started_at).total_seconds() * 1000
        )
        snapshot.record_count = total_records

        await db.commit()
        await db.refresh(snapshot)
        return snapshot

    async def get_snapshot_data(
        self,
        db: AsyncSession,
        *,
        snapshot_type: str,
        time_range: str,
        filters: dict[str, Any] | None = None,
        ensure_fresh: bool = True,
    ) -> dict[str, Any]:
        filters = filters or {}
        snapshot = await self._get_snapshot(db, snapshot_type, time_range, filters)
        if not snapshot or ensure_fresh:
            snapshot = await self.refresh_snapshot(
                db, snapshot_type=snapshot_type, time_range=time_range, filters=filters
            )
        entries_stmt = select(AdminAnalyticsSnapshotData).where(
            AdminAnalyticsSnapshotData.snapshot_id == snapshot.id
        )
        entries = (await db.execute(entries_stmt)).scalars().all()
        data = {entry.data_type: entry.payload for entry in entries}
        return data

    async def export_snapshot(
        self,
        db: AsyncSession,
        *,
        snapshot_type: str,
        time_range: str,
        filters: dict[str, Any] | None = None,
    ) -> ExportResult:
        filters = filters or {}
        data = await self.data_provider(db, snapshot_type, time_range, filters)
        rows = data.get("rows", [])
        if not isinstance(rows, list):
            rows = []
        return await self.storage.save_csv(rows, prefix=f"{snapshot_type}_{time_range}")

    async def _get_snapshot(
        self,
        db: AsyncSession,
        snapshot_type: str,
        time_range: str,
        filters: dict[str, Any],
    ) -> AdminAnalyticsSnapshot | None:
        stmt = (
            select(AdminAnalyticsSnapshot)
            .where(
                AdminAnalyticsSnapshot.snapshot_type == snapshot_type,
                AdminAnalyticsSnapshot.time_range == time_range,
            )
            .limit(1)
        )
        result = await db.execute(stmt)
        return result.scalar_one_or_none()

    async def _get_or_create_snapshot(
        self,
        db: AsyncSession,
        snapshot_type: str,
        time_range: str,
        filters: dict[str, Any],
    ) -> AdminAnalyticsSnapshot:
        snapshot = await self._get_snapshot(db, snapshot_type, time_range, filters)
        if snapshot:
            snapshot.filters = filters
            return snapshot

        snapshot = AdminAnalyticsSnapshot(
            snapshot_type=snapshot_type,
            time_range=time_range,
            filters=filters,
            status="pending",
        )
        db.add(snapshot)
        await db.commit()
        await db.refresh(snapshot)
        return snapshot

    async def _default_collect(
        self,
        db: AsyncSession,
        snapshot_type: str,
        time_range: str,
        filters: dict[str, Any],
    ) -> dict[str, Any]:
        summary = await self._default_summary(db)
        rows: list[dict[str, Any]] = []

        if snapshot_type == "users":
            rows = [
                {"metric": "total_users", "value": summary["total_users"]},
                {"metric": "active_users", "value": summary["active_users"]},
            ]
        elif snapshot_type == "content":
            rows = [
                {"metric": "articles", "value": summary["article_count"]},
                {"metric": "videos", "value": summary["video_count"]},
            ]
        elif snapshot_type == "system":
            rows = [
                {"metric": "user_growth", "value": summary["active_users"]},
            ]
        else:  # dashboard or default
            rows = [
                {"metric": "total_users", "value": summary["total_users"]},
                {"metric": "articles", "value": summary["article_count"]},
                {"metric": "videos", "value": summary["video_count"]},
            ]

        return {
            "summary": summary,
            "rows": rows,
        }

    async def _default_summary(self, db: AsyncSession) -> dict[str, Any]:
        total_users = await db.scalar(select(func.count()).select_from(User))
        active_users = await db.scalar(
            select(func.count()).select_from(User).where(User.is_active.is_(True))
        )
        article_count = await db.scalar(select(func.count()).select_from(Article))
        video_count = await db.scalar(select(func.count()).select_from(Video))

        return {
            "total_users": int(total_users or 0),
            "active_users": int(active_users or 0),
            "article_count": int(article_count or 0),
            "video_count": int(video_count or 0),
            "time_range": datetime.now(timezone.utc).isoformat(),
        }
