"""服务工厂模块

用于创建和提供服务实例，支持依赖注入。
"""

from functools import lru_cache
from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from app.services.auth_orchestrator_service import AuthOrchestratorService

from fastapi import Depends

from app.config import Settings, get_settings
from app.crud.base import CRUDBase
from app.crud.user import user as user_crud
from app.crud.user_device import CRUDUserDevice
from app.crud.user_device import user_device as user_device_crud
from app.crud.video import video as video_crud
from app.crud.video_folder import video_folder as folder_crud
from app.services.behavior_tracking_service import BehaviorTrackingService
from app.services.content_service import ContentService
from app.services.device_fingerprint_service import DeviceFingerprintService
from app.services.device_info_parser import DeviceInfoParser
from app.services.folder_stats_service import FolderStatsService
from app.services.history_cache_service import HistoryCacheService
from app.services.interfaces.token_service_interface import ITokenService
from app.services.oss_access_service import OSSAccessService
from app.services.partial_upload import PartialUpload
from app.services.password_service import PasswordHashService
from app.services.post_aggregation_service import PostAggregationService
from app.services.post_video_upload_service import PostVideoUploadService
from app.services.post_cache_service import PostCacheService
from app.services.recommendation_cache_service import RecommendationCacheService
from app.services.recommendation_evaluation_service import RecommendationEvaluationService
from app.services.scratch_aggregation_service import ScratchAggregationService
from app.services.sms_service import SmsService
from app.services.tag_hot_service import TagHotService
from app.services.task_state_service import TaskStateService
from app.services.task_status_service import TaskStatusService
from app.services.token_service import TokenService
from app.services.user_aggregation_service import UserAggregationService
from app.services.user_cache_service import UserCacheService
from app.services.user_recommendation_service import UserRecommendationService
from app.services.user_stats_service import UserStatsCacheService
from app.services.video_aggregation_service import VideoAggregationService
from app.services.video_folder_service import VideoFolderService
from app.services.wechat_integration_service import WeChatIntegrationService
from app.utils.bloom_filters import post_bloom_filter


@lru_cache
def get_password_service() -> PasswordHashService:
    """
    获取 PasswordHashService 的实例。
    """
    return PasswordHashService()


@lru_cache
def get_settings_dep() -> Settings:
    """
    获取 Settings 的依赖项提供者。
    """
    return get_settings()


@lru_cache
def get_user_device_crud() -> CRUDUserDevice:
    """
    获取 CRUDUserDevice 的实例。
    """
    return user_device_crud


@lru_cache
def get_user_crud() -> CRUDBase:
    """获取 CRUDUser 的实例。"""
    return user_crud


@lru_cache
def get_video_crud() -> CRUDBase:
    """获取 CRUDVideo 的实例。"""
    return video_crud


@lru_cache
def get_folder_crud() -> CRUDBase:
    """获取 CRUDVideoFolder 的实例。"""
    return folder_crud


@lru_cache
def get_user_cache_service() -> UserCacheService:
    """
    获取 UserCacheService 的实例。
    """
    return UserCacheService()


@lru_cache
def get_user_stats_cache_service() -> UserStatsCacheService:
    """
    获取 UserStatsCacheService 的实例。
    """
    return UserStatsCacheService()


@lru_cache
def get_tag_hot_service() -> TagHotService:
    """获取 TagHotService 的实例。"""
    return TagHotService()


def get_token_service(settings: Settings = Depends(get_settings_dep)) -> ITokenService:
    """
    获取 TokenService 的实例。
    """
    return TokenService(auth_config=settings)


from app.services.interfaces.device_service_interface import IDeviceTrustService


def get_device_trust_service(
    token_service: ITokenService = Depends(get_token_service),
    user_device_crud: CRUDUserDevice = Depends(get_user_device_crud),
    settings: Settings = Depends(get_settings_dep),
) -> IDeviceTrustService:
    """
    获取 DeviceTrustService 的实例。
    """
    from app.services.device_service import DeviceTrustService

    return DeviceTrustService(
        token_service=token_service,
        user_device_crud=user_device_crud,
        auth_config=settings,
    )


from app.services.interfaces.user_management_service_interface import IUserManagementService
from app.services.user_management_service import UserManagementService


def get_user_management_service(
    password_service: PasswordHashService = Depends(get_password_service),
) -> IUserManagementService:
    """
    获取 UserManagementService 的实例。
    """
    return UserManagementService(password_service=password_service)


def get_user_aggregation_service(
    user_cache_service: UserCacheService = Depends(get_user_cache_service),
    user_stats_service: UserStatsCacheService = Depends(get_user_stats_cache_service),
) -> UserAggregationService:
    """
    获取 UserAggregationService 的实例。
    """
    return UserAggregationService(
        user_cache_service=user_cache_service,
        user_stats_service=user_stats_service,
    )


@lru_cache
def get_user_recommendation_service() -> UserRecommendationService:
    """获取 UserRecommendationService 的实例。"""
    return UserRecommendationService()


from app.services.interfaces.sms_auth_service_interface import ISMSAuthService
from app.services.interfaces.sms_service_interface import ISmsService
from app.services.sms_auth_service import SMSAuthService


@lru_cache
def get_sms_service() -> SmsService:
    """
    获取 SmsService 的实例。
    """
    return SmsService()


def get_sms_auth_service(
    token_service: ITokenService = Depends(get_token_service),
    device_trust_service: IDeviceTrustService = Depends(get_device_trust_service),
    user_management_service: IUserManagementService = Depends(get_user_management_service),
    user_aggregation_service: UserAggregationService = Depends(get_user_aggregation_service),
    sms_service: "ISmsService" = Depends(get_sms_service),
) -> ISMSAuthService:
    """
    获取 SMSAuthService 的实例。
    """
    return SMSAuthService(
        token_service=token_service,
        device_trust_service=device_trust_service,
        user_management_service=user_management_service,
        user_aggregation_service=user_aggregation_service,
        sms_service=sms_service,
    )


from app.services.interfaces.wechat_auth_service_interface import IWeChatAuthService
from app.services.wechat_auth_service import WeChatAuthService


@lru_cache
def get_wechat_integration_service() -> "WeChatIntegrationService":
    """
    获取 WeChatIntegrationService 的实例。
    """
    return WeChatIntegrationService()


def get_wechat_auth_service(
    token_service: ITokenService = Depends(get_token_service),
    user_management_service: IUserManagementService = Depends(get_user_management_service),
    device_trust_service: IDeviceTrustService = Depends(get_device_trust_service),
    user_aggregation_service: UserAggregationService = Depends(get_user_aggregation_service),
    settings: Settings = Depends(get_settings_dep),
    wechat_integration_service: "WeChatIntegrationService" = Depends(
        get_wechat_integration_service
    ),
    sms_service: ISmsService = Depends(get_sms_service),
) -> IWeChatAuthService:
    """
    获取 WeChatAuthService 的实例。
    """
    return WeChatAuthService(
        token_service=token_service,
        user_management_service=user_management_service,
        device_trust_service=device_trust_service,
        user_aggregation_service=user_aggregation_service,
        auth_config=settings,
        wechat_integration_service=wechat_integration_service,
        sms_service=sms_service,
    )


def get_auth_orchestrator_service(
    token_service: ITokenService = Depends(get_token_service),
    device_trust_service: IDeviceTrustService = Depends(get_device_trust_service),
    user_management_service: IUserManagementService = Depends(get_user_management_service),
    sms_auth_service: ISMSAuthService = Depends(get_sms_auth_service),
    wechat_auth_service: IWeChatAuthService = Depends(get_wechat_auth_service),
) -> "AuthOrchestratorService":
    """
    获取 AuthOrchestratorService 的实例。
    """
    from app.services.auth_orchestrator_service import AuthOrchestratorService

    return AuthOrchestratorService(
        token_service=token_service,
        device_trust_service=device_trust_service,
        user_management_service=user_management_service,
        sms_auth_service=sms_auth_service,
        wechat_auth_service=wechat_auth_service,
    )


from app.services.article_aggregation_service import ArticleAggregationService
from app.services.article_cache_service import ArticleCacheService
from app.services.content_stats_service import ContentStatsService
from app.services.recommendation_service import RecommendationService
from app.services.user_cache_service import UserCacheService
from app.services.user_stats_service import UserStatsCacheService
from app.services.video_cache_service import VideoCacheService


@lru_cache
def get_article_cache_service() -> ArticleCacheService:
    """
    获取 ArticleCacheService 的实例。
    """
    return ArticleCacheService()


@lru_cache
def get_video_cache_service() -> VideoCacheService:
    """
    获取 VideoCacheService 的实例。
    """
    return VideoCacheService()


@lru_cache
def get_content_stats_service() -> ContentStatsService:
    """
    获取 ContentStatsService 的实例。
    """
    return ContentStatsService()


def get_article_aggregation_service(
    article_cache_service: ArticleCacheService = Depends(get_article_cache_service),
    content_stats_service: ContentStatsService = Depends(get_content_stats_service),
    user_cache_service: UserCacheService = Depends(get_user_cache_service),
) -> ArticleAggregationService:
    """
    获取 ArticleAggregationService 的实例。
    """
    return ArticleAggregationService(
        article_cache_service=article_cache_service,
        content_stats_service=content_stats_service,
        user_cache_service=user_cache_service,
    )


@lru_cache
def get_recommendation_service() -> RecommendationService:
    """
    获取 RecommendationService 的实例。
    """
    return RecommendationService()


def get_behavior_tracking_service(
    video_cache_service: VideoCacheService = Depends(get_video_cache_service),
) -> BehaviorTrackingService:
    """
    获取 BehaviorTrackingService 的实例。
    """
    return BehaviorTrackingService(video_cache_service=video_cache_service)


@lru_cache
def get_article_content_service() -> ContentService:
    """
    获取用于文章的 ContentService 实例。
    """
    return ContentService("article")


@lru_cache
def get_video_content_service() -> ContentService:
    """
    获取用于视频的 ContentService 实例。
    """
    return ContentService("video")


@lru_cache
def get_device_fingerprint_service() -> DeviceFingerprintService:
    """
    获取 DeviceFingerprintService 的实例。
    """
    return DeviceFingerprintService()


@lru_cache
def get_device_info_parser() -> DeviceInfoParser:
    """
    获取 DeviceInfoParser 的实例。
    """
    return DeviceInfoParser()


@lru_cache
def get_folder_stats_service() -> FolderStatsService:
    """
    获取 FolderStatsService 的实例。
    """
    return FolderStatsService()


@lru_cache
def get_history_cache_service() -> HistoryCacheService:
    """
    获取 HistoryCacheService 的实例。
    """
    return HistoryCacheService()


@lru_cache
def get_partial_upload_service() -> PartialUpload:
    """
    获取 PartialUpload 的实例。
    """
    return PartialUpload()


@lru_cache
def get_recommendation_cache_service() -> RecommendationCacheService:
    """
    获取 RecommendationCacheService 的实例。
    """
    return RecommendationCacheService()


def get_oss_access_service(
    partial_upload: PartialUpload = Depends(get_partial_upload_service),
) -> OSSAccessService:
    """获取 OSSAccessService 的实例"""

    return OSSAccessService(partial_upload=partial_upload)


def get_recommendation_evaluation_service(
    video_cache_service: VideoCacheService = Depends(get_video_cache_service),
) -> RecommendationEvaluationService:
    """
    获取 RecommendationEvaluationService 的实例。
    """
    return RecommendationEvaluationService(video_cache_service=video_cache_service)


@lru_cache
def get_task_state_service() -> TaskStateService:
    """
    获取 TaskStateService 的实例。
    """
    return TaskStateService()


@lru_cache
def get_task_status_service() -> TaskStatusService:
    """
    获取 TaskStatusService 的实例。
    """
    return TaskStatusService()


def get_video_aggregation_service(
    video_cache_service: VideoCacheService = Depends(get_video_cache_service),
    content_stats_service: ContentStatsService = Depends(get_content_stats_service),
    user_cache_service: UserCacheService = Depends(get_user_cache_service),
) -> VideoAggregationService:
    """
    获取 VideoAggregationService 的实例。
    """
    return VideoAggregationService(
        video_cache_service=video_cache_service,
        content_stats_service=content_stats_service,
        user_cache_service=user_cache_service,
    )


def get_video_folder_service(
    user_crud: CRUDBase = Depends(get_user_crud),
    video_crud: CRUDBase = Depends(get_video_crud),
    folder_crud: CRUDBase = Depends(get_folder_crud),
    content_service: ContentService = Depends(get_video_content_service),
    stats_service: FolderStatsService = Depends(get_folder_stats_service),
    cache_service: VideoCacheService = Depends(get_video_cache_service),
    tag_hot_service: TagHotService = Depends(get_tag_hot_service),
) -> VideoFolderService:
    """
    获取增强的 VideoFolderService 实例，用于编排视频和文件夹业务。
    """
    return VideoFolderService(
        user_crud=user_crud,
        video_crud=video_crud,
        folder_crud=folder_crud,
        content_service=content_service,
        stats_service=stats_service,
        cache_service=cache_service,
        tag_hot_service=tag_hot_service,
    )


@lru_cache
def get_notification_service() -> "NotificationService":
    """
    获取 NotificationService 的实例。
    """
    from app.services.notification_service import NotificationService

    return NotificationService()


@lru_cache
def get_post_notification_service() -> "PostNotificationService":
    """
    获取 PostNotificationService 的实例。
    """
    from app.services.post_notification_service import PostNotificationService

    return PostNotificationService()


@lru_cache
def get_scratch_stats_service() -> "ScratchStatsService":
    """
    获取 ScratchStatsService 的实例。
    """
    from app.services.scratch_stats_service import ScratchStatsService

    return ScratchStatsService()


def get_scratch_aggregation_service(
    scratch_stats_service: "ScratchStatsService" = Depends(get_scratch_stats_service),
) -> ScratchAggregationService:
    """
    获取 ScratchAggregationService 的实例。
    """

    return ScratchAggregationService(scratch_stats_service=scratch_stats_service)


@lru_cache
def get_post_cache_service() -> PostCacheService:
    """
    获取 PostCacheService 的实例。
    """
    return PostCacheService(bloom_filter=post_bloom_filter)


@lru_cache
def get_post_video_upload_service() -> PostVideoUploadService:
    """获取 PostVideoUploadService 的实例。"""
    return PostVideoUploadService()


@lru_cache
def get_topic_stats_service() -> "TopicStatsService":
    """
    获取 TopicStatsService 的实例。
    """
    from app.config.post_config import PostConfig
    from app.services.topic_stats_service import TopicStatsService

    config = PostConfig()
    return TopicStatsService(config=config)


def get_post_aggregation_service(
    post_cache_service: PostCacheService = Depends(get_post_cache_service),
    content_stats_service: "ContentStatsService" = Depends(get_content_stats_service),
    user_cache_service: UserCacheService = Depends(get_user_cache_service),
    topic_stats_service: "TopicStatsService" = Depends(get_topic_stats_service),
    post_notification_service: "PostNotificationService" = Depends(get_post_notification_service),
) -> PostAggregationService:
    """
    获取 PostAggregationService 的实例。
    """
    return PostAggregationService(
        post_cache_service=post_cache_service,
        content_stats_service=content_stats_service,
        user_cache_service=user_cache_service,
        topic_stats_service=topic_stats_service,
        post_notification_service=post_notification_service,
    )
