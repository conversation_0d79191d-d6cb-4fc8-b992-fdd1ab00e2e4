"""用户缓存服务模块"""

from sqlalchemy.ext.asyncio import AsyncSession

from app import crud, schemas
from app.config import settings
from app.services.base_cache_service import BaseCacheService
from app.utils.bloom_filters import user_bloom_filter


class UserCacheService(BaseCacheService["models.User", "schemas.UserBase", "crud.CRUDUser"]):
    """
    用户缓存服务
    - 继承自 BaseCacheService，提供用户相关的特定配置
    """

    def __init__(self):
        """初始化服务，配置用户特定的缓存参数"""
        super().__init__(
            crud_model=crud.user,
            schema_model=schemas.UserBase,
            bloom_filter=user_bloom_filter,
            cache_key_prefix="user:base",
            entity_name="用户",
            cache_enabled_setting=settings.USER_CACHE_ENABLED,
            cache_expire_seconds=settings.USER_CACHE_EXPIRE_SECONDS,
        )

    async def get_users_by_ids(
        self,
        db: AsyncSession,
        *,
        user_ids: list[int],
    ) -> list[schemas.UserBase]:
        """
        根据ID批量获取用户缓存

        Args:
            db: 数据库会话
            user_ids: 用户ID列表

        Returns:
            用户基础信息列表（保持输入顺序，过滤不存在的ID）
        """
        if not user_ids:
            return []

        # 去重但保持原始顺序
        unique_ids = list(dict.fromkeys(user_ids))
        results_map = await self.get_entities_batch(db, unique_ids)

        return [results_map[user_id] for user_id in unique_ids if results_map.get(user_id)]


# 全局实例将在 service_factory 中创建和管理
