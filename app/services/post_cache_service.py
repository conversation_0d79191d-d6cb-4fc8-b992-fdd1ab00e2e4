"""沸点缓存服务模块"""

import asyncio
import json

from sqlalchemy.ext.asyncio import AsyncSession

from app import models, schemas
from app.config import settings
from app.core.logging import logger
from app.crud.post import CRUDPost
from app.crud.post import post as crud_post
from app.db import redis
from app.services.base_cache_service import BaseCacheService
from app.utils.bloom_filters import BloomFilterService


class PostCacheService(BaseCacheService[models.Post, schemas.PostBase, CRUDPost]):
    """
    沸点缓存服务
    - 缓存沸点基础数据
    - 遵循 Cache-Aside 模式
    - 实现版本控制以确保数据一致性
    """

    def __init__(self, bloom_filter: BloomFilterService):
        super().__init__(
            crud_model=crud_post,
            schema_model=schemas.PostBase,
            bloom_filter=bloom_filter,
            cache_key_prefix="post",
            entity_name="沸点",
            cache_enabled_setting=settings.POST_CACHE_ENABLED,
            cache_expire_seconds=settings.POST_CACHE_EXPIRE_SECONDS,
        )
        self.media_cache_prefix = f"{self.cache_key_prefix}:media"

    async def get_posts_by_ids(
        self,
        db: AsyncSession,
        *,
        post_ids: list[int],
    ) -> list[schemas.PostBase]:
        """
        批量获取沸点基础信息

        Args:
            db: 数据库会话
            post_ids: 沸点ID列表

        Returns:
            沸点基础信息列表
        """
        if not post_ids:
            return []

        # 去重并保持顺序
        unique_ids = list(dict.fromkeys(post_ids))

        # 统一通过批量接口获取，避免在同一会话上并发查询
        posts_map = await self.get_entities_batch(db, unique_ids)

        return [posts_map[post_id] for post_id in unique_ids if posts_map.get(post_id)]

    def _get_media_cache_key(self, post_id: int) -> str:
        return f"{self.media_cache_prefix}:{post_id}"

    async def get_media_map(
        self,
        db: AsyncSession,
        *,
        post_ids: list[int],
    ) -> dict[int, list[schemas.PostMediaOut]]:
        """
        批量获取帖子媒体数据，优先读取缓存，未命中时回源并写入缓存。
        """
        if not post_ids:
            return {}

        unique_ids = list(dict.fromkeys(post_ids))
        media_map: dict[int, list[schemas.PostMediaOut]] = {}
        ids_to_fetch: list[int] = []

        if self.cache_enabled:
            try:
                redis_slave = await redis.get_redis_slave()
                pipe = redis_slave.pipeline()
                for post_id in unique_ids:
                    pipe.get(self._get_media_cache_key(post_id))
                cache_results = await pipe.execute()
                for post_id, cached in zip(unique_ids, cache_results, strict=False):
                    if cached is None:
                        ids_to_fetch.append(post_id)
                        continue
                    try:
                        payload = json.loads(cached)
                        media_map[post_id] = [
                            schemas.PostMediaOut.model_validate(item) for item in payload
                        ]
                    except Exception as exc:
                        logger.warning(
                            "解析沸点媒体缓存失败，准备回源查询",
                            extra={"post_id": post_id, "error": str(exc)},
                        )
                        ids_to_fetch.append(post_id)
            except Exception as exc:
                logger.warning(f"批量读取沸点媒体缓存失败，降级为数据库查询: {exc}")
                ids_to_fetch = unique_ids.copy()
        else:
            ids_to_fetch = unique_ids.copy()

        if ids_to_fetch:
            media_records = await self.crud.get_media_by_post_ids(db, post_ids=ids_to_fetch)
            grouped: dict[int, list[models.PostMedia]] = {}
            for record in media_records:
                grouped.setdefault(record.post_id, []).append(record)

            for post_id in ids_to_fetch:
                records = grouped.get(post_id, [])
                if records:
                    media_map[post_id] = [
                        schemas.PostMediaOut.model_validate(record) for record in records
                    ]
                else:
                    media_map[post_id] = []

            if self.cache_enabled:
                try:
                    redis_master = await redis.get_redis_master()
                    pipe = redis_master.pipeline()
                    for post_id in ids_to_fetch:
                        cache_key = self._get_media_cache_key(post_id)
                        payload = json.dumps(
                            [
                                media_item.model_dump(mode="json")
                                for media_item in media_map.get(post_id, [])
                            ]
                        )
                        pipe.set(cache_key, payload, ex=self.expire_seconds)
                    await pipe.execute()
                except Exception as exc:
                    logger.warning(f"批量写入沸点媒体缓存失败: {exc}")

        return media_map

    async def get_by_id(
        self,
        db: AsyncSession,
        *,
        entity_id: int,
    ) -> schemas.PostBase | None:
        """获取单个沸点基础信息"""
        return await self.get_entity(db, entity_id=entity_id)

    async def get_posts_by_author(
        self,
        db: AsyncSession,
        *,
        author_id: int,
        limit: int = 20,
        offset: int = 0,
    ) -> list[schemas.PostBase]:
        """
        获取指定作者的沸点列表

        Args:
            db: 数据库会话
            author_id: 作者ID
            limit: 限制数量
            offset: 偏移量

        Returns:
            沸点基础信息列表
        """
        cache_key = f"posts:author:{author_id}:limit:{limit}:offset:{offset}"

        if self.cache_enabled:
            try:
                cached_data = await self._get_from_cache(cache_key)
                if cached_data:
                    return [self.schema.model_validate(post) for post in cached_data]
            except Exception as e:
                logger.warning(f"从缓存获取作者沸点列表失败: {e}")

        # 从数据库获取
        posts = await self.crud.get_posts_by_author(
            db,
            author_id=author_id,
            limit=limit,
            offset=offset,
        )

        # 转换为schema
        post_schemas = [self.schema.model_validate(post) for post in posts]

        # 缓存结果
        if self.cache_enabled and post_schemas:
            try:
                cache_data = [post.model_dump() for post in post_schemas]
                await self._set_to_cache(cache_key, cache_data, expire_seconds=300)  # 5分钟缓存
            except Exception as e:
                logger.warning(f"缓存作者沸点列表失败: {e}")

        return post_schemas

    async def get_posts_by_topic(
        self,
        db: AsyncSession,
        *,
        topic: str,
        limit: int = 20,
        offset: int = 0,
    ) -> list[schemas.PostBase]:
        """
        获取指定话题的沸点列表

        Args:
            db: 数据库会话
            topic: 话题标签
            limit: 限制数量
            offset: 偏移量

        Returns:
            沸点基础信息列表
        """
        cache_key = f"posts:topic:{topic}:limit:{limit}:offset:{offset}"

        if self.cache_enabled:
            try:
                cached_data = await self._get_from_cache(cache_key)
                if cached_data:
                    return [self.schema.model_validate(post) for post in cached_data]
            except Exception as e:
                logger.warning(f"从缓存获取话题沸点列表失败: {e}")

        # 从数据库获取
        posts = await self.crud.get_posts_by_topic(
            db,
            topic=topic,
            limit=limit,
            offset=offset,
        )

        # 转换为schema
        post_schemas = [self.schema.model_validate(post) for post in posts]

        # 缓存结果
        if self.cache_enabled and post_schemas:
            try:
                cache_data = [post.model_dump() for post in post_schemas]
                await self._set_to_cache(cache_key, cache_data, expire_seconds=300)  # 5分钟缓存
            except Exception as e:
                logger.warning(f"缓存话题沸点列表失败: {e}")

        return post_schemas

    async def get_hot_posts(
        self,
        db: AsyncSession,
        *,
        limit: int = 20,
        offset: int = 0,
    ) -> list[schemas.PostBase]:
        """
        获取热门沸点列表

        Args:
            db: 数据库会话
            limit: 限制数量
            offset: 偏移量

        Returns:
            沸点基础信息列表
        """
        cache_key = f"posts:hot:limit:{limit}:offset:{offset}"

        if self.cache_enabled:
            try:
                cached_data = await self._get_from_cache(cache_key)
                if cached_data:
                    return [self.schema.model_validate(post) for post in cached_data]
            except Exception as e:
                logger.warning(f"从缓存获取热门沸点列表失败: {e}")

        # 从数据库获取
        posts = await self.crud.get_hot_posts(
            db,
            limit=limit,
            offset=offset,
        )

        # 转换为schema
        post_schemas = [self.schema.model_validate(post) for post in posts]

        # 缓存结果
        if self.cache_enabled and post_schemas:
            try:
                cache_data = [post.model_dump() for post in post_schemas]
                await self._set_to_cache(cache_key, cache_data, expire_seconds=60)  # 1分钟缓存
            except Exception as e:
                logger.warning(f"缓存热门沸点列表失败: {e}")

        return post_schemas

    async def get_following_posts(
        self,
        db: AsyncSession,
        *,
        user_id: int,
        limit: int = 20,
        offset: int = 0,
    ) -> list[schemas.PostBase]:
        """
        获取关注用户的沸点列表

        Args:
            db: 数据库会话
            user_id: 用户ID
            limit: 限制数量
            offset: 偏移量

        Returns:
            沸点基础信息列表
        """
        cache_key = f"posts:following:{user_id}:limit:{limit}:offset:{offset}"

        if self.cache_enabled:
            try:
                cached_data = await self._get_from_cache(cache_key)
                if cached_data:
                    return [self.schema.model_validate(post) for post in cached_data]
            except Exception as e:
                logger.warning(f"从缓存获取关注沸点列表失败: {e}")

        # 从数据库获取
        posts = await self.crud.get_following_posts(
            db,
            user_id=user_id,
            limit=limit,
            offset=offset,
        )

        # 转换为schema
        post_schemas = [self.schema.model_validate(post) for post in posts]

        # 缓存结果
        if self.cache_enabled and post_schemas:
            try:
                cache_data = [post.model_dump() for post in post_schemas]
                await self._set_to_cache(cache_key, cache_data, expire_seconds=180)  # 3分钟缓存
            except Exception as e:
                logger.warning(f"缓存关注沸点列表失败: {e}")

        return post_schemas

    async def invalidate_author_cache(self, author_id: int) -> None:
        """
        清除指定作者的相关缓存

        Args:
            author_id: 作者ID
        """
        if not self.cache_enabled:
            return

        try:
            # 清除作者相关的缓存模式
            patterns = [
                f"posts:author:{author_id}:*",
                "posts:following:*",  # 关注列表可能包含该作者
                "posts:hot:*",  # 热门列表可能包含该作者
            ]

            for pattern in patterns:
                await self._delete_cache_pattern(pattern)

        except Exception as e:
            logger.warning(f"清除作者缓存失败: {e}")

    async def invalidate_topic_cache(self, topic: str) -> None:
        """
        清除指定话题的相关缓存

        Args:
            topic: 话题标签
        """
        if not self.cache_enabled:
            return

        try:
            pattern = f"posts:topic:{topic}:*"
            await self._delete_cache_pattern(pattern)
        except Exception as e:
            logger.warning(f"清除话题缓存失败: {e}")

    async def invalidate_hot_cache(self) -> None:
        """清除热门沸点缓存"""
        if not self.cache_enabled:
            return

        try:
            pattern = "posts:hot:*"
            await self._delete_cache_pattern(pattern)
        except Exception as e:
            logger.warning(f"清除热门缓存失败: {e}")
