from __future__ import annotations

from datetime import datetime, timedelta, timezone
from typing import Any

from sqlalchemy import func, select
from sqlalchemy.ext.asyncio import AsyncSession

from app.models.admin_monitoring import AdminAlertEvent, AdminAlertRule, AdminMonitoringMetric
from app.schemas.admin_monitoring import MetricSeriesResponse, MetricPoint
from app.services.monitoring.alert_engine import MonitoringAlertEngine
from app.services.monitoring.collector import MonitoringCollector


class MonitoringService:
    """监控模块应用服务"""

    def __init__(self, collector: MonitoringCollector, alert_engine: MonitoringAlertEngine) -> None:
        self.collector = collector
        self.alert_engine = alert_engine

    async def get_dashboard(self, db: AsyncSession, time_range: str = "5m") -> dict[str, Any]:
        since = self._time_range_start(time_range)

        def metric_summary(metric: str, unit: str = "") -> dict[str, Any]:
            summary = self._metric_summary(db, metric, since, unit=unit)
            return summary

        system_metrics = {
            "cpu": await metric_summary("cpu", "percent"),
            "memory": await metric_summary("memory", "percent"),
            "disk": await metric_summary("disk", "percent"),
            "network": await metric_summary("network", "bytes/sec"),
        }

        application_metrics = {
            "requests": await metric_summary("requests.count", "count"),
            "database": await metric_summary("database.connections", "count"),
            "cache": await metric_summary("cache.hit_rate", "percent"),
        }

        alerts_stmt = (
            select(AdminAlertEvent)
            .where(AdminAlertEvent.triggered_at >= since)
            .order_by(AdminAlertEvent.triggered_at.desc())
            .limit(10)
        )
        alerts_result = await db.execute(alerts_stmt)
        recent_alerts = [
            {
                "id": alert.event_id,
                "rule_id": alert.rule_id,
                "severity": alert.status,
                "triggered_at": alert.triggered_at,
                "message": alert.message,
            }
            for alert in alerts_result.scalars().all()
        ]

        active_count_stmt = select(func.count()).select_from(AdminAlertEvent).where(
            AdminAlertEvent.status == "active"
        )
        total_today_stmt = select(func.count()).select_from(AdminAlertEvent).where(
            AdminAlertEvent.triggered_at >= since
        )
        active_count = (await db.execute(active_count_stmt)).scalar_one()
        total_today = (await db.execute(total_today_stmt)).scalar_one()

        return {
            "system_metrics": system_metrics,
            "application_metrics": application_metrics,
            "alerts": {
                "active": active_count,
                "total_today": total_today,
                "recent": recent_alerts,
            },
        }

    async def _metric_summary(
        self, db: AsyncSession, metric: str, since: datetime, *, unit: str
    ) -> dict[str, Any]:
        stmt = (
            select(
                func.avg(AdminMonitoringMetric.value).label("avg"),
                func.max(AdminMonitoringMetric.value).label("max"),
                func.min(AdminMonitoringMetric.value).label("min"),
                func.sum(AdminMonitoringMetric.value).label("sum"),
            )
            .where(
                AdminMonitoringMetric.metric_name == metric,
                AdminMonitoringMetric.timestamp >= since,
            )
        )
        result = await db.execute(stmt)
        row = result.first()
        latest_stmt = (
            select(AdminMonitoringMetric)
            .where(AdminMonitoringMetric.metric_name == metric)
            .order_by(AdminMonitoringMetric.timestamp.desc())
            .limit(1)
        )
        latest_metric = (await db.execute(latest_stmt)).scalars().first()
        if not row or row.avg is None:
            return {"current": 0, "average": 0, "max": 0, "total": 0, "unit": unit, "timestamp": None}

        return {
            "current": float(latest_metric.value) if latest_metric else 0,
            "average": float(row.avg),
            "max": float(row.max or 0),
            "total": float(row.sum or 0),
            "unit": unit,
            "timestamp": latest_metric.timestamp if latest_metric else None,
        }

    async def get_metric_series(
        self,
        db: AsyncSession,
        *,
        metric_name: str,
        start: datetime,
        end: datetime,
    ) -> MetricSeriesResponse:
        stmt = (
            select(AdminMonitoringMetric)
            .where(
                AdminMonitoringMetric.metric_name == metric_name,
                AdminMonitoringMetric.timestamp >= start,
                AdminMonitoringMetric.timestamp <= end,
            )
            .order_by(AdminMonitoringMetric.timestamp.asc())
        )
        result = await db.execute(stmt)
        metrics = result.scalars().all()
        points = [MetricPoint(timestamp=m.timestamp, value=float(m.value)) for m in metrics]

        return MetricSeriesResponse(metric_name=metric_name, interval="raw", points=points)

    async def list_alerts(
        self,
        db: AsyncSession,
        *,
        status: str | None = None,
        severity: str | None = None,
        page: int = 1,
        page_size: int = 20,
    ) -> dict[str, Any]:
        stmt = select(AdminAlertEvent)
        if status and status != "all":
            stmt = stmt.where(AdminAlertEvent.status == status)
        if severity:
            stmt = stmt.where(AdminAlertEvent.notification_status == severity)

        stmt = stmt.order_by(AdminAlertEvent.triggered_at.desc())

        total = (await db.execute(select(func.count()).select_from(stmt.subquery()))).scalar_one()
        stmt = stmt.offset((page - 1) * page_size).limit(page_size)
        events = (await db.execute(stmt)).scalars().all()

        return {
            "total": total,
            "page": page,
            "page_size": page_size,
            "alerts": [
                {
                    "id": event.event_id,
                    "rule_id": event.rule_id,
                    "status": event.status,
                    "triggered_at": event.triggered_at,
                    "resolved_at": event.resolved_at,
                    "message": event.message,
                    "metadata": event.context,
                }
                for event in events
            ],
        }

    async def list_rules(self, db: AsyncSession) -> list[dict[str, Any]]:
        stmt = select(AdminAlertRule).order_by(AdminAlertRule.created_at.desc())
        result = await db.execute(stmt)
        rules = result.scalars().all()
        return [self._rule_to_dict(rule) for rule in rules]

    async def create_alert_rule(self, db: AsyncSession, payload: dict[str, Any]) -> dict[str, Any]:
        rule = AdminAlertRule(
            name=payload["name"],
            metric_name=payload["metric_name"],
            operator=payload.get("operator", "greater_than"),
            threshold=payload.get("threshold", 0),
            duration=payload.get("duration", "5m"),
            severity=payload.get("severity", "info"),
            channels=payload.get("channels", []),
            enabled=payload.get("enabled", True),
            description=payload.get("description"),
        )
        db.add(rule)
        await db.commit()
        await db.refresh(rule)
        return self._rule_to_dict(rule)

    async def update_alert_rule(
        self, db: AsyncSession, rule_id: int, payload: dict[str, Any]
    ) -> dict[str, Any]:
        stmt = select(AdminAlertRule).where(AdminAlertRule.id == rule_id)
        result = await db.execute(stmt)
        rule = result.scalar_one()

        for field in ("metric_name", "operator", "threshold", "duration", "severity"):
            if field in payload and payload[field] is not None:
                setattr(rule, field, payload[field])
        if "channels" in payload and payload["channels"] is not None:
            rule.channels = payload["channels"]
        if "enabled" in payload and payload["enabled"] is not None:
            rule.enabled = payload["enabled"]
        if "description" in payload:
            rule.description = payload["description"]

        await db.commit()
        await db.refresh(rule)
        return self._rule_to_dict(rule)

    async def delete_alert_rule(self, db: AsyncSession, rule_id: int) -> None:
        stmt = select(AdminAlertRule).where(AdminAlertRule.id == rule_id)
        result = await db.execute(stmt)
        rule = result.scalar_one()
        await db.delete(rule)
        await db.commit()

    async def list_channels(self) -> list[dict[str, Any]]:
        return [
            {
                "name": "dingtalk",
                "display_name": "钉钉通知",
                "enabled": True,
                "config": {},
                "last_test_status": None,
            },
            {
                "name": "email",
                "display_name": "邮件通知",
                "enabled": False,
                "config": {},
                "last_test_status": None,
            },
        ]

    async def test_channel(self, channel: str) -> dict[str, Any]:
        return {"channel": channel, "status": "success"}

    def _time_range_start(self, time_range: str) -> datetime:
        now = datetime.now(timezone.utc)
        mapping = {
            "5m": timedelta(minutes=5),
            "15m": timedelta(minutes=15),
            "30m": timedelta(minutes=30),
            "1h": timedelta(hours=1),
            "6h": timedelta(hours=6),
            "24h": timedelta(hours=24),
        }
        return now - mapping.get(time_range, timedelta(minutes=5))

    def _rule_to_dict(self, rule: AdminAlertRule) -> dict[str, Any]:
        return {
            "id": rule.id,
            "name": rule.name,
            "metric_name": rule.metric_name,
            "operator": rule.operator,
            "threshold": float(rule.threshold),
            "duration": rule.duration,
            "severity": rule.severity,
            "channels": rule.channels,
            "enabled": rule.enabled,
            "description": rule.description,
        }
