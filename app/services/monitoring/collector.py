from __future__ import annotations

from dataclasses import dataclass
from datetime import datetime, timezone
from typing import Any, Sequence

from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from app.models.admin_monitoring import AdminMonitoringMetric


@dataclass
class MetricSample:
    metric_name: str
    value: float
    labels: dict[str, Any] | None = None
    timestamp: datetime | None = None
    metadata: dict[str, Any] | None = None


class MonitoringCollector:
    """负责采集指标并写入数据库"""

    def __init__(self, sampler: Any | None = None) -> None:
        self._sampler = sampler

    async def collect_and_record(self, db: AsyncSession, source: str) -> None:
        if not self._sampler:
            return
        samples = await self._sampler()
        await self.record_samples(db, samples, source=source)

    async def record_samples(
        self,
        db: AsyncSession,
        samples: Sequence[MetricSample],
        *,
        source: str,
    ) -> None:
        now = datetime.now(timezone.utc)
        for sample in samples:
            metric = AdminMonitoringMetric(
                metric_name=sample.metric_name,
                value=sample.value,
                labels=sample.labels or {},
                timestamp=sample.timestamp or now,
                source=source,
                metadata=sample.metadata or {},
            )
            db.add(metric)
        await db.commit()

    async def latest_metric(
        self, db: AsyncSession, metric_name: str
    ) -> AdminMonitoringMetric | None:
        stmt = (
            select(AdminMonitoringMetric)
            .where(AdminMonitoringMetric.metric_name == metric_name)
            .order_by(AdminMonitoringMetric.timestamp.desc())
        )
        result = await db.execute(stmt)
        return result.scalars().first()
