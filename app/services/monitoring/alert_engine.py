from __future__ import annotations

from datetime import datetime, timedelta, timezone
from typing import Iterable

from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from app.models.admin_monitoring import AdminAlertEvent, AdminAlertRule, AdminMonitoringMetric


class MonitoringAlertEngine:
    """阈值告警评估引擎"""

    async def evaluate_rules(self, db: AsyncSession) -> None:
        rules = await self._load_active_rules(db)
        now = datetime.now(timezone.utc)

        for rule in rules:
            duration = self._parse_duration(rule.duration)
            window_start = now - duration
            metric_value = await self._latest_metric_value(
                db, rule.metric_name, window_start
            )
            if metric_value is None:
                continue

            if self._matches(rule.operator, float(metric_value), float(rule.threshold)):
                await self._create_event(db, rule, float(metric_value))

        await db.commit()

    async def _load_active_rules(self, db: AsyncSession) -> Iterable[AdminAlertRule]:
        stmt = select(AdminAlertRule).where(AdminAlertRule.enabled.is_(True))
        result = await db.execute(stmt)
        return result.scalars().all()

    async def _latest_metric_value(
        self, db: AsyncSession, metric_name: str, window_start: datetime
    ) -> float | None:
        stmt = (
            select(AdminMonitoringMetric)
            .where(
                AdminMonitoringMetric.metric_name == metric_name,
                AdminMonitoringMetric.timestamp >= window_start,
            )
            .order_by(AdminMonitoringMetric.timestamp.desc())
        )
        result = await db.execute(stmt)
        metric = result.scalars().first()
        if not metric:
            return None
        return float(metric.value)

    async def _create_event(
        self, db: AsyncSession, rule: AdminAlertRule, value: float
    ) -> None:
        event = AdminAlertEvent(
            rule_id=rule.id,
            event_id=f"{rule.name}_{datetime.now(timezone.utc).timestamp()}",
            status="active",
            trigger_value=value,
            context={"threshold": float(rule.threshold)},
            message=f"{rule.metric_name} exceeded threshold {rule.threshold}",
            notification_status="pending",
        )
        db.add(event)

    def _parse_duration(self, duration_str: str) -> timedelta:
        if duration_str.endswith("m"):
            return timedelta(minutes=int(duration_str[:-1] or "5"))
        if duration_str.endswith("h"):
            return timedelta(hours=int(duration_str[:-1] or "1"))
        return timedelta(minutes=5)

    def _matches(self, operator: str, value: float, threshold: float) -> bool:
        ops = {
            "greater_than": value > threshold,
            "greater_or_equal": value >= threshold,
            "less_than": value < threshold,
            "less_or_equal": value <= threshold,
            "equal": value == threshold,
            "not_equal": value != threshold,
        }
        return ops.get(operator, False)
