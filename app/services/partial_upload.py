"""分片上传服务"""

import oss2
from oss2 import SizedFileAdapter, determine_part_size
from oss2.credentials import EnvironmentVariableCredentialsProvider

from app.config import get_settings
from app.core.logging import logger

settings = get_settings()


class PartialUpload:
    """分片上传服务，支持大文件分片上传到阿里云OSS"""

    def __init__(self):
        self.auth = oss2.ProviderAuthV4(EnvironmentVariableCredentialsProvider())
        # Remove the bucket name from the OSS_ENDPOINT if it's present, as oss2 SDK will add it automatically
        endpoint = settings.OSS_ENDPOINT.replace(f"https://{settings.OSS_BUCKET_NAME}.", "https://")
        # For OSS V4 signature, we need to specify the region correctly
        # The region should be just the region part without 'oss-' prefix
        region = settings.OSS_REGION.replace("oss-", "")
        self.bucket = oss2.Bucket(self.auth, endpoint, settings.OSS_BUCKET_NAME, region=region)

    def upload(
        self,
        file_data: bytes,
        file_hash: str,
        file_type: str = "image",
        retry_times: int = 3,
        file_extension: str | None = None,
    ) -> str | None:
        """上传文件到OSS

        Args:
            file_data: 文件二进制数据
            file_hash: 文件哈希值
            file_type: 文件类型，可选值：image, video，默认image
            retry_times: 重试次数，默认3次

        Returns:
            str: 上传成功返回文件URL，失败返回None
        """
        # 根据文件类型设置存储路径和扩展名
        file_type_config = {
            "image": {"path": "steam/images", "ext": ".webp"},
            "video": {"path": "steam/videos", "ext": ".webm"},
            "gif": {"path": "steam/images", "ext": ".gif"},
            "post_video": {"path": "steam/post-videos", "ext": ".mp4"},
            "backpack": {"path": "backpack", "ext": ""},  # 背包文件保持原扩展名
        }
        config = file_type_config.get(file_type, file_type_config["image"])
        extension = file_extension if file_extension is not None else config["ext"]
        object_name = f"{config['path']}/{file_hash}{extension}"

        # 小文件直接上传
        if len(file_data) < settings.OSS_MULTIPART_THRESHOLD:
            return self._upload_small_file(file_data, object_name, retry_times)

        # 大文件分片上传
        return self._upload_large_file(file_data, object_name, retry_times)

    def _upload_small_file(
        self, file_data: bytes, object_name: str, retry_times: int
    ) -> str | None:
        """小文件直接上传

        Args:
            file_data: 文件二进制数据
            object_name: OSS对象名称
            retry_times: 重试次数

        Returns:
            str: 上传成功返回文件URL，失败返回None
        """
        for i in range(retry_times):
            try:
                self.bucket.put_object(object_name, file_data)
                return f"/{object_name}"
            except Exception as e:
                logger.error(f"Failed to upload small file: {e}, retry {i + 1}/{retry_times}")
                if i == retry_times - 1:
                    return None

    def _upload_large_file(
        self, file_data: bytes, object_name: str, retry_times: int
    ) -> str | None:
        """大文件分片上传

        Args:
            file_data: 文件二进制数据
            object_name: OSS对象名称
            retry_times: 重试次数

        Returns:
            str: 上传成功返回文件URL，失败返回None
        """
        import io

        for i in range(retry_times):
            try:
                # 初始化分片上传
                upload_id = self.bucket.init_multipart_upload(object_name).upload_id
                parts = []

                # 计算分片大小和数量
                total_size = len(file_data)
                part_size = determine_part_size(total_size, preferred_size=1 * 1024 * 1024)
                part_count = (total_size - 1) // part_size + 1

                # 使用 SizedFileAdapter 进行分片上传
                with io.BytesIO(file_data) as f:
                    sized_file = SizedFileAdapter(f, total_size)
                    for j in range(part_count):
                        result = self.bucket.upload_part(object_name, upload_id, j + 1, sized_file)
                        parts.append(oss2.models.PartInfo(j + 1, result.etag))

                # 完成分片上传
                self.bucket.complete_multipart_upload(object_name, upload_id, parts)
                return f"/{object_name}"

            except Exception as e:
                logger.error(f"Failed to upload large file: {e}, retry {i + 1}/{retry_times}")
                if i == retry_times - 1:
                    return None
                # 取消分片上传
                try:
                    self.bucket.abort_multipart_upload(object_name, upload_id)
                except Exception as abort_e:
                    logger.error(f"Failed to abort multipart upload: {abort_e}")

    def upload_directory(
        self, local_directory_path: str, oss_target_path: str, retry_times: int = 3
    ) -> bool:
        """上传整个目录到OSS，并支持事务性回滚

        Args:
            local_directory_path: 本地目录路径
            oss_target_path: OSS上的目标路径 (例如 'steam/hls/some_hash')
            retry_times: 单个文件上传的重试次数

        Returns:
            bool: 目录上传是否成功
        """
        import os

        uploaded_files = []
        try:
            for root, _, files in os.walk(local_directory_path):
                for file in files:
                    local_file_path = os.path.join(root, file)
                    relative_path = os.path.relpath(local_file_path, local_directory_path)
                    oss_object_name = os.path.join(oss_target_path, relative_path).replace(
                        "\\", "/"
                    )

                    file_size = os.path.getsize(local_file_path)
                    if file_size < settings.OSS_MULTIPART_THRESHOLD:
                        with open(local_file_path, "rb") as f:
                            file_data = f.read()
                        result = self._upload_small_file(file_data, oss_object_name, retry_times)
                    else:
                        # _upload_large_file 需要接收文件路径而不是二进制数据
                        result = self._upload_large_file_from_path(
                            local_file_path, oss_object_name, retry_times
                        )

                    if not result:
                        raise Exception(f"Failed to upload file: {local_file_path}")

                    uploaded_files.append(oss_object_name)
            logger.info(
                f"Successfully uploaded directory {local_directory_path} to {oss_target_path}"
            )
            return True
        except Exception as e:
            logger.error(f"Failed to upload directory: {e}. Rolling back...")
            if uploaded_files:
                # 批量删除已上传的文件
                self.bucket.batch_delete_objects(uploaded_files)
                logger.info(f"Rolled back {len(uploaded_files)} files from OSS.")
            return False

    def _upload_large_file_from_path(
        self, file_path: str, object_name: str, retry_times: int
    ) -> str | None:
        """从文件路径分片上传大文件"""
        import os

        for i in range(retry_times):
            try:
                total_size = os.path.getsize(file_path)
                part_size = determine_part_size(total_size, preferred_size=settings.OSS_PART_SIZE)
                upload_id = self.bucket.init_multipart_upload(object_name).upload_id

                with open(file_path, "rb") as f:
                    parts = []
                    part_number = 1
                    offset = 0
                    while offset < total_size:
                        num_to_upload = min(part_size, total_size - offset)
                        # SizedFileAdapter is not needed here as we are managing chunks manually
                        result = self.bucket.upload_part(
                            object_name, upload_id, part_number, f, num_to_upload
                        )
                        parts.append(oss2.models.PartInfo(part_number, result.etag))
                        offset += num_to_upload
                        part_number += 1

                self.bucket.complete_multipart_upload(object_name, upload_id, parts)
                return f"/{object_name}"
            except Exception as e:
                logger.error(
                    f"Failed to upload large file from path {file_path}: {e}, retry {i + 1}/{retry_times}"
                )
                if i == retry_times - 1:
                    return None
                try:
                    if "upload_id" in locals():
                        self.bucket.abort_multipart_upload(object_name, upload_id)
                except Exception as abort_e:
                    logger.error(f"Failed to abort multipart upload: {abort_e}")
