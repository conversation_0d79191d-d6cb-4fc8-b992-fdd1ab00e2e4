from __future__ import annotations

import csv
from dataclasses import dataclass
from datetime import UTC, datetime
from pathlib import Path
from typing import Any


@dataclass
class ExportResult:
    path: str
    filename: str
    rows: int


class ExportStorage:
    def __init__(self, base_dir: str | Path = "tmp/analytics_exports") -> None:
        self.base_dir = Path(base_dir)
        self.base_dir.mkdir(parents=True, exist_ok=True)

    async def save_csv(self, rows: list[dict[str, Any]], prefix: str) -> ExportResult:
        timestamp = datetime.now(UTC).strftime("%Y%m%d%H%M%S")
        filename = f"{prefix}_{timestamp}.csv"
        path = self.base_dir / filename

        if rows:
            headers = list(rows[0].keys())
        else:
            headers = []

        with path.open("w", newline="", encoding="utf-8") as f:
            writer = csv.DictWriter(f, fieldnames=headers)
            writer.writeheader()
            for row in rows:
                writer.writerow(row)

        return ExportResult(path=str(path), filename=filename, rows=len(rows))
