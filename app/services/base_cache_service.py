"""通用基础缓存服务模块"""

import asyncio
from typing import TypeVar

import pybreaker
from pydantic import BaseModel
from sqlalchemy.ext.asyncio import AsyncSession

from app.config import settings
from app.core.breaker import db_circuit_breaker
from app.core.logging import logger
from app.crud.base import CRUDBase
from app.db import redis
from app.db.session import Base
from app.utils.bloom_filters import BloomFilterService
from app.utils.time_utils import TimeUtils

ModelType = TypeVar("ModelType", bound=Base)
SchemaType = TypeVar("SchemaType", bound=BaseModel)
CRUDType = TypeVar("CRUDType", bound=CRUDBase)


class BaseCacheService[ModelType: Base, SchemaType: BaseModel, CRUDType: CRUDBase]:
    """
    通用基础缓存服务
    - 缓存 Pydantic 基础数据
    - 遵循 Cache-Aside 模式
    - 实现版本控制以确保数据一致性
    """

    def __init__(
        self,
        *,
        crud_model: CRUDType,
        schema_model: type[SchemaType],
        bloom_filter: BloomFilterService,
        cache_key_prefix: str,
        entity_name: str,
        cache_enabled_setting: bool,
        cache_expire_seconds: int,
    ):
        """初始化服务"""
        self.crud = crud_model
        self.schema = schema_model
        self.bloom_filter = bloom_filter
        self.cache_key_prefix = cache_key_prefix
        self.entity_name = entity_name
        self.cache_enabled = cache_enabled_setting
        self.expire_seconds = cache_expire_seconds

        self.db_query_semaphore = asyncio.Semaphore(settings.DB_QUERY_CONCURRENCY_LIMIT)

        # 新增：降级控制参数
        self.bloom_filter_bypass = False  # 是否绕过布隆过滤器
        self.cache_bypass = False  # 是否绕过缓存
        self.bloom_filter_error_count = 0  # 布隆过滤器错误计数
        self.bloom_filter_error_threshold = 5  # 错误阈值
        self.bloom_filter_miss_threshold = 0.8  # 布隆过滤器全部miss的降级阈值

        logger.info(
            f"{self.entity_name} 服务数据库查询并发信号量已初始化，限制为: {settings.DB_QUERY_CONCURRENCY_LIMIT}"
        )

    def _get_cache_key(self, entity_id: int) -> str:
        """统一生成缓存键"""
        return f"{self.cache_key_prefix}:{entity_id}"

    @db_circuit_breaker
    async def _get_entity_from_db(self, db: AsyncSession, entity_id: int) -> ModelType | None:
        """从数据库获取单个实体（受熔断器和并发限制保护）"""
        async with self.db_query_semaphore:
            return await self.crud.get(db=db, id=entity_id)

    @db_circuit_breaker
    async def _get_entities_batch_from_db(
        self, db: AsyncSession, entity_ids: list[int]
    ) -> list[ModelType]:
        """从数据库批量获取实体（受熔断器和并发限制保护）"""
        async with self.db_query_semaphore:
            return await self.crud.get_multi_by_ids(db=db, ids=entity_ids)

    async def get_entity(self, db: AsyncSession, entity_id: int) -> SchemaType | None:
        """
        获取实体基础信息，优先从缓存读取，未命中则回填。
        当 Bloom 判定不存在时，如果 settings.CACHE_SINGLE_GET_FALLBACK 为 True，则尝试一次 DB 兜底并回填。
        """
        bloom_positive = True
        if self.cache_enabled:
            try:
                bloom_positive = await self.bloom_filter.exists(str(entity_id))
            except Exception as e:
                logger.error(f"布隆过滤器检查失败（降级为 True）：{e}")
                bloom_positive = True

            if not bloom_positive:
                if settings.CACHE_SINGLE_GET_FALLBACK:
                    logger.warning(
                        f"Bloom Negative 但启用单查降级(CACHE_SINGLE_GET_FALLBACK=True)，"
                        f"尝试 DB 兜底获取 {self.entity_name}_id={entity_id}"
                    )
                    # 跳过缓存读取，继续走到 DB 兜底分支
                else:
                    return None

            if bloom_positive:
                cache_key = self._get_cache_key(entity_id)
                try:
                    cached_data_map = await redis.redis_slave_client.hgetall(cache_key)
                    if cached_data_map:
                        logger.info(f"缓存命中: {cache_key}")
                        asyncio.create_task(
                            redis.redis_master_client.expire(cache_key, self.expire_seconds)
                        )
                        data_str = cached_data_map.get(b"data") or cached_data_map.get("data")
                        if isinstance(data_str, str):
                            data_str = data_str.encode()
                        if data_str == b"null":
                            return None
                        if data_str:
                            return self.schema.model_validate_json(data_str)
                except Exception as e:
                    logger.error(f"从Redis获取 {self.entity_name} 缓存失败: {e}, key: {cache_key}")

        logger.info(
            "缓存未命中或触发降级，准备从数据库读取",
            extra={"entity_id": entity_id, "cache_key": self._get_cache_key(entity_id)},
        )
        try:
            db_entity = await self._get_entity_from_db(db, entity_id)
        except pybreaker.CircuitBreakerError:
            logger.error(f"数据库熔断器开启，无法获取 {self.entity_name}_id={entity_id}")
            return None

        if db_entity:
            # 异步写回缓存（即使是降级路径）
            await self.set_entity(db_entity)
            return self.schema.from_orm(db_entity)
        else:
            logger.warning(
                "数据库查询未返回结果，将写入空缓存",
                extra={"entity_id": entity_id, "cache_key": self._get_cache_key(entity_id)},
            )
            await self.set_null_cache(entity_id)
            return None

    async def get_entities_batch(
        self, db: AsyncSession, entity_ids: list[int]
    ) -> dict[int, SchemaType | None]:
        """
        批量获取实体基础信息，优先从缓存读取，未命中则回填。
        """
        logger.debug(
            "get_entities_batch 开始",
            extra={"entity_ids": entity_ids, "cache_enabled": self.cache_enabled},
        )

        if not entity_ids:
            return {}

        results: dict[int, SchemaType | None] = dict.fromkeys(entity_ids)
        missing_ids = list(entity_ids)

        if self.cache_enabled:
            try:
                exists_list = await self.bloom_filter.exists_multi([str(eid) for eid in entity_ids])
                logger.debug(
                    "布隆过滤器批量查询结果",
                    extra={"entity_ids": entity_ids, "exists_list": exists_list},
                )

                ids_to_fetch = [
                    eid for eid, exists in zip(entity_ids, exists_list, strict=False) if exists
                ]
                logger.debug(
                    "准备从缓存读取的ID",
                    extra={"ids_to_fetch": ids_to_fetch, "entity_ids": entity_ids},
                )

                # 新增：布隆过滤器降级机制检查
                if not ids_to_fetch and entity_ids:
                    # 如果布隆过滤器说都不存在，但我们确实有ID要查询
                    # 可能是布隆过滤器问题，启用降级模式
                    logger.warning(
                        "布隆过滤器判定所有ID不存在，启用降级模式",
                        extra={"entity_ids": entity_ids},
                    )
                    # 降级：跳过布隆过滤器，直接尝试缓存查询
                    ids_to_fetch = entity_ids
                elif not ids_to_fetch:
                    logger.debug(
                        "布隆过滤器判定所有ID不存在，返回空结果",
                        extra={"entity_ids": entity_ids},
                    )
                    return results

                # 使用 pipeline 批量执行 HGETALL
                pipe = redis.redis_slave_client.pipeline()
                for entity_id in ids_to_fetch:
                    pipe.hgetall(self._get_cache_key(entity_id))
                cached_hashes = await pipe.execute()
                logger.debug(
                    "缓存批量读取完成",
                    extra={"requested_ids": ids_to_fetch, "hash_count": len(cached_hashes)},
                )

                still_missing_ids = []
                for i, cached_hash in enumerate(cached_hashes):
                    entity_id = ids_to_fetch[i]
                    logger.debug(
                        "处理缓存返回",
                        extra={"entity_id": entity_id, "has_cache": bool(cached_hash)},
                    )

                    if cached_hash:
                        logger.debug(
                            "Redis cached_hash for entity_id {}: {}", entity_id, cached_hash
                        )
                        logger.debug("Redis cached_hash type: {}", type(cached_hash))
                        logger.debug(
                            "Redis cached_hash keys: {}",
                            list(cached_hash.keys()) if cached_hash else "None",
                        )

                        data_str = cached_hash.get(b"data")
                        logger.debug("Entity {} data_str: {}", entity_id, data_str)
                        logger.debug("Entity {} data_str type: {}", entity_id, type(data_str))
                        logger.debug(
                            "Entity {} data_str length: {}",
                            entity_id,
                            len(data_str) if data_str else 0,
                        )

                        data_str = cached_hash.get(b"data") or cached_hash.get("data")
                        if isinstance(data_str, str):
                            data_str = data_str.encode()

                        if data_str is None:
                            still_missing_ids.append(entity_id)
                            continue

                        if data_str == b"null":
                            results[entity_id] = None
                            continue

                        if len(data_str) == 0:
                            still_missing_ids.append(entity_id)
                            continue

                        try:
                            results[entity_id] = self.schema.model_validate_json(data_str)
                        except Exception as e:
                            logger.error(
                                "缓存数据解析失败",
                                extra={
                                    "entity_id": entity_id,
                                    "error": str(e),
                                },
                            )
                            still_missing_ids.append(entity_id)
                    else:
                        still_missing_ids.append(entity_id)
                        logger.debug("Entity {} not found in cache", entity_id)
                missing_ids = still_missing_ids
                logger.debug("缓存读取后仍缺失的ID", extra={"missing_ids": missing_ids})
            except Exception as e:
                logger.error(
                    f"从Redis批量获取 {self.entity_name} 缓存失败: {e}",
                    extra={"entity_ids": entity_ids},
                )

        if missing_ids:
            logger.info(f"批量缓存未命中: {len(missing_ids)} 个, ids: {missing_ids}")

            # 添加详细的调试信息
            logger.debug(
                f"DEBUG: About to call {self.crud.__class__.__name__}.get_multi_by_ids with IDs: {missing_ids}"
            )

            try:
                db_entities = await self._get_entities_batch_from_db(db, missing_ids)
                logger.debug(
                    "数据库查询返回",
                    extra={
                        "requested_ids": missing_ids,
                        "returned_count": len(db_entities),
                    },
                )

                # 记录实际返回的实体ID
                actual_entity_ids = [entity.id for entity in db_entities] if db_entities else []
                logger.debug(f"DEBUG: Actual entities returned from DB: {actual_entity_ids}")
                logger.debug(
                    f"DEBUG: Missing entities not found in DB: {set(missing_ids) - set(actual_entity_ids)}"
                )

                # 对于视频类型，记录可能的过滤原因
                if self.entity_name.lower() == "video" and len(db_entities) < len(missing_ids):
                    logger.warning(
                        f"DEBUG: Video CRUD可能应用了业务逻辑过滤 - 请求{len(missing_ids)}个，实际返回{len(db_entities)}个"
                    )
                    logger.warning(
                        "DEBUG: 建议检查 get_multi_by_ids 方法是否包含 is_published/is_approved 等过滤条件"
                    )

                await self.set_entities_batch(db_entities)
                for entity in db_entities:
                    results[entity.id] = self.schema.from_orm(entity)
            except pybreaker.CircuitBreakerError:
                logger.error(f"数据库熔断器开启，无法批量获取 {self.entity_name}")
            except Exception as e:
                logger.error(
                    "批量数据库查询异常",
                    extra={"error": str(e), "missing_ids": missing_ids},
                )
                # 即使缓存写入失败，也要处理数据库查询结果
                if "db_entities" in locals() and db_entities:
                    logger.info(f"缓存写入失败，但数据库查询成功，处理 {len(db_entities)} 个实体")
                    for entity in db_entities:
                        results[entity.id] = self.schema.from_orm(entity)

        logger.debug(
            "get_entities_batch 完成",
            extra={"entity_ids": entity_ids, "result_keys": list(results.keys())},
        )
        return results

    async def set_entity(self, entity: ModelType, check_version: bool = False) -> None:
        if not self.cache_enabled:
            return
        cache_key = self._get_cache_key(entity.id)
        entity_base = self.schema.from_orm(entity)
        # 使用 warnings=True 来抑制序列化警告
        entity_json = entity_base.model_dump_json(warnings=False)
        new_version = entity.cache_version
        expire_time = self.expire_seconds

        # 添加时间戳元数据
        timestamp = TimeUtils.get_utc_now()
        metadata = {
            "cached_at": TimeUtils.format_for_jwt(timestamp),
            "expires_at": TimeUtils.format_for_jwt(
                TimeUtils.calculate_expiry_seconds(expire_time, timestamp)
            ),
        }

        try:
            if check_version:
                lua_script = """
                local current_version = redis.call('HGET', KEYS[1], 'version')
                if not current_version or tonumber(ARGV[2]) > tonumber(current_version) then
                    redis.call('HSET', KEYS[1], 'data', ARGV[1], 'version', ARGV[2], 'cached_at', ARGV[4], 'expires_at', ARGV[5])
                    redis.call('EXPIRE', KEYS[1], ARGV[3])
                    return 1
                else
                    return 0
                end
                """
                script = redis.redis_master_client.register_script(lua_script)
                updated = await script(
                    keys=[cache_key],
                    args=[
                        entity_json,
                        new_version,
                        expire_time,
                        metadata["cached_at"],
                        metadata["expires_at"],
                    ],
                )
                if updated:
                    logger.info(
                        f"通过版本检查，成功更新 {self.entity_name} 缓存: {cache_key}, version: {new_version}"
                    )
                else:
                    logger.warning(
                        f"版本过旧，跳过 {self.entity_name} 缓存更新: {cache_key}, version: {new_version}"
                    )
            else:
                pipe = redis.redis_master_client.pipeline()
                pipe.hset(
                    cache_key,
                    mapping={
                        "data": entity_json,
                        "version": new_version,
                        "cached_at": metadata["cached_at"],
                        "expires_at": metadata["expires_at"],
                    },
                )
                pipe.expire(cache_key, expire_time)
                await pipe.execute()
                logger.info(
                    f"直接写入 {self.entity_name} 缓存: {cache_key}, version: {new_version}"
                )
        except Exception as e:
            logger.error(f"向Redis写入 {self.entity_name} 缓存失败: {e}, key: {cache_key}")

    async def set_entities_batch(self, entities: list[ModelType]) -> None:
        if not self.cache_enabled or not entities:
            return

        logger.debug("set_entities_batch called with {} entities", len(entities))
        pipe = redis.redis_master_client.pipeline()

        # 批量时间戳
        timestamp = TimeUtils.get_utc_now()
        metadata = {
            "cached_at": TimeUtils.format_for_jwt(timestamp),
            "expires_at": TimeUtils.format_for_jwt(
                TimeUtils.calculate_expiry_seconds(self.expire_seconds, timestamp)
            ),
        }

        for entity in entities:
            cache_key = self._get_cache_key(entity.id)
            logger.debug("Processing entity {} for cache write", entity.id)

            try:
                entity_base = self.schema.from_orm(entity)
                # 使用 warnings=False 来抑制序列化警告
                entity_json = entity_base.model_dump_json(warnings=False)
                version = entity.cache_version

                logger.debug("Entity {} JSON length: {}", entity.id, len(entity_json))
                logger.debug(
                    "Entity {} JSON preview: {}...",
                    entity.id,
                    entity_json[:100],
                )
                logger.debug("Entity {} version: {}", entity.id, version)

                pipe.hset(
                    cache_key,
                    mapping={
                        "data": entity_json,
                        "version": version,
                        "cached_at": metadata["cached_at"],
                        "expires_at": metadata["expires_at"],
                    },
                )
                pipe.expire(cache_key, self.expire_seconds)
                logger.debug("Successfully queued entity {} for cache write", entity.id)
            except Exception as e:
                logger.warning(
                    "Failed to process entity {} for cache: {}",
                    entity.id,
                    e,
                )

        try:
            await pipe.execute()
            logger.debug("Successfully executed pipeline for {} entities", len(entities))
        except Exception as e:
            logger.error("Failed to execute pipeline: {}", e)

        logger.info(f"已批量写入 {len(entities)} 个 {self.entity_name} 缓存")

    async def set_null_cache(self, entity_id: int) -> None:
        if not self.cache_enabled:
            return
        cache_key = self._get_cache_key(entity_id)
        pipe = redis.redis_master_client.pipeline()
        pipe.hset(cache_key, mapping={"data": "null", "version": 0})
        pipe.expire(cache_key, settings.NULL_CACHE_EXPIRE_SECONDS)
        await pipe.execute()
        logger.debug(f"为空 {self.entity_name} ID {entity_id} 写入空缓存: {cache_key}")

    async def invalidate_entity(self, entity_id: int) -> None:
        if not self.cache_enabled:
            return
        cache_key = self._get_cache_key(entity_id)
        try:
            await redis.delete_key(cache_key)
            logger.info(f"{self.entity_name} 缓存已删除: {cache_key}")
        except Exception as e:
            logger.error(f"从Redis删除 {self.entity_name} 缓存失败: {e}, key: {cache_key}")

    # ===== 降级机制相关方法 =====

    async def get_entities_batch_with_fallback(
        self, db: AsyncSession, entity_ids: list[int]
    ) -> dict[int, SchemaType | None]:
        """
        带完整降级机制的批量获取方法

        降级顺序：
        1. 正常缓存流程
        2. 直接数据库查询
        """
        logger.debug(f"{self.entity_name} 开始降级获取，entity_ids: {entity_ids}")

        # 第一层：正常缓存流程
        try:
            results = await self.get_entities_batch(db, entity_ids)
            # 检查结果完整性
            valid_results = sum(1 for v in results.values() if v is not None)
            success_rate = valid_results / len(entity_ids) if entity_ids else 0

            logger.debug(f"{self.entity_name} 缓存成功率: {success_rate:.2%}")

            if success_rate >= 0.2:  # 如果有20%以上的成功率，就认为是可接受的
                return results
            else:
                logger.warning(
                    f"{self.entity_name} 缓存命中率过低({success_rate:.2%})，尝试直接数据库查询"
                )
        except Exception as e:
            logger.error(f"{self.entity_name} 缓存查询失败: {e}")

        # 第二层：直接数据库查询
        try:
            logger.info(f"{self.entity_name} 启用降级：直接数据库查询")
            db_entities = await self._get_entities_batch_from_db(db, entity_ids)

            # 异步写回缓存
            if db_entities:
                asyncio.create_task(self.set_entities_batch(db_entities))

            # 转换为结果字典
            result = {}
            entities_map = {e.id: self.schema.from_orm(e) for e in db_entities}

            for entity_id in entity_ids:
                result[entity_id] = entities_map.get(entity_id)

            logger.info(f"{self.entity_name} 数据库查询返回 {len(db_entities)} 个实体")
            return result

        except Exception as e:
            logger.error(f"{self.entity_name} 数据库查询也失败: {e}")
            # 返回全None结果
            return dict.fromkeys(entity_ids)
