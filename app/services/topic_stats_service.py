"""话题统计服务"""

import json
import logging
from datetime import datetime, timedelta
from typing import Optional

from sqlalchemy.ext.asyncio import AsyncSession

from app import crud
from app.config.post_config import PostConfig
from app.db.redis import (
    delete_key,
    get_key,
    set_key,
    sorted_set_add,
    sorted_set_get_range,
    sorted_set_increment_by,
)
from app.schemas.topic_stats import (
    HotTopic,
    HotTopicsResponse,
    TopicDetail,
    TopicListQuery,
    TrendingTopic,
    TrendingTopicsResponse,
)

logger = logging.getLogger(__name__)


class TopicStatsService:
    """话题统计服务"""

    def __init__(self, config: PostConfig):
        self.config = config
        self.hot_topics_cache_key = "topics:hot"
        self.trending_topics_cache_key = "topics:trending"
        self.topic_detail_cache_prefix = "topic:detail"
        self.cache_expire = config.POST_TRENDING_TOPICS_CACHE_SECONDS

    def _get_topic_detail_cache_key(self, topic: str) -> str:
        """获取话题详情缓存键"""
        return f"{self.topic_detail_cache_prefix}:{topic}"

    async def increment_topic_usage(
        self,
        db: AsyncSession,
        *,
        topic: str,
        likes_delta: int = 0,
        comments_delta: int = 0,
        reposts_delta: int = 0,
        views_delta: int = 0,
    ) -> None:
        """增加话题使用统计"""
        try:
            # 更新数据库统计
            if likes_delta or comments_delta or reposts_delta or views_delta:
                await crud.topic_stats.update_interaction_stats(
                    db,
                    topic=topic,
                    likes_delta=likes_delta,
                    comments_delta=comments_delta,
                    reposts_delta=reposts_delta,
                    views_delta=views_delta,
                    commit=False,
                )
            else:
                # 只是增加帖子数量
                await crud.topic_stats.increment_post_count(db, topic=topic, commit=False)

            # 更新热度分数
            await crud.topic_stats.update_hot_score(db, topic=topic, commit=True)

            # 清除相关缓存
            await self._clear_topic_caches(topic)

        except Exception as e:
            logger.error(f"更新话题统计失败: {topic}, error: {e}")
            raise

    async def decrement_topic_usage(self, db: AsyncSession, *, topic: str) -> None:
        """减少话题使用统计（删除帖子时调用）"""
        try:
            # 更新数据库统计
            await crud.topic_stats.decrement_post_count(db, topic=topic, commit=False)
            
            # 更新热度分数
            await crud.topic_stats.update_hot_score(db, topic=topic, commit=True)

            # 清除相关缓存
            await self._clear_topic_caches(topic)

        except Exception as e:
            logger.error(f"减少话题统计失败: {topic}, error: {e}")
            raise

    async def get_hot_topics(
        self, db: AsyncSession, *, query_params: TopicListQuery
    ) -> HotTopicsResponse:
        """获取热门话题列表"""
        try:
            # 尝试从缓存获取
            cache_key = f"{self.hot_topics_cache_key}:{query_params.sort_by}:{query_params.sort_order}"
            if query_params.min_post_count:
                cache_key += f":min_{query_params.min_post_count}"
            if query_params.days:
                cache_key += f":days_{query_params.days}"

            cached_data = await get_key(cache_key)
            if cached_data:
                try:
                    all_topics = [HotTopic(**item) for item in json.loads(cached_data)]
                    return self._paginate_topics(all_topics, query_params, HotTopicsResponse)
                except (json.JSONDecodeError, TypeError):
                    logger.warning(f"缓存数据格式错误，重新查询: {cache_key}")

            # 从数据库查询
            offset = (query_params.page - 1) * query_params.size
            topics_data = await crud.topic_stats.get_hot_topics(
                db,
                limit=query_params.size * 5,  # 获取更多数据用于缓存
                offset=0,
                min_post_count=query_params.min_post_count,
                days=query_params.days,
            )

            # 转换为响应模型
            hot_topics = []
            for i, stats in enumerate(topics_data):
                hot_topic = HotTopic(
                    topic=stats.topic,
                    post_count=stats.post_count,
                    hot_score=stats.hot_score,
                    trend_score=stats.trend_score,
                    rank=i + 1,
                    rank_change=None,  # TODO: 实现排名变化计算
                )
                hot_topics.append(hot_topic)

            # 缓存结果
            if hot_topics:
                cache_data = [topic.model_dump() for topic in hot_topics]
                await set_key(cache_key, json.dumps(cache_data), expire=self.cache_expire)

            # 分页返回
            return self._paginate_topics(hot_topics, query_params, HotTopicsResponse)

        except Exception as e:
            logger.error(f"获取热门话题失败: {e}")
            raise

    async def get_trending_topics(
        self, db: AsyncSession, *, query_params: TopicListQuery
    ) -> TrendingTopicsResponse:
        """获取趋势话题列表"""
        try:
            # 尝试从缓存获取
            cache_key = f"{self.trending_topics_cache_key}:min_{query_params.min_post_count or 0}"
            cached_data = await get_key(cache_key)
            if cached_data:
                try:
                    all_topics = [TrendingTopic(**item) for item in json.loads(cached_data)]
                    return self._paginate_topics(all_topics, query_params, TrendingTopicsResponse)
                except (json.JSONDecodeError, TypeError):
                    logger.warning(f"缓存数据格式错误，重新查询: {cache_key}")

            # 从数据库查询
            topics_data = await crud.topic_stats.get_trending_topics(
                db,
                limit=query_params.size * 5,  # 获取更多数据用于缓存
                offset=0,
                min_post_count=query_params.min_post_count,
            )

            # 转换为响应模型
            trending_topics = []
            for i, stats in enumerate(topics_data):
                # 计算增长率（简化版本）
                growth_rate = stats.trend_score / max(stats.hot_score, 1.0) if stats.hot_score > 0 else 0.0
                
                trending_topic = TrendingTopic(
                    topic=stats.topic,
                    post_count=stats.post_count,
                    trend_score=stats.trend_score,
                    growth_rate=round(growth_rate, 2),
                    rank=i + 1,
                )
                trending_topics.append(trending_topic)

            # 缓存结果
            if trending_topics:
                cache_data = [topic.model_dump() for topic in trending_topics]
                await set_key(cache_key, json.dumps(cache_data), expire=self.cache_expire)

            # 分页返回
            return self._paginate_topics(trending_topics, query_params, TrendingTopicsResponse)

        except Exception as e:
            logger.error(f"获取趋势话题失败: {e}")
            raise

    async def get_topic_detail(self, db: AsyncSession, *, topic: str) -> Optional[TopicDetail]:
        """获取话题详情"""
        try:
            # 尝试从缓存获取
            cache_key = self._get_topic_detail_cache_key(topic)
            cached_data = await get_key(cache_key)
            if cached_data:
                try:
                    return TopicDetail(**json.loads(cached_data))
                except (json.JSONDecodeError, TypeError):
                    logger.warning(f"话题详情缓存数据格式错误: {topic}")

            # 从数据库查询
            stats = await crud.topic_stats.get_by_topic(db, topic=topic)
            if not stats:
                return None

            # 转换为响应模型
            topic_detail = TopicDetail(
                topic=stats.topic,
                post_count=stats.post_count,
                total_likes=stats.total_likes,
                total_comments=stats.total_comments,
                total_reposts=stats.total_reposts,
                total_views=stats.total_views,
                hot_score=stats.hot_score,
                trend_score=stats.trend_score,
                last_post_at=stats.last_post_at,
                peak_time=stats.peak_time,
                created_at=stats.created_at,
            )

            # 缓存结果
            await set_key(
                cache_key,
                json.dumps(topic_detail.model_dump(), default=str),
                expire=self.cache_expire,
            )

            return topic_detail

        except Exception as e:
            logger.error(f"获取话题详情失败: {topic}, error: {e}")
            raise

    async def update_hot_rankings(self, db: AsyncSession) -> int:
        """更新热门话题排行榜（定时任务调用）"""
        try:
            # 批量更新热度分数
            updated_count = await crud.topic_stats.batch_update_hot_scores(db)
            
            # 获取最新的热门话题
            hot_topics = await crud.topic_stats.get_hot_topics(db, limit=100)
            
            # 更新Redis排行榜
            if hot_topics:
                rankings = {stats.topic: stats.hot_score for stats in hot_topics}
                await sorted_set_add(self.hot_topics_cache_key, rankings)
            
            # 清除相关缓存
            await self._clear_all_topic_caches()
            
            logger.info(f"更新热门话题排行榜完成，更新了 {updated_count} 个话题")
            return updated_count

        except Exception as e:
            logger.error(f"更新热门话题排行榜失败: {e}")
            raise

    async def _clear_topic_caches(self, topic: str) -> None:
        """清除特定话题的相关缓存"""
        try:
            # 清除话题详情缓存
            await delete_key(self._get_topic_detail_cache_key(topic))
            
            # 清除排行榜缓存（通过模式匹配）
            cache_patterns = [
                f"{self.hot_topics_cache_key}:*",
                f"{self.trending_topics_cache_key}:*",
            ]
            for pattern in cache_patterns:
                # 注意：这里简化处理，实际可能需要更精确的缓存清理策略
                await delete_key(pattern.replace("*", ""))

        except Exception as e:
            logger.warning(f"清除话题缓存失败: {topic}, error: {e}")

    async def _clear_all_topic_caches(self) -> None:
        """清除所有话题相关缓存"""
        try:
            cache_keys = [
                self.hot_topics_cache_key,
                self.trending_topics_cache_key,
            ]
            for key in cache_keys:
                await delete_key(key)

        except Exception as e:
            logger.warning(f"清除所有话题缓存失败: {e}")

    def _paginate_topics(self, topics: list, query_params: TopicListQuery, response_class):
        """分页处理话题列表"""
        total = len(topics)
        start = (query_params.page - 1) * query_params.size
        end = start + query_params.size
        page_topics = topics[start:end]
        
        has_next = end < total
        
        return response_class(
            topics=page_topics,
            total=total,
            page=query_params.page,
            size=query_params.size,
            has_next=has_next,
        )
