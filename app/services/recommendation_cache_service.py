import logging
import random

from app.config import settings
from app.db import redis
from app.schemas.recommendation import RecommendationItem

logger = logging.getLogger(__name__)

# 供测试注入的客户端别名；默认回退到 app.db.redis 中的实例
redis_master_client = None
redis_slave_client = None


class RecommendationCacheService:
    """
    封装推荐系统所有与Redis缓存相关的操作。
    - 用户个人推荐队列 (Sorted Set)
    - 结果分页缓存 (String)
    - 用户推荐缓存 (Sorted Set)
    - 用户相似度缓存 (Hash)
    """

    def __init__(self):
        self.cache_enabled = settings.RECOMMENDATION_CACHE_ENABLED
        self.user_queue_expire_seconds = settings.RECOMMENDATION_USER_QUEUE_EXPIRE_SECONDS
        self.result_cache_expire_seconds = settings.RECOMMENDATION_CACHE_EXPIRE_SECONDS
        self.null_cache_expire_seconds = settings.RECOMMENDATION_NULL_CACHE_EXPIRE_SECONDS
        self.cache_jitter_seconds = settings.RECOMMENDATION_CACHE_JITTER_SECONDS
        # 捕获当前可用的 Redis 客户端，便于测试注入
        self._redis_master_client = redis_master_client or redis.redis_master_client
        self._redis_slave_client = redis_slave_client or redis.redis_slave_client
        logger.info(
            f"RecommendationCacheService initialized. User queue TTL: {self.user_queue_expire_seconds}s, "
            f"Result cache TTL: {self.result_cache_expire_seconds}s"
        )

    def _get_master_client(self):
        client = self._redis_master_client or redis_master_client or redis.redis_master_client
        if client is None:
            raise RuntimeError("Redis master client is not initialized")
        return client

    def _get_slave_client(self):
        client = self._redis_slave_client or redis_slave_client or redis.redis_slave_client
        if client is None:
            return self._get_master_client()
        return client

    # --- 用户个人推荐队列 (recs:user:{user_id}) ---

    def _get_user_queue_key(self, user_id: int) -> str:
        """获取用户个人推荐队列的Redis键"""
        return f"recs:user:{user_id}"

    async def get_user_recommendation_queue(
        self, user_id: int, page: int, limit: int
    ) -> list[tuple[str, float]]:
        """从用户的个人推荐队列（Sorted Set）中分页获取内容ID和分数"""
        if not self.cache_enabled:
            return []

        key = self._get_user_queue_key(user_id)
        start = (page - 1) * limit
        end = start + limit - 1

        try:
            # 使用 ZREVRANGE 获取成员和分数
            client = self._get_slave_client()
            items_with_scores = await client.zrevrange(key, start, end, withscores=True)
            # zrevrange 返回的是 bytes, 需要解码
            return [(item.decode("utf-8"), score) for item, score in items_with_scores]
        except Exception as e:
            logger.error(
                f"Failed to get user recommendation queue from Redis for user {user_id}: {e}"
            )
            return []

    async def set_user_recommendation_queue(
        self, user_id: int, recommendations: list[RecommendationItem]
    ):
        """将完整的推荐列表（ID和分数）写入用户的个人推荐队列（Sorted Set）"""
        if not self.cache_enabled or not recommendations:
            return

        key = self._get_user_queue_key(user_id)
        # 构建 ZADD 的映射参数
        mapping = {f"{item.content_type}:{item.content_id}": item.score for item in recommendations}

        try:
            # 使用 pipeline 保证原子性
            client = self._get_slave_client()
            async with client.pipeline() as pipe:
                # 先删除旧的 key，再添加新的
                pipe.delete(key)
                pipe.zadd(key, mapping)
                # 设置TTL
                expire_time = self.user_queue_expire_seconds + random.randint(
                    0, self.cache_jitter_seconds
                )
                pipe.expire(key, expire_time)
                await pipe.execute()
            logger.info(
                f"User recommendation queue for user {user_id} has been set. Items: {len(recommendations)}"
            )
        except Exception as e:
            logger.error(
                f"Failed to set user recommendation queue to Redis for user {user_id}: {e}"
            )

    async def check_user_recommendation_queue_exists(self, user_id: int) -> bool:
        """检查用户的个人推荐队列是否存在"""
        if not self.cache_enabled:
            return False

        key = self._get_user_queue_key(user_id)
        try:
            client = self._get_slave_client()
            return await client.exists(key) > 0
        except Exception as e:
            logger.error(
                f"Failed to check existence of user recommendation queue for user {user_id}: {e}"
            )
            return False

    async def invalidate_user_cache(self, user_id: int) -> None:
        """使用户的推荐缓存失效"""
        if not self.cache_enabled:
            return

        key = self._get_user_queue_key(user_id)
        try:
            client = self._get_master_client()
            await client.delete(key)
            logger.info(f"Recommendation cache invalidated for user {user_id}")
        except Exception as e:
            logger.error(f"Failed to invalidate recommendation cache for user {user_id}: {e}")

    async def clear_user_cache(self, user_id: int) -> None:
        """向后兼容的缓存清理方法"""
        await self.invalidate_user_cache(user_id)

    async def invalidate_content_cache(self, content_type: str, content_id: int) -> None:
        """使指定内容相关的推荐缓存失效"""
        if not self.cache_enabled:
            return

        member = str(content_id)
        hot_key = f"rec_pool:hot:{content_type}"
        latest_key = f"rec_pool:latest:{content_type}"
        similar_key = f"rec_pool:similar:{content_type}:{content_id}"

        try:
            client = self._get_master_client()
            async with client.pipeline() as pipe:
                pipe.zrem(hot_key, member)
                pipe.zrem(latest_key, member)
                pipe.delete(similar_key)
                await pipe.execute()
            logger.info(
                "Recommendation content cache invalidated for %s:%s",
                content_type,
                content_id,
            )
        except Exception as e:
            logger.error(
                "Failed to invalidate recommendation cache for %s:%s: %s",
                content_type,
                content_id,
                e,
            )

    # --- 用户推荐缓存相关方法 ---

    def _get_user_recommendation_key(self, user_id: int, algorithm_type: str = "hybrid") -> str:
        """获取用户推荐缓存的Redis键"""
        return f"user_recs:{user_id}:{algorithm_type}"

    def _get_user_similarity_key(self, user_id1: int, user_id2: int) -> str:
        """获取用户相似度缓存的Redis键"""
        min_id, max_id = min(user_id1, user_id2), max(user_id1, user_id2)
        return f"user_similarity:{min_id}:{max_id}"

    async def get_user_recommendations_cache(
        self, user_id: int, algorithm_type: str, page: int, page_size: int
    ) -> list[tuple[str, float]]:
        """从缓存获取用户推荐结果"""
        if not self.cache_enabled:
            return []

        key = self._get_user_recommendation_key(user_id, algorithm_type)
        start = (page - 1) * page_size
        end = start + page_size - 1

        try:
            client = self._get_slave_client()
            items_with_scores = await client.zrevrange(key, start, end, withscores=True)
            if not items_with_scores and hasattr(client, "get"):
                sentinel_value = await client.get(key)
                if isinstance(sentinel_value, bytes):
                    sentinel_value = sentinel_value.decode("utf-8")
                if sentinel_value == "__EMPTY__":
                    return []
            # 处理Redis返回的数据，可能是bytes或str
            result = []
            for item, score in items_with_scores:
                if isinstance(item, bytes):
                    item_str = item.decode("utf-8")
                else:
                    item_str = str(item)
                result.append((item_str, score))
            return result
        except Exception as e:
            logger.error(f"Failed to get user recommendations cache for user {user_id}: {e}")
            return []

    async def set_user_recommendations_cache(
        self, user_id: int, algorithm_type: str, recommendations: list[RecommendationItem]
    ) -> None:
        """设置用户推荐缓存"""
        if not self.cache_enabled:
            return

        key = self._get_user_recommendation_key(user_id, algorithm_type)
        client = self._get_master_client()
        expire_time = self.result_cache_expire_seconds + random.randint(0, self.cache_jitter_seconds)

        if not recommendations:
            if hasattr(client, "set"):
                await client.set(key, "__EMPTY__", ex=expire_time)
            else:
                await client.delete(key)
            logger.debug(
                "Stored empty recommendation marker for user %s algorithm %s", user_id, algorithm_type
            )
            return

        mapping = {f"user:{rec.content_id}": rec.score for rec in recommendations}

        try:
            async with client.pipeline() as pipe:
                pipe.delete(key)
                pipe.zadd(key, mapping)
                pipe.expire(key, expire_time)
                await pipe.execute()
            logger.info(
                f"User recommendations cache set for user {user_id}, algorithm {algorithm_type}"
            )
        except Exception as e:
            logger.error(f"Failed to set user recommendations cache for user {user_id}: {e}")

    async def invalidate_user_recommendations_cache(self, user_id: int) -> None:
        """使用户推荐缓存失效"""
        if not self.cache_enabled:
            return

        try:
            # 使用模式匹配删除所有用户推荐缓存
            pattern = f"user_recs:{user_id}:*"
            client = self._get_master_client()
            keys_to_delete = await client.keys(pattern)

            if keys_to_delete:
                await client.delete(*keys_to_delete)
            logger.info(f"User recommendations cache invalidated for user {user_id}")
        except Exception as e:
            logger.error(f"Failed to invalidate user recommendations cache for user {user_id}: {e}")

    async def get_user_similarity_cache(
        self, user_id1: int, user_id2: int
    ) -> dict[str, float] | None:
        """从缓存获取用户相似度"""
        if not self.cache_enabled:
            return None

        key = self._get_user_similarity_key(user_id1, user_id2)

        try:
            client = self._get_slave_client()
            cached_data = await client.hgetall(key)
            if cached_data:
                # 处理Redis返回的数据，可能是bytes或str
                result = {}
                for k, v in cached_data.items():
                    if isinstance(k, bytes):
                        key_str = k.decode("utf-8")
                    else:
                        key_str = str(k)
                    result[key_str] = float(v)
                return result
            return None
        except Exception as e:
            logger.error(
                f"Failed to get user similarity cache for users {user_id1}, {user_id2}: {e}"
            )
            return None

    async def set_user_similarity_cache(
        self, user_id1: int, user_id2: int, similarities: dict[str, float]
    ) -> None:
        """设置用户相似度缓存"""
        if not self.cache_enabled or not similarities:
            return

        key = self._get_user_similarity_key(user_id1, user_id2)

        try:
            # 将相似度数据存储为Hash
            client = self._get_master_client()
            await client.hset(key, mapping=similarities)
            # 设置过期时间（6小时）
            await client.expire(key, 3600 * 6)
            logger.info(f"User similarity cache set for users {user_id1}, {user_id2}")
        except Exception as e:
            logger.error(
                f"Failed to set user similarity cache for users {user_id1}, {user_id2}: {e}"
            )

    async def invalidate_user_similarity_cache(self, user_id: int) -> None:
        """使指定用户的所有相似度缓存失效"""
        if not self.cache_enabled:
            return

        try:
            # 查找所有包含该用户ID的相似度缓存键
            pattern = f"user_similarity:*{user_id}*"
            client = self._get_master_client()
            keys = await client.keys(pattern)

            if keys:
                await client.delete(*keys)
            logger.info(f"User similarity cache invalidated for user {user_id}")
        except Exception as e:
            logger.error(f"Failed to invalidate user similarity cache for user {user_id}: {e}")
