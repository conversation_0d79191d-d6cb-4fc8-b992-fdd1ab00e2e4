import logging

from sqlalchemy import create_engine, text
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import Session, sessionmaker

from app.config import settings

logger = logging.getLogger(__name__)

# 创建异步SQLAlchemy引擎
engine = create_async_engine(
    settings.DATABASE_URL.replace("postgresql://", "postgresql+asyncpg://"),
    # 连接池配置 - 优化连接池大小以处理更高并发
    pool_size=20,  # 连接池基础大小
    max_overflow=30,  # 最大溢出连接数，总共可达50个连接
    pool_timeout=30.0,  # 获取连接超时时间(秒)
    pool_pre_ping=True,  # 连接前预检查，确保连接有效性
    pool_recycle=1800,  # 连接回收时间(秒)，避免MySQL的wait_timeout
    # echo=False,  # 设置为True可以在控制台查看SQL语句，用于调试
    future=True,
)

# 创建同步SQLAlchemy引擎（用于Celery任务）
# 需要将数据库URL转换为同步驱动（psycopg2）
# 移除任何异步驱动前缀（如 +asyncpg），然后添加 +psycopg2
sync_db_url = settings.DATABASE_URL
# 移除可能存在的异步驱动前缀
if "postgresql+asyncpg://" in sync_db_url:
    sync_db_url = sync_db_url.replace("postgresql+asyncpg://", "postgresql+psycopg2://")
elif "postgresql://" in sync_db_url:
    sync_db_url = sync_db_url.replace("postgresql://", "postgresql+psycopg2://")
else:
    # 如果已经有其他驱动前缀，需要替换
    import re
    sync_db_url = re.sub(r"postgresql\+\w+://", "postgresql+psycopg2://", sync_db_url)

sync_engine = create_engine(
    sync_db_url,
    # 连接池配置
    pool_size=10,  # 较小的连接池，因为Celery任务通常不需要太多并发
    max_overflow=20,
    pool_timeout=30.0,
    pool_pre_ping=True,
    pool_recycle=1800,
    # echo=False,
    future=True,
)

# 创建异步会话工厂
SessionLocal = sessionmaker(
    engine,
    class_=AsyncSession,
    expire_on_commit=False,
    autocommit=False,
    autoflush=False,
)

# 创建同步会话工厂（用于Celery任务）
SyncSessionLocal = sessionmaker(
    sync_engine,
    class_=Session,
    expire_on_commit=False,
    autocommit=False,
    autoflush=False,
)

# 创建Base类，所有模型都将继承这个类
Base = declarative_base()


async def get_db():
    """获取数据库会话的依赖函数
    Yields:
        Session: 数据库会话实例
    """
    db = SessionLocal()
    try:
        # 验证连接是否有效
        await db.execute(text("SELECT 1"))
        yield db
    except Exception:
        raise
    finally:
        await db.close()
