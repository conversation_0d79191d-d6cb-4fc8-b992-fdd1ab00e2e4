from __future__ import annotations

from typing import Any, Sequence

from sqlalchemy import Select, func, select
from sqlalchemy.ext.asyncio import AsyncSession

from app.crud.base import CRUDBase
from app.models import AdminConfigChangeLog, AdminConfigSetting, AdminFeatureToggle


class CRUDAdminConfigSetting(CRUDBase[AdminConfigSetting, Any, Any]):
    """配置项 CRUD 封装"""

    async def get_by_key(
        self, db: AsyncSession, *, key: str, for_update: bool = False
    ) -> AdminConfigSetting | None:
        stmt: Select = select(AdminConfigSetting).where(AdminConfigSetting.key == key)
        if for_update:
            stmt = stmt.with_for_update()
        return (await db.execute(stmt)).scalar_one_or_none()

    async def list_settings(
        self, db: AsyncSession, *, category: str | None = None
    ) -> Sequence[AdminConfigSetting]:
        stmt = select(AdminConfigSetting)
        if category:
            stmt = stmt.where(AdminConfigSetting.category == category)
        stmt = stmt.order_by(AdminConfigSetting.category, AdminConfigSetting.key)
        return (await db.execute(stmt)).scalars().all()


class CRUDAdminFeatureToggle(CRUDBase[AdminFeatureToggle, Any, Any]):
    """功能开关 CRUD 封装"""

    async def get_by_name(self, db: AsyncSession, *, name: str) -> AdminFeatureToggle | None:
        stmt = select(AdminFeatureToggle).where(AdminFeatureToggle.name == name)
        return (await db.execute(stmt)).scalar_one_or_none()

    async def list_toggles(self, db: AsyncSession) -> Sequence[AdminFeatureToggle]:
        stmt = select(AdminFeatureToggle).order_by(AdminFeatureToggle.name)
        return (await db.execute(stmt)).scalars().all()


class CRUDAdminConfigChangeLog(CRUDBase[AdminConfigChangeLog, Any, Any]):
    """配置变更历史 CRUD"""

    async def list_change_logs(
        self,
        db: AsyncSession,
        *,
        config_key: str | None = None,
        from_date: Any | None = None,
        to_date: Any | None = None,
        offset: int = 0,
        limit: int = 20,
    ) -> Sequence[AdminConfigChangeLog]:
        stmt = select(AdminConfigChangeLog)
        if config_key:
            stmt = stmt.where(AdminConfigChangeLog.config_key == config_key)
        if from_date:
            stmt = stmt.where(AdminConfigChangeLog.changed_at >= from_date)
        if to_date:
            stmt = stmt.where(AdminConfigChangeLog.changed_at <= to_date)
        stmt = (
            stmt.order_by(AdminConfigChangeLog.changed_at.desc())
            .offset(offset)
            .limit(limit)
        )
        return (await db.execute(stmt)).scalars().all()

    async def count_change_logs(
        self,
        db: AsyncSession,
        *,
        config_key: str | None = None,
        from_date: Any | None = None,
        to_date: Any | None = None,
    ) -> int:
        stmt = select(func.count(AdminConfigChangeLog.id))
        if config_key:
            stmt = stmt.where(AdminConfigChangeLog.config_key == config_key)
        if from_date:
            stmt = stmt.where(AdminConfigChangeLog.changed_at >= from_date)
        if to_date:
            stmt = stmt.where(AdminConfigChangeLog.changed_at <= to_date)
        return (await db.execute(stmt)).scalar_one()


admin_config_settings = CRUDAdminConfigSetting(AdminConfigSetting)
admin_feature_toggles = CRUDAdminFeatureToggle(AdminFeatureToggle)
admin_config_change_logs = CRUDAdminConfigChangeLog(AdminConfigChangeLog)
