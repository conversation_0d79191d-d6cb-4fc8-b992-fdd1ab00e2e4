from sqlalchemy import func, select
from sqlalchemy.ext.asyncio import AsyncSession

from app.crud.base import CRUDBase
from app.models.tag import Tag, video_tags
from app.models.video import Video
from app.schemas.tag import TagCreate, TagUpdate


class CRUDTag(CRUDBase[Tag, TagCreate, TagUpdate]):
    """标签的 CRUD 操作"""

    async def get_by_name(self, db: AsyncSession, *, name: str) -> Tag | None:
        result = await db.execute(select(self.model).where(self.model.name == name))
        return result.scalar_one_or_none()

    async def get_or_create(
        self,
        db: AsyncSession,
        *,
        name: str,
        is_default: bool = False,
        content_type: str | None = None,
    ) -> Tag:
        tag = await self.get_by_name(db, name=name)
        if not tag:
            tag = await self.create(
                db,
                obj_in=TagCreate(name=name, is_default=is_default, content_type=content_type),
            )
        return tag

    async def get_or_create_multi(
        self,
        db: AsyncSession,
        *,
        names: list[str],
        is_default: bool = False,
        content_type: str | None = None,
    ) -> list[Tag]:
        tags = []
        for name in names:
            tag = await self.get_or_create(
                db,
                name=name,
                is_default=is_default,
                content_type=content_type,
            )
            tags.append(tag)
        return tags

    async def get_top_tags_in_category(
        self, db: AsyncSession, *, category_id: int, limit: int = 10
    ):
        top_tags_query = (
            select(
                video_tags.c.tag_id,
                func.count(video_tags.c.tag_id).label("tag_count"),
            )
            .join(Video, Video.id == video_tags.c.video_id)
            .filter(Video.category_id == category_id)
            .group_by(video_tags.c.tag_id)
            .order_by(func.count(video_tags.c.tag_id).desc())
            .limit(limit)
        )
        result = await db.execute(top_tags_query)
        top_tag_ids = result.all()

        tag_ids = [item.tag_id for item in top_tag_ids]
        if not tag_ids:
            return []

        tags_query = select(Tag).filter(Tag.id.in_(tag_ids))
        result = await db.execute(tags_query)
        return result.scalars().all()

    async def get_default_tags(
        self,
        db: AsyncSession,
        content_type: str | None = None,
    ) -> list[Tag]:
        query = select(self.model).where(self.model.is_default)
        if content_type:
            query = query.where(self.model.content_type == content_type)
        result = await db.execute(query)
        return result.scalars().all()

    async def get_multi_with_filters(
        self,
        db: AsyncSession,
        *,
        content_type: str | None = None,
        skip: int = 0,
        limit: int = 100,
    ) -> list[Tag]:
        query = select(self.model)
        if content_type is not None:
            query = query.where(self.model.content_type == content_type)
        query = query.order_by(self.model.id.asc())
        query = query.offset(skip).limit(limit)
        result = await db.execute(query)
        return result.scalars().all()


tag = CRUDTag(Tag)
