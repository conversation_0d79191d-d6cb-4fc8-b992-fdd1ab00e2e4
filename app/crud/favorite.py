from datetime import datetime
from typing import Any

from sqlalchemy import and_, func, or_, select
from sqlalchemy.ext.asyncio import AsyncSession

from app.crud.base import CRUDBase
from app.models.article import Article
from app.models.favorite import Favorite
from app.models.video import Video
from app.models.outbox import OutboxMessage
from app.schemas.favorite import FavoriteCreate, FavoriteUpdate


class CRUDFavorite(CRUDBase[Favorite, FavoriteCreate, FavoriteUpdate]):
    """收藏CRUD操作"""

    async def get_by_user_and_content(
        self, db: AsyncSession, *, user_id: int, content_type: str, content_id: int
    ) -> Favorite | None:
        """根据用户和内容获取收藏记录"""
        result = await db.execute(
            select(self.model).where(
                and_(
                    self.model.user_id == user_id,
                    self.model.content_type == content_type,
                    self.model.content_id == content_id,
                )
            )
        )
        return result.scalar_one_or_none()

    async def toggle_favorite(
        self,
        db: AsyncSession,
        *,
        user_id: int,
        content_type: str,
        content_id: int,
        note: str | None = None,
        commit: bool = False,
    ) -> tuple[Favorite, bool]:
        """
        切换收藏状态，并创建统计更新事件。
        注意：此方法现在默认不提交事务。
        """
        existing_favorite = await self.get_by_user_and_content(
            db, user_id=user_id, content_type=content_type, content_id=content_id
        )

        is_new_favorite_or_reactivated = False
        value_change = 0

        if existing_favorite:
            is_currently_active = existing_favorite.is_active
            existing_favorite.is_active = not is_currently_active
            existing_favorite.updated_at = datetime.utcnow()
            if note is not None:
                existing_favorite.note = note
            db.add(existing_favorite)
            is_new_favorite_or_reactivated = existing_favorite.is_active
            value_change = 1 if is_new_favorite_or_reactivated else -1
            favorite_object_to_return = existing_favorite
        else:
            favorite_data = {
                "user_id": user_id,
                "content_type": content_type,
                "content_id": content_id,
                "note": note,
                "is_active": True,
            }
            new_favorite = self.model(**favorite_data)
            db.add(new_favorite)
            is_new_favorite_or_reactivated = True
            value_change = 1
            favorite_object_to_return = new_favorite

        # 为作者创建用户统计更新事件
        stats_payload = await self._build_user_stats_payload(
            db=db,
            content_type=content_type,
            content_id=content_id,
            value_change=value_change,
        )
        if stats_payload:
            db.add(OutboxMessage(topic="stats.update", payload=stats_payload))

        if commit:
            await db.commit()
            await db.refresh(favorite_object_to_return)

        return favorite_object_to_return, is_new_favorite_or_reactivated

    async def _build_user_stats_payload(
        self,
        *,
        db: AsyncSession,
        content_type: str,
        content_id: int,
        value_change: int,
    ) -> dict[str, int | str] | None:
        """
        构建用户统计更新所需的payload，仅在能定位作者且有有效增量时返回。
        """
        if value_change == 0:
            return None

        author_id = await self._resolve_content_author_id(
            db=db, content_type=content_type, content_id=content_id
        )
        if author_id is None:
            return None

        return {
            "entity_type": "user",
            "entity_id": author_id,
            "field_name": "total_favorites_count",
            "value_change": value_change,
        }

    async def _resolve_content_author_id(
        self, *, db: AsyncSession, content_type: str, content_id: int
    ) -> int | None:
        """
        根据内容类型查询作者ID，目前支持文章、视频。
        """
        if content_type == "article":
            result = await db.execute(select(Article.author_id).where(Article.id == content_id))
        elif content_type == "video":
            result = await db.execute(select(Video.author_id).where(Video.id == content_id))
        else:
            return None

        return result.scalar_one_or_none()

    async def get_content_favorite_count(
        self, db: AsyncSession, *, content_type: str, content_id: int
    ) -> int:
        """获取内容的点赞数量"""
        result = await db.execute(
            select(func.count(self.model.id)).where(
                and_(
                    self.model.content_type == content_type,
                    self.model.content_id == content_id,
                    self.model.is_active,
                )
            )
        )
        return result.scalar() or 0

    async def get_user_favorite_content_ids(
        self, db: AsyncSession, *, user_id: int, content_type: str
    ) -> list[int]:
        """获取用户收藏的特定类型内容的ID列表"""
        result = await db.execute(
            select(self.model.content_id).where(
                and_(
                    self.model.user_id == user_id,
                    self.model.content_type == content_type,
                    self.model.is_active,
                )
            )
        )
        return result.scalars().all()

    async def is_favorited_by_user(
        self, db: AsyncSession, *, user_id: int, content_type: str, content_id: int
    ) -> bool:
        """检查用户是否已点赞"""
        favorite = await self.get_by_user_and_content(
            db, user_id=user_id, content_type=content_type, content_id=content_id
        )
        return favorite is not None and favorite.is_active

    async def get_content_favorites_batch(
        self, db: AsyncSession, *, content_items: list[tuple[str, int]], user_id: int | None = None
    ) -> dict[tuple[str, int], dict[str, Any]]:
        """批量获取内容的收藏信息

        Args:
            content_items: [(content_type, content_id), ...] 列表
            user_id: 可选的用户ID，用于检查用户是否已收藏

        Returns:
            {(content_type, content_id): {"favorite_count": int, "is_favorited": bool}}
        """
        if not content_items:
            return {}

        # 构建查询条件
        conditions = []
        for content_type, content_id in content_items:
            conditions.append(
                and_(
                    self.model.content_type == content_type,
                    self.model.content_id == content_id,
                )
            )

        # 获取收藏统计
        favorite_counts_result = await db.execute(
            select(
                self.model.content_type,
                self.model.content_id,
                func.count(self.model.id).label("favorite_count"),
            )
            .where(
                and_(
                    self.model.is_active,
                    or_(*conditions),
                )
            )
            .group_by(self.model.content_type, self.model.content_id)
        )
        favorite_counts = favorite_counts_result.all()

        # 构建结果字典
        result = {}
        for content_type, content_id in content_items:
            result[(content_type, content_id)] = {
                "favorite_count": 0,
                "is_favorited": False,
            }

        # 填充收藏数量
        for content_type, content_id, count in favorite_counts:
            result[(content_type, content_id)]["favorite_count"] = count

        # 如果提供了用户ID，检查用户收藏状态
        if user_id:
            user_favorites_result = await db.execute(
                select(self.model.content_type, self.model.content_id).where(
                    and_(
                        self.model.user_id == user_id,
                        self.model.is_active,
                        or_(*conditions),
                    )
                )
            )
            user_favorites = user_favorites_result.all()

            for content_type, content_id in user_favorites:
                if (content_type, content_id) in result:
                    result[(content_type, content_id)]["is_favorited"] = True

        return result


favorite = CRUDFavorite(Favorite)
