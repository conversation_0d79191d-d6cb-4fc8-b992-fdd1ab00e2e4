"""话题统计CRUD操作"""

from datetime import datetime, timedelta
from typing import Optional

from sqlalchemy import and_, desc, func, select
from sqlalchemy.ext.asyncio import AsyncSession

from app.crud.base import CRUDBase
from app.models.topic_stats import TopicStats, TopicTrend
from app.schemas.topic_stats import (
    TopicStatsCreate,
    TopicStatsUpdate,
    TopicTrendCreate,
)


class CRUDTopicStats(CRUDBase[TopicStats, TopicStatsCreate, TopicStatsUpdate]):
    """话题统计CRUD操作类"""

    async def get_by_topic(self, db: AsyncSession, *, topic: str) -> Optional[TopicStats]:
        """根据话题获取统计信息"""
        result = await db.execute(select(TopicStats).where(TopicStats.topic == topic))
        return result.scalar_one_or_none()

    async def get_or_create_by_topic(
        self, db: AsyncSession, *, topic: str, commit: bool = True
    ) -> TopicStats:
        """获取或创建话题统计记录"""
        stats = await self.get_by_topic(db, topic=topic)
        if not stats:
            create_data = TopicStatsCreate(topic=topic, post_count=0)
            stats = await self.create(db, obj_in=create_data, commit=commit)
        return stats

    async def increment_post_count(
        self, db: AsyncSession, *, topic: str, commit: bool = True
    ) -> TopicStats:
        """增加话题的帖子数量"""
        stats = await self.get_or_create_by_topic(db, topic=topic, commit=False)
        stats.post_count += 1
        stats.last_post_at = datetime.utcnow()
        
        if commit:
            await db.commit()
            await db.refresh(stats)
        else:
            await db.flush()
        
        return stats

    async def decrement_post_count(
        self, db: AsyncSession, *, topic: str, commit: bool = True
    ) -> Optional[TopicStats]:
        """减少话题的帖子数量"""
        stats = await self.get_by_topic(db, topic=topic)
        if not stats:
            return None
        
        stats.post_count = max(0, stats.post_count - 1)
        
        if commit:
            await db.commit()
            await db.refresh(stats)
        else:
            await db.flush()
        
        return stats

    async def update_interaction_stats(
        self,
        db: AsyncSession,
        *,
        topic: str,
        likes_delta: int = 0,
        comments_delta: int = 0,
        reposts_delta: int = 0,
        views_delta: int = 0,
        commit: bool = True,
    ) -> TopicStats:
        """更新话题的互动统计"""
        stats = await self.get_or_create_by_topic(db, topic=topic, commit=False)
        
        stats.total_likes = max(0, stats.total_likes + likes_delta)
        stats.total_comments = max(0, stats.total_comments + comments_delta)
        stats.total_reposts = max(0, stats.total_reposts + reposts_delta)
        stats.total_views = max(0, stats.total_views + views_delta)
        
        if commit:
            await db.commit()
            await db.refresh(stats)
        else:
            await db.flush()
        
        return stats

    async def get_hot_topics(
        self,
        db: AsyncSession,
        *,
        limit: int = 20,
        offset: int = 0,
        min_post_count: Optional[int] = None,
        days: Optional[int] = None,
    ) -> list[TopicStats]:
        """获取热门话题列表"""
        query = select(TopicStats)
        
        # 过滤条件
        if min_post_count:
            query = query.where(TopicStats.post_count >= min_post_count)
        
        if days:
            since_date = datetime.utcnow() - timedelta(days=days)
            query = query.where(TopicStats.last_post_at >= since_date)
        
        # 按热度分数排序
        query = query.order_by(desc(TopicStats.hot_score)).offset(offset).limit(limit)
        
        result = await db.execute(query)
        return result.scalars().all()

    async def get_trending_topics(
        self,
        db: AsyncSession,
        *,
        limit: int = 20,
        offset: int = 0,
        min_post_count: Optional[int] = None,
    ) -> list[TopicStats]:
        """获取趋势话题列表（按趋势分数排序）"""
        query = select(TopicStats)
        
        if min_post_count:
            query = query.where(TopicStats.post_count >= min_post_count)
        
        # 按趋势分数排序
        query = query.order_by(desc(TopicStats.trend_score)).offset(offset).limit(limit)
        
        result = await db.execute(query)
        return result.scalars().all()

    async def calculate_hot_score(
        self,
        db: AsyncSession,
        *,
        topic: str,
        like_weight: float = 1.0,
        comment_weight: float = 2.0,
        repost_weight: float = 3.0,
        view_weight: float = 0.1,
        time_decay: float = 0.8,
    ) -> float:
        """计算话题热度分数"""
        stats = await self.get_by_topic(db, topic=topic)
        if not stats:
            return 0.0
        
        # 基础分数：各种互动的加权和
        base_score = (
            stats.total_likes * like_weight +
            stats.total_comments * comment_weight +
            stats.total_reposts * repost_weight +
            stats.total_views * view_weight
        )
        
        # 时间衰减：根据最后发帖时间计算衰减
        if stats.last_post_at:
            hours_since_last_post = (datetime.utcnow() - stats.last_post_at).total_seconds() / 3600
            time_factor = time_decay ** (hours_since_last_post / 24)  # 按天衰减
        else:
            time_factor = 0.1  # 如果没有最后发帖时间，给一个很小的因子
        
        hot_score = base_score * time_factor
        return round(hot_score, 2)

    async def update_hot_score(
        self, db: AsyncSession, *, topic: str, commit: bool = True
    ) -> Optional[TopicStats]:
        """更新话题热度分数"""
        stats = await self.get_by_topic(db, topic=topic)
        if not stats:
            return None
        
        new_score = await self.calculate_hot_score(db, topic=topic)
        stats.hot_score = new_score
        
        if commit:
            await db.commit()
            await db.refresh(stats)
        else:
            await db.flush()
        
        return stats

    async def batch_update_hot_scores(self, db: AsyncSession) -> int:
        """批量更新所有话题的热度分数"""
        # 获取所有有帖子的话题
        result = await db.execute(
            select(TopicStats).where(TopicStats.post_count > 0)
        )
        all_stats = result.scalars().all()
        
        updated_count = 0
        for stats in all_stats:
            new_score = await self.calculate_hot_score(db, topic=stats.topic)
            if abs(stats.hot_score - new_score) > 0.01:  # 只有变化较大时才更新
                stats.hot_score = new_score
                updated_count += 1
        
        await db.commit()
        return updated_count

    async def get_topic_count(
        self,
        db: AsyncSession,
        *,
        min_post_count: Optional[int] = None,
        days: Optional[int] = None,
    ) -> int:
        """获取话题总数"""
        query = select(func.count(TopicStats.id))
        
        if min_post_count:
            query = query.where(TopicStats.post_count >= min_post_count)
        
        if days:
            since_date = datetime.utcnow() - timedelta(days=days)
            query = query.where(TopicStats.last_post_at >= since_date)
        
        result = await db.execute(query)
        return result.scalar() or 0


class CRUDTopicTrend(CRUDBase[TopicTrend, TopicTrendCreate, None]):
    """话题趋势CRUD操作类"""

    async def get_topic_trends(
        self,
        db: AsyncSession,
        *,
        topic: str,
        period_type: str = "day",
        days: int = 7,
    ) -> list[TopicTrend]:
        """获取话题的趋势数据"""
        since_date = datetime.utcnow() - timedelta(days=days)
        
        query = select(TopicTrend).where(
            and_(
                TopicTrend.topic == topic,
                TopicTrend.period_type == period_type,
                TopicTrend.period_start >= since_date,
            )
        ).order_by(TopicTrend.period_start)
        
        result = await db.execute(query)
        return result.scalars().all()

    async def create_or_update_trend(
        self,
        db: AsyncSession,
        *,
        topic: str,
        period_type: str,
        period_start: datetime,
        **stats_data,
    ) -> TopicTrend:
        """创建或更新趋势数据"""
        # 查找现有记录
        query = select(TopicTrend).where(
            and_(
                TopicTrend.topic == topic,
                TopicTrend.period_type == period_type,
                TopicTrend.period_start == period_start,
            )
        )
        result = await db.execute(query)
        trend = result.scalar_one_or_none()
        
        if trend:
            # 更新现有记录
            for key, value in stats_data.items():
                if hasattr(trend, key):
                    setattr(trend, key, getattr(trend, key, 0) + value)
        else:
            # 创建新记录
            create_data = TopicTrendCreate(
                topic=topic,
                period_type=period_type,
                period_start=period_start,
                **stats_data,
            )
            trend = await self.create(db, obj_in=create_data, commit=False)
        
        await db.commit()
        await db.refresh(trend)
        return trend


# 创建全局实例
topic_stats = CRUDTopicStats(TopicStats)
topic_trend = CRUDTopicTrend(TopicTrend)
