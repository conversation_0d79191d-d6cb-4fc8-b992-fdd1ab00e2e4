"""沸点CRUD操作"""

from types import SimpleNamespace

from sqlalchemy import and_, desc, func, or_, select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from app.crud.base import CRUDBase
from app.models.post import (
    Post,
    PostMedia,
    PostMention,
    PostPollVote,
    PostStatus,
    PostType,
    PostVisibility,
)
from app.models.user import user_follow
from app.schemas.post import (
    PostCreate,
    PostDraftSave,
    PostMediaCreate,
    PostMentionCreate,
    PostPollVoteCreate,
    PostUpdate,
)


class CRUDPost(CRUDBase[Post, PostCreate, PostUpdate]):
    """沸点CRUD操作类"""

    async def get_by_id_for_author(
        self,
        db: AsyncSession,
        *,
        post_id: int,
        author_id: int,
    ) -> Post | None:
        """根据ID获取指定作者的沸点"""
        query = select(Post).where(Post.id == post_id, Post.author_id == author_id)
        result = await db.execute(query)
        return result.scalar_one_or_none()

    async def get_multi_by_ids(
        self,
        db: AsyncSession,
        *,
        ids: list[int],
    ) -> list[Post]:
        """根据ID列表批量获取沸点"""
        if not ids:
            return []

        query = select(Post).where(Post.id.in_(ids), Post.status != PostStatus.DELETED)
        result = await db.execute(query)
        return result.scalars().all()

    async def get_posts_by_author(
        self,
        db: AsyncSession,
        *,
        author_id: int,
        limit: int = 20,
        offset: int = 0,
    ) -> list[Post]:
        """获取指定作者的沸点列表"""
        query = select(Post).where(Post.author_id == author_id)

        query = query.order_by(desc(Post.created_at)).offset(offset).limit(limit)

        result = await db.execute(query)
        return result.scalars().all()

    async def get_drafts_by_author(
        self,
        db: AsyncSession,
        *,
        author_id: int,
        limit: int = 20,
        offset: int = 0,
    ) -> list[Post]:
        """获取作者的草稿列表"""
        query = (
            select(Post)
            .options(selectinload(Post.media))
            .where(
                Post.author_id == author_id,
                Post.status == PostStatus.DRAFT,
            )
            .order_by(desc(Post.updated_at))
            .offset(offset)
            .limit(limit)
        )
        result = await db.execute(query)
        return result.scalars().all()

    async def create_draft(
        self,
        db: AsyncSession,
        *,
        author_id: int,
        draft_in: PostDraftSave,
    ) -> Post:
        """保存新的草稿"""
        data = draft_in.model_dump(exclude_unset=True)
        data.pop("media_files", None)
        data.update(
            {
                "author_id": author_id,
                "status": PostStatus.DRAFT,
                "visibility": data.get("visibility", PostVisibility.PRIVATE),
                "post_type": data.get("post_type", PostType.TEXT),
            }
        )
        db_obj = Post(**data)
        db.add(db_obj)
        await db.flush()
        return db_obj

    async def update_draft(
        self,
        db: AsyncSession,
        *,
        draft: Post,
        draft_in: PostDraftSave,
    ) -> Post:
        """更新草稿"""
        update_data = draft_in.model_dump(exclude_unset=True, exclude={"id"})
        update_data.pop("media_files", None)
        if "visibility" not in update_data and draft.visibility is None:
            update_data["visibility"] = PostVisibility.PRIVATE
        if "post_type" not in update_data and draft.post_type is None:
            update_data["post_type"] = PostType.TEXT
        for field, value in update_data.items():
            setattr(draft, field, value)
        draft.cache_version += 1
        db.add(draft)
        await db.flush()
        return draft

    async def delete_draft(
        self,
        db: AsyncSession,
        *,
        draft: Post,
        hard_delete: bool = False,
    ) -> None:
        """删除草稿"""
        if hard_delete:
            await db.delete(draft)
        else:
            draft.status = PostStatus.DELETED
            draft.cache_version += 1
            db.add(draft)
        await db.flush()

    async def get_posts_by_topic(
        self,
        db: AsyncSession,
        *,
        topic: str,
        limit: int = 20,
        offset: int = 0,
    ) -> list[Post]:
        """获取指定话题的沸点列表"""
        query = (
            select(Post)
            .where(
                and_(
                    Post.topic == topic,
                    Post.status == PostStatus.PUBLISHED,
                    Post.visibility == PostVisibility.PUBLIC,
                )
            )
            .order_by(desc(Post.created_at))
            .offset(offset)
            .limit(limit)
        )

        result = await db.execute(query)
        return result.scalars().all()

    async def get_hot_posts(
        self,
        db: AsyncSession,
        *,
        limit: int = 20,
        offset: int = 0,
    ) -> list[Post]:
        """获取热门沸点列表"""
        query = (
            select(Post)
            .where(
                and_(
                    Post.status == PostStatus.PUBLISHED,
                    Post.visibility == PostVisibility.PUBLIC,
                    Post.is_hot == True,
                )
            )
            .order_by(desc(Post.hot_score), desc(Post.created_at))
            .offset(offset)
            .limit(limit)
        )

        result = await db.execute(query)
        return result.scalars().all()

    async def get_following_posts(
        self,
        db: AsyncSession,
        *,
        user_id: int,
        limit: int = 20,
        offset: int = 0,
    ) -> list[Post]:
        """获取关注用户的沸点列表"""
        # 子查询：获取用户关注的所有用户ID
        following_subquery = select(user_follow.c.followed_id).where(
            user_follow.c.follower_id == user_id
        )

        query = (
            select(Post)
            .where(
                and_(
                    Post.author_id.in_(following_subquery),
                    Post.status == PostStatus.PUBLISHED,
                    or_(
                        Post.visibility == PostVisibility.PUBLIC,
                        Post.visibility == PostVisibility.FOLLOWERS,
                    ),
                )
            )
            .order_by(desc(Post.created_at))
            .offset(offset)
            .limit(limit)
        )

        result = await db.execute(query)
        return result.scalars().all()

    async def get_posts_with_filters(
        self,
        db: AsyncSession,
        *,
        author_id: int | None = None,
        post_type: str | None = None,
        topic: str | None = None,
        visibility: str | None = None,
        is_hot: bool | None = None,
        limit: int = 20,
        offset: int = 0,
        sort_by: str = "created_at",
        sort_order: str = "desc",
    ) -> list[Post]:
        """根据过滤条件获取沸点列表"""
        query = select(Post).where(Post.status == PostStatus.PUBLISHED)

        # 应用过滤条件
        if author_id:
            query = query.where(Post.author_id == author_id)

        if post_type:
            query = query.where(Post.post_type == post_type)

        if topic:
            query = query.where(Post.topic == topic)

        if visibility:
            query = query.where(Post.visibility == visibility)
        else:
            # 默认只显示公开的沸点
            query = query.where(Post.visibility == PostVisibility.PUBLIC)

        if is_hot is not None:
            query = query.where(Post.is_hot == is_hot)

        # 应用排序
        if sort_order.lower() == "desc":
            if sort_by == "hot_score":
                query = query.order_by(desc(Post.hot_score), desc(Post.created_at))
            elif sort_by == "like_count":
                query = query.order_by(desc(Post.like_count), desc(Post.created_at))
            elif sort_by == "comment_count":
                query = query.order_by(desc(Post.comment_count), desc(Post.created_at))
            else:  # created_at
                query = query.order_by(desc(Post.created_at))
        else:
            if sort_by == "hot_score":
                query = query.order_by(Post.hot_score, Post.created_at)
            elif sort_by == "like_count":
                query = query.order_by(Post.like_count, Post.created_at)
            elif sort_by == "comment_count":
                query = query.order_by(Post.comment_count, Post.created_at)
            else:  # created_at
                query = query.order_by(Post.created_at)

        query = query.offset(offset).limit(limit)

        result = await db.execute(query)
        return result.scalars().all()

    async def count_posts_with_filters(
        self,
        db: AsyncSession,
        *,
        author_id: int | None = None,
        post_type: str | None = None,
        topic: str | None = None,
        visibility: str | None = None,
        is_hot: bool | None = None,
    ) -> int:
        """统计符合过滤条件的沸点数量"""
        query = select(func.count()).select_from(Post).where(Post.status == PostStatus.PUBLISHED)

        if author_id:
            query = query.where(Post.author_id == author_id)

        if post_type:
            query = query.where(Post.post_type == post_type)

        if topic:
            query = query.where(Post.topic == topic)

        if visibility:
            query = query.where(Post.visibility == visibility)
        else:
            query = query.where(Post.visibility == PostVisibility.PUBLIC)

        if is_hot is not None:
            query = query.where(Post.is_hot == is_hot)

        result = await db.execute(query)
        count = result.scalar_one()
        return count or 0

    async def count_following_posts(
        self,
        db: AsyncSession,
        *,
        user_id: int,
    ) -> int:
        """统计关注用户的沸点数量"""
        following_subquery = select(user_follow.c.followed_id).where(
            user_follow.c.follower_id == user_id
        )

        query = (
            select(func.count())
            .select_from(Post)
            .where(
                and_(
                    Post.author_id.in_(following_subquery),
                    Post.status == PostStatus.PUBLISHED,
                    or_(
                        Post.visibility == PostVisibility.PUBLIC,
                        Post.visibility == PostVisibility.FOLLOWERS,
                    ),
                )
            )
        )

        result = await db.execute(query)
        count = result.scalar_one()
        return count or 0

    async def get_post_reposts(
        self,
        db: AsyncSession,
        *,
        original_post_id: int,
        limit: int = 20,
        offset: int = 0,
    ) -> list[Post]:
        """获取沸点的转发列表"""
        query = (
            select(Post)
            .where(
                and_(
                    Post.original_post_id == original_post_id,
                    Post.status == PostStatus.PUBLISHED,
                    Post.visibility == PostVisibility.PUBLIC,
                )
            )
            .order_by(desc(Post.created_at))
            .offset(offset)
            .limit(limit)
        )

        result = await db.execute(query)
        return result.scalars().all()

    async def get_with_relations(
        self,
        db: AsyncSession,
        *,
        post_id: int,
    ) -> Post | None:
        """获取包含关联关系的沸点"""
        query = (
            select(Post)
            .options(
                selectinload(Post.author),
                selectinload(Post.media),
                selectinload(Post.mentions),
                selectinload(Post.poll_votes),
                selectinload(Post.original_post),
            )
            .where(Post.id == post_id)
        )

        result = await db.execute(query)
        return result.scalar_one_or_none()

    async def update_stats(
        self,
        db: AsyncSession,
        *,
        post_id: int,
        like_count: int | None = None,
        comment_count: int | None = None,
        repost_count: int | None = None,
        view_count: int | None = None,
    ) -> Post | None:
        """更新沸点统计数据"""
        post = await self.get(db, id=post_id)
        if not post:
            return None

        if like_count is not None:
            post.like_count = like_count
        if comment_count is not None:
            post.comment_count = comment_count
        if repost_count is not None:
            post.repost_count = repost_count
        if view_count is not None:
            post.view_count = view_count

        # 更新缓存版本
        post.cache_version += 1

        await db.commit()
        await db.refresh(post)
        return post

    async def update_hot_score(
        self,
        db: AsyncSession,
        *,
        post_id: int,
        hot_score: int,
        is_hot: bool,
    ) -> Post | None:
        """更新沸点热度分数"""
        post = await self.get(db, id=post_id)
        if not post:
            return None

        post.hot_score = hot_score
        post.is_hot = is_hot
        post.cache_version += 1

        await db.commit()
        await db.refresh(post)
        return post

    async def delete(
        self,
        db: AsyncSession,
        *,
        post_id: int,
        commit: bool = True,
    ) -> SimpleNamespace | None:
        """物理删除沸点"""
        post = await self.get(db, id=post_id)
        if not post:
            return None

        cached_topic = post.topic
        cached_author_id = post.author_id

        await db.delete(post)

        if commit:
            await db.commit()
        else:
            await db.flush()

        # 返回一个包含必要字段的轻量对象，便于后续逻辑复用
        return SimpleNamespace(
            id=post_id,
            author_id=cached_author_id,
            topic=cached_topic,
        )

    async def create_post_media(
        self, db: AsyncSession, *, obj_in: PostMediaCreate, commit: bool = True
    ) -> PostMedia:
        """创建沸点媒体文件记录"""
        db_obj = PostMedia(**obj_in.model_dump())
        db.add(db_obj)
        if commit:
            await db.commit()
            await db.refresh(db_obj)
        else:
            await db.flush()
        return db_obj

    async def get_media_by_post_ids(
        self,
        db: AsyncSession,
        *,
        post_ids: list[int],
    ) -> list[PostMedia]:
        """按帖子ID批量获取媒体记录，保持原始顺序"""
        if not post_ids:
            return []

        query = (
            select(PostMedia)
            .where(PostMedia.post_id.in_(post_ids))
            .order_by(PostMedia.post_id, PostMedia.sort_order, PostMedia.id)
        )
        result = await db.execute(query)
        return result.scalars().all()

    async def create_post_mention(
        self, db: AsyncSession, *, obj_in: PostMentionCreate, commit: bool = True
    ) -> PostMention:
        """创建沸点@提及记录"""
        db_obj = PostMention(**obj_in.model_dump())
        db.add(db_obj)
        if commit:
            await db.commit()
            await db.refresh(db_obj)
        else:
            await db.flush()
        return db_obj

    async def create_post_poll_vote(
        self, db: AsyncSession, *, obj_in: PostPollVoteCreate, commit: bool = True
    ) -> PostPollVote:
        """创建沸点投票记录"""
        db_obj = PostPollVote(**obj_in.model_dump())
        db.add(db_obj)
        if commit:
            await db.commit()
            await db.refresh(db_obj)
        else:
            await db.flush()
        return db_obj

    async def get_poll_vote_counts(
        self,
        db: AsyncSession,
        *,
        post_ids: list[int],
    ) -> dict[int, dict[int, int]]:
        """批量统计投票选项票数"""

        if not post_ids:
            return {}

        query = (
            select(
                PostPollVote.post_id,
                PostPollVote.option_index,
                func.count().label("vote_count"),
            )
            .where(PostPollVote.post_id.in_(post_ids))
            .group_by(PostPollVote.post_id, PostPollVote.option_index)
        )

        result = await db.execute(query)
        rows = result.all()

        stats: dict[int, dict[int, int]] = {}
        for post_id, option_index, vote_count in rows:
            stats.setdefault(post_id, {})[option_index] = vote_count

        return stats

    async def get_user_poll_choices(
        self,
        db: AsyncSession,
        *,
        post_ids: list[int],
        user_id: int,
    ) -> dict[int, list[int]]:
        """获取用户在指定帖子上的投票选项"""

        if not post_ids:
            return {}

        query = (
            select(PostPollVote.post_id, PostPollVote.option_index)
            .where(
                PostPollVote.post_id.in_(post_ids),
                PostPollVote.user_id == user_id,
            )
            .order_by(PostPollVote.created_at)
        )

        result = await db.execute(query)
        rows = result.all()

        user_choices: dict[int, list[int]] = {}
        for post_id, option_index in rows:
            user_choices.setdefault(post_id, []).append(option_index)

        return user_choices


# 创建实例
post = CRUDPost(Post)
