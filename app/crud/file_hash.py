from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from app.crud.base import CRUDBase
from app.models.file_hash import FileHash
from app.schemas.file_hash import FileHashCreate, FileHashUpdate


class CRUDFileHash(CRUDBase[FileHash, FileHashCreate, FileHashUpdate]):
    async def get_by_hash(self, db: AsyncSession, *, file_hash: str) -> FileHash | None:
        """根据哈希值获取文件记录"""
        result = await db.execute(select(self.model).where(self.model.file_hash == file_hash))
        return result.scalar_one_or_none()

    async def get_by_path(self, db: AsyncSession, *, file_path: str) -> FileHash | None:
        """根据文件路径获取文件记录"""
        result = await db.execute(select(self.model).where(self.model.file_path == file_path))
        return result.scalar_one_or_none()


file_hash = CRUDFileHash(FileHash)
