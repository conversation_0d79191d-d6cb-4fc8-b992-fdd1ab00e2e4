"""
搜索相关的 CRUD 操作

包括：
- 搜索历史记录管理
- 搜索统计数据管理
- 搜索建议管理
"""

from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta, date
from sqlalchemy import select, func, and_, or_, delete
from sqlalchemy.ext.asyncio import AsyncSession

from app.models.search_history import SearchHistory
from app.models.search_stats import SearchKeywordStats, SearchSuggestion
from app.core.logging import logger


class CRUDSearchHistory:
    """搜索历史 CRUD"""
    
    @staticmethod
    async def create(
        db: AsyncSession,
        *,
        user_id: Optional[int],
        query: str,
        content_type: str,
        filters: Optional[Dict[str, Any]] = None,
        result_count: int = 0,
        session_id: Optional[str] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
    ) -> SearchHistory:
        """创建搜索历史记录"""
        history = SearchHistory(
            user_id=user_id,
            query=query,
            content_type=content_type,
            filters=filters,
            result_count=result_count,
            session_id=session_id,
            ip_address=ip_address,
            user_agent=user_agent,
        )
        db.add(history)
        await db.commit()
        await db.refresh(history)
        logger.info(f"创建搜索历史: user_id={user_id}, query='{query}'")
        return history
    
    @staticmethod
    async def get_user_history(
        db: AsyncSession,
        user_id: int,
        content_type: Optional[str] = None,
        limit: int = 20,
    ) -> List[SearchHistory]:
        """获取用户搜索历史"""
        stmt = select(SearchHistory).where(
            SearchHistory.user_id == user_id
        )
        
        if content_type and content_type != "all":
            stmt = stmt.where(SearchHistory.content_type == content_type)
        
        stmt = stmt.order_by(SearchHistory.created_at.desc()).limit(limit)
        
        result = await db.execute(stmt)
        return result.scalars().all()
    
    @staticmethod
    async def delete_by_id(
        db: AsyncSession,
        history_id: int,
        user_id: int,
    ) -> bool:
        """删除指定的搜索历史"""
        stmt = delete(SearchHistory).where(
            and_(
                SearchHistory.id == history_id,
                SearchHistory.user_id == user_id
            )
        )
        result = await db.execute(stmt)
        await db.commit()
        return result.rowcount > 0
    
    @staticmethod
    async def delete_user_history(
        db: AsyncSession,
        user_id: int,
    ) -> int:
        """清空用户的所有搜索历史"""
        stmt = delete(SearchHistory).where(SearchHistory.user_id == user_id)
        result = await db.execute(stmt)
        await db.commit()
        logger.info(f"清空用户搜索历史: user_id={user_id}, 删除数={result.rowcount}")
        return result.rowcount
    
    @staticmethod
    async def update_clicked_result(
        db: AsyncSession,
        history_id: int,
        clicked_result_id: int,
        clicked_result_position: int,
    ) -> bool:
        """更新点击的结果"""
        stmt = select(SearchHistory).where(SearchHistory.id == history_id)
        result = await db.execute(stmt)
        history = result.scalar_one_or_none()
        
        if history:
            history.clicked_result_id = clicked_result_id
            history.clicked_result_position = clicked_result_position
            await db.commit()
            return True
        return False
    
    @staticmethod
    async def cleanup_old_records(
        db: AsyncSession,
        days: int = 90,
    ) -> int:
        """清理过期的搜索历史（超过指定天数）"""
        cutoff_date = datetime.now() - timedelta(days=days)
        stmt = delete(SearchHistory).where(SearchHistory.created_at < cutoff_date)
        result = await db.execute(stmt)
        await db.commit()
        logger.info(f"清理过期搜索历史: 删除数={result.rowcount}")
        return result.rowcount


class CRUDSearchStats:
    """搜索统计 CRUD"""
    
    @staticmethod
    async def get_or_create_keyword_stats(
        db: AsyncSession,
        keyword: str,
        content_type: str,
        stat_date: Optional[date] = None,
    ) -> SearchKeywordStats:
        """获取或创建关键词统计"""
        if stat_date is None:
            stat_date = date.today()
        
        stmt = select(SearchKeywordStats).where(
            and_(
                SearchKeywordStats.keyword == keyword,
                SearchKeywordStats.content_type == content_type,
                SearchKeywordStats.stat_date == stat_date,
            )
        )
        result = await db.execute(stmt)
        stats = result.scalar_one_or_none()
        
        if not stats:
            stats = SearchKeywordStats(
                keyword=keyword,
                content_type=content_type,
                stat_date=stat_date,
            )
            db.add(stats)
            await db.commit()
            await db.refresh(stats)
        
        return stats
    
    @staticmethod
    async def increment_search_count(
        db: AsyncSession,
        keyword: str,
        content_type: str,
        result_count: int,
    ) -> None:
        """增加搜索次数"""
        stats = await CRUDSearchStats.get_or_create_keyword_stats(
            db, keyword, content_type
        )
        
        # 更新统计
        stats.search_count += 1
        stats.last_searched_at = datetime.now()
        
        # 更新平均结果数（增量计算）
        if stats.result_count_avg is None:
            stats.result_count_avg = result_count
        else:
            stats.result_count_avg = (
                (stats.result_count_avg * (stats.search_count - 1) + result_count) 
                / stats.search_count
            )
        
        await db.commit()
    
    @staticmethod
    async def get_trending_keywords(
        db: AsyncSession,
        content_type: str = "all",
        period: str = "day",
        limit: int = 10,
    ) -> List[SearchKeywordStats]:
        """获取热门搜索词"""
        # 确定时间范围
        if period == "hour":
            time_delta = timedelta(hours=1)
        elif period == "day":
            time_delta = timedelta(days=1)
        elif period == "week":
            time_delta = timedelta(days=7)
        else:
            time_delta = timedelta(days=1)
        
        start_date = (datetime.now() - time_delta).date()
        
        stmt = select(SearchKeywordStats).where(
            SearchKeywordStats.stat_date >= start_date
        )
        
        if content_type != "all":
            stmt = stmt.where(SearchKeywordStats.content_type == content_type)
        
        stmt = stmt.order_by(SearchKeywordStats.search_count.desc()).limit(limit)
        
        result = await db.execute(stmt)
        return result.scalars().all()
    
    @staticmethod
    async def get_search_stats(
        db: AsyncSession,
        start_date: Optional[date] = None,
        end_date: Optional[date] = None,
    ) -> Dict[str, Any]:
        """获取搜索统计数据"""
        if start_date is None:
            start_date = date.today() - timedelta(days=7)
        if end_date is None:
            end_date = date.today()
        
        # 总搜索次数
        stmt = select(func.sum(SearchKeywordStats.search_count)).where(
            and_(
                SearchKeywordStats.stat_date >= start_date,
                SearchKeywordStats.stat_date <= end_date,
            )
        )
        result = await db.execute(stmt)
        total_searches = result.scalar() or 0
        
        # 按类型统计
        stmt = select(
            SearchKeywordStats.content_type,
            func.sum(SearchKeywordStats.search_count)
        ).where(
            and_(
                SearchKeywordStats.stat_date >= start_date,
                SearchKeywordStats.stat_date <= end_date,
            )
        ).group_by(SearchKeywordStats.content_type)
        
        result = await db.execute(stmt)
        search_by_type = {row[0]: row[1] for row in result}
        
        return {
            "total_searches": total_searches,
            "search_by_type": search_by_type,
            "start_date": start_date,
            "end_date": end_date,
        }


class CRUDSearchSuggestion:
    """搜索建议 CRUD"""
    
    @staticmethod
    async def create_or_update(
        db: AsyncSession,
        keyword: str,
        content_type: str,
        suggestion_type: str,
        weight: int = 0,
    ) -> SearchSuggestion:
        """创建或更新搜索建议"""
        stmt = select(SearchSuggestion).where(
            and_(
                SearchSuggestion.keyword == keyword,
                SearchSuggestion.content_type == content_type,
                SearchSuggestion.suggestion_type == suggestion_type,
            )
        )
        result = await db.execute(stmt)
        suggestion = result.scalar_one_or_none()
        
        if suggestion:
            suggestion.weight = weight
            suggestion.updated_at = datetime.now()
        else:
            suggestion = SearchSuggestion(
                keyword=keyword,
                content_type=content_type,
                suggestion_type=suggestion_type,
                weight=weight,
            )
            db.add(suggestion)
        
        await db.commit()
        await db.refresh(suggestion)
        return suggestion
    
    @staticmethod
    async def get_suggestions(
        db: AsyncSession,
        prefix: str,
        content_type: str = "all",
        limit: int = 10,
    ) -> List[SearchSuggestion]:
        """获取搜索建议"""
        stmt = select(SearchSuggestion).where(
            and_(
                SearchSuggestion.keyword.ilike(f"{prefix}%"),
                SearchSuggestion.is_active == True,
            )
        )
        
        if content_type != "all":
            stmt = stmt.where(SearchSuggestion.content_type == content_type)
        
        stmt = stmt.order_by(SearchSuggestion.weight.desc()).limit(limit)
        
        result = await db.execute(stmt)
        return result.scalars().all()
    
    @staticmethod
    async def update_from_trending(
        db: AsyncSession,
        content_type: str = "all",
        limit: int = 100,
    ) -> int:
        """从热门搜索更新建议"""
        # 获取热门关键词
        trending = await CRUDSearchStats.get_trending_keywords(
            db, content_type=content_type, period="week", limit=limit
        )
        
        count = 0
        for stat in trending:
            await CRUDSearchSuggestion.create_or_update(
                db=db,
                keyword=stat.keyword,
                content_type=stat.content_type,
                suggestion_type="trending",
                weight=stat.search_count,
            )
            count += 1
        
        logger.info(f"从热门搜索更新建议: 更新数={count}")
        return count


# 导出实例
search_history = CRUDSearchHistory()
search_stats = CRUDSearchStats()
search_suggestion = CRUDSearchSuggestion()
