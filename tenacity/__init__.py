"""测试环境使用的 tenacity 轻量替身。

该模块仅提供最小实现，避免在单元测试中引入真实依赖。
"""
from __future__ import annotations

from typing import Any, Callable, Iterable, Type


class _TenacityPlaceholder:
    def __init__(self, *args: Any, **kwargs: Any) -> None:  # noqa: D401
        """记录参数但不执行任何操作"""

    def __call__(self, *args: Any, **kwargs: Any) -> None:  # noqa: D401
        """保持兼容的可调用行为"""


def retry(*args: Any, **kwargs: Any) -> Callable[[Callable[..., Any]], Callable[..., Any]]:
    """返回原样的装饰器，用于跳过重试逻辑"""

    def decorator(func: Callable[..., Any]) -> Callable[..., Any]:
        return func

    return decorator


def before_log(*args: Any, **kwargs: Any) -> Callable[..., None]:
    return lambda *a, **k: None


def after_log(*args: Any, **kwargs: Any) -> Callable[..., None]:
    return lambda *a, **k: None


def retry_if_exception_type(
    exceptions: Iterable[Type[BaseException]] | Type[BaseException] | None = None,
) -> _TenacityPlaceholder:
    return _TenacityPlaceholder(exceptions)


def stop_after_attempt(attempts: int) -> _TenacityPlaceholder:
    return _TenacityPlaceholder(attempts)


def wait_exponential(*args: Any, **kwargs: Any) -> _TenacityPlaceholder:
    return _TenacityPlaceholder(*args, **kwargs)
