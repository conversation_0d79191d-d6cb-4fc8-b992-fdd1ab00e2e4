"""Add topic stats tables for 热门话题功能

Revision ID: add_topic_stats_tables
Revises: add_post_tables
Create Date: 2024-01-15 00:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'add_topic_stats_tables'
down_revision = 'add_post_tables'  # 依赖于帖子表
branch_labels = None
depends_on = None


def upgrade():
    # 创建话题统计主表
    op.create_table('topic_stats',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('topic', sa.String(length=100), nullable=False, comment='话题标签'),
        
        # 统计数据
        sa.Column('post_count', sa.Integer(), nullable=False, server_default='0', comment='使用该话题的帖子数量'),
        sa.Column('total_likes', sa.Integer(), nullable=False, server_default='0', comment='该话题下所有帖子的总点赞数'),
        sa.Column('total_comments', sa.Integer(), nullable=False, server_default='0', comment='该话题下所有帖子的总评论数'),
        sa.Column('total_reposts', sa.Integer(), nullable=False, server_default='0', comment='该话题下所有帖子的总转发数'),
        sa.Column('total_views', sa.Integer(), nullable=False, server_default='0', comment='该话题下所有帖子的总浏览数'),
        
        # 热度计算
        sa.Column('hot_score', sa.Float(), nullable=False, server_default='0.0', comment='热度分数'),
        sa.Column('trend_score', sa.Float(), nullable=False, server_default='0.0', comment='趋势分数（近期增长）'),
        
        # 时间统计
        sa.Column('last_post_at', postgresql.TIMESTAMP(timezone=True), nullable=True, comment='最后一次使用该话题的时间'),
        sa.Column('peak_time', postgresql.TIMESTAMP(timezone=True), nullable=True, comment='话题热度峰值时间'),
        
        # 元数据
        sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), nullable=False),
        sa.Column('updated_at', postgresql.TIMESTAMP(timezone=True), nullable=False),
        
        sa.PrimaryKeyConstraint('id')
    )
    
    # 创建话题趋势数据表
    op.create_table('topic_trends',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('topic', sa.String(length=100), nullable=False, comment='话题标签'),
        
        # 时间段统计（按小时/天统计）
        sa.Column('period_type', sa.String(length=10), nullable=False, comment='统计周期类型: hour/day'),
        sa.Column('period_start', postgresql.TIMESTAMP(timezone=True), nullable=False, comment='统计周期开始时间'),
        
        # 该时间段内的统计数据
        sa.Column('post_count', sa.Integer(), nullable=False, server_default='0', comment='新增帖子数'),
        sa.Column('likes_count', sa.Integer(), nullable=False, server_default='0', comment='新增点赞数'),
        sa.Column('comments_count', sa.Integer(), nullable=False, server_default='0', comment='新增评论数'),
        sa.Column('reposts_count', sa.Integer(), nullable=False, server_default='0', comment='新增转发数'),
        sa.Column('views_count', sa.Integer(), nullable=False, server_default='0', comment='新增浏览数'),
        
        # 计算得出的分数
        sa.Column('period_score', sa.Float(), nullable=False, server_default='0.0', comment='该时间段的热度分数'),
        
        sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), nullable=False),
        
        sa.PrimaryKeyConstraint('id')
    )
    
    # 创建索引
    # topic_stats 表的索引
    op.create_index('idx_topic_stats_topic', 'topic_stats', ['topic'], unique=True)
    op.create_index('idx_topic_stats_hot_score', 'topic_stats', ['hot_score'])
    op.create_index('idx_topic_stats_trend_score', 'topic_stats', ['trend_score'])
    op.create_index('idx_topic_stats_post_count', 'topic_stats', ['post_count'])
    op.create_index('idx_topic_stats_last_post_at', 'topic_stats', ['last_post_at'])
    op.create_index('idx_topic_stats_updated_at', 'topic_stats', ['updated_at'])
    
    # topic_trends 表的索引
    op.create_index('idx_topic_trends_topic', 'topic_trends', ['topic'])
    op.create_index('idx_topic_trends_period_start', 'topic_trends', ['period_start'])
    op.create_index('idx_topic_trends_period_score', 'topic_trends', ['period_score'])
    op.create_index('idx_topic_trends_topic_period', 'topic_trends', ['topic', 'period_type', 'period_start'])
    
    # 为现有的 posts 表的 topic 字段创建索引（如果还没有的话）
    try:
        op.create_index('idx_posts_topic', 'posts', ['topic'])
    except Exception:
        # 如果索引已存在，忽略错误
        pass
    
    # 扩展 tags 表支持 post 内容类型（如果需要的话）
    # 检查是否需要添加 'post' 到 content_type 枚举
    # 这里假设 tags 表已经存在并且有 content_type 字段
    try:
        # 添加 'post' 到 content_type 枚举（如果还没有的话）
        op.execute("ALTER TYPE content_type ADD VALUE IF NOT EXISTS 'post'")
    except Exception:
        # 如果枚举类型不存在或已经有该值，忽略错误
        pass


def downgrade():
    # 删除索引
    op.drop_index('idx_topic_trends_topic_period', table_name='topic_trends')
    op.drop_index('idx_topic_trends_period_score', table_name='topic_trends')
    op.drop_index('idx_topic_trends_period_start', table_name='topic_trends')
    op.drop_index('idx_topic_trends_topic', table_name='topic_trends')
    
    op.drop_index('idx_topic_stats_updated_at', table_name='topic_stats')
    op.drop_index('idx_topic_stats_last_post_at', table_name='topic_stats')
    op.drop_index('idx_topic_stats_post_count', table_name='topic_stats')
    op.drop_index('idx_topic_stats_trend_score', table_name='topic_stats')
    op.drop_index('idx_topic_stats_hot_score', table_name='topic_stats')
    op.drop_index('idx_topic_stats_topic', table_name='topic_stats')
    
    try:
        op.drop_index('idx_posts_topic', table_name='posts')
    except Exception:
        # 如果索引不存在，忽略错误
        pass
    
    # 删除表
    op.drop_table('topic_trends')
    op.drop_table('topic_stats')
    
    # 注意：我们不删除 content_type 枚举中的 'post' 值，
    # 因为可能有其他地方在使用，删除可能会导致数据丢失
