"""修复BackpackItem模型的用户标识字段

Revision ID: fix_backpack_user_id  
Revises: 
Create Date: 2025-09-23 03:58:00.000000

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'fix_backpack_user_id'
down_revision: Union[str, None] = '96c0db1daac8'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """
    将BackpackItem模型的username字段改为user_id字段
    """
    bind = op.get_bind()
    inspector = sa.inspect(bind)
    columns = {column['name'] for column in inspector.get_columns('backpack_items')}

    if 'user_id' in columns:
        # 前一个迁移已经完成了列和约束的迁移，这里可以直接返回
        return

    # 1. 添加新的user_id列
    op.add_column('backpack_items', sa.Column('user_id', sa.Integer(), nullable=True))
    
    # 2. 根据username填充user_id数据
    # 注意：这需要根据实际的用户数据来填充
    op.execute("""
        UPDATE backpack_items 
        SET user_id = (
            SELECT id FROM users 
            WHERE users.username = backpack_items.username
        )
        WHERE EXISTS (
            SELECT 1 FROM users 
            WHERE users.username = backpack_items.username
        )
    """)
    
    # 3. 将user_id列设置为不可空
    op.alter_column('backpack_items', 'user_id', nullable=False)
    
    # 4. 添加外键约束
    op.create_foreign_key(
        'fk_backpack_items_user_id', 
        'backpack_items', 
        'users', 
        ['user_id'], 
        ['id']
    )
    
    # 5. 添加索引
    op.create_index('ix_backpack_items_user_id', 'backpack_items', ['user_id'])
    
    # 6. 删除旧的username列（可选，建议先测试后再执行）
    # op.drop_index('ix_backpack_items_username', 'backpack_items')
    # op.drop_column('backpack_items', 'username')


def downgrade() -> None:
    """
    回滚操作：将user_id字段改回username字段
    """
    # 1. 重新添加username列
    op.add_column('backpack_items', sa.Column('username', sa.String(50), nullable=True))
    
    # 2. 根据user_id填充username数据
    op.execute("""
        UPDATE backpack_items 
        SET username = (
            SELECT username FROM users 
            WHERE users.id = backpack_items.user_id
        )
        WHERE user_id IS NOT NULL
    """)
    
    # 3. 将username列设置为不可空
    op.alter_column('backpack_items', 'username', nullable=False)
    
    # 4. 添加username索引
    op.create_index('ix_backpack_items_username', 'backpack_items', ['username'])
    
    # 5. 删除user_id相关约束和列
    op.drop_constraint('fk_backpack_items_user_id', 'backpack_items', type_='foreignkey')
    op.drop_index('ix_backpack_items_user_id', 'backpack_items')
    op.drop_column('backpack_items', 'user_id')