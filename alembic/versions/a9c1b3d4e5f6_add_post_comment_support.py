"""add post comment support

Revision ID: a9c1b3d4e5f6
Revises: add_post_tables
Create Date: 2025-02-15 00:00:00.000000

"""

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = "a9c1b3d4e5f6"
down_revision = "add_post_tables"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.execute("ALTER TYPE comment_type ADD VALUE IF NOT EXISTS 'post'")
    op.add_column("comments", sa.Column("post_id", sa.Integer(), nullable=True))
    op.create_index(op.f("ix_comments_post_id"), "comments", ["post_id"], unique=False)
    op.create_foreign_key(
        "comments_post_id_fkey", "comments", "posts", ["post_id"], ["id"]
    )


def downgrade() -> None:
    op.drop_constraint("comments_post_id_fkey", "comments", type_="foreignkey")
    op.drop_index(op.f("ix_comments_post_id"), table_name="comments")
    op.drop_column("comments", "post_id")
    # 保留新增的枚举值以避免影响已有记录
