"""Add admin enhancement phase 1 tables

Revision ID: 0a6e2c539f38
Revises: add_audit_logs_001
Create Date: 2025-10-11 08:57:15.601180
"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql
from sqlalchemy.sql import func

# revision identifiers, used by Alembic.
revision: str = "0a6e2c539f38"
down_revision: Union[str, None] = "add_audit_logs_001"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Admin configuration settings
    op.create_table(
        "admin_config_settings",
        sa.Column("id", sa.BigInteger(), primary_key=True, autoincrement=True),
        sa.Column("category", sa.String(length=100), nullable=False),
        sa.Column("key", sa.String(length=255), nullable=False),
        sa.Column("value", sa.Text(), nullable=False),
        sa.Column("value_type", sa.String(length=32), nullable=False),
        sa.Column("description", sa.String(length=255), nullable=True),
        sa.Column(
            "metadata",
            postgresql.JSONB(astext_type=sa.Text()),
            nullable=False,
            server_default=sa.text("'{}'::jsonb"),
        ),
        sa.Column("version", sa.BigInteger(), nullable=False, server_default="1"),
        sa.Column(
            "updated_at",
            sa.DateTime(timezone=True),
            nullable=False,
            server_default=func.now(),
        ),
        sa.Column(
            "updated_by",
            sa.Integer(),
            sa.ForeignKey("users.id", ondelete="SET NULL"),
            nullable=True,
        ),
        sa.Column(
            "is_sensitive",
            sa.Boolean(),
            nullable=False,
            server_default=sa.text("false"),
        ),
        sa.UniqueConstraint("key", name="uq_admin_config_settings_key"),
        sa.CheckConstraint("version >= 1", name="ck_admin_config_settings_version_positive"),
        comment="系统配置项存储表",
    )
    op.create_index(
        "ix_admin_config_settings_category",
        "admin_config_settings",
        ["category"],
    )
    op.create_index(
        "ix_admin_config_settings_category_key",
        "admin_config_settings",
        ["category", "key"],
    )
    op.create_index(
        "ix_admin_config_settings_is_sensitive",
        "admin_config_settings",
        ["is_sensitive"],
    )

    # Feature toggles
    op.create_table(
        "admin_feature_toggles",
        sa.Column("id", sa.BigInteger(), primary_key=True, autoincrement=True),
        sa.Column("name", sa.String(length=100), nullable=False),
        sa.Column(
            "is_enabled",
            sa.Boolean(),
            nullable=False,
            server_default=sa.text("false"),
        ),
        sa.Column("description", sa.String(length=255), nullable=True),
        sa.Column("scope", sa.String(length=50), nullable=False, server_default="global"),
        sa.Column(
            "config",
            postgresql.JSONB(astext_type=sa.Text()),
            nullable=False,
            server_default=sa.text("'{}'::jsonb"),
        ),
        sa.Column(
            "updated_at",
            sa.DateTime(timezone=True),
            nullable=False,
            server_default=func.now(),
        ),
        sa.Column(
            "updated_by",
            sa.Integer(),
            sa.ForeignKey("users.id", ondelete="SET NULL"),
            nullable=True,
        ),
        sa.UniqueConstraint("name", name="uq_admin_feature_toggles_name"),
        comment="后台功能开关管理表",
    )
    op.create_index(
        "ix_admin_feature_toggles_scope",
        "admin_feature_toggles",
        ["scope"],
    )
    op.create_index(
        "ix_admin_feature_toggles_is_enabled",
        "admin_feature_toggles",
        ["is_enabled"],
    )

    # Config change logs
    op.create_table(
        "admin_config_change_logs",
        sa.Column("id", sa.BigInteger(), primary_key=True, autoincrement=True),
        sa.Column("change_id", sa.String(length=128), nullable=False),
        sa.Column("config_key", sa.String(length=255), nullable=False),
        sa.Column("category", sa.String(length=100), nullable=True),
        sa.Column("old_value", sa.Text(), nullable=True),
        sa.Column("new_value", sa.Text(), nullable=True),
        sa.Column("change_type", sa.String(length=32), nullable=False),
        sa.Column("change_reason", sa.Text(), nullable=True),
        sa.Column(
            "changed_by",
            sa.Integer(),
            sa.ForeignKey("users.id", ondelete="SET NULL"),
            nullable=True,
        ),
        sa.Column(
            "changed_at",
            sa.DateTime(timezone=True),
            nullable=False,
            server_default=func.now(),
        ),
        sa.Column(
            "rollback_id",
            sa.String(length=128),
            sa.ForeignKey("admin_config_change_logs.change_id", ondelete="SET NULL"),
            nullable=True,
        ),
        sa.Column(
            "is_rollback",
            sa.Boolean(),
            nullable=False,
            server_default=sa.text("false"),
        ),
        sa.UniqueConstraint("change_id", name="uq_admin_config_change_logs_change_id"),
        comment="配置变更历史与回滚记录",
    )
    op.create_index(
        "ix_admin_config_change_logs_config_key",
        "admin_config_change_logs",
        ["config_key"],
    )
    op.create_index(
        "ix_admin_config_change_logs_category_changed_at",
        "admin_config_change_logs",
        ["category", "changed_at"],
    )
    op.create_index(
        "ix_admin_config_change_logs_changed_by",
        "admin_config_change_logs",
        ["changed_by"],
    )
    op.create_index(
        "ix_admin_config_change_logs_changed_at",
        "admin_config_change_logs",
        ["changed_at"],
    )
    op.create_index(
        "ix_admin_config_change_logs_rollback_id",
        "admin_config_change_logs",
        ["rollback_id"],
    )

    # Monitoring metrics
    op.create_table(
        "admin_monitoring_metrics",
        sa.Column("id", sa.BigInteger(), primary_key=True, autoincrement=True),
        sa.Column("metric_name", sa.String(length=150), nullable=False),
        sa.Column("value", sa.Numeric(precision=18, scale=6), nullable=False),
        sa.Column(
            "labels",
            postgresql.JSONB(astext_type=sa.Text()),
            nullable=False,
            server_default=sa.text("'{}'::jsonb"),
        ),
        sa.Column(
            "timestamp",
            sa.DateTime(timezone=True),
            nullable=False,
            server_default=func.now(),
        ),
        sa.Column("source", sa.String(length=64), nullable=False, server_default="system"),
        sa.Column(
            "metadata",
            postgresql.JSONB(astext_type=sa.Text()),
            nullable=False,
            server_default=sa.text("'{}'::jsonb"),
        ),
        comment="监控指标时序数据",
    )
    op.create_index(
        "ix_admin_monitoring_metrics_metric_name",
        "admin_monitoring_metrics",
        ["metric_name"],
    )
    op.create_index(
        "ix_admin_monitoring_metrics_timestamp",
        "admin_monitoring_metrics",
        ["timestamp"],
    )
    op.create_index(
        "ix_admin_monitoring_metrics_metric_timestamp",
        "admin_monitoring_metrics",
        ["metric_name", "timestamp"],
    )
    op.create_index(
        "ix_admin_monitoring_metrics_source",
        "admin_monitoring_metrics",
        ["source"],
    )

    # Alert rules
    op.create_table(
        "admin_alert_rules",
        sa.Column("id", sa.BigInteger(), primary_key=True, autoincrement=True),
        sa.Column("name", sa.String(length=150), nullable=False),
        sa.Column("metric_name", sa.String(length=150), nullable=False),
        sa.Column("operator", sa.String(length=32), nullable=False),
        sa.Column("threshold", sa.Numeric(precision=18, scale=6), nullable=False),
        sa.Column("duration", sa.String(length=32), nullable=False, server_default="0m"),
        sa.Column("severity", sa.String(length=32), nullable=False, server_default="info"),
        sa.Column(
            "channels",
            postgresql.JSONB(astext_type=sa.Text()),
            nullable=False,
            server_default=sa.text("'[]'::jsonb"),
        ),
        sa.Column(
            "enabled",
            sa.Boolean(),
            nullable=False,
            server_default=sa.text("true"),
        ),
        sa.Column("description", sa.Text(), nullable=True),
        sa.Column(
            "created_at",
            sa.DateTime(timezone=True),
            nullable=False,
            server_default=func.now(),
        ),
        sa.Column(
            "updated_at",
            sa.DateTime(timezone=True),
            nullable=False,
            server_default=func.now(),
        ),
        sa.Column(
            "created_by",
            sa.Integer(),
            sa.ForeignKey("users.id", ondelete="SET NULL"),
            nullable=True,
        ),
        sa.Column(
            "updated_by",
            sa.Integer(),
            sa.ForeignKey("users.id", ondelete="SET NULL"),
            nullable=True,
        ),
        sa.UniqueConstraint("name", name="uq_admin_alert_rules_name"),
        comment="系统监控告警规则定义",
    )
    op.create_index(
        "ix_admin_alert_rules_metric_name",
        "admin_alert_rules",
        ["metric_name"],
    )
    op.create_index(
        "ix_admin_alert_rules_enabled",
        "admin_alert_rules",
        ["enabled"],
    )
    op.create_index(
        "ix_admin_alert_rules_severity",
        "admin_alert_rules",
        ["severity"],
    )
    op.create_index(
        "ix_admin_alert_rules_created_at",
        "admin_alert_rules",
        ["created_at"],
    )

    # Alert events
    op.create_table(
        "admin_alert_events",
        sa.Column("id", sa.BigInteger(), primary_key=True, autoincrement=True),
        sa.Column(
            "rule_id",
            sa.BigInteger(),
            sa.ForeignKey("admin_alert_rules.id", ondelete="SET NULL"),
            nullable=True,
        ),
        sa.Column("event_id", sa.String(length=128), nullable=False),
        sa.Column("status", sa.String(length=32), nullable=False, server_default="active"),
        sa.Column(
            "trigger_value",
            sa.Numeric(precision=18, scale=6),
            nullable=False,
        ),
        sa.Column(
            "triggered_at",
            sa.DateTime(timezone=True),
            nullable=False,
            server_default=func.now(),
        ),
        sa.Column("resolved_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column(
            "context",
            postgresql.JSONB(astext_type=sa.Text()),
            nullable=False,
            server_default=sa.text("'{}'::jsonb"),
        ),
        sa.Column("message", sa.Text(), nullable=True),
        sa.Column(
            "notification_status",
            sa.String(length=32),
            nullable=False,
            server_default="pending",
        ),
        sa.UniqueConstraint("event_id", name="uq_admin_alert_events_event_id"),
        comment="监控告警触发事件",
    )
    op.create_index(
        "ix_admin_alert_events_rule_id",
        "admin_alert_events",
        ["rule_id"],
    )
    op.create_index(
        "ix_admin_alert_events_status",
        "admin_alert_events",
        ["status"],
    )
    op.create_index(
        "ix_admin_alert_events_triggered_at",
        "admin_alert_events",
        ["triggered_at"],
    )
    op.create_index(
        "ix_admin_alert_events_notification_status",
        "admin_alert_events",
        ["notification_status"],
    )

    # Analytics snapshots
    op.create_table(
        "admin_analytics_snapshots",
        sa.Column("id", sa.BigInteger(), primary_key=True, autoincrement=True),
        sa.Column("snapshot_type", sa.String(length=64), nullable=False),
        sa.Column("time_range", sa.String(length=32), nullable=False, server_default="last_7d"),
        sa.Column(
            "filters",
            postgresql.JSONB(astext_type=sa.Text()),
            nullable=False,
            server_default=sa.text("'{}'::jsonb"),
        ),
        sa.Column("record_count", sa.BigInteger(), nullable=False, server_default="0"),
        sa.Column(
            "generated_at",
            sa.DateTime(timezone=True),
            nullable=False,
            server_default=func.now(),
        ),
        sa.Column(
            "generation_duration",
            sa.BigInteger(),
            nullable=False,
            server_default="0",
        ),
        sa.Column(
            "status",
            sa.String(length=32),
            nullable=False,
            server_default="pending",
        ),
        sa.UniqueConstraint(
            "snapshot_type",
            "time_range",
            name="uq_admin_analytics_snapshots_type_range",
        ),
        comment="后台运营分析快照概览",
    )
    op.create_index(
        "ix_admin_analytics_snapshots_type",
        "admin_analytics_snapshots",
        ["snapshot_type"],
    )
    op.create_index(
        "ix_admin_analytics_snapshots_time_range",
        "admin_analytics_snapshots",
        ["time_range"],
    )
    op.create_index(
        "ix_admin_analytics_snapshots_generated_at",
        "admin_analytics_snapshots",
        ["generated_at"],
    )
    op.create_index(
        "ix_admin_analytics_snapshots_status",
        "admin_analytics_snapshots",
        ["status"],
    )

    # Analytics snapshot data
    op.create_table(
        "admin_analytics_snapshot_data",
        sa.Column("id", sa.BigInteger(), primary_key=True, autoincrement=True),
        sa.Column(
            "snapshot_id",
            sa.BigInteger(),
            sa.ForeignKey("admin_analytics_snapshots.id", ondelete="CASCADE"),
            nullable=False,
        ),
        sa.Column(
            "payload",
            postgresql.JSONB(astext_type=sa.Text()),
            nullable=False,
            server_default=sa.text("'{}'::jsonb"),
        ),
        sa.Column(
            "data_type",
            sa.String(length=64),
            nullable=False,
            server_default="summary",
        ),
        sa.Column(
            "created_at",
            sa.DateTime(timezone=True),
            nullable=False,
            server_default=func.now(),
        ),
        comment="后台运营分析快照具体数据载荷",
    )
    op.create_index(
        "ix_admin_analytics_snapshot_data_snapshot_id",
        "admin_analytics_snapshot_data",
        ["snapshot_id"],
    )
    op.create_index(
        "ix_admin_analytics_snapshot_data_data_type",
        "admin_analytics_snapshot_data",
        ["data_type"],
    )
    op.create_index(
        "ix_admin_analytics_snapshot_data_created_at",
        "admin_analytics_snapshot_data",
        ["created_at"],
    )


def downgrade() -> None:
    op.drop_index(
        "ix_admin_analytics_snapshot_data_created_at", table_name="admin_analytics_snapshot_data"
    )
    op.drop_index(
        "ix_admin_analytics_snapshot_data_data_type", table_name="admin_analytics_snapshot_data"
    )
    op.drop_index(
        "ix_admin_analytics_snapshot_data_snapshot_id", table_name="admin_analytics_snapshot_data"
    )
    op.drop_table("admin_analytics_snapshot_data")

    op.drop_index(
        "ix_admin_analytics_snapshots_status", table_name="admin_analytics_snapshots"
    )
    op.drop_index(
        "ix_admin_analytics_snapshots_generated_at", table_name="admin_analytics_snapshots"
    )
    op.drop_index(
        "ix_admin_analytics_snapshots_time_range", table_name="admin_analytics_snapshots"
    )
    op.drop_index(
        "ix_admin_analytics_snapshots_type", table_name="admin_analytics_snapshots"
    )
    op.drop_table("admin_analytics_snapshots")

    op.drop_index(
        "ix_admin_alert_events_notification_status", table_name="admin_alert_events"
    )
    op.drop_index("ix_admin_alert_events_triggered_at", table_name="admin_alert_events")
    op.drop_index("ix_admin_alert_events_status", table_name="admin_alert_events")
    op.drop_index("ix_admin_alert_events_rule_id", table_name="admin_alert_events")
    op.drop_table("admin_alert_events")

    op.drop_index("ix_admin_alert_rules_created_at", table_name="admin_alert_rules")
    op.drop_index("ix_admin_alert_rules_severity", table_name="admin_alert_rules")
    op.drop_index("ix_admin_alert_rules_enabled", table_name="admin_alert_rules")
    op.drop_index("ix_admin_alert_rules_metric_name", table_name="admin_alert_rules")
    op.drop_table("admin_alert_rules")

    op.drop_index("ix_admin_monitoring_metrics_source", table_name="admin_monitoring_metrics")
    op.drop_index(
        "ix_admin_monitoring_metrics_metric_timestamp", table_name="admin_monitoring_metrics"
    )
    op.drop_index("ix_admin_monitoring_metrics_timestamp", table_name="admin_monitoring_metrics")
    op.drop_index("ix_admin_monitoring_metrics_metric_name", table_name="admin_monitoring_metrics")
    op.drop_table("admin_monitoring_metrics")

    op.drop_index(
        "ix_admin_config_change_logs_rollback_id", table_name="admin_config_change_logs"
    )
    op.drop_index(
        "ix_admin_config_change_logs_changed_at", table_name="admin_config_change_logs"
    )
    op.drop_index(
        "ix_admin_config_change_logs_changed_by", table_name="admin_config_change_logs"
    )
    op.drop_index(
        "ix_admin_config_change_logs_category_changed_at",
        table_name="admin_config_change_logs",
    )
    op.drop_index(
        "ix_admin_config_change_logs_config_key", table_name="admin_config_change_logs"
    )
    op.drop_table("admin_config_change_logs")

    op.drop_index("ix_admin_feature_toggles_is_enabled", table_name="admin_feature_toggles")
    op.drop_index("ix_admin_feature_toggles_scope", table_name="admin_feature_toggles")
    op.drop_table("admin_feature_toggles")

    op.drop_index("ix_admin_config_settings_is_sensitive", table_name="admin_config_settings")
    op.drop_index("ix_admin_config_settings_category_key", table_name="admin_config_settings")
    op.drop_index("ix_admin_config_settings_category", table_name="admin_config_settings")
    op.drop_table("admin_config_settings")
