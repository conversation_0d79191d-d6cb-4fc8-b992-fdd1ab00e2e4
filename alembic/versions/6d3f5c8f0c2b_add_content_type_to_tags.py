"""add content_type to tags

Revision ID: 6d3f5c8f0c2b
Revises: 5b7d2f3a1c84
Create Date: 2025-10-09 09:40:00.000000

"""
from collections.abc import Sequence

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "6d3f5c8f0c2b"
down_revision: str | None = "5b7d2f3a1c84"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    op.add_column(
        "tags",
        sa.Column("content_type", sa.String(length=32), nullable=True),
    )
    op.create_index("ix_tags_content_type", "tags", ["content_type"], unique=False)


def downgrade() -> None:
    op.drop_index("ix_tags_content_type", table_name="tags")
    op.drop_column("tags", "content_type")
