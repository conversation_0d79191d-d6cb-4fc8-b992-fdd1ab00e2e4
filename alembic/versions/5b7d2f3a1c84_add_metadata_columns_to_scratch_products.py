"""add metadata columns to scratch products

Revision ID: 5b7d2f3a1c84
Revises: 4f5c2a18e7b3
Create Date: 2025-09-30 13:00:00.000000

"""
from collections.abc import Sequence

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "5b7d2f3a1c84"
down_revision: str | Sequence[str] | None = "4f5c2a18e7b3"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    op.add_column("scratch_products", sa.Column("category", sa.String(length=64), nullable=True))
    op.add_column("scratch_products", sa.Column("difficulty", sa.String(length=32), nullable=True))
    op.add_column("scratch_products", sa.Column("badges", sa.JSON(), nullable=True))


def downgrade() -> None:
    op.drop_column("scratch_products", "badges")
    op.drop_column("scratch_products", "difficulty")
    op.drop_column("scratch_products", "category")

