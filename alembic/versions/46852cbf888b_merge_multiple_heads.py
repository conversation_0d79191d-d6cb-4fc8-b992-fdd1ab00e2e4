"""merge_multiple_heads

Revision ID: 46852cbf888b
Revises: 1f79d8f78ef0, a9c1b3d4e5f6, add_topic_stats_tables
Create Date: 2025-10-27 03:04:20.091261

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '46852cbf888b'
down_revision: Union[str, None] = ('1f79d8f78ef0', 'a9c1b3d4e5f6', 'add_topic_stats_tables')
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    pass


def downgrade() -> None:
    pass
