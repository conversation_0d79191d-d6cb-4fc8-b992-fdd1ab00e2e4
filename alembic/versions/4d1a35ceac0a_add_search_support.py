"""add_search_support

为文章、视频、Scratch项目添加全文搜索支持，包括：
- 启用 pg_trgm 扩展
- 添加 search_vector 字段
- 创建 GIN 和 GIST 索引
- 创建触发器自动维护搜索向量
- 创建搜索相关表（历史、统计、建议）

Revision ID: 4d1a35ceac0a
Revises: 46852cbf888b
Create Date: 2025-10-27 03:04:29.421380
"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '4d1a35ceac0a'
down_revision: Union[str, None] = '46852cbf888b'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ========== 1. 启用 pg_trgm 扩展 ==========
    op.execute('CREATE EXTENSION IF NOT EXISTS pg_trgm;')
    
    # ========== 2. 为 articles 表添加搜索支持 ==========
    # 添加 search_vector 字段
    op.add_column('articles', sa.Column('search_vector', postgresql.TSVECTOR(), nullable=True))
    
    # 创建 GIN 索引（全文搜索）
    op.create_index(
        'idx_articles_search_vector',
        'articles',
        ['search_vector'],
        unique=False,
        postgresql_using='gin'
    )
    
    # 创建 GIST 索引（模糊搜索）
    op.execute("""
        CREATE INDEX idx_articles_title_trgm 
        ON articles USING GIST(title gist_trgm_ops);
    """)
    
    # 创建触发器函数
    op.execute("""
        CREATE OR REPLACE FUNCTION articles_search_vector_update() 
        RETURNS TRIGGER AS $$
        BEGIN
            NEW.search_vector := 
                setweight(to_tsvector('simple', COALESCE(NEW.title, '')), 'A') ||
                setweight(to_tsvector('simple', COALESCE(NEW.description, '')), 'B') ||
                setweight(to_tsvector('simple', COALESCE(NEW.content, '')), 'C');
            RETURN NEW;
        END;
        $$ LANGUAGE plpgsql;
    """)
    
    # 创建触发器
    op.execute("""
        CREATE TRIGGER articles_search_vector_trigger
        BEFORE INSERT OR UPDATE ON articles
        FOR EACH ROW
        EXECUTE FUNCTION articles_search_vector_update();
    """)
    
    # 初始化现有数据的搜索向量
    op.execute("""
        UPDATE articles 
        SET search_vector = 
            setweight(to_tsvector('simple', COALESCE(title, '')), 'A') ||
            setweight(to_tsvector('simple', COALESCE(description, '')), 'B') ||
            setweight(to_tsvector('simple', COALESCE(content, '')), 'C')
        WHERE search_vector IS NULL;
    """)
    
    # ========== 3. 为 videos 表添加搜索支持 ==========
    op.add_column('videos', sa.Column('search_vector', postgresql.TSVECTOR(), nullable=True))
    
    op.create_index(
        'idx_videos_search_vector',
        'videos',
        ['search_vector'],
        unique=False,
        postgresql_using='gin'
    )
    
    op.execute("""
        CREATE INDEX idx_videos_title_trgm 
        ON videos USING GIST(title gist_trgm_ops);
    """)
    
    op.execute("""
        CREATE OR REPLACE FUNCTION videos_search_vector_update() 
        RETURNS TRIGGER AS $$
        BEGIN
            NEW.search_vector := 
                setweight(to_tsvector('simple', COALESCE(NEW.title, '')), 'A') ||
                setweight(to_tsvector('simple', COALESCE(NEW.description, '')), 'B');
            RETURN NEW;
        END;
        $$ LANGUAGE plpgsql;
    """)
    
    op.execute("""
        CREATE TRIGGER videos_search_vector_trigger
        BEFORE INSERT OR UPDATE ON videos
        FOR EACH ROW
        EXECUTE FUNCTION videos_search_vector_update();
    """)
    
    op.execute("""
        UPDATE videos 
        SET search_vector = 
            setweight(to_tsvector('simple', COALESCE(title, '')), 'A') ||
            setweight(to_tsvector('simple', COALESCE(description, '')), 'B')
        WHERE search_vector IS NULL;
    """)
    
    # ========== 4. 为 scratch_products 表添加搜索支持 ==========
    op.add_column('scratch_products', sa.Column('search_vector', postgresql.TSVECTOR(), nullable=True))
    
    op.create_index(
        'idx_scratch_products_search_vector',
        'scratch_products',
        ['search_vector'],
        unique=False,
        postgresql_using='gin'
    )
    
    op.execute("""
        CREATE INDEX idx_scratch_products_title_trgm 
        ON scratch_products USING GIST(title gist_trgm_ops);
    """)
    
    op.execute("""
        CREATE OR REPLACE FUNCTION scratch_products_search_vector_update() 
        RETURNS TRIGGER AS $$
        BEGIN
            NEW.search_vector := 
                setweight(to_tsvector('simple', COALESCE(NEW.title, '')), 'A') ||
                setweight(to_tsvector('simple', COALESCE(NEW.description, '')), 'B') ||
                setweight(to_tsvector('simple', COALESCE(NEW.category, '')), 'C');
            RETURN NEW;
        END;
        $$ LANGUAGE plpgsql;
    """)
    
    op.execute("""
        CREATE TRIGGER scratch_products_search_vector_trigger
        BEFORE INSERT OR UPDATE ON scratch_products
        FOR EACH ROW
        EXECUTE FUNCTION scratch_products_search_vector_update();
    """)
    
    op.execute("""
        UPDATE scratch_products 
        SET search_vector = 
            setweight(to_tsvector('simple', COALESCE(title, '')), 'A') ||
            setweight(to_tsvector('simple', COALESCE(description, '')), 'B') ||
            setweight(to_tsvector('simple', COALESCE(category, '')), 'C')
        WHERE search_vector IS NULL;
    """)
    
    # ========== 5. 创建搜索历史表 ==========
    op.create_table(
        'search_history',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('user_id', sa.Integer(), nullable=True),
        sa.Column('query', sa.Text(), nullable=False),
        sa.Column('content_type', sa.String(length=32), nullable=False),
        sa.Column('filters', postgresql.JSONB(), nullable=True),
        sa.Column('result_count', sa.Integer(), server_default='0', nullable=False),
        sa.Column('clicked_result_id', sa.Integer(), nullable=True),
        sa.Column('clicked_result_position', sa.Integer(), nullable=True),
        sa.Column('session_id', sa.String(length=128), nullable=True),
        sa.Column('ip_address', postgresql.INET(), nullable=True),
        sa.Column('user_agent', sa.Text(), nullable=True),
        sa.Column('created_at', sa.TIMESTAMP(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.CheckConstraint("content_type IN ('article', 'video', 'scratch', 'all')", name='check_content_type'),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_search_history_created', 'search_history', ['created_at'], unique=False)
    op.create_index('idx_search_history_query', 'search_history', ['query', 'content_type'], unique=False)
    op.create_index('idx_search_history_user', 'search_history', ['user_id', 'created_at'], unique=False)
    
    # ========== 6. 创建搜索关键词统计表 ==========
    op.create_table(
        'search_keywords_stats',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('keyword', sa.String(length=255), nullable=False),
        sa.Column('content_type', sa.String(length=32), nullable=False),
        sa.Column('search_count', sa.Integer(), server_default='0', nullable=False),
        sa.Column('result_count_avg', sa.Numeric(precision=10, scale=2), nullable=True),
        sa.Column('click_through_rate', sa.Numeric(precision=5, scale=4), nullable=True),
        sa.Column('last_searched_at', sa.TIMESTAMP(timezone=True), nullable=True),
        sa.Column('stat_date', sa.Date(), nullable=False),
        sa.Column('created_at', sa.TIMESTAMP(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.TIMESTAMP(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.CheckConstraint("content_type IN ('article', 'video', 'scratch', 'all')", name='check_keywords_content_type'),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('keyword', 'content_type', 'stat_date', name='uq_keyword_type_date')
    )
    op.create_index('idx_search_keywords_stats_count', 'search_keywords_stats', ['search_count', 'stat_date'], unique=False)
    op.create_index('idx_search_keywords_stats_date', 'search_keywords_stats', ['stat_date'], unique=False)
    op.create_index('idx_search_keywords_stats_keyword', 'search_keywords_stats', ['keyword', 'content_type'], unique=False)
    
    # ========== 7. 创建搜索建议表 ==========
    op.create_table(
        'search_suggestions',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('keyword', sa.String(length=255), nullable=False),
        sa.Column('content_type', sa.String(length=32), nullable=False),
        sa.Column('suggestion_type', sa.String(length=32), nullable=False),
        sa.Column('weight', sa.Integer(), server_default='0', nullable=False),
        sa.Column('is_active', sa.Boolean(), server_default='true', nullable=False),
        sa.Column('created_at', sa.TIMESTAMP(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.TIMESTAMP(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.CheckConstraint("content_type IN ('article', 'video', 'scratch', 'all')", name='check_suggestions_content_type'),
        sa.CheckConstraint("suggestion_type IN ('trending', 'popular', 'related')", name='check_suggestion_type'),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('keyword', 'content_type', 'suggestion_type', name='uq_keyword_type_suggestion')
    )
    op.create_index('idx_search_suggestions_keyword', 'search_suggestions', ['keyword', 'content_type', 'is_active'], unique=False)
    op.create_index('idx_search_suggestions_weight', 'search_suggestions', ['weight', 'content_type'], unique=False)


def downgrade() -> None:
    # 删除搜索建议表
    op.drop_index('idx_search_suggestions_weight', table_name='search_suggestions')
    op.drop_index('idx_search_suggestions_keyword', table_name='search_suggestions')
    op.drop_table('search_suggestions')
    
    # 删除搜索统计表
    op.drop_index('idx_search_keywords_stats_keyword', table_name='search_keywords_stats')
    op.drop_index('idx_search_keywords_stats_date', table_name='search_keywords_stats')
    op.drop_index('idx_search_keywords_stats_count', table_name='search_keywords_stats')
    op.drop_table('search_keywords_stats')
    
    # 删除搜索历史表
    op.drop_index('idx_search_history_user', table_name='search_history')
    op.drop_index('idx_search_history_query', table_name='search_history')
    op.drop_index('idx_search_history_created', table_name='search_history')
    op.drop_table('search_history')
    
    # 删除 scratch_products 的搜索支持
    op.execute('DROP TRIGGER IF EXISTS scratch_products_search_vector_trigger ON scratch_products;')
    op.execute('DROP FUNCTION IF EXISTS scratch_products_search_vector_update();')
    op.execute('DROP INDEX IF EXISTS idx_scratch_products_title_trgm;')
    op.drop_index('idx_scratch_products_search_vector', table_name='scratch_products')
    op.drop_column('scratch_products', 'search_vector')
    
    # 删除 videos 的搜索支持
    op.execute('DROP TRIGGER IF EXISTS videos_search_vector_trigger ON videos;')
    op.execute('DROP FUNCTION IF EXISTS videos_search_vector_update();')
    op.execute('DROP INDEX IF EXISTS idx_videos_title_trgm;')
    op.drop_index('idx_videos_search_vector', table_name='videos')
    op.drop_column('videos', 'search_vector')
    
    # 删除 articles 的搜索支持
    op.execute('DROP TRIGGER IF EXISTS articles_search_vector_trigger ON articles;')
    op.execute('DROP FUNCTION IF EXISTS articles_search_vector_update();')
    op.execute('DROP INDEX IF EXISTS idx_articles_title_trgm;')
    op.drop_index('idx_articles_search_vector', table_name='articles')
    op.drop_column('articles', 'search_vector')
    
    # 删除扩展（谨慎操作）
    # op.execute('DROP EXTENSION IF EXISTS pg_trgm;')
