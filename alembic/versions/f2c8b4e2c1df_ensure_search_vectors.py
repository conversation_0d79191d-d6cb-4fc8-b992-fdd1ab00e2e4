"""ensure search vectors and triggers

为文章、视频、Scratch 表补充全文检索列、索引及触发器，
保证模型字段与数据库结构一致。
"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql


# revision identifiers, used by Alembic.
revision = "f2c8b4e2c1df"
down_revision = "e3a9f0a1b4c1"
branch_labels = None
depends_on = None


def _column_exists(inspector: sa.Inspector, table: str, column: str) -> bool:
    columns = [col["name"] for col in inspector.get_columns(table)]
    return column in columns


def upgrade() -> None:
    bind = op.get_bind()
    inspector = sa.inspect(bind)

    op.execute("CREATE EXTENSION IF NOT EXISTS pg_trgm;")

    # ===== Articles =====
    if not _column_exists(inspector, "articles", "search_vector"):
        op.add_column(
            "articles",
            sa.Column("search_vector", postgresql.TSVECTOR(), nullable=True, comment="全文搜索向量"),
        )

    op.execute(
        """
        CREATE OR REPLACE FUNCTION articles_search_vector_update()
        RETURNS TRIGGER AS $$
        BEGIN
            NEW.search_vector :=
                setweight(to_tsvector('simple', COALESCE(NEW.title, '')), 'A') ||
                setweight(to_tsvector('simple', COALESCE(NEW.description, '')), 'B') ||
                setweight(to_tsvector('simple', COALESCE(NEW.content, '')), 'C');
            RETURN NEW;
        END;
        $$ LANGUAGE plpgsql;
        """
    )

    op.execute("DROP TRIGGER IF EXISTS articles_search_vector_trigger ON articles;")
    op.execute(
        """
        CREATE TRIGGER articles_search_vector_trigger
        BEFORE INSERT OR UPDATE ON articles
        FOR EACH ROW
        EXECUTE FUNCTION articles_search_vector_update();
        """
    )
    op.execute(
        """
        CREATE INDEX IF NOT EXISTS idx_articles_search_vector
        ON articles USING GIN(search_vector);
        """
    )
    op.execute(
        """
        CREATE INDEX IF NOT EXISTS idx_articles_title_trgm
        ON articles USING GIST (title gist_trgm_ops);
        """
    )
    op.execute(
        """
        UPDATE articles
        SET search_vector =
            setweight(to_tsvector('simple', COALESCE(title, '')), 'A') ||
            setweight(to_tsvector('simple', COALESCE(description, '')), 'B') ||
            setweight(to_tsvector('simple', COALESCE(content, '')), 'C');
        """
    )

    # ===== Videos =====
    if not _column_exists(inspector, "videos", "search_vector"):
        op.add_column(
            "videos",
            sa.Column("search_vector", postgresql.TSVECTOR(), nullable=True, comment="全文搜索向量"),
        )

    op.execute(
        """
        CREATE OR REPLACE FUNCTION videos_search_vector_update()
        RETURNS TRIGGER AS $$
        BEGIN
            NEW.search_vector :=
                setweight(to_tsvector('simple', COALESCE(NEW.title, '')), 'A') ||
                setweight(to_tsvector('simple', COALESCE(NEW.description, '')), 'B');
            RETURN NEW;
        END;
        $$ LANGUAGE plpgsql;
        """
    )

    op.execute("DROP TRIGGER IF EXISTS videos_search_vector_trigger ON videos;")
    op.execute(
        """
        CREATE TRIGGER videos_search_vector_trigger
        BEFORE INSERT OR UPDATE ON videos
        FOR EACH ROW
        EXECUTE FUNCTION videos_search_vector_update();
        """
    )
    op.execute(
        """
        CREATE INDEX IF NOT EXISTS idx_videos_search_vector
        ON videos USING GIN(search_vector);
        """
    )
    op.execute(
        """
        CREATE INDEX IF NOT EXISTS idx_videos_title_trgm
        ON videos USING GIST (title gist_trgm_ops);
        """
    )
    op.execute(
        """
        UPDATE videos
        SET search_vector =
            setweight(to_tsvector('simple', COALESCE(title, '')), 'A') ||
            setweight(to_tsvector('simple', COALESCE(description, '')), 'B');
        """
    )

    # ===== Scratch Products =====
    if not _column_exists(inspector, "scratch_products", "search_vector"):
        op.add_column(
            "scratch_products",
            sa.Column("search_vector", postgresql.TSVECTOR(), nullable=True, comment="全文搜索向量"),
        )

    op.execute(
        """
        CREATE OR REPLACE FUNCTION scratch_products_search_vector_update()
        RETURNS TRIGGER AS $$
        BEGIN
            NEW.search_vector :=
                setweight(to_tsvector('simple', COALESCE(NEW.title, '')), 'A') ||
                setweight(to_tsvector('simple', COALESCE(NEW.description, '')), 'B') ||
                setweight(to_tsvector('simple', COALESCE(NEW.category, '')), 'C');
            RETURN NEW;
        END;
        $$ LANGUAGE plpgsql;
        """
    )

    op.execute("DROP TRIGGER IF EXISTS scratch_products_search_vector_trigger ON scratch_products;")
    op.execute(
        """
        CREATE TRIGGER scratch_products_search_vector_trigger
        BEFORE INSERT OR UPDATE ON scratch_products
        FOR EACH ROW
        EXECUTE FUNCTION scratch_products_search_vector_update();
        """
    )
    op.execute(
        """
        CREATE INDEX IF NOT EXISTS idx_scratch_products_search_vector
        ON scratch_products USING GIN(search_vector);
        """
    )
    op.execute(
        """
        CREATE INDEX IF NOT EXISTS idx_scratch_products_title_trgm
        ON scratch_products USING GIST (title gist_trgm_ops);
        """
    )
    op.execute(
        """
        UPDATE scratch_products
        SET search_vector =
            setweight(to_tsvector('simple', COALESCE(title, '')), 'A') ||
            setweight(to_tsvector('simple', COALESCE(description, '')), 'B') ||
            setweight(to_tsvector('simple', COALESCE(category, '')), 'C');
        """
    )


def downgrade() -> None:
    op.execute("DROP TRIGGER IF EXISTS scratch_products_search_vector_trigger ON scratch_products;")
    op.execute("DROP FUNCTION IF EXISTS scratch_products_search_vector_update();")
    op.execute("DROP INDEX IF EXISTS idx_scratch_products_title_trgm;")
    op.execute("DROP INDEX IF EXISTS idx_scratch_products_search_vector;")
    op.execute("ALTER TABLE scratch_products DROP COLUMN IF EXISTS search_vector;")

    op.execute("DROP TRIGGER IF EXISTS videos_search_vector_trigger ON videos;")
    op.execute("DROP FUNCTION IF EXISTS videos_search_vector_update();")
    op.execute("DROP INDEX IF EXISTS idx_videos_title_trgm;")
    op.execute("DROP INDEX IF EXISTS idx_videos_search_vector;")
    op.execute("ALTER TABLE videos DROP COLUMN IF EXISTS search_vector;")

    op.execute("DROP TRIGGER IF EXISTS articles_search_vector_trigger ON articles;")
    op.execute("DROP FUNCTION IF EXISTS articles_search_vector_update();")
    op.execute("DROP INDEX IF EXISTS idx_articles_title_trgm;")
    op.execute("DROP INDEX IF EXISTS idx_articles_search_vector;")
    op.execute("ALTER TABLE articles DROP COLUMN IF EXISTS search_vector;")
