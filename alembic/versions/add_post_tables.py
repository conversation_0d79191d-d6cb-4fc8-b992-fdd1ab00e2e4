"""Add post tables for 沸点功能

Revision ID: add_post_tables
Revises: 
Create Date: 2024-01-01 00:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'add_post_tables'
down_revision = None  # 请根据实际情况修改
branch_labels = None
depends_on = None


def upgrade():
    # 创建沸点主表
    op.create_table('posts',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('author_id', sa.Integer(), nullable=False),
        sa.Column('content', sa.Text(), nullable=True, comment='沸点文本内容'),
        sa.Column('post_type', sa.String(length=20), nullable=False, comment='沸点类型'),
        sa.Column('status', sa.String(length=20), nullable=False, comment='沸点状态'),
        sa.Column('visibility', sa.String(length=20), nullable=False, comment='可见性'),
        sa.Column('original_post_id', sa.Integer(), nullable=True, comment='原始沸点ID（转发时使用）'),
        sa.Column('repost_comment', sa.Text(), nullable=True, comment='转发评论'),
        sa.Column('topic', sa.String(length=100), nullable=True, comment='话题标签'),
        sa.Column('location', sa.String(length=200), nullable=True, comment='位置信息'),
        sa.Column('link_url', sa.String(length=500), nullable=True, comment='分享链接URL'),
        sa.Column('link_title', sa.String(length=200), nullable=True, comment='链接标题'),
        sa.Column('link_description', sa.Text(), nullable=True, comment='链接描述'),
        sa.Column('link_image', sa.String(length=500), nullable=True, comment='链接预览图'),
        sa.Column('poll_options', postgresql.JSONB(astext_type=sa.Text()), nullable=True, comment='投票选项JSON'),
        sa.Column('poll_expires_at', postgresql.TIMESTAMP(timezone=True), nullable=True, comment='投票截止时间'),
        sa.Column('poll_multiple_choice', sa.Boolean(), nullable=True, comment='是否允许多选'),
        sa.Column('like_count', sa.Integer(), nullable=True, comment='点赞数'),
        sa.Column('comment_count', sa.Integer(), nullable=True, comment='评论数'),
        sa.Column('repost_count', sa.Integer(), nullable=True, comment='转发数'),
        sa.Column('view_count', sa.Integer(), nullable=True, comment='浏览数'),
        sa.Column('is_pinned', sa.Boolean(), nullable=True, comment='是否置顶'),
        sa.Column('is_hot', sa.Boolean(), nullable=True, comment='是否热门'),
        sa.Column('hot_score', sa.Integer(), nullable=True, comment='热度分数'),
        sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), nullable=False),
        sa.Column('updated_at', postgresql.TIMESTAMP(timezone=True), nullable=False),
        sa.Column('cache_version', sa.Integer(), server_default='1', nullable=False, comment='缓存版本号'),
        sa.ForeignKeyConstraint(['author_id'], ['users.id'], ),
        sa.ForeignKeyConstraint(['original_post_id'], ['posts.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    
    # 创建索引
    op.create_index('idx_posts_author_created', 'posts', ['author_id', 'created_at'])
    op.create_index('idx_posts_status_created', 'posts', ['status', 'created_at'])
    op.create_index('idx_posts_type_created', 'posts', ['post_type', 'created_at'])
    op.create_index('idx_posts_topic', 'posts', ['topic'])
    op.create_index('idx_posts_hot_score', 'posts', ['hot_score'])
    op.create_index('idx_posts_visibility_status', 'posts', ['visibility', 'status'])
    op.create_index(op.f('ix_posts_id'), 'posts', ['id'])

    # 创建沸点媒体文件表
    op.create_table('post_media',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('post_id', sa.Integer(), nullable=False),
        sa.Column('media_type', sa.String(length=20), nullable=False, comment='媒体类型: image, video, audio'),
        sa.Column('media_url', sa.String(length=500), nullable=False, comment='媒体文件URL'),
        sa.Column('thumbnail_url', sa.String(length=500), nullable=True, comment='缩略图URL'),
        sa.Column('media_size', sa.Integer(), nullable=True, comment='文件大小（字节）'),
        sa.Column('media_width', sa.Integer(), nullable=True, comment='媒体宽度'),
        sa.Column('media_height', sa.Integer(), nullable=True, comment='媒体高度'),
        sa.Column('media_duration', sa.Integer(), nullable=True, comment='媒体时长（秒）'),
        sa.Column('sort_order', sa.Integer(), nullable=True, comment='排序顺序'),
        sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), nullable=False),
        sa.ForeignKeyConstraint(['post_id'], ['posts.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    
    op.create_index('idx_post_media_post_id', 'post_media', ['post_id'])
    op.create_index('idx_post_media_type', 'post_media', ['media_type'])
    op.create_index(op.f('ix_post_media_id'), 'post_media', ['id'])

    # 创建沸点@提及表
    op.create_table('post_mentions',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('post_id', sa.Integer(), nullable=False),
        sa.Column('mentioned_user_id', sa.Integer(), nullable=False),
        sa.Column('mention_text', sa.String(length=100), nullable=False, comment='@提及的文本'),
        sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), nullable=False),
        sa.ForeignKeyConstraint(['mentioned_user_id'], ['users.id'], ),
        sa.ForeignKeyConstraint(['post_id'], ['posts.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    
    op.create_index('idx_post_mentions_post_id', 'post_mentions', ['post_id'])
    op.create_index('idx_post_mentions_user_id', 'post_mentions', ['mentioned_user_id'])
    op.create_index(op.f('ix_post_mentions_id'), 'post_mentions', ['id'])

    # 创建沸点投票记录表
    op.create_table('post_poll_votes',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('post_id', sa.Integer(), nullable=False),
        sa.Column('user_id', sa.Integer(), nullable=False),
        sa.Column('option_index', sa.Integer(), nullable=False, comment='投票选项索引'),
        sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), nullable=False),
        sa.ForeignKeyConstraint(['post_id'], ['posts.id'], ),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    
    op.create_index('idx_post_poll_votes_post_id', 'post_poll_votes', ['post_id'])
    op.create_index('idx_post_poll_votes_user_id', 'post_poll_votes', ['user_id'])
    op.create_index('idx_post_poll_votes_unique', 'post_poll_votes', ['post_id', 'user_id', 'option_index'], unique=True)
    op.create_index(op.f('ix_post_poll_votes_id'), 'post_poll_votes', ['id'])

    # 更新reviews表，添加post内容类型支持
    op.execute("ALTER TYPE content_type ADD VALUE 'post'")


def downgrade():
    # 删除表和索引
    op.drop_table('post_poll_votes')
    op.drop_table('post_mentions')
    op.drop_table('post_media')
    op.drop_table('posts')
    
    # 注意：无法直接删除枚举值，需要重新创建枚举类型
    # 这里省略了复杂的枚举类型回滚逻辑
