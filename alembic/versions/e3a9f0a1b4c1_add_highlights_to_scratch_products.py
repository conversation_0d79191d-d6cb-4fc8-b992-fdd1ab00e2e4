"""add highlights column to scratch products"""

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = "e3a9f0a1b4c1"
down_revision = "c059f8d3ae2b"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.add_column(
        "scratch_products",
        sa.Column(
            "highlights",
            sa.JSON(),
            nullable=True,
            comment="项目亮点列表",
            server_default=sa.text("'[]'::json"),
        ),
    )
    op.execute("UPDATE scratch_products SET highlights = '[]'::json WHERE highlights IS NULL")
    op.alter_column("scratch_products", "highlights", server_default=None)


def downgrade() -> None:
    op.drop_column("scratch_products", "highlights")
