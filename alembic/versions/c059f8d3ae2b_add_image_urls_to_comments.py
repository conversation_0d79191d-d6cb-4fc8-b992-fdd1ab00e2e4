"""add image urls to comments"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = "c059f8d3ae2b"
down_revision = "4d1a35ceac0a"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.add_column(
        "comments",
        sa.Column(
            "image_urls",
            postgresql.JSONB(astext_type=sa.Text()),
            nullable=False,
            server_default=sa.text("'[]'::jsonb"),
        ),
    )
    op.alter_column("comments", "image_urls", server_default=None)


def downgrade() -> None:
    op.drop_column("comments", "image_urls")
