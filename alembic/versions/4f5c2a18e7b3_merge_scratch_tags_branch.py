"""merge scratch related heads

Revision ID: 4f5c2a18e7b3
Revises: ('1d8b1ce4c7d2', 'fix_backpack_user_id')
Create Date: 2025-09-30 12:30:00.000000

"""
from collections.abc import Sequence

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "4f5c2a18e7b3"
down_revision = ("1d8b1ce4c7d2", "fix_backpack_user_id")
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    # 合并迁移，不包含额外数据库变更
    pass


def downgrade() -> None:
    # 回滚同样无需处理
    pass
