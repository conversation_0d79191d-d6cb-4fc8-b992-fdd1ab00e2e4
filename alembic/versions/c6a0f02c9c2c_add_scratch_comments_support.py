"""
add scratch comments support

Revision ID: c6a0f02c9c2c
Revises: 87b46ed94cd9
Create Date: 2025-09-28 06:26:37

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'c6a0f02c9c2c'
down_revision = '87b46ed94cd9'
branch_labels = None
depends_on = None


def upgrade():
    op.execute("ALTER TYPE comment_type ADD VALUE IF NOT EXISTS 'scratch'")
    op.add_column('comments', sa.Column('scratch_id', sa.Integer(), nullable=True))
    op.create_index('ix_comments_scratch_id', 'comments', ['scratch_id'])
    op.create_foreign_key(
        'comments_scratch_id_fkey',
        'comments',
        'scratch_products',
        ['scratch_id'],
        ['project_id'],
        ondelete='SET NULL'
    )


def downgrade():
    op.drop_constraint('comments_scratch_id_fkey', 'comments', type_='foreignkey')
    op.drop_index('ix_comments_scratch_id', table_name='comments')
    op.drop_column('comments', 'scratch_id')

    op.execute("UPDATE comments SET comment_type = 'article' WHERE comment_type = 'scratch'")

    op.execute("ALTER TYPE comment_type RENAME TO comment_type_old")
    new_comment_type = postgresql.ENUM('article', 'video', name='comment_type')
    new_comment_type.create(op.get_bind())
    op.execute(
        "ALTER TABLE comments ALTER COLUMN comment_type TYPE comment_type "
        "USING comment_type::text::comment_type"
    )
    op.execute("DROP TYPE comment_type_old")
