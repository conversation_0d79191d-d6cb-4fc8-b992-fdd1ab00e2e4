"""add tag_stats table for unified hot tags

Revision ID: 1f79d8f78ef0
Revises: 0a6e2c539f38
Create Date: 2025-10-12 10:15:00.000000
"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.sql import func

# revision identifiers, used by Alembic.
revision: str = "1f79d8f78ef0"
down_revision: Union[str, None] = "0a6e2c539f38"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.create_table(
        "tag_stats",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("tag_id", sa.Integer(), nullable=False),
        sa.Column("content_type", sa.String(length=32), nullable=False),
        sa.Column("total_score", sa.Float(), nullable=False, server_default="0"),
        sa.Column("unique_users_est", sa.Integer(), nullable=False, server_default="0"),
        sa.Column("window_delta", sa.Float(), nullable=False, server_default="0"),
        sa.Column("last_seen_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column(
            "created_at",
            sa.DateTime(timezone=True),
            nullable=False,
            server_default=func.now(),
        ),
        sa.Column(
            "updated_at",
            sa.DateTime(timezone=True),
            nullable=False,
            server_default=func.now(),
        ),
        sa.ForeignKeyConstraint(
            ["tag_id"],
            ["tags.id"],
            name="fk_tag_stats_tag_id",
            ondelete="CASCADE",
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("tag_id", "content_type", name="uq_tag_stats_tag_content_type"),
    )
    op.create_index("ix_tag_stats_tag_id", "tag_stats", ["tag_id"])
    op.create_index("ix_tag_stats_content_type", "tag_stats", ["content_type"])


def downgrade() -> None:
    op.drop_index("ix_tag_stats_content_type", table_name="tag_stats")
    op.drop_index("ix_tag_stats_tag_id", table_name="tag_stats")
    op.drop_table("tag_stats")
