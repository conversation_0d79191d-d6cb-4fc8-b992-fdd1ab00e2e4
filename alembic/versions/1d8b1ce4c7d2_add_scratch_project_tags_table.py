"""add scratch project tags table

Revision ID: 1d8b1ce4c7d2
Revises: c6a0f02c9c2c
Create Date: 2025-09-30 12:00:00.000000

"""
from collections.abc import Sequence

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "1d8b1ce4c7d2"
down_revision: str | None = "c6a0f02c9c2c"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    op.create_table(
        "scratch_project_tags",
        sa.Column("project_id", sa.Integer(), sa.<PERSON>("scratch_products.project_id"), primary_key=True),
        sa.<PERSON>umn("tag_id", sa.Integer(), sa.<PERSON>ey("tags.id"), primary_key=True),
        sa.Column("created_at", sa.DateTime(timezone=True), server_default=sa.func.now(), nullable=False),
    )


def downgrade() -> None:
    op.drop_table("scratch_project_tags")

