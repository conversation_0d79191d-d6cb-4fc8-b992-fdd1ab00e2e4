# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg
logs/

# Virtual Environment
venv/
env/
ENV/

# IDE files
.idea/
.vscode/
.codelf/
.claude/
.codex/
.specify/
.augment/
.ruff_cache/
*.swp
*.swo

# Environment variables
.env*

# Database
steam_data.db

# Docker
Dockerfile
README.docker.md

# Project specific
uv.lock
steam_aggregation_backend.egg-info/

# General
.DS_Store
Thumbs.db

