# Spec Kit 集成说明
- 使用中文回答
最后更新：2025-10-11

## 环境状态
- `uv 0.8.10` 已安装，可用于依赖管理与 `specify` CLI 安装。
- `specify-cli 0.0.19` 已就绪，可执行 `specify init/check` 等命令。
- 系统默认 `python3 --version` 为 3.8.10，但项目虚拟环境 `.venv` 使用 `Python 3.12.10`，已满足 README 对 3.11+ 的要求。
- 初始化生成了 `.specify/`、`.codex/`、`AGENTS.md` 等 Spec Kit 配套目录与脚本。

## 运行前准备
- 在终端中导出 `CODEX_HOME=/usr/local/data/steam_Aggregation_backend/.codex`，确保 Codex CLI 能读取 Slash 命令所需的缓存与配置。
- `.codex/` 可能保存代理认证信息，如需避免泄露，请在 `.gitignore` 加入 `.codex/` 或明确排除敏感子目录。
- 建议先执行 `specify check` 验证 Git、AI 代理 CLI、Python 等依赖是否满足运行要求。

## Spec Kit 工作流
1. `/speckit.constitution`：定义代码质量、测试、性能、用户体验等原则，为后续规格与计划提供约束。
2. `/speckit.specify`：编写功能规格，聚焦"做什么/为什么"，不讨论具体实现细节。
3. `/speckit.plan`：结合 FastAPI、SQLAlchemy、Celery 等既有技术栈生成实现方案与工件（数据模型、API 契约等）。
4. `/speckit.tasks`：拆分成可执行任务，确保与 `app/`、`tests/` 目录结构相呼应，便于团队协作。
5. `/speckit.implement`：依据任务顺序执行开发；必要时可插入 `/speckit.clarify`、`/speckit.analyze`、`/speckit.checklist` 等提升结果质量。

## 当前进行中的功能

### 管理系统增强阶段一（配置与监控基线）
**分支**: `002-admin-enhancement-phase1`
**状态**: Phase 0 完成，准备进入基础架构实施阶段

#### 已完成工件
- **API 契约**: `.specify/specs/002-admin-enhancement-phase1/contracts/`
  - `admin_config.md` - 配置管理 API 契约
  - `admin_monitoring.md` - 监控告警 API 契约
  - `admin_analytics.md` - 数据分析 API 契约
- **数据模型**: `.specify/specs/002-admin-enhancement-phase1/data-model.md`
  - 完整的数据库表结构设计
  - 索引优化策略
  - 数据保留策略
- **技术调研**: `.specify/specs/002-admin-enhancement-phase1/research.md`
  - 指标采集方案评估
  - 数据源分析
  - 技术选型建议
  - 风险评估与缓解措施
- **快速入门**: `.specify/specs/002-admin-enhancement-phase1/quickstart.md`
  - 环境准备指南
  - 服务启动流程
  - 功能验证步骤
  - 常见问题解决

#### 核心功能模块
1. **动态配置中心** (P0)
   - 配置查询、更新、回滚
   - 功能开关管理
   - 配置变更审计
   - 敏感字段脱敏

2. **系统监控与告警** (P1)
   - 实时性能监控
   - 告警规则管理
   - 多渠道通知
   - 监控面板展示

3. **基础数据分析** (P1)
   - 用户活跃度统计
   - 内容访问分析
   - 系统性能统计
   - 数据导出功能

#### 配置中心注意事项
- **权限要求**: 所有配置操作需要相应的 RBAC 权限
  - `admin_config:read` - 配置查询
  - `admin_config:write` - 配置修改
  - `admin_monitoring:*` - 监控功能权限
  - `admin_analytics:*` - 分析功能权限

- **性能要求**:
  - 配置更新到生效延迟 ≤ 5秒
  - 监控面板响应 < 500ms
  - 数据导出 50k行 < 40秒

- **安全要求**:
  - 敏感配置值在响应中脱敏
  - 所有操作记录审计日志
  - 配置回滚需要二次确认

- **运维要求**:
  - 配置变更需要广播到所有服务节点
  - 监控数据需要定期清理
  - 告警规则需要定期测试

## 与现有项目约定的衔接
- 目录职责保持不变：`app/` 存放 FastAPI 业务模块（`api/`、`services/`、`crud/`、`schemas/`、`tasks/`），`tests/` 镜像业务结构，`alembic/` 管理数据库迁移，`scripts/` 与 `run_worker.sh`/`run_beat.sh` 提供运维脚本。
- 常用命令：`uv pip install -r pyproject.toml` 安装依赖，`uvicorn app.main:app --reload` 启动本地 API，`pytest` 运行测试，`ruff check app tests` 进行静态检查，`alembic upgrade head` 同步数据库。
- 编码规范：4 空格缩进、100 字符行宽、倾向双引号；模块使用蛇形命名，类使用帕斯卡命名；按"标准库 → 第三方 → app.*"顺序分组导入。
- 测试策略：`test_*.py` 命名规则，新增功能需在 `tests/` 对应模块提供用例；Celery 任务复用 `tests/mocks/` 测试桩，目标覆盖率 ≥85%。
- 提交流程：遵循 `type(scope): summary` 规范（type 取 `feat`/`fix`/`refactor`/`docs`/`chore` 等），PR 需说明问题背景、解决方案、验证方式以及关联 issue/工单。
- 运维提醒：使用 `.env` 装载本地变量，`docker-compose.yml` 启动全栈环境，执行 `./run_worker.sh` 与 `./run_beat.sh` 前保证 Redis 服务就绪。

## 新增工作流指南

### 配置管理工作流
1. **配置变更流程**:
   ```
   1. 提交配置变更请求 → 2. 权限验证 → 3. 配置验证 → 4. 数据库更新 →
   5. 缓存刷新 → 6. 广播通知 → 7. 审计记录
   ```

2. **功能开关管理**:
   ```
   1. 创建开关定义 → 2. 代码集成监听 → 3. 开关状态变更 →
   4. 实时生效 → 5. 状态监控
   ```

3. **配置回滚流程**:
   ```
   1. 选择回滚点 → 2. 确认回滚范围 → 3. 执行回滚 →
   4. 验证结果 → 5. 记录回滚审计
   ```

### 监控告警工作流
1. **指标采集流程**:
   ```
   1. 定时采集触发 → 2. 多源数据收集 → 3. 数据预处理 →
   4. 存储到时序数据库 → 5. 更新缓存
   ```

2. **告警触发流程**:
   ```
   1. 规则评估 → 2. 阈值判断 → 3. 告警生成 →
   4. 通知发送 → 5. 状态跟踪
   ```

3. **告警处理流程**:
   ```
   1. 告警接收 → 2. 问题诊断 → 3. 问题解决 →
   4. 告警确认 → 5. 处理记录
   ```

### 数据分析工作流
1. **数据聚合流程**:
   ```
   1. 定时任务触发 → 2. 数据源查询 → 3. 数据聚合计算 →
   4. 结果存储 → 5. 缓存更新
   ```

2. **数据导出流程**:
   ```
   1. 导出请求 → 2. 权限验证 → 3. 任务创建 →
   4. 异步处理 → 5. 文件生成 → 6. 下载通知
   ```

## 待办提示
- 如果后续在系统级执行 `specify`，请确保使用 `.venv`（或等效 3.11+ 环境）；避免落回 3.8.10 以免兼容问题。
- 运行 `/speckit.constitution` 生成项目宪章后，请在团队内共享并将关键准则纳入 code review 评估标准。
- **新增**: 实施配置中心功能时，请务必参考 `.specify/specs/002-admin-enhancement-phase1/quickstart.md` 中的配置中心注意事项。
- **新增**: 监控功能实施时，请注意性能影响，建议先在测试环境验证采集频率对系统的影响。
- **新增**: 数据分析功能涉及大量数据查询，请确保数据库索引优化，避免影响生产环境性能。
